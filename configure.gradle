def BUILD_ID = "" //Temporary, do not commit with a value.  This is so you can test against a dexus pr build.
def GROUP_SUFFIX = (!BUILD_ID.isEmpty() ? ".TMP.Azure.Pipeline-" + BUILD_ID : "")

ext.dexus = [
        'buildLocal': false,
        'snapshot'  : false,
        'version'   : '0.17.6',
        'group'     : 'com.mindbodyonline.android' + GROUP_SUFFIX
]

ext.androidSdk = [
        'targetSdkVersion'  : 35,
        'compileSdkVersion' : 35, // Should be greater or equal to targetSdkVersion
        'minSdkVersion'     : 23,
        'supportLibVersion' : '34.0.0' // Should match the compile SDK version
]
