package com.mindbodyonline.ui.screen.schedule

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.mindbodyonline.ui.R
import com.mindbodyonline.ui.common.model.TabContent
import com.mindbodyonline.ui.common.views.HorizontalPagerWithTabs
import com.mindbodyonline.ui.common.views.MBNavLayoutContainer
import com.mindbodyonline.ui.common.views.TopBarMenuItem
import com.mindbodyonline.ui.component.mbClass.ClassScheduleState
import java.util.Calendar

@Composable
fun BusinessScheduleScreen(
    onBackPressed: () -> Unit = {},
    timezoneId: String?,
    emptyListText: String,
    emptyListSubText: String,
    menuItems: List<TopBarMenuItem> = listOf(),
    hasClasses: Boolean = false,
    hasAppointments: Boolean = false,
    onClassClick: (Int) -> Unit = {},
    onActionButtonClick: (Int) -> Unit = {},
    classScheduleState: ClassScheduleState = ClassScheduleState(),
    appointmentCategories: List<Pair<String?, String?>> = listOf(),
    onAppointmentCategoryClick: (String?) -> Unit = {},
    onDateSelected: (Calendar, Calendar) -> Unit,
    goToDate: Calendar? = null,
    onTabSelected: (String) -> Unit = {},
) {
    // for showing progress bar between activity initialization and fetching class schedules
    // the class schedule list level progress bar is shown separately in the ClassList pager UI
    val isLoading = remember { mutableStateOf(true) }

    val pagerScreenList: MutableList<TabContent> = mutableListOf()
    if (hasClasses) {
        pagerScreenList.add(
            TabContent(
                title = stringResource(R.string.classes),
                id = "classes_tab",
                action = {
                    ScheduleClassesScreen(
                        classScheduleState = classScheduleState,
                        emptyListText = emptyListText,
                        emptyListSubText = emptyListSubText,
                        onDateSelected = { startDate, endDate ->
                            isLoading.value = false
                            onDateSelected.invoke(startDate, endDate)
                        },
                        onClassClick = onClassClick,
                        onActionButtonClick = onActionButtonClick,
                        timezoneId = timezoneId,
                        goToDate = goToDate
                    )
                }
            )
        )
    } else {
        isLoading.value = false
    }
    if (hasAppointments) {
        pagerScreenList.add(
            TabContent(
                title = stringResource(R.string.appointments),
                id = "appointments_tab",
                action = {
                    ScheduleAppointmentsScreen(
                        categories = appointmentCategories,
                        onCategoryClick = onAppointmentCategoryClick
                    )
                }
            )
        )
    }

    MBNavLayoutContainer(
        title = stringResource(R.string.schedule),
        menuItems = menuItems,
        isLoading = isLoading.value,
        onBack = { onBackPressed() },
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            HorizontalPagerWithTabs(
                pagerScreenList = pagerScreenList,
                onTabSelected = onTabSelected
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun BusinessScheduleScreenPreview() {
    BusinessScheduleScreen(
        onBackPressed = {},
        onAppointmentCategoryClick = {},
        emptyListText = "",
        emptyListSubText = "",
        timezoneId = "America/Los_Angeles",
        goToDate = null,
        onDateSelected = { startDate, endDate -> }
    )
}
