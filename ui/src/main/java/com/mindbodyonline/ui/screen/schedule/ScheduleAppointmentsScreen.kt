package com.mindbodyonline.ui.screen.schedule

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mindbodyonline.ui.R
import com.mindbodyonline.ui.theme.dividerColor
import com.mindbodyonline.ui.theme.neutralBlack
import com.mindbodyonline.ui.theme.neutralDarkGrey

@Composable
fun ScheduleAppointmentsScreen(
    categories: List<Pair<String?, String?>>,
    onCategoryClick: (String?) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color.White)
    ) {
        categories.forEach { (categoryName, appointmentCount) ->
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
                    .clickable { onCategoryClick(categoryName) }
                    .semantics { contentDescription = "AppointmentCategoryRow" }
            ) {
                Text(
                    text = categoryName ?: "",
                    color = neutralBlack,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold
                )
                Text(
                    text = appointmentCount ?: "",
                    color = neutralBlack,
                    fontSize = 14.sp,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
            HorizontalDivider(
                modifier = Modifier.fillMaxWidth(),
                color = dividerColor,
                thickness = 1.dp
            )
        }

        if (categories.isEmpty()) {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                Text(
                    text = stringResource(R.string.no_appointments_available),
                    fontSize = 16.sp,
                    color = neutralDarkGrey
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SchedAppointmentsScreenPreview() {
    ScheduleAppointmentsScreen(
        categories = listOf(
            Pair("Yoga appointment", "12 appointments"),
            Pair("Yoga appointment", "12 appointments"),
            Pair("Yoga appointment", "12 appointments"),
            Pair("Yoga appointment", "12 appointments"),
        ),
        onCategoryClick = {}
    )
}
