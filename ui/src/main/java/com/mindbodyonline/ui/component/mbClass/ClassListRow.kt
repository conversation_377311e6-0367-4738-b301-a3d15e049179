package com.mindbodyonline.ui.component.mbClass

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mindbodyonline.ui.theme.basil
import com.mindbodyonline.ui.theme.bgLightGrey
import com.mindbodyonline.ui.theme.brandLightGreen
import com.mindbodyonline.ui.theme.dividerColor
import com.mindbodyonline.ui.theme.neutralBlack
import com.mindbodyonline.ui.theme.neutralDarkGrey
import com.mindbodyonline.ui.theme.neutralGrey
import com.mindbodyonline.ui.theme.neutralLightGrey
import java.util.Calendar

@Composable
fun ClassListRow(
    classListItem: ClassListItem,
    onClassClick: () -> Unit = {},
    onActionButtonClick: () -> Unit = {},
) {
    Column {
        HorizontalDivider(color = dividerColor)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .defaultMinSize(minHeight = 100.dp)
                .background(color = Color.White)
                .clickable(onClick = onClassClick)
                .padding(vertical = 8.dp)
                .semantics { contentDescription = "ClassListRow" },
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.Top
        ) {
            Row(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 16.dp, end = 8.dp)
                    .align(Alignment.CenterVertically)
            ) {
                Column(
                    horizontalAlignment = Alignment.Start,
                    modifier = Modifier.padding(end = 16.dp)
                ) {

                    classListItem.startTimeText?.let {
                        Text(
                            text = it,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = neutralBlack,
                        )
                    }

                    classListItem.timeZoneText?.let {
                        Text(
                            text = it,
                            fontSize = 14.sp,
                            color = neutralBlack,
                            fontWeight = FontWeight.SemiBold,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )
                    }

                    Text(
                        text = "(${classListItem.duration}min)",
                        fontSize = 14.sp,
                        color = neutralDarkGrey
                    )
                }

                Column(
                    horizontalAlignment = Alignment.Start,
                    modifier = Modifier
                        .padding(end = 16.dp)
                        .align(Alignment.CenterVertically)
                ) {
                    classListItem.classTypeText?.let {
                        ClassTypeText(
                            iconResId = classListItem.classTypeIcon,
                            textToDisplay = it
                        )
                    }

                    Text(
                        text = classListItem.name,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = neutralBlack,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    Text(
                        text = "w/ ${classListItem.staffName}",
                        fontSize = 12.sp,
                        color = neutralDarkGrey,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }

            Column(
                horizontalAlignment = Alignment.End,
                modifier = Modifier
                    .padding(end = 16.dp)
                    .align(Alignment.CenterVertically)
            ) {
                if (classListItem.showDynamicPrice) {
                    Text(
                        text = classListItem.dynamicPriceText ?: "",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = if (classListItem.highlightDynamicPrice) basil else neutralDarkGrey
                    )
                }
                if (classListItem.showOriginalPrice) {
                    Text(
                        text = classListItem.originalPriceText ?: "",
                        fontSize = 12.sp,
                        color = if (classListItem.highlightOriginalPrice) basil else neutralDarkGrey
                    )
                }
                if (classListItem.isPrimaryCta) {
                    classListItem.ctaText?.let {
                        Surface(
                            shape = RoundedCornerShape(4.dp),
                            color = brandLightGreen,
                            border = BorderStroke(1.dp, basil),
                            modifier = Modifier
                                .clickable(onClick = onActionButtonClick)
                                .padding(
                                    vertical = 4.dp
                                )
                        ) {
                            Text(
                                text = it.uppercase(),
                                color = basil,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.SemiBold,
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                            )
                        }
                    }
                } else {
                    classListItem.ctaText?.let {
                        Surface(
                            color = Color.White,
                            border = BorderStroke(1.dp, neutralGrey),
                            modifier = Modifier
                                .clickable(onClick = onActionButtonClick)
                                .padding(
                                    vertical = 4.dp
                                )
                        ) {
                            Text(
                                text = it.uppercase(),
                                color = neutralDarkGrey,
                                fontSize = 14.sp,
                                modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ClassTypeText(
    iconResId: Int?,
    textToDisplay: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(bottom = 4.dp)
    ) {
        if (iconResId != null) {
            Icon(
                painter = painterResource(id = iconResId),
                contentDescription = null,
                modifier = Modifier.padding(end = 8.dp)
            )
        }
        Text(
            text = textToDisplay,
            fontSize = 12.sp,
            color = neutralBlack,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )
    }

}


@Preview(showBackground = true)
@Composable
fun ClassListRowPreview() {
    val sampleClassItem = ClassListItem(
        name = "Class name",
        classTypeText = "BARRE",
        startTimeText = "12:00 pm",
        timeZoneText = "PDT",
        staffName = "John Smith",
        startTime = Calendar.getInstance(),
        endTime = Calendar.getInstance(),
        duration = 60,
        dynamicPriceText = "$15.00",
        originalPriceText = "was $18.00",
        ctaText = "Book",
        isPrimaryCta = true,
        showOriginalPrice = true,
        showDynamicPrice = true
    )

    ClassListRow(
        classListItem = sampleClassItem,
        onClassClick = {},
        onActionButtonClick = {}
    )
}
