package com.mindbodyonline.ui.common.views

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.mindbodyonline.ui.common.model.TabContent
import com.mindbodyonline.ui.theme.baseLight
import com.mindbodyonline.ui.theme.bgLightGrey
import com.mindbodyonline.ui.theme.neutralBlack
import kotlinx.coroutines.launch

@Composable
fun HorizontalPagerWithTabs(
    pagerScreenList: List<TabContent>,
    onTabSelected: ((String) -> Unit)? = null
) {
    if (pagerScreenList.isEmpty()) return

    val coroutineScope = rememberCoroutineScope()
    val pagerState = rememberPagerState(pageCount = { pagerScreenList.size })

    LaunchedEffect(pagerState.currentPage) {
        onTabSelected?.invoke(pagerScreenList[pagerState.currentPage].id)
    }

    if (pagerScreenList.size > 1) {
        TabRow(
            selectedTabIndex = pagerState.currentPage,
            containerColor = bgLightGrey,
            modifier = Modifier.fillMaxWidth(),
            indicator = { tabPositions ->
                Box(
                    modifier = Modifier
                        .tabIndicatorOffset(tabPositions[pagerState.currentPage])
                        .width(tabPositions[pagerState.currentPage].width)
                        .height(4.dp)
                        .background(color = neutralBlack)
                )
            }
        ) {
            pagerScreenList.forEachIndexed { index, tabContent ->
                Tab(
                    selected = pagerState.currentPage == index,
                    onClick = {
                        coroutineScope.launch {
                            pagerState.animateScrollToPage(index)
                        }
                    }
                ) {
                    Text(
                        text = tabContent.title,
                        modifier = Modifier.padding(16.dp),
                        color = neutralBlack
                    )
                }
            }
        }
    }

    HorizontalPager(
        state = pagerState,
        modifier = Modifier.background(baseLight)
    ) { page ->
        pagerScreenList[page].action.invoke()
    }
}

@Preview(showBackground = true)
@Composable
fun HorizontalPagerWithTabsPreview() {
    val tabs: List<TabContent> = listOf(
        TabContent(
            title = "Tab 1",
            id = "tab1",
            action = {
                Text(
                    "Content of Tab 1", modifier = Modifier
                        .fillMaxSize()
                        .wrapContentSize(Alignment.Center)
                )
            }
        ),
        TabContent(
            title = "Tab 2",
            id = "tab2",
            action = {
                Text(
                    "Content of Tab 2", modifier = Modifier
                        .fillMaxSize()
                        .wrapContentSize(Alignment.Center)
                )
            }
        ),
        TabContent(
            title = "Tab 3",
            id = "tab3",
            action = {
                Text(
                    "Content of Tab 3", modifier = Modifier
                        .fillMaxSize()
                        .wrapContentSize(Alignment.Center)
                )
            }
        )
    )

    HorizontalPagerWithTabs(pagerScreenList = tabs)
}
