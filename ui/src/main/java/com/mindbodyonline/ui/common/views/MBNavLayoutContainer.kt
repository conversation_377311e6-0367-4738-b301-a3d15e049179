package com.mindbodyonline.ui.common.views

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mindbodyonline.ui.R
import com.mindbodyonline.ui.screen.appointment.components.AppointmentBookingBottomBar

data class TopBarMenuItem(
    @DrawableRes val iconResId: Int,
    val contentDescription: String? = null,
    val badgeCount: MutableState<Int>? = null,
    val onClick: () -> Unit,
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MBNavLayoutContainer(
        title: String,
        onBack: (() -> Unit),
        bottomBar: @Composable (() -> Unit) = {},
        menuItems: List<TopBarMenuItem> = emptyList(),
        isLoading: Boolean = false,
        content: @Composable (PaddingValues) -> Unit
) {
    Scaffold(
            topBar = {
                CenterAlignedTopAppBar(
                        modifier = Modifier.height(40.dp),
                        title = {
                            Text(
                                    text = title,
                                    style = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.SemiBold),
                                    modifier = Modifier
                                            .fillMaxHeight()
                                            .wrapContentHeight(Alignment.CenterVertically)
                            )
                        },
                        navigationIcon = {
                            IconButton(onClick = onBack) {
                                Icon(
                                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                        contentDescription = "Back"
                                )
                            }
                        },
                        actions = {
                            menuItems.forEach { item ->
                                Box(
                                    modifier = Modifier
                                        .fillMaxHeight()
                                        .padding(horizontal = 8.dp)
                                        .clickable(onClick = item.onClick),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        painter = painterResource(id = item.iconResId),
                                        contentDescription = item.contentDescription,
                                    )
                                    item.badgeCount?.value?.takeIf { it > 0 }?.let {
                                        Text(
                                            text = it.toString(),
                                            fontSize = 12.sp,
                                            modifier = Modifier.padding(start = 28.dp, top = 20.dp)
                                        )
                                    }
                                }
                            }
                        },
                        colors = TopAppBarDefaults.topAppBarColors(containerColor = Color.White)
                )
            },
            bottomBar = bottomBar,
    ) { paddingValues ->
        content(paddingValues)
    }
    if (isLoading) {
        Box(
                modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.3f)), // Dim the background
                contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(color = Color.Black)
        }
    }
}

@Preview
@Composable
private fun MBLayoutContainerPreview() {
    MBNavLayoutContainer(
            title = "Home",
            onBack = {},
            bottomBar = {
                AppointmentBookingBottomBar(
                        bottomBarText = "Pay after your service",
                        bottomBarButtonText = "Book",
                        onBottomBarButtonClick = {},
                )
            },
            menuItems = listOf(
                TopBarMenuItem(
                    iconResId = R.drawable.ic_heart_filled_in,
                    badgeCount = remember { mutableIntStateOf(1) },
                    onClick = {}
                ),
                TopBarMenuItem(
                    iconResId = R.drawable.share_icon,
                    onClick = {}
                )
            ),
    ) { }
}
