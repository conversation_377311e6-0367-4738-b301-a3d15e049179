<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Select time screen -->

    <string name="selectTime_expanded_header_text">Choose a time with %s</string>
    <string name="selectTime_collapsed_header_text">Choose a time</string>
    <string name="selectTime_no_times_available">Sorry, there\'s nothing available</string>
    <string name="selectTime_next_available_booking_desc">The next available booking is %s %s %s</string>
    <string name="selectTime_go_to_date_cta">Go to %1$s, %2$s %3$s</string>
    <string name="selectTime_no_dates_in_range_desc">See what\’s available in the next %d days</string>
    <string name="selectTime_load_more_label">Load more dates</string>

    <string name="morning">Morning</string>
    <string name="afternoon">Afternoon</string>
    <string name="evening">Evening</string>

    <!-- Booking Confirmation screen -->

    <string name="bookingConfirmation_add_to_calendar">Add to calendar</string>
    <string name="bookingConfirmation_share_class">Share this class</string>
    <string name="bookingConfirmation_message_label">Message</string>
    <string name="bookingConfirmation_copy_link">Copy link</string>
    <string name="bookingConfirmation_link_copied">Link copied</string>
    <string name="bookingConfirmation_share_classpass">Share ClassPass</string>
    <string name="bookingConfirmation_share_link">Share link</string>
    <string name="bookingConfirmation_give_classpass">Give %s of free ClassPass</string>
    <string name="bookingConfirmation_share_classpass_gift"><![CDATA[Share your love of studio fitness with a friend. They\'ll get %s free to try top-rated studios & gyms on ClassPass.]]></string>
    <string name="bookingConfirmation_learn_more">Learn more</string>
    <string name="bookingConfirmation_invite_section_title">Have you seen our new Deals section?</string>
    <string name="bookingConfirmation_invite_section_body">Get access to exclusive deals and more from the best brands!</string>
    <string name="bookingConfirmation_view_deals">View deals</string>

    <string name="bookingConfirmation_check_label">Check</string>
    <string name="bookingConfirmation_share_label">Share</string>
    <string name="bookingConfirmation_classpass_label">ClassPass</string>
    <string name="bookingConfirmation_deals_label">Deals</string>

    <!-- Phone Input screen -->

    <string name="phoneInput_title">Stay updated on waitlist changes</string>
    <string name="phoneInput_subtitle">What\'s your number?</string>
    <string name="phoneInput_fine_print">Please ensure your phone number is up to date. By entering in your number you agree to be contacted about this booking, terms apply.</string>
    <!-- The word or phrase in the above text that should be a clickable link -->
    <string name="phoneInput_fine_print_hyperlink_text">terms</string>
    <!-- The url the word/phrase above should open when clicked -->
    <string name="phoneInput_fine_print_hyperlink_url">https://co.mindbodyonline.com/legal/sms-texting-terms</string>
    <string name="phoneInput_cta_label">Confirm</string>

    <!-- Waitlist Consent screen -->

    <string name="waitlistConsent_title">Join the waitlist</string>
    <string name="waitlistConsent_fine_print">By clicking “Confirm”, you agree to receiving SMS from [name of studio] to the number on your profile with updates about the waitlist. SMS Texting Terms apply.</string>
    <string name="waitlistConsent_fine_print_hyperlink_text">SMS Texting Terms</string>"
    <string name="waitlistConsent_fine_print_hyperlink_url">@string/phoneInput_fine_print_hyperlink_url</string>
    <string name="waitlistConsent_cta_label">Confirm</string>
    <string name="waitlistConsent_cancel">Go back</string>

    <!-- General -->

    <string name="back_button_ada_description">Back button</string>
    <string name="close_button_text">Close</string>
    <string name="content_description_favorite">Favorite</string>

    <string name="select_payment">Select Payment</string>
    <string name="select_payment_method">Select payment method</string>
    <string name="add_credit_card">Add Credit Card</string>
    <string name="confirm_appointment">Confirm Appointment</string>
    <string name="request_appointment">Request Appointment</string>

    <string name="credit_card_required">Credit Card Required</string>
    <string name="late_no_show_cancellation_policy_text">Free cancellation before %1$s at %2$s. Afterward, a late cancellation fee of $%3$s or a no-show fee of $%4$s may apply.</string>
    <string name="no_show_cancellation_policy_text">Free cancellation before %1$s at %2$s. Afterward, a no-show fee of $%3$s may apply.</string>
    <string name="late_cancellation_policy_text">Free cancellation before %1$s at %2$s. Afterward, a late cancellation fee of $%3$s may apply.</string>
    <string name="cancellation_policy_text">Free cancellation.</string>
    <string name="late_cancel_or_no_show_fee_text">Late cancel or no show fees may apply.</string>
    <string name="no_consumer_mode_cancellation_policy_text">To cancel this appointment you will need to contact the business directly.</string>
    <string name="add_payment_method">Add payment method</string>
    <string name="country">Country</string>
    <string name="postal_code">Postal code</string>
    <string name="mindbody_consumer_agreement">Mindbody Consumer Agreement</string>
    <string name="consumer_agreement_full_text">This credit card will be used as the default card for any future transactions with this business. Click to read the full %1$s.</string>
    <string name="you_wont_be_charged">You won\'t be charged</string>
    <string name="continue_text">Continue</string>
    <string name="country_drop_down_content_description">Expand country list</string>
    <string name="liability_waiver">Liability Waiver</string>
    <string name="liability_waiver_required">In order to book you need to consent to the terms in %1$s\'s liability waiver</string>
    <string name="ok">OK</string>
    <string name="appointment_request_confirmation_title">We’ve requested your appointment</string>
    <string name="appointment_request_confirmation_text">You’ll be informed once your booking has been confirmed by %1$s</string>
    <string name="appointment_name_staff">%1$s w/ %2$s</string>
    <string name="appointment_date_time">%1$s at %2$s - %3$s</string>
    <string name="pay_after_your_service">Pay after your service</string>
    <string name="request_needs_approval">The business will need to approve your request</string>
    <string name="book">Book</string>
    <string name="next">Next</string>
    <string name="request">Request</string>
    <string name="phone_number_usage_information">You agree that %1$s may contact you via phone or text message (including SMS) to this number to provide updates about your reservation. SMS Texting Terms apply.</string>
    <string name="sms_texting_terms">SMS Texting Terms</string>
    <string name="cancellation_policy_header">Cancellation Policy</string>
    <string name="appointment_booking_notes_hint">Add any notes or requests here&#8230;</string>
    <string name="phone_number_title">Where can they reach you?</string>
    <string name="not_accepted">Not accepted</string>
    <string name="add_card">Add Card</string>
    <string name="last_four_digit_card_number">&#8226;&#8226;&#8226;&#8226; &#8226;&#8226;&#8226;&#8226; &#8226;&#8226;&#8226;&#8226; %s</string>
    <string name="state_or_province">State/Province</string>
    <string name="province_drop_down_content_description">Expand state/province list</string>
    <string name="address">Address</string>
    <string name="city">City</string>
    <string name="required_for_booking">Required for booking</string>
    <string name="agree">Agree</string>
    <string name="payment_method_not_selected_error_message">Please select a payment method</string>
    <string name="phone_number_error_message">Check your phone number</string>
    <string name="card_number_error_message">Check your card number</string>
    <string name="card_type_not_accepted_error_message">%1$s not accepted at the studio</string>
    <string name="expiry_date_error_message">Check your expiry date</string>
    <string name="cvc_error_message">Check your CVC</string>
    <string name="country_error_message">Check your country</string>
    <string name="state_province_error_message">Check your state/province</string>
    <string name="address_error_message">Check your address</string>
    <string name="city_error_message">Check your city</string>
    <string name="postal_code_error_message">Check your postal code</string>
    <string name="start_date">Start date</string>
    <string name="renewal_date">Renewal date</string>
    <string name="recurring_amount">Recurring amount</string>
    <string name="auto_renews">Auto-renews</string>
    <string name="classes">Classes</string>
    <string name="appointments">Appointments</string>
    <string name="schedule">Schedule</string>
    <string name="no_appointments_available">There are no appointments for this business.</string>
    <string name="filter_ada_description">Filter Button</string>
</resources>
