plugins {
    id 'org.jetbrains.kotlin.plugin.compose' version '2.1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'newrelic'
apply from: '../configure.gradle'
apply plugin: "kotlin-allopen"
apply plugin: 'kotlin-kapt'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'kotlin-parcelize'
apply plugin: 'de.mobilej.unmock'

//https://github.com/bjoernQ/unmock-plugin
unMock {
    keep "android.location.Location"
    keep "android.net.Uri"
    keepStartingWith "org."
    keepStartingWith "libcore."
    keepAndRename "java.nio.charset.Charsets" to "xjava.nio.charset.Charsets"
}

if (rootProject.ext.enableCoverage) {
    apply from: "$project.rootDir/jacoco.gradle"
}

configurations.all {
    resolutionStrategy {
        //Make sure our snapshot libraries are up-to-date
        cacheChangingModulesFor 0, 'seconds'
    }
}

// Exclude Firebase modules and protobuf from androidTest configuration to avoid conflicts
configurations.androidTestImplementation {
    exclude group: 'com.google.firebase', module: 'firebase-perf'
    exclude group: 'com.google.firebase', module: 'firebase-core'
    exclude group: 'com.google.firebase', module: 'firebase-analytics'
    exclude group: 'com.google.firebase', module: 'firebase-crashlytics'
    exclude group: 'com.google.firebase', module: 'firebase-config'
    exclude group: 'com.google.firebase', module: 'firebase-messaging'
    exclude group: 'com.google.protobuf', module: 'protobuf-lite'
    exclude group: 'com.google.protobuf', module: 'protobuf-java'
}

def testSupportLibVersion = "3.0.2"
def mockitoVersion = '5.8.0'
def optimizelyVersion = '5.0.0'
def mockKVersion = "1.13.5"
def placesVersion = '4.2.0'

repositories {
    maven { url 'https://jitpack.io' }
    maven {
        credentials {
            username = '<EMAIL>'
            password = 'androidmaven'
        }
        url "https://mindbody.mycloudrepo.io/repositories/android-maven-${dexus.snapshot ? 'snapshot' : 'release'}"
    }
    maven { url "https://appboy.github.io/appboy-android-sdk/sdk" }
    maven { url "https://maven.singular.net" }

    // Internal maven repository, for Dexus packages.  Use this if we stop hosting on CloudRepo.
//    maven {
//        url "https://android.mbodev.me/artifactory/libs-${dexus.snapshot ? 'snapshot' : 'release'}"
//    }
}

android {
    namespace = "com.mindbodyonline.connect"
    compileSdkVersion androidSdk.compileSdkVersion

    defaultConfig {
        minSdkVersion androidSdk.minSdkVersion
        targetSdkVersion androidSdk.targetSdkVersion

        // Update baseVersionCode number each time we release
        def baseVersionCode = 255
        def versionCodeIncrement = 100
        def patchVersionCodeIncrement = 3
        versionCode versionCodeIncrement * baseVersionCode + patchVersionCodeIncrement
        versionName "7.86.3"
        buildConfigField("String", "PLACES_API_KEY", PLACES_API_KEY)
        buildConfigField("String", "GOOGLE_MAPS_SERVICES_API_KEY", GOOGLE_MAPS_SERVICES_API_KEY)
        applicationId "com.mindbodyonline.connect"
        testApplicationId "com.mindbodyonline.connect.instrumentTest"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        testInstrumentationRunnerArguments clearPackageData: 'true'
    }

    buildFeatures {
        buildConfig = true
        compose true
    }

    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KaptGenerateStubs).configureEach {
        kotlinOptions {
            jvmTarget = "11"
        }
    }

    packagingOptions {
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/DEPENDENCIES.txt'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/dependencies.txt'
        exclude 'META-INF/LGPL2.1'
        exclude 'META-INF/services/javax.annotation.processing.Processor'
        exclude 'LICENSE.txt'
        exclude 'APK LICENSE.txt'
        exclude "**/attach_hotspot_windows.dll"
        exclude "META-INF/licenses/**"
        exclude "META-INF/AL2.0"
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
        lintConfig file('lint.xml')
        disable 'MissingTranslation'
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = "11"
    }

    signingConfigs {
        config {
            keyAlias 'mbconnectrelease'
            keyPassword project.KEY_PASSWORD
            storeFile file(project.KEYSTORE_FILENAME)
            storePassword project.KEYSTORE_PASSWORD
        }
        debugConfig {
            keyAlias 'androiddebugkey'
            keyPassword 'android'
            storeFile file("misc_tools/debug.keystore")
            storePassword 'android'
        }
    }
    buildTypes {
        debug {
            versionNameSuffix '-dev'
            debuggable true
            jniDebuggable true
            pseudoLocalesEnabled true
            ext.enableCrashlytics = false
            signingConfig signingConfigs.debugConfig

            testCoverageEnabled rootProject.ext.enableCoverage
        }
        release {
            signingConfig signingConfigs.config
        }
    }

    testOptions {
        execution 'ANDROIDX_TEST_ORCHESTRATOR'
        unitTests.returnDefaultValues true
        allOpen {
            annotation("com.mindbodyonline.OpenWhileTesting")
        }
    }

    flavorDimensions "builds"

    productFlavors {
        play {}
        uxResearch {
            versionNameSuffix "-ux"
        }
    }

    dependencies {

        coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.3")

        //region Jetpack Compose
        implementation "androidx.activity:activity-compose:1.10.1"
        implementation "androidx.compose.material:material:1.7.8"
        implementation "androidx.compose.animation:animation:1.7.8"
        implementation "androidx.compose.ui:ui-tooling:1.7.8"
        implementation "androidx.navigation:navigation-compose:2.8.8"
        //endregion

        implementation "com.rokt:roktsdk:$roktsdk_version"

        //region Library modules
        implementation project(':libraries:ViewPagerIndicator')
        implementation project(path: ':android-tutorial-bubbles')
        implementation "com.github.skydoves:balloon:1.3.6" //https://github.com/skydoves/Balloon

        // Dependencies for the user research team
        uxResearchImplementation project(path: ':UserzoomSDK')

        //region Jitpack temp region
        // TODO: Temp jitpack dependency, remove once this dependency has a new release for bar height and thumb diameter
        implementation 'com.github.syedowaisali:crystal-range-seekbar:aa8b49208cb292d44392a84d8a6882e384bc7eac'
        //endregion

        //region Architecture components
        def lifecycle_version = "2.8.7"
        def fragment_version = "1.8.6"
        def activity_version = "1.10.1"

        implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
        implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
        implementation "androidx.fragment:fragment-ktx:$fragment_version"
        implementation "androidx.activity:activity-ktx:$activity_version"
        implementation 'androidx.navigation:navigation-fragment-ktx:2.8.8'


        //endregion

        //region Support libs
        implementation "androidx.legacy:legacy-support-v4:1.0.0"
        implementation "androidx.appcompat:appcompat:1.7.0"
        implementation "com.google.android.material:material:1.12.0"
        implementation "androidx.percentlayout:percentlayout:1.0.0"
        implementation "androidx.recyclerview:recyclerview:1.4.0"
        implementation "androidx.cardview:cardview:1.0.0"
        implementation "androidx.multidex:multidex:2.0.1"
        implementation "androidx.constraintlayout:constraintlayout:2.2.1"
        implementation "androidx.browser:browser:1.8.0"
        //endregion

        //region Play services
        implementation "com.google.android.gms:play-services-maps:19.2.0"
        implementation "com.google.android.gms:play-services-base:18.7.0"
        implementation "com.google.android.gms:play-services-location:21.3.0"
        implementation "com.google.android.gms:play-services-fitness:21.2.0"
        implementation "com.google.android.gms:play-services-auth:21.3.0"
        implementation "com.google.firebase:firebase-core:21.1.1"
        implementation 'com.google.firebase:firebase-analytics:22.4.0'
        implementation "com.google.firebase:firebase-messaging:24.1.1"
        implementation 'com.google.firebase:firebase-crashlytics:19.4.3'
        implementation 'com.google.firebase:firebase-perf:21.0.5'
        implementation 'com.google.firebase:firebase-config:22.1.1'
        implementation 'com.google.android.play:review:2.0.2'
        implementation("com.google.android.play:app-update-ktx:2.1.0")
        //endregion

        //region Application specific dependencies
        implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
        implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.1'
        implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
        implementation("org.jetbrains.kotlin:kotlin-reflect:2.1.0")
        implementation 'com.google.code.gson:gson:2.10.1'
        implementation 'se.emilsjolander:stickylistheaders:2.1.4'
        implementation 'com.squareup.picasso:picasso:2.71828'
        implementation 'com.j256.ormlite:ormlite-android:4.48'
        implementation 'com.android.volley:volley:1.2.1'
        implementation "com.squareup.retrofit2:retrofit:2.9.0"
        implementation "com.squareup.retrofit2:converter-gson:2.9.0"
        implementation 'com.facebook.android:facebook-share:18.0.2'
        implementation 'com.airbnb.android:lottie:3.0.6'
        implementation 'com.github.hrskrs:InstaDotView:1.1'
        implementation 'com.ogaclejapan.smarttablayout:library:2.0.0@aar'
        implementation 'com.tbuonomo:dotsindicator:4.2'
        implementation 'com.googlecode.libphonenumber:libphonenumber:8.13.15'

        //Newrelic
        implementation "com.newrelic.agent.android:android-agent:$newrelic_version"

        //Tealium
        implementation 'com.tealium:kotlin-core:1.0.0'
        implementation 'com.tealium:kotlin-visitor-service:1.0.0'
        implementation 'com.tealium:kotlin-lifecycle:1.0.0'
        implementation 'com.tealium:kotlin-collect-dispatcher:1.0.1'

        //Braze
        implementation 'com.braze:android-sdk-ui:33.1.0'

        //Branch
        implementation 'io.branch.sdk.android:library:5.16.1'
        implementation 'com.google.firebase:firebase-appindexing:20.0.0'
        implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'

        implementation 'com.github.didikk:sticky-nestedscrollview:1.0.1'

        //The following is how detecting the referrer works, old broadcast methods are deprecated
        implementation 'com.android.installreferrer:installreferrer:2.2'

        implementation 'com.google.android.flexbox:flexbox:3.0.0'

        // Optimizely
        implementation "com.optimizely.ab:android-sdk:$optimizelyVersion"
        debugImplementation 'com.noveogroup.android:android-logger:1.3.6'
        //endregion

        // Places
        implementation "com.google.android.libraries.places:places:$placesVersion"

        //glance
        // For AppWidgets support
        implementation "androidx.glance:glance-appwidget:1.1.1"
        //endregion

        //region Test dependencies
        testImplementation 'junit:junit:4.13.2'

        androidTestImplementation 'androidx.test:runner:1.6.2'
        androidTestUtil 'androidx.test:orchestrator:1.5.1'
        androidTestImplementation 'androidx.test:core:1.6.1'

        //Mockito is used in both unit testing and instrumented testing
        testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.8.1'
        testImplementation "org.mockito:mockito-core:$mockitoVersion"
         testImplementation "org.mockito:mockito-inline:5.2.0"
        testImplementation "org.mockito.kotlin:mockito-kotlin:5.2.0"
        testImplementation "io.mockk:mockk:$mockKVersion"
        testImplementation 'androidx.arch.core:core-testing:2.2.0'
        androidTestImplementation "org.mockito:mockito-android:$mockitoVersion"
        androidTestImplementation "com.nhaarman.mockitokotlin2:mockito-kotlin:2.2.0"
        androidTestImplementation 'androidx.arch.core:core-testing:2.2.0'
        androidTestImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.8.1'

        //Roboelectric is useful for quickly mocking application Context
        testImplementation "org.robolectric:robolectric:4.5.1"
        testImplementation "androidx.test.ext:junit:1.2.1"
        testImplementation("org.slf4j:slf4j-simple:2.0.9")

        //endregion

        //region Debug dependencies
        debugCompileOnly 'org.glassfish:javax.annotation:10.0-b28'

        // Chuck interceptor
        debugImplementation "com.github.chuckerteam.chucker:library:3.5.2"
        // OkHttp3
        debugImplementation "com.squareup.okhttp3:okhttp:4.12.0"
        debugImplementation "com.squareup.okhttp3:okhttp-urlconnection:4.10.0"
        //endregion

        //region Dexus dependencies
        //Our custom libraries
        def dexusExt = dexus.version + (dexus.snapshot ? '-SNAPSHOT' : '')
        androidTestImplementation (dexus.group + ":espresso-ktx:$dexusExt") {
            exclude(group: 'androidx.test.espresso')
        }
        debugImplementation dexus.group + ":dexus-debug:$dexusExt"
        implementation dexus.group + ":dexus-base:$dexusExt"
        implementation dexus.group + ":dexus-views:$dexusExt"
        implementation dexus.group + ":dexus-api-sales:$dexusExt"
        implementation dexus.group + ":dexus-identity-ui:$dexusExt"
        implementation dexus.group + ":dexus-api-identity:$dexusExt"
        implementation dexus.group + ":dexus-auth-okhttp:$dexusExt"
        implementation dexus.group + ":pickaspot-ui:$dexusExt"
        implementation dexus.group + ":pickaspot-domain:$dexusExt"
        implementation dexus.group + ":pickaspot-api:$dexusExt"
        implementation dexus.group + ":lumber-core:$dexusExt"
        implementation dexus.group + ":lumber-newrelic:$dexusExt"
        implementation dexus.group + ":lumber-timber:$dexusExt"
        //endregion

        //This dependency is used as work manager runtime lib internally,
        //if we don't add this lib it throws PendingIntent related exception
        implementation 'androidx.work:work-runtime:2.10.1'

        // required dependency for accessing TimeZoneApi via Google Maps Services -
        implementation 'com.google.maps:google-maps-services:2.2.0'

        implementation 'com.stripe:stripe-android:20.42.0'

        // WorkManager API
        def work_version = "2.10.1"
        implementation "androidx.work:work-runtime:$work_version"
        implementation "androidx.work:work-runtime-ktx:$work_version"


        //UIAutomator
        androidTestImplementation "androidx.test:runner:1.6.2"
        androidTestUtil "androidx.test:orchestrator:1.5.1"
        androidTestImplementation("androidx.test.uiautomator:uiautomator:2.4.0-alpha01")
        androidTestImplementation('androidx.test.espresso:espresso-core:3.1.1')
        androidTestImplementation('androidx.test.espresso:espresso-contrib:3.1.1')
        androidTestImplementation('androidx.test.espresso:espresso-intents:3.1.1')
        androidTestImplementation('androidx.test.espresso:espresso-idling-resource:3.1.1')

    }

    buildFeatures {
        dataBinding true
        viewBinding true
    }
}

dependencies {
    implementation(project(":ui"))
    implementation(project(":resources"))
    implementation(project(":analytics"))
    implementation project(':libraries:imagecropper')
    implementation project(':libraries:williamchart')

    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.8.9'
    implementation 'androidx.navigation:navigation-ui-ktx:2.8.9'
}

// This needs to remain at the bottom of the file
apply plugin: 'com.google.gms.google-services'