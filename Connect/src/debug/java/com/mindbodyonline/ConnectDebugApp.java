package com.mindbodyonline;

import android.content.Context;

import com.mindbodyonline.android.api.clients.MbClientsAPIManager;
import com.mindbodyonline.android.api.sales.MBSalesApi;
import com.mindbodyonline.android.api.sales.SalesApiFactoryKt;
import com.mindbodyonline.android.debug.drawer.DebugDrawer;
import com.mindbodyonline.android.util.log.MBLog;
import com.mindbodyonline.android.volley.OkHttp3Stack;
import com.mindbodyonline.connect.utils.DebugApiInterceptor;
import com.mindbodyonline.connect.utils.Endpoint;
import com.mindbodyonline.connect.utils.MBVolleyUtils;
import com.mindbodyonline.connect.utils.MockLocationProvider;
import com.mindbodyonline.connect.utils.SharedPreferencesUtils;
import com.mindbodyonline.connect.utils.api.ApiCallUtils;
import com.mindbodyonline.data.services.SalesAccessParams;
import com.mindbodyonline.data.services.http.MbDataService;

import com.android.volley.RequestQueue;
import com.android.volley.toolbox.Volley;
import com.chuckerteam.chucker.api.ChuckerInterceptor;

import java.lang.reflect.Field;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;

import okhttp3.OkHttpClient;

public class ConnectDebugApp extends ConnectApp {

    public MockLocationProvider mock;

    public static final String ENDPOINT_KEY = "DEBUG_ENDPOINT_STORE_KEY";

    @Override
    public void onCreate() {
        super.onCreate();
        DebugDrawer.inject(this, new ConnectDebugDrawerContract());

        MbDataService.getServiceInstance().setRequestQueue(createDebugRequestQueue(this));
        // Re-setting the instance here because Chucker is not being attached to MBSalesApi calls.
        // When MBSalesAPI is initialized in ConnectApp.java, Chucker is not yet attached, so we need to reset the instance to ensure proper attachment.
        MBSalesApi.setInstance(SalesApiFactoryKt.SalesApi(this, new SalesAccessParams(), MbDataService.getServiceInstance().getRequestQueue()));
        MbClientsAPIManager.INSTANCE.init(this, MbDataService.getServiceInstance().getRequestQueue(), new SalesAccessParams());
        int endpoint = SharedPreferencesUtils.getGlobalSharedPrefs().getInt(ENDPOINT_KEY,
            debuggableMode() ? Endpoint.DEVELOPMENT.ordinal() : Endpoint.PRODUCTION.ordinal());
        ApiCallUtils.setEndpoint(Endpoint.values()[endpoint]);
    }

    /**
     * Creates a new request queue to allow Stetho to inspect network traffic
     */
    private RequestQueue createDebugRequestQueue(Context context) {
        SSLSocketFactory sslSocketFactory = MBVolleyUtils.getMboSslSocketFactory();

        OkHttpClient debugClient = new OkHttpClient.Builder()
            .sslSocketFactory(sslSocketFactory, trustManager(sslSocketFactory))
            .addInterceptor(new ChuckerInterceptor.Builder(context).build())
            .addInterceptor(DebugApiInterceptor.getInstance())
            .build();

        return Volley.newRequestQueue(context, new OkHttp3Stack(debugClient));
    }

    @Override
    public void restartApplication() {
        super.restartApplication();
        ApiCallUtils.setEndpoint(debuggableMode() ? Endpoint.DEVELOPMENT : Endpoint.PRODUCTION);
    }

    protected void onDestroy() {
        mock.shutdown();
    }

    /**
     * Attempts to get the {@link TrustManager} from within the {@link SSLSocketFactory} using
     * reflection (this is the technique OkHttp uses). Since it's not guaranteed that we have a
     * specific implementation, we check to see if we have a wrapped {@link SSLSocketFactory} first
     * and use that one instead before attempting to get the {@link TrustManager}
     *
     * If the above does not work, we default to a {@link TrustManager} with a null {@link KeyStore}
     * Note: Passing a null keystore will use the system default.
     */
    public static X509TrustManager trustManager(SSLSocketFactory sslSocketFactory) {
        try {
            Field[] fields = sslSocketFactory.getClass().getDeclaredFields();

            // check if we have a wrapper
            for (Field field : fields) {
                if (field.getType() == SSLSocketFactory.class) {
                    field.setAccessible(true);
                    return trustManager((SSLSocketFactory) field.get(sslSocketFactory));
                }
            }
        } catch (IllegalAccessException e) {
            // we set it accessible, so this should never happen
            MBLog.e(TAG, "Unable to access inner SSLSocketFactory", e);
        }

        // fetch the trust manager
        X509TrustManager manager = MBVolleyUtils.trustManager(sslSocketFactory);

        if (manager == null) {
            // try to create a default implementation
            try {
                TrustManagerFactory factory = TrustManagerFactory.getInstance("X509");
                factory.init((KeyStore) null);

                for (TrustManager trustManager : factory.getTrustManagers()) {
                    if (trustManager instanceof X509TrustManager) {
                        manager = (X509TrustManager) trustManager;
                        break;
                    }
                }
            } catch (NoSuchAlgorithmException | KeyStoreException e) {
                MBLog.e(TAG, "Unable to create a default trust manager", e);
            }
        }

        return manager;
    }
}
