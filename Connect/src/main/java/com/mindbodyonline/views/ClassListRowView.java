package com.mindbodyonline.views;

import static com.mindbodyonline.connect.quickbook.QuickBookDialog.KEY_SELECTED_USER;
import static com.mindbodyonline.connect.utils.FamilyAccountsUtilsKt.isFamilyAccountType;
import static com.mindbodyonline.connect.utils.TimeUtils.getTimeZoneOffset;
import static com.mindbodyonline.framework.abvariant.ABEventKeysKt.HOME_LMO_SEE_ALL_BOOK_NOW;
import static com.mindbodyonline.framework.abvariant.ABEventKeysKt.HOME_SIMILAR_HISTORY_SEE_ALL_BOOK_NOW;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Parcelable;
import android.text.Html;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.maps.model.LatLng;
import com.mindbodyonline.android.api.sales.MBSalesApi;
import com.mindbodyonline.android.api.sales.model.enums.CServiceCategoryType;
import com.mindbodyonline.android.api.sales.model.pos.cart.CartAbandonReason;
import com.mindbodyonline.android.api.sales.model.search.DistanceUnit;
import com.mindbodyonline.android.util.TaskCallback;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.activities.list.ReviewListActivity;
import com.mindbodyonline.connect.analytics.OriginComponent;
import com.mindbodyonline.connect.analytics.OriginView;
import com.mindbodyonline.connect.analytics.UtilsKt;
import com.mindbodyonline.connect.analytics.checkout.BookingEventTracker;
import com.mindbodyonline.connect.common.repository.PerformanceName;
import com.mindbodyonline.connect.common.utilities.ContextUtilKt;
import com.mindbodyonline.connect.fragments.custom.MBDialogFragment;
import com.mindbodyonline.connect.quickbook.QuickBookDialog;
import com.mindbodyonline.connect.quickbook.QuickBookDialogV2;
import com.mindbodyonline.connect.quickbook.QuickBookViewModel;
import com.mindbodyonline.connect.quickbook.QuickBookViewModelV2.QuickBookInitializer;
import com.mindbodyonline.connect.quickbook.QuickBookViewModelV2.QuickBookType;
import com.mindbodyonline.connect.quickbook.QuickBookViewModelV2.QuickbookFailureReason;
import com.mindbodyonline.connect.tealium.InventoryType;
import com.mindbodyonline.connect.tealium.PageName;
import com.mindbodyonline.connect.tealium.TrackingHelperUtils;
import com.mindbodyonline.connect.utils.AlarmUtils;
import com.mindbodyonline.connect.utils.AnalyticsUtils;
import com.mindbodyonline.connect.utils.BookingUtils;
import com.mindbodyonline.connect.utils.CalendarUtils;
import com.mindbodyonline.connect.utils.Constants;
import com.mindbodyonline.connect.utils.DeepLinkUtils;
import com.mindbodyonline.connect.utils.DialogUtils;
import com.mindbodyonline.connect.utils.DialogUtilsKtKt;
import com.mindbodyonline.connect.utils.FamilyAccountsUtilsKt;
import com.mindbodyonline.connect.utils.GeoLocationUtils;
import com.mindbodyonline.connect.utils.GeofenceUtils;
import com.mindbodyonline.connect.utils.IntentUtils;
import com.mindbodyonline.connect.utils.MBPhoneUtils;
import com.mindbodyonline.connect.utils.PaymentUtils;
import com.mindbodyonline.connect.utils.SharedPreferencesUtils;
import com.mindbodyonline.connect.utils.ToastUtils;
import com.mindbodyonline.connect.utils.Utils;
import com.mindbodyonline.connect.utils.ViewUtils;
import com.mindbodyonline.connect.utils.api.ModelTranslationKt;
import com.mindbodyonline.connect.utils.api.RecommenderType;
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference;
import com.mindbodyonline.connect.utils.time.DateTimeFormatUtilsKt;
import com.mindbodyonline.connect.utils.time.DateTimePattern;
import com.mindbodyonline.data.services.MBAuth;
import com.mindbodyonline.data.services.MBStaticCache;
import com.mindbodyonline.data.services.locator.ServiceLocator;
import com.mindbodyonline.domain.BookabilityStatus;
import com.mindbodyonline.domain.ClassTypeObject;
import com.mindbodyonline.domain.ClassTypeObject.ClassStatus;
import com.mindbodyonline.domain.Enrollment;
import com.mindbodyonline.domain.Location;
import com.mindbodyonline.domain.User;
import com.mindbodyonline.domain.quickbook.CTOQuickBookResponse;
import com.mindbodyonline.domain.user.action.ClassBookingAction;
import com.mindbodyonline.framework.abvariant.ABHelperUtils;
import com.mindbodyonline.framework.abvariant.DevelopmentFlag;
import com.mindbodyonline.views.dialog.InAppPurchasesBlockedDialog;
import com.mindbodyonline.views.dialog.SignIntoClassDialog;
import com.mindbodyonline.views.dialog.VerifyAccountDialog;
import com.mindbodyonline.views.dialog.familyaccounts.FamilyAccount;
import com.mindbodyonline.views.dialog.scheduleitempricing.GuestScheduleItemPricingDialog;
import com.squareup.picasso.Picasso;

import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

import kotlin.Unit;

public class ClassListRowView extends FrameLayout {
    private TextView classTimeText;
    private TextView startTimeText;
    private TextView durationText;
    private TextView timezoneText;
    private TextView instructorText;
    private RatingBar ratingBar;
    private TextView ratingText;
    private TextView classDateText;
    private TextView classNameText;
    private TextView classLocationText;
    private TextView actionButtonText;
    private View actionButtonLayout;
    private ImageView locationLogo;

    private TaskCallback<ClassTypeObject> dataChangedCallback;
    private TaskCallback<ClassBookingAction> actionListener;
    private ClassTypeObject cto;
    private GoogleApiClient mGoogleApiClient;
    private DisplayMode displayMode = DisplayMode.NORMAL;
    private boolean instanceSaved;

    private TextView originalPriceText;
    private TextView dynamicPriceText;
    private TextView numSpotsLeftText;
    private TextView classTypes;
    private boolean adjustedForOffset;
    private View flexBadge;
    private View ratingContainer;

    private String originLocationForAnalytics;
    private String homeCarouselContext;

    public ClassListRowView(Context context) {
        super(context);
        initialize(context);
    }

    public ClassListRowView(Context context, DisplayMode displayMode) {
        super(context);
        this.displayMode = displayMode;
        initialize(context);
    }

    public ClassListRowView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ClassListRowView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initialize(context);
    }

    public ClassListRowView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initialize(context);
    }

    private void initialize(Context context) {
        LayoutInflater.from(context).inflate(R.layout.view_class_list_row, this, true);

        setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));

        classTypes = findViewById(R.id.class_row_type);
        locationLogo = findViewById(R.id.business_logo);
        classTimeText = findViewById(R.id.class_time);
        startTimeText = findViewById(R.id.start_time_left);
        timezoneText = findViewById(R.id.time_zone_left);
        durationText = findViewById(R.id.duration_left);
        instructorText = findViewById(R.id.instructor_text);
        ratingBar = findViewById(R.id.rating_bar);
        ratingText = findViewById(R.id.ratings_count);
        classDateText = findViewById(R.id.class_date);
        classNameText = findViewById(R.id.class_name);
        classLocationText = findViewById(R.id.class_location);
        actionButtonText = findViewById(R.id.action_button);
        actionButtonLayout = findViewById(R.id.action_button_layout);

        originalPriceText = findViewById(R.id.original_price);
        dynamicPriceText = findViewById(R.id.dynamic_price);
        flexBadge = findViewById(R.id.flex_badge);
        numSpotsLeftText = findViewById(R.id.num_spots_left);

        ratingContainer = findViewById(R.id.rating_container);

        instanceSaved = false;

        // general updates for favorites card
        if (displayMode == DisplayMode.FAVORITE) {
            classTimeText.setMaxLines(2);
            classTimeText.setVisibility(VISIBLE);
        } else {
            startTimeText.setVisibility(VISIBLE);
            instructorText.setVisibility(displayMode == DisplayMode.STAFF_CLASSES ? GONE : VISIBLE);
            durationText.setVisibility(VISIBLE);
        }

        initLocationServices();
    }

    public void setDataChangedCallback(TaskCallback<ClassTypeObject> taskCallback) {
        dataChangedCallback = taskCallback;
    }

    public ClassTypeObject getClassTypeObject() {
        return cto;
    }

    public void setClass(ClassTypeObject cto) {
        this.cto = cto;
        if (cto == null) return;

        // We want the date to be the main header of the list row, when rebooking
        if (displayMode == DisplayMode.REBOOK) {
            classNameText.setText(cto.getShortDate());
        } else {
            classNameText.setText(Html.fromHtml(cto.getName()));
        }

        String instructor = null;
        if (displayMode == DisplayMode.STAFF_CLASSES) {
            classLocationText.setVisibility(View.GONE);
            classDateText.setVisibility(VISIBLE);
            classDateText.setText(cto.getShortDate());
        } else {
            if (cto.getStaff() != null) {
                instructor = cto.getStaff().toString();
            } else {
                instructor = getContext().getString(R.string.default_staff_name);
            }
            classDateText.setVisibility(GONE);
        }

        String classTime;

        if (cto.getStartTime().equals(cto.getEndTime())) {
            classTime = getResources().getString(R.string.to_be_determined_short);
            startTimeText.setText(classTime);
            timezoneText.setText(null);
        } else {
            String timeZoneString = null;
            int timeZoneOffset = 0;
            if (cto.getLocation() != null && cto.getLocation().getTimezoneId() != null) {
                TimeZone classTZ = TimeZone.getTimeZone(cto.getLocation().getTimezoneId());
                TimeZone thisTimeZone = TimeZone.getDefault();

                if (!thisTimeZone.equals(classTZ)) {
                    timeZoneString = classTZ.getDisplayName(
                            classTZ.inDaylightTime(cto.getStartDate()), TimeZone.SHORT
                    );
                }

                if (!adjustedForOffset) {
                    timeZoneOffset = getTimeZoneOffset(classTZ, thisTimeZone, cto.getStartDate().getTime());
                }
            }

            long startTimeMillis = cto.getStartDate().getTime();
            long endTimeMillis = cto.getEndDate().getTime();

            classTime = DateUtils.formatDateRange(getContext(),
                            startTimeMillis,
                            endTimeMillis,
                            DateUtils.FORMAT_SHOW_TIME)
                    .replaceAll(":00", "")
                    .toLowerCase(Locale.getDefault())
                    // Remove the space before am/pm (only works for English-like locales)
                    .replaceAll(" ([ap])", "$1");

            String startClassTime;

            startClassTime = DateTimeFormatUtilsKt.INSTANCE.formatDateWithTimeZone(
                            cto.getStartDate(),
                            DateTimePattern.SHORT_TIME_WITH_PERIOD_PATTERN,
                            cto.getTimeZone() != null ? cto.getTimeZone() : TimeZone.getDefault(),
                            cto.getLocation() != null ? cto.getLocation().getLocale() : Locale.getDefault())
                    .toLowerCase(Locale.getDefault());

            startTimeText.setText(startClassTime);

            if (!Utils.isNullOrBlank(timeZoneString)) {
                classTime = getResources().getString(R.string.time_zone_append, classTime, timeZoneString);
            }

            timezoneText.setText(timeZoneString);
        }

        if (!TextUtils.isEmpty(instructor)) {
            classTime = getResources().getString(displayMode == DisplayMode.FAVORITE ?
                R.string.time_with_instructor_multiline :
                R.string.time_with_instructor, classTime, instructor);
        }
        classTimeText.setText(classTime);
        instructorText.setText(getResources().getString(R.string.with, instructor));

        long durationMinutes = CalendarUtils.minutesBetween(cto.getStartDate(), cto.getEndDate());
        if (durationMinutes > 0) {
            durationText.setText(getResources().getString(R.string.num_min_parentheses, durationMinutes));
        } else {
            durationText.setText(null);
        }

        timezoneText.setVisibility(
            displayMode == DisplayMode.FAVORITE ||
                Utils.isNullOrBlank(timezoneText.getText().toString()) ? GONE : VISIBLE);

        //For now, we are using the studio ratings until we can get individual class ratings
        if (cto.getLocation() != null) {
            ratingBar.setRating(ViewUtils.applyRating(cto.getLocation().getAverageConnectRating()));
            String reviewsTextString = getResources().getString(R.string.num_total_studio_reviews_plain, cto.getLocation().getTotalConnectRatings());
            ratingText.setText(reviewsTextString);
            ratingText.setContentDescription(reviewsTextString + ","
                    + getContext().getString(R.string.stars_ada_description, ViewUtils.adaRatingDescription(cto.getLocation().getAverageConnectRating())));
        }

        boolean ratingsVisible =
                cto.getLocation() != null &&
                displayMode != DisplayMode.REBOOK &&
                displayMode != DisplayMode.EVENTS &&
                displayMode != DisplayMode.LOCATIONSCHEDULE &&
                displayMode != DisplayMode.BUSINESS_DETAILS &&
                displayMode != DisplayMode.BUSINESS_FOCUSSED_DEAL_DETAILS &&
                cto.getLocation().getTotalConnectRatings() > 0;
        ratingBar.setVisibility(ratingsVisible ? VISIBLE : GONE);
        ratingText.setVisibility(ratingsVisible ? VISIBLE : GONE);

        classTypes.setCompoundDrawablesWithIntrinsicBounds(
            cto.isVirtual() ? R.drawable.ic_virtual_icon : 0, 0, 0, 0);
        classTypes.setCompoundDrawablePadding(cto.isVirtual() ? ViewUtils.dpToPx(8, getContext()) : 0);

        String firstClassType = "";
        if (!Utils.isEmpty(cto.getClassTypes())) {
            firstClassType = cto.getClassTypes().get(0);
        }
        String category = cto.getCategory();
        String firstSecondaryCategory = "";
        if (!Utils.isEmpty(cto.getSecondaryCategories())) {
            firstSecondaryCategory = cto.getSecondaryCategories()[0];
        }
        if (cto instanceof Enrollment) {
            classTypes.setText(R.string.event_tag);
            classTypes.setVisibility(VISIBLE);
        } else if (displayMode != DisplayMode.FAVORITE && !Utils.isEmpty(cto.getClassTypes())) {
            if (!Utils.isNullOrBlank(firstClassType)) {
                if (!Utils.isNullOrBlank(firstSecondaryCategory)) {
                    classTypes.setText(getContext().getString(R.string.categories_text, firstClassType, firstSecondaryCategory));
                } else {
                    classTypes.setText(firstClassType);
                }
            } else {
                classTypes.setText(TextUtils.join("/", cto.getClassTypes()));
            }
            classTypes.setVisibility(VISIBLE);
        } else if (displayMode != DisplayMode.FAVORITE && (cto.isVirtual() || !Utils.isNullOrBlank(category))) {
            if (!Utils.isNullOrBlank(firstSecondaryCategory)) {
                classTypes.setText(getContext().getString(R.string.categories_text, category, firstSecondaryCategory));
            } else {
                classTypes.setText(category);
            }
            classTypes.setVisibility(VISIBLE);
        } else {
            classTypes.setVisibility(GONE);
        }

        Location location = cto.getLocation();
        if (displayMode == DisplayMode.REBOOK || displayMode == DisplayMode.EVENTS ||
                displayMode == DisplayMode.STAFF_CLASSES || displayMode == DisplayMode.LOCATIONSCHEDULE ||
                displayMode == DisplayMode.BUSINESS_DETAILS || displayMode == DisplayMode.BUSINESS_FOCUSSED_DEAL_DETAILS ||
                location == null || location.getCombinedSiteLocationName().isEmpty()) {
            classLocationText.setVisibility(View.GONE);
        } else if (displayMode == DisplayMode.FAVORITE) {
            if (TextUtils.isEmpty(location.getLocationNameSubtext())) {
                classLocationText.setVisibility(GONE);
            } else {
                classLocationText.setText(location.getLocationNameSubtext());
            }
        } else {
            classLocationText.setVisibility(View.VISIBLE);

            StringBuilder locationString = new StringBuilder(location.getCombinedSiteLocationName().trim());
            String distanceString = "";
            if (cto.getDistanceInKm() != null) {
                distanceString = GeoLocationUtils.getNumDistanceString(
                    DistanceUnit.KILOMETERS, cto.getDistanceInKm().doubleValue(), 1, true);
            } else if (location.getLatitude() != 0) {
                android.location.Location latLngLocation = GeoLocationUtils.getCurrentLocation(getContext());
                if (latLngLocation != null) {
                    LatLng businessLatLng = new LatLng(location.getLatitude(), location.getLongitude());
                    LatLng myLatLng = new LatLng(latLngLocation.getLatitude(), latLngLocation.getLongitude());
                    distanceString = GeoLocationUtils.getNumDistanceString(
                        DistanceUnit.MILES, GeoLocationUtils.distanceBetweenLatLngsInMiles(
                            businessLatLng, myLatLng), 1, true);
                }
            }
            locationString.append(locationString.length() > 0 ? " - " : "")
                .append(distanceString);

            classLocationText.setText(locationString.toString());
        }

        if (displayMode == DisplayMode.STAFF_CLASSES || displayMode == DisplayMode.SEARCH ||
                displayMode == DisplayMode.REBOOK || displayMode == DisplayMode.EVENTS ||
                displayMode == DisplayMode.LOCATIONSCHEDULE || displayMode == DisplayMode.BUSINESS_DETAILS ||
                displayMode == DisplayMode.BUSINESS_FOCUSSED_DEAL_DETAILS ||
                displayMode == DisplayMode.QUALIFIED_CLASSES_FROM_PASS || displayMode == DisplayMode.QUALIFIED_EVENTS_FROM_PASS ||
                displayMode == DisplayMode.QUALIFIED_CLASSES_FOR_DEAL || displayMode == DisplayMode.QUALIFIED_EVENTS_FOR_DEAL ||
                displayMode == DisplayMode.QUALIFIED_CLASSES_FROM_PRICING || displayMode == DisplayMode.QUALIFIED_EVENTS_FROM_PRICING) {
            locationLogo.setVisibility(GONE);
        } else {
            locationLogo.setVisibility(VISIBLE);

            if (location != null) {
                Picasso.get()
                    .load(location.getStudioImageUrl())
                    .fit().centerInside()
                    .placeholder(R.drawable.no_biz_logo)
                    .error(R.drawable.no_biz_logo)
                    .tag(getContext().getClass())
                    .config(Bitmap.Config.RGB_565).into(locationLogo);
            }
        }

        /**
         * Conditions:
         * 1. Class is not booked
         * and
         * 2. Class is free to enroll
         */
        boolean showTopAsFree = !cto.isBooked() && cto.isFreeToEnroll();
        /**
         * Conditions:
         * 1. Class is a flex class which means it has to be subscribed through flex passes and may be virtual
         * and
         * 2. User is a flex subscriber, which means user has passes to book a flex classes
         * and
         * 3. Class is booked
         * or
         * 4. Class is not free to enroll
         */
        boolean showFlexOption = cto.isFlexClass() && (MBAuth.getUser() != null && MBAuth.getUser().isFlexSubscriber()) && !showTopAsFree;
        /**
         * Conditions:
         * 1. Class is a flex class which means it has to be subscribed through flex passes and may be virtual
         * and
         * 2. User is a flex subscriber, which means user has a flex subscription
         * and
         * 3. Class is not free to enroll
         * and
         * 4. Class is not booked
         * or
         * 1. Class is not free to enroll
         * and
         * 2. Class is not booked
         * and
         * 3. Class has a drop in price
         */
        boolean showTopPrice = (showFlexOption && !cto.isBooked()) || (!showTopAsFree && !cto.isBooked() && cto.getDropInPrice() != null && cto.requiresPayment());
        /**
         * Conditions:
         * 1. Class is a flex class which means it has to be subscribed through flex passes and may be virtual
         * and
         * 2. User is a flex subscriber, which means user has a flex subscription
         * and
         * 3. Class is not free to enroll
         * and
         * 4. Class is not booked
         * or
         * 1. prev condition for showTopPrice is true
         * and
         * 2. There is no surge price.
         */
        boolean showBottomPrice = (showFlexOption && !cto.isBooked()) || (showTopPrice && !cto.isSurge());

        boolean showNumSpots = false;
        Locale checkedLocale = cto.getLocation() != null ? cto.getLocation().getLocale() : Locale.getDefault();
        if (showFlexOption && !cto.isBooked()) {
            dynamicPriceText.setText(PaymentUtils.getFormattedCurrency(BigDecimal.ZERO, checkedLocale, true));
            dynamicPriceText.setTextColor(ContextCompat.getColor(getContext(), R.color.brand_text_color));
            originalPriceText.setText(getResources().getString(R.string.flex_price_suffix));
            originalPriceText.setTextColor(ContextCompat.getColor(getContext(), R.color.neutral_dark_grey));
        } else if (showTopPrice) {
            boolean isIntroOffer = cto.getPricingReference() != null && cto.getPricingReference().isIntroOffer();
            boolean isDropIn = cto.getPricingReference() != null && cto.getPricingReference().isSingleSession();
            if (cto.getDspoPrice() == null) {
                boolean classIsFree = cto.getDropInPrice().compareTo(BigDecimal.ZERO) == 0;
                dynamicPriceText.setText(classIsFree ? getResources().getString(R.string.free_price)
                    : PaymentUtils.getFormattedCurrency(cto.getDropInPrice().doubleValue(), checkedLocale, true));
                dynamicPriceText.setTextColor(ContextCompat.getColor(getContext(),
                    classIsFree ? R.color.brand_text_color : R.color.text_black));
                setOriginalPriceText(isIntroOffer, isDropIn);

            } else {
                originalPriceText.setText(getResources().getString(R.string.old_price,
                    PaymentUtils.getFormattedCurrency(cto.getDropInPrice().doubleValue(), checkedLocale)));
                dynamicPriceText.setText(PaymentUtils.getFormattedCurrency(
                    cto.getDspoPrice().doubleValue(), checkedLocale, true));
                dynamicPriceText.setTextColor(ContextCompat.getColor(getContext(), R.color.brand_text_color));
            }
            originalPriceText.setTextColor(ContextCompat.getColor(getContext(),
                isIntroOffer ? R.color.brand_text_color : R.color.neutral_dark_grey));
        } else if (showTopAsFree) {
            dynamicPriceText.setText(getResources().getString(R.string.free_price));
            dynamicPriceText.setTextColor(ContextCompat.getColor(getContext(), R.color.brand_text_color));
        }

        if (showTopPrice) {
            numSpotsLeftText.setText(getResources().getQuantityString(R.plurals.num_spots_left, cto.getDynamicPricingOpenings(), cto.getDynamicPricingOpenings()));
            showNumSpots = cto.getDynamicPricingOpenings() > 0 && cto.getDynamicPricingOpenings() <= 2;
        }

        flexBadge.setVisibility(showFlexOption ? VISIBLE : GONE);
        originalPriceText.setVisibility(showBottomPrice ? VISIBLE : INVISIBLE);
        dynamicPriceText.setVisibility(showTopAsFree || showTopPrice ? VISIBLE : GONE);
        numSpotsLeftText.setVisibility(showNumSpots ? VISIBLE : GONE);

        //TODO work with the class statuses

        final boolean geofenceOn = GeoLocationUtils.locationServicesEnabled(getContext()) && SharedPreferencesUtils.isConnectSignInsEnabled();
        final Date window = new Date(System.currentTimeMillis() + Constants.GEOFENCE_START_WINDOW_IN_MILLS);
        boolean showSignin = !cto.hasClassPassed() && cto.hasVisits() && geofenceOn &&
            cto.getStartDate().before(window) &&
            cto.getLocation().getStudioAllowsGeofenceCheckins() &&
            isInRadius() && !cto.onTheWaitlist() && !cto.isSignedIn();
        if (showSignin) {
            setClassStatus(ClassTypeObject.ClassStatus.SIGNIN);
        } else {
            setClassStatus(cto.getStatusEnum());
        }

        ratingContainer.setOnClickListener(view -> {
            if (location != null) {
                Intent listActivityIntent = ReviewListActivity.newIntent(view.getContext(), location.getId(), null, location.getTotalConnectRatings());
                view.getContext().startActivity(listActivityIntent);
            }
        });
    }

    private void setOriginalPriceText(boolean isIntroOffer, boolean isDropIn) {
        Resources res = getResources();
        String priceText = "";
        if (isIntroOffer) {
            priceText = res.getString(R.string.intro_offer_label);
        } else if (isDropIn) {
            priceText = res.getString(R.string.drop_in);
        }
        originalPriceText.setText(priceText.toUpperCase());
    }

    private synchronized void initLocationServices() {
        if (mGoogleApiClient != null) {
            return;
        }

        mGoogleApiClient = new GoogleApiClient.Builder(getContext())
            .addConnectionCallbacks(new GoogleApiClient.ConnectionCallbacks() {
                @Override
                public void onConnected(Bundle bundle) {
                    setClass(cto);
                }

                @Override
                public void onConnectionSuspended(int i) {

                }
            })
            .addApi(LocationServices.API)
            .build();
    }

    public void setClassStatus(final ClassTypeObject.ClassStatus classStatus) {
        setActionButtonText(classStatus);
        switch (classStatus) {
            case BOOKABLE:
            case WAITLISTABLE:
                showQuickBookDialog(classStatus);
                break;
            case PAST:
                setActionButtonClickListener(getCallListener());
                break;
            case UNBOOKABLE:
                if (cto != null && cto.bookabilityIsBlocked()) {
                    setActionOnBookablilityBlocked(classStatus);
                } else if (isFamilyAccountType() && !cto.isFlexOptionEnabled()) {
                    showQuickBookDialog(classStatus);
                } else {
                    setActionButtonClickListener(getCallListener());
                }
                break;
            case SIGNIN:
                setActionButtonClickListener(v -> {
                    trackSeeAllOptimizelyEvent();
                    if (actionListener != null) actionListener.onTaskComplete(new ClassBookingAction(classStatus, cto));

                    int siteId = (cto == null || cto.getLocation() == null) ? 0 : cto.getLocation().getSiteId();
                    String className = cto == null ? getResources().getString(R.string.your_class) : cto.getName();
                    SignIntoClassDialog dialog = SignIntoClassDialog.newInstance(siteId, className);
                    dialog.setCancelCallback(result -> {
                        cto.setStatus(new BookabilityStatus(BookabilityStatus.SIGNED_IN, ""));
                        setClass(cto);
                        if (dataChangedCallback != null) {
                            dataChangedCallback.onTaskComplete(cto);
                        }
                    });
                    dialog.show(((FragmentActivity) ContextUtilKt.unwrapToActivity(getContext()))
                        .getSupportFragmentManager(), SignIntoClassDialog.class.getSimpleName());
                    //TODO go to upcoming?
                });
                break;
            case BOOKED:
                if (cto != null && cto.bookabilityIsBlocked()) {
                    setActionOnBookablilityBlocked(classStatus);
                } else if (isFamilyAccountType() && !cto.isFlexOptionEnabled()) {
                    showQuickBookDialog(classStatus);
                } else {
                    setActionButtonClickListener(null);
                }
                break;
            case WAITLISTED:
            case CANCELLED:
            case SIGNEDIN:
                setActionButtonClickListener(null);
                break;
            case FULL:
                setActionOnBookablilityBlocked(classStatus);
                return; //Return here jumps later setting action button enabled state to true
            default:
                setActionButtonClickListener(v -> {
                    trackSeeAllOptimizelyEvent();
                    if (actionListener != null) actionListener.onTaskComplete(new ClassBookingAction(classStatus, cto));
                });
                break;
        }
        enableActionButtonState(true);
    }

    private boolean isInRadius() {
        if (cto.getLocation() == null) return false;
        boolean isInRadius = false;

        android.location.Location location = GeoLocationUtils.getCurrentLocation(getContext());
        if (location != null) {
            double distance = GeoLocationUtils.distanceBetweenLatLngsInMeters(
                new LatLng(cto.getLocation().getLatitude(), cto.getLocation().getLongitude()),
                new LatLng(location.getLatitude(), location.getLongitude()));

            isInRadius = distance < GeofenceUtils.DEFAULT_RADIUS;
        }

        return isInRadius;
    }

    private OnClickListener getCallListener() {
        trackSeeAllOptimizelyEvent();
        if (cto.getLocation() != null && IntentUtils.canCallNumber(getContext(), cto.getLocation().getPhone())) {
            return v -> {
                Intent intent = IntentUtils.getCallIntent(getContext(), cto.getLocation().getPhone());
                if (intent != null) {
                    getContext().startActivity(intent);
                } else {
                    ToastUtils.show(getContext().getString(R.string.call_studio_failed_message));
                }
            };
        } else {
            return null;
        }
    }

    public void setActionButtonText(ClassStatus classStatus) {
        if (classStatus == null || classStatus.name().equals(ClassStatus.NONE.name()) || classStatus.name().equals(ClassStatus.HISTORY.name())) {
            actionButtonText.setVisibility(GONE);
        } else {
            actionButtonText.setVisibility(VISIBLE);
            if (isFamilyAccountType()) {
                if ((classStatus == ClassStatus.BOOKED || classStatus == ClassStatus.UNBOOKABLE) && !cto.isFlexOptionEnabled()) {
                    setTextAndContendDescriptionOnActionButton(ClassStatus.valueOf(ClassStatus.BOOKABLE.name()).getActionStringResId());
                } else {
                    setTextAndContendDescriptionOnActionButton(classStatus.getActionStringResId());
                }
            } else {
                setTextAndContendDescriptionOnActionButton(classStatus.getActionStringResId());
            }
        }
    }

    private void setActionOnBookablilityBlocked(ClassStatus classStatus){
        setActionButtonClickListener(v -> {
            if (cto != null && cto.bookabilityIsBlocked() && actionListener != null) {
                actionListener.onTaskComplete(new ClassBookingAction(classStatus, cto));
            }
        });
    }

    private void showQuickBookDialog(ClassStatus classStatus) {
        setActionButtonClickListener(v -> {
            trackSeeAllOptimizelyEvent();
            if (!instanceSaved) {
                if (actionListener != null) actionListener.onTaskComplete(new ClassBookingAction(classStatus, cto));
                quickBookClass(cto, null);
            }
        });
    }

    /**
     * Setting text on action button and adding content description
     *
     * @param actionStringResourceId this integer parameter sets to text as string resource id.
     */
    private void setTextAndContendDescriptionOnActionButton(int actionStringResourceId) {
        int stringResourceId = actionStringResourceId;
        if (cto != null
                && (cto.bookabilityIsBlocked() || cto.isVirtual())
                && cto.getStatus() != null
                && !cto.isBooked()
        ) {
            switch (cto.getStatus().getId()) {
                case BookabilityStatus.CLASS_FULL:
                case BookabilityStatus.ONLINE_CAPACITY_FULL:
                    stringResourceId = R.string.full_menu_title;
                    break;
                default:
                    stringResourceId = cto.isCancelled() ? R.string.cancelled_text : R.string.cta_details;
                    break;
            }
        }
        actionButtonText.setText(stringResourceId);
        actionButtonText.setContentDescription(getContext().getString(R.string.book_cta_ada_description,
            getContext().getResources().getText(stringResourceId)));
    }

    public void enableActionButtonState(boolean enabled) {
        actionButtonText.setBackgroundResource(
            enabled ? R.drawable.rounded_border_button_selector : R.drawable.rounded_disabled);
        actionButtonText.setTextColor(
            ContextCompat.getColor(getContext(), enabled ? R.color.text_body_selector : R.color.text_body)
        );
    }

    private void setActionButtonClickListener(OnClickListener listener) {
        actionButtonLayout.setOnClickListener(listener);
        actionButtonLayout.setEnabled(listener != null);
        actionButtonLayout.setClickable(listener != null);
    }

    private void quickBookClass(final ClassTypeObject cto, final FamilyAccount selectedFamilyAccount) {
        if (cto.getLocation() == null) return;

        Context activity = ContextUtilKt.unwrapToActivity(getContext());

        String staffName = null;
        if (cto.getStaff() != null) {
            staffName = cto.getStaff().getDisplayName();
        }

        TrackingHelperUtils.INSTANCE.trackCheckoutBook(cto.getLocation(), cto.getName(),
            cto instanceof Enrollment ? InventoryType.ENROLLMENT : InventoryType.CLASS,
            PageName.CLASS_LIST, staffName, cto.getGatewayId());

        OriginComponent originComponent = cto instanceof Enrollment ? OriginComponent.EVENT_CARD : OriginComponent.CLASS_TIME_CARD;
        BookingEventTracker.trackCtoBookTap(UtilsKt.getOriginViewFromDisplayMode(displayMode), originComponent, cto);

        if (activity instanceof FragmentActivity) {
            FragmentActivity act = (FragmentActivity) activity;

            if (MBAuth.isGuestUser()) {
                // Waiting on API work for the following to be used instead
                Location location = cto.getLocation();
                //TODO [865063] Remove all references to class instance ID from the app
                GuestScheduleItemPricingDialog.newInstance(
                    location.getSiteId(),
                    cto.getId(),
                    cto instanceof Enrollment ? CServiceCategoryType.Enrollment : CServiceCategoryType.Class,
                    location.getLocale(),
                    DeepLinkUtils.getClassUri(location.getId(), cto.getId())
                ).show(act.getSupportFragmentManager(), null);
                return;
            }

            User user = MBAuth.getUser();
            if (user == null || !user.isVerified()) {
                VerifyAccountDialog dialog = new VerifyAccountDialog();
                dialog.show(act.getSupportFragmentManager(), VerifyAccountDialog.class.getSimpleName());
                return;
            }

            // Separate event for favorite class QuickBookDialog
            if (MBStaticCache.getInstance().isFavoriteBusiness(cto.getLocation())) {
                AnalyticsUtils.logClassEvent("(Favorites) | Book now tapped", cto);
            }

            MBDialogFragment quickbookDialog;
            TaskCallback<CTOQuickBookResponse> successCallback = result -> {
                Location location = null;
                if (result != null && result.getClassTypeObject() != null) {
                    location = result.getClassTypeObject().getLocation();
                    if (result.getClassTypeObject().hasVisits()) {
                        AlarmUtils.scheduleNotificationsForClass(result.getClassTypeObject());
                    }
                }

                DialogUtils.showSuccessfulClassBookingDialog(
                        act,
                        result != null ? result.getClassTypeObject() : null,
                        result != null ? result.getCart() : null,
                        result != null ? result.getPaymentMethod() : null,
                        result != null ? result.getPricingReference() : null,
                        location,
                        result != null && result.getShowAttributionSurvey(),
                        result != null ? result.getOrderId() : 0L,
                        result != null ? result.getQbViewState() : null
                );
                // Full screen confirmation screen is fire and forget, so no need to wait for it to close
                onBookingConfirmationDialogClosed(act, result != null ? result.getClassTypeObject() : null);
            };

            String phoneString = MBPhoneUtils.getInternationalPhoneNumber(cto.getLocation(),
                ServiceLocator.getDeviceRepository().getNetworkCountryCode(false));

            if (FamilyAccountsUtilsKt.isFamilyAccountType() || !DevelopmentFlag.Development_QBV2.isFeatureEnabled() || cto.getInventoryRefJson() == null) {
                QuickBookDialog quickBookV1 = QuickBookDialog.newInstance(cto, selectedFamilyAccount, UtilsKt.getOriginViewFromDisplayMode(displayMode), OriginComponent.CLASS_TIME_CARD);
                ServiceLocator.getPerfMeasureRepo().startPerformanceMeasure(System.currentTimeMillis(), PerformanceName.BOOK_TO_COMPLETE_OR_ABANDON);
                ServiceLocator.getPerfMeasureRepo().startPerformanceMeasure(System.currentTimeMillis(), PerformanceName.BOOK_TO_QB_READY);
                quickBookV1.setSuccessCallback(successCallback);
                quickBookV1.setSelectedFamilyMemberCallback(result -> onFamilyAccountSelected(quickBookV1, result));
                quickBookV1.setFailureCallback(failureResult -> {
                    if (failureResult == QuickbookFailureReason.IAP) {
                        InAppPurchasesBlockedDialog dialog = InAppPurchasesBlockedDialog.newInstance(
                            phoneString
                        );
                        dialog.show(act.getSupportFragmentManager(), null);
                    }
                });
                quickbookDialog = quickBookV1;
            } else {
                if (BookingUtils.INSTANCE.checkAndInformClassStartedOrDone(cto, this.getContext())) {
                    return;
                }
                quickbookDialog = QuickBookDialogV2.newInstance(
                    new QuickBookInitializer(QuickBookType.Class, cto.getInventoryRefJson(),
                        ModelTranslationKt.toLocationReference(cto.getLocation()),
                        cto.getDspoPrice() != null),
                    () -> {
                        successCallback.onTaskComplete(new CTOQuickBookResponse(
                                cto,
                                null,
                                null,
                                null,
                                false,
                                0L,
                                null
                        ));
                        return Unit.INSTANCE;
                    },
                    failureReason -> {
                        if (failureReason == QuickbookFailureReason.IAP) {
                            InAppPurchasesBlockedDialog dialog = InAppPurchasesBlockedDialog.newInstance(
                                phoneString);
                            dialog.show(act.getSupportFragmentManager(), null);
                        } else {
                            BookingUtils.INSTANCE.checkAndInformClassStartedOrDone(cto, this.getContext());
                        }
                        return Unit.INSTANCE;
                    }
                );
            }

            quickbookDialog.setCancelCallback(result -> {
                if (cto.requiresPayment()) {
                    CartAbandonReason reason = new CartAbandonReason();
                    reason.setAsAbandon("User dismissed quickbook");
                    MBSalesApi.deleteCart(cto.getLocation().getSiteId(), reason, null, null, FamilyAccountsUtilsKt.getUserIdForDependentUser());
                }
            });

            // We need to provide new tag everytime so that it reopens a new dialog for selected family account.
            quickbookDialog.show(act.getSupportFragmentManager(), QuickBookDialog.SELECTED_USER_TAG != null ? QuickBookDialog.SELECTED_USER_TAG : QuickBookDialog.TAG);
            if (originLocationForAnalytics != null) {
                Map<String, Object> data = new HashMap<>();
                data.put(AnalyticsUtils.KEY_QB_OPENED_FROM, originLocationForAnalytics);
                data.put(AnalyticsUtils.KEY_STUDIO_ID, cto.getLocation().getSiteId());
                data.put(AnalyticsUtils.KEY_QB_IS_FREE, cto.isFreeToEnroll());
                boolean bottomPriceShown = (dynamicPriceText.getVisibility() == View.VISIBLE);
                String price = (cto.isFreeToEnroll() || (cto.getDropInPrice() != null && cto.getDropInPrice().compareTo(BigDecimal.ZERO) == 0))
                    ? PaymentUtils.getFormattedCurrency(BigDecimal.ZERO, cto.getLocation().getLocale()) :
                    (bottomPriceShown ? dynamicPriceText.getText().toString() :
                        (originalPriceText.getVisibility() == View.VISIBLE) ? originalPriceText.getText().toString() :
                            PaymentUtils.getFormattedCurrency(BigDecimal.ZERO, cto.getLocation().getLocale()));
                data.put(AnalyticsUtils.KEY_QB_TRIGGER_PRICE, price);
                QuickBookViewModel.sendQuickBookOpenedEvent(data);
            }
        }
    }

    private void onBookingConfirmationDialogClosed(FragmentActivity activity, ClassTypeObject cto) {
        setClass(cto);
        if (dataChangedCallback != null) {
            dataChangedCallback.onTaskComplete(cto);
        }
        BookingUtils.onBookingConfirmationDialogClosed(activity, cto.getLocation());
    }

    /***
     * This method dismisses existing dialog and open new one with new tag. It calls
     * quickBookClass() with classTypeObject and also provides familyAccount.
     * @param quickBook existing object of dialog which needs to dismiss
     * @param result call back result of type map having class type object and family account object
     */
    private void onFamilyAccountSelected(QuickBookDialog quickBook, Map<String, Object> result) {
        if (result != null) {
            quickBook.dismiss();
            FamilyAccount familyAccount = (FamilyAccount) result.get(KEY_SELECTED_USER);
            if (familyAccount != null && cto != null) {
                QuickBookDialog.SELECTED_USER_TAG = QuickBookDialog.TAG + familyAccount.getId();
                SharedPreferencesUtils.setSelectedUserId((familyAccount.isPrimaryUser()) ? "" : familyAccount.getId());
                quickBookClass(cto, familyAccount);
            }
        }
    }

    public void setActionListener(TaskCallback<ClassBookingAction> actionListener) {
        this.actionListener = actionListener;
    }

    @Override
    protected Parcelable onSaveInstanceState() {
        instanceSaved = true;
        return super.onSaveInstanceState();
    }

    public void adjustForTimezoneOffset(boolean adjust) {
        adjustedForOffset = adjust;
    }

    public void setOriginLocationForAnalytics(String originLocationForAnalytics) {
        this.originLocationForAnalytics = originLocationForAnalytics;
    }

    public void setHomeCarouselContext(String homeCarouselContext) {
        this.homeCarouselContext = homeCarouselContext;
    }

    private void trackSeeAllOptimizelyEvent() {
        if (homeCarouselContext != null && !homeCarouselContext.isEmpty()) {
            if (homeCarouselContext.equalsIgnoreCase(RecommenderType.LMO.getType())) {
                ServiceLocator.getAbTestFramework().track(HOME_LMO_SEE_ALL_BOOK_NOW);
            } else if (homeCarouselContext.equalsIgnoreCase(RecommenderType.SIMILAR_HISTORY.getType())) {
                ServiceLocator.getAbTestFramework().track(HOME_SIMILAR_HISTORY_SEE_ALL_BOOK_NOW);
            }
        }
    }
}
