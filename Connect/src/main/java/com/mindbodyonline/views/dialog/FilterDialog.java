package com.mindbodyonline.views.dialog;

import android.content.DialogInterface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;

import com.mindbodyonline.android.util.TaskCallback;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.adapters.filters.IFilter;
import com.mindbodyonline.connect.fragments.custom.MBDialogFragment;

import org.jetbrains.annotations.Nullable;

import java.util.Calendar;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;

public class FilterDialog<T> extends MBDialogFragment {
    private ViewGroup filterContainer;
    private View closeButton, saveButton;
    private Set<IFilter<T>> enabledFilters = new LinkedHashSet<>();
    private Set<IFilter<T>> availableFilters = new HashSet<>();
    private TaskCallback<Set<IFilter<T>>> saveFiltersCallback;
    private TaskCallback<Calendar> mDismissCallback;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.dialog_filter, container, false);

        filterContainer = root.findViewById(R.id.filter_container);
        closeButton = root.findViewById(R.id.close_button);
        saveButton = root.findViewById(R.id.save_button);

        for (IFilter<T> filter : availableFilters) {
            addFilterRow(filter);
        }

        return root;
    }

    public void setFilters(Set<IFilter<T>> availableFilters, Set<IFilter<T>> enabledFilters) {
        this.availableFilters.addAll(availableFilters);
        this.enabledFilters.addAll(enabledFilters);
    }

    private void addFilterRow(final IFilter<T> filter) {
        View filterRow = LayoutInflater.from(getActivity()).inflate(R.layout.dialog_filter_row, filterContainer, false);
        ((TextView) filterRow.findViewById(R.id.filter_text)).setText(filter.getDescriptionStringResourceId());
        SwitchCompat filterSwitch = filterRow.findViewById(R.id.filter_switch);
        filterSwitch.setChecked(enabledFilters.contains(filter));
        filterSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                enabledFilters.add(filter);
            } else {
                enabledFilters.remove(filter);
            }
        });
        filterSwitch.setContentDescription(filterRow.getContext().getResources().getText(filter.getDescriptionStringResourceId()));
        filterContainer.addView(filterRow);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        closeButton.setOnClickListener(v -> dismiss());

        saveButton.setOnClickListener(v -> {
            if (saveFiltersCallback != null) {
                saveFiltersCallback.onTaskComplete(enabledFilters);
            }
            dismiss();
        });

        fixDialogForWidth();
    }

    public void setSaveFiltersCallback(TaskCallback<Set<IFilter<T>>> saveFiltersCallback) {
        this.saveFiltersCallback = saveFiltersCallback;
    }

    public void setDismissListener(TaskCallback<Calendar> taskCallback) {
        mDismissCallback = taskCallback;
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        if (mDismissCallback != null) {
            mDismissCallback.onTaskComplete(null);
        }
    }
}
