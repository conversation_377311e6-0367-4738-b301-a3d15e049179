package com.mindbodyonline.framework.abvariant

import com.mindbodyonline.connect.BuildConfig
import com.mindbodyonline.connect.utils.BreadcrumbsUtils
import com.mindbodyonline.data.services.locator.ServiceLocator

object ReleaseFlagManager {
    var featuresMap: MutableMap<String, FeatureFlagData>? = null
    var previousCalledFlags = mutableSetOf<String>()
    var featureFlagsCache = mutableMapOf<String, Boolean>()
    var developmentFlagsCache = mutableMapOf<String, Boolean>()
}

interface DevelopmentFlagContract {
    val key: String
    val isReleaseReady: Boolean
    val localDebugFlagValue: Boolean
    fun isFeatureEnabled(): Boolean {
        val defaultValue = if (BuildConfig.DEBUG) localDebugFlagValue else isReleaseReady
        val returnFeatureEnabled = when (isFlagEnabled(key, FlagType.DevelopmentFlag)) {
            true -> defaultValue
            else -> {
                when {
                    !ServiceLocator.abTestFramework.isFrameworkUp() -> defaultValue
                    ReleaseFlagManager.featuresMap?.containsKey(key) == null -> defaultValue
                    ReleaseFlagManager.featuresMap?.containsKey(key) == false -> defaultValue
                    else -> false
                }
            }
        }
        ReleaseFlagManager.previousCalledFlags.takeUnless { it.contains(key) }?.let {
            BreadcrumbsUtils.breadcrumbEnabledFeature(key)
            ReleaseFlagManager.previousCalledFlags.add(key)
        }
        return returnFeatureEnabled
    }
}

interface FeatureFlagsContract {
    val key: String
    // Overrides the value of isFeatureEnabled in debuggable builds
    // with this value, if it is set.
    val devOverride: Boolean?
        get() = null

    fun isFeatureEnabled(): Boolean {
        devOverride?.let {
            if (BuildConfig.DEBUG) return it
        }

        ReleaseFlagManager.previousCalledFlags.takeUnless { it.contains(key) }?.let {
            BreadcrumbsUtils.breadcrumbEnabledFeature(key)
            ReleaseFlagManager.previousCalledFlags.add(key)
        }
        return isFlagEnabled(key, FlagType.FeatureFlag)
    }

    fun isFeatureEnabled(userAttributes: MutableMap<String, Any>): Boolean {
        devOverride?.let {
            if (BuildConfig.DEBUG) return it
        }

        ReleaseFlagManager.previousCalledFlags.takeUnless { it.contains(key) }?.let {
            BreadcrumbsUtils.breadcrumbEnabledFeature(key)
            ReleaseFlagManager.previousCalledFlags.add(key)
        }
        return isFlagEnabled(key, FlagType.FeatureFlag, userAttributes)
    }

    // Syntactic sugar for Kotlin usages.
    operator fun invoke(): Boolean = isFeatureEnabled()
}

/**
 * Checks the local flag cache for the value first.
 * If not found in the cache, fetches it from Optimizely.
 * @return True if flag is enabled, else false.
 */
fun isFlagEnabled(
    key: String,
    flagType: FlagType,
): Boolean {
    val flagsCache = when (flagType) {
        FlagType.FeatureFlag -> ReleaseFlagManager.featureFlagsCache
        FlagType.DevelopmentFlag -> ReleaseFlagManager.developmentFlagsCache
    }
    return flagsCache.takeIf { it.contains(key) }?.get(key) ?: run {
        ServiceLocator.abTestFramework.isFeatureEnabled(key).also { enabled ->
            flagsCache[key] = enabled
        }
    }
}

fun isFlagEnabled(
    key: String,
    flagType: FlagType,
    userAttributes: MutableMap<String, Any>
): Boolean {
    val flagsCache = when (flagType) {
        FlagType.FeatureFlag -> ReleaseFlagManager.featureFlagsCache
        FlagType.DevelopmentFlag -> ReleaseFlagManager.developmentFlagsCache
    }
    return flagsCache.takeIf { it.contains(key) }?.get(key) ?: run {
        ServiceLocator.abTestFramework.isFeatureEnabled(key, userAttributes).also { enabled ->
            flagsCache[key] = enabled
        }
    }
}

enum class FlagType {
    FeatureFlag,
    DevelopmentFlag
}

data class FeatureFlagData(val key: String)

/**
 * This is our list of current development flags. Development flags are treated the same as Feature flags in
 * Optimizely but not in our code base.  Specifically this handles checking for flags that do not yet
 * exist in Optimizely, no connectivity/servers down, or failure to initialize the framework. For
 * these cases, behavior will pivot on debug vs release build. For debug build, it will resort to the
 * localDebugFlagValue (which has a default value of true). For release builds it will resort to the
 * isReleaseReady value. Setting the isReleaseReady value to true should be done prior to the intentional
 * release.  Do NOT merge this code with a default value of true until it is ready for release.
 */
enum class DevelopmentFlag(override val key: String, override val isReleaseReady: Boolean, override val localDebugFlagValue: Boolean = true) : DevelopmentFlagContract {
    Development_QBV2(key = "development_android_qbv2", false, localDebugFlagValue = false),
    DEVELOPMENT_ENABLE_APPOINTMENT_REVIEW(key = "development_android_enable_appointment_review", isReleaseReady = true),
    DEVELOPMENT_FREQUENT_LOGOUT_FIX("development_android_frequent_logout_fix", isReleaseReady = true),
    DEVELOPMENT_CLASS_SCHEDULE_START_RANGE_FIX("android_class_schedule_start_range_fix", isReleaseReady = true),
    DEVELOPMENT_IDEAL_PAYMENT_FLOW("development_android_ideal_payment_flow", isReleaseReady = true),
    DEVELOPMENT_SAME_DAY_BOOKING_FIX("development_android_same_day_booking_fix", isReleaseReady = true),
    DEVELOPMENT_IDEAL_CONTRACT_PAYMENT_OPTION("development_android_ideal_recurring_payment_option", isReleaseReady = true),
    DEVELOPMENT_AUTO_SUGGEST_IMPROVEMENTS("development_android_auto_suggest_improvements", isReleaseReady = true),
    // Set as false as currently shared UI on backend has login issues from multiple IDPs and a create account cookie navigation problem on Android.
    DEVELOPMENT_BANCONTACT_PAYMENT_FLOW("development_android_bancontact_payment_flow", isReleaseReady = true),
    DEVELOPMENT_KLARNA_PAYMENT_FLOW("development_android_klarna_payment_flow", isReleaseReady = true),
    DEVELOPMENT_GOOGLE_PAY_PAYMENT_FLOW("development_android_google_pay_payment_flow", isReleaseReady = true),
    DEVELOPMENT_GOOGLE_PAY_CONTRACT_PAYMENT_FLOW("development_android_google_pay_contract_payment_flow", isReleaseReady = true),
    DEVELOPMENT_BANCONTACT_CONTRACT_PAYMENT_FLOW("development_android_bancontact_contract_payment_flow", isReleaseReady = true),
    DEVELOPMENT_TWINT_PAYMENT_FLOW("development_twint_payment_flow", isReleaseReady = true),
    DEVELOPMENT_FPX_PAYMENT_FLOW("development_fpx_payment_flow", isReleaseReady = true),
    DEVELOPMENT_ALIPAY_PAYMENT_FLOW("development_alipay_payment_flow", isReleaseReady = true),
    DEVELOPMENT_CLASS_SCHEDULE_REFACTOR("development_android_class_schedule_refactor", isReleaseReady = true),
    DEVELOPMENT_HIDE_BOOKER_RATING("development_android_hide_booker_rating", isReleaseReady = true),
    DEVELOPMENT_APPOINTMENT_DETAILS_REORDERING("development_android_appointment_details_reordering", isReleaseReady = true),
    DEVELOPMENT_ENABLE_ENROLLMENT_DEAL_PURCHASE("development_android_enable_enrollment_deal_purchase", isReleaseReady = true),
    DEVELOPMENT_APP_LAUNCH_STEPS("development_android_app_launch_steps", isReleaseReady = true),
    DEVELOPMENT_REMOVE_FAV_BUSINESSES_CALL_DURING_APP_LAUNCH("development_android_remove_fav_businesses_call_during_app_launch", isReleaseReady = true),
    DEVELOPMENT_TRACK_OPTIMIZELY_EVENTS("development_android_track_optimizely_events", localDebugFlagValue = false, isReleaseReady = false),
    DEVELOPMENT_SHOW_ENROLLMENT_PRICING("development_android_show_enrollment_pricing", isReleaseReady = true),
    DEVELOPMENT_ANDROID_NO_AUTH_TOKEN_INTERCEPT("development_android_no_auth_token_intercept", isReleaseReady = true),
    DEVELOPMENT_ANDROID_LOGOUT_ALL_NEWRELIC_USERID_EXCEPTION_FIX("development_android_logout_all_newrelic_userid_exception_fix", isReleaseReady = true),
    DEVELOPMENT_LOG_NEWRELIC_BREADCRUMBS("android_log_newrelic_breadcrumbs", isReleaseReady = true),
    DEVELOPMENT_REMOVE_NAVIGATE_CALL_FROM_MARKET_OBSERVER("development_android_remove_navigate_call_from_market_observer", isReleaseReady = true),
}

/**
 * Standard Feature flags that also handles logging one time upon first call to isFeatureEnabled.  These
 * are different than our Release Flags.
 */
enum class FeatureFlag(
    override val key: String,
    override val devOverride: Boolean? = null
) : FeatureFlagsContract {
    PICK_A_SPOT(key = "android_pick_a_spot"),
    UPDATED_APPT_CALENDAR(key = "updated_appt_calendar"),
    //Controls the amount of time we are fetching calendar for appointment time slots availablity
    APPT_CALENDAR_FETCH_TIME(key = "appt_calendar_fetch_time"),
    RANDOMIZE_ANY_STAFF_SELECTION("android_randomize_any_staff"),
    IDEAL_PAYMENT_FLOW("android_ideal_payment_flow"),
    IDEAL_CONTRACT_PAYMENT_OPTION("android_ideal_recurring_payment_option"),
    AUTO_SUGGEST_IMPROVEMENTS("android_auto_suggest_improvements"),
    AUTO_SUGGEST_SPELL_CORRECT("android_auto_suggest_spell_correct"),
    SHARED_LOGIN_UI_FLOW("android_shared_login_ui_flow", devOverride = true),
    BANCONTACT_PAYMENT_FLOW("android_bancontact_payment_flow"),
    KLARNA_PAYMENT_FLOW("android_klarna_payment_flow"),
    GOOGLE_PAY_PAYMENT_FLOW("android_google_pay_payment_flow"),
    GOOGLE_PAY_CONTRACT_PAYMENT_FLOW("android_google_pay_contract_payment_flow"),
    BANCONTACT_CONTRACT_PAYMENT_FLOW("android_bancontact_contract_payment_flow"),
    DEAL_DETAILS_UI_REVAMP("android_deal_details_ui_revamp"),
    BUSINESS_DETAILS_REBRAND("android_business_details_rebrand"),
    TWINT_PAYMENT_FLOW("android_twint_payment_flow"),
    FPX_PAYMENT_FLOW("android_fpx_payment_flow"),
    ALIPAY_PAYMENT_FLOW("android_alipay_payment_flow"),
    PAYNOW_PAYMENT_FLOW("android_paynow_payment_flow"),
    WECHAT_PAYMENT_FLOW("android_wechat_payment_flow"),
    WAITLIST_CONFIRMATION_REBRAND("android_waitlist_confirmation_rebrand"),
    RESERVE_WITH_CREDIT_CARD("android_reserve_with_credit_card"),
    ROKT("android_rokt_ad"),
    CLASS_SCHEDULE_UI_REFACTOR("android_class_schedule_ui_refactor", devOverride = false),
    STRIPE_PAYMENT_INTENT_STATUS_POLLING("android_stripe_payment_intent_status_polling"),
    FTC_AUDIT_LOG_SCREENSHOT_CAPTURE("android_ftc_audit_log_screenshot_capture"),
    ANDROID_TOKEN_DELEGATION_FOR_SCOPES("android_token_delegation_for_scopes"),
    DEALS_NEAR_YOU_REDIRECTION("android_deals_near_you_redirection"),
    FTC_TERMS_AND_CONDITIONS("android_ftc_terms_and_conditions"),
    FTC_MANAGE_CONTRACTS("android_ftc_manage_contracts"),
    BUCKETING_TEST("android_bucketing_test"),
}
