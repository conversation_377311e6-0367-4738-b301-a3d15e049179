package com.mindbodyonline.framework.abvariant

/**
 * Experiment keys for A/B Tests and Feature Tests.
 */
// Feature test for the Invite A Friend dialog
const val INVITE_A_FRIEND_TEST = "invite_a_friend_android_test"

// Feature Experiment keys for the Auto Suggest Improvements
const val AUTO_SUGGEST_SORT_VARIABLE_DEFAULT_VALUE = "-score,distance"
const val AUTO_SUGGEST_SORT_VARIABLE_DISTANCE_VALUE = "distance"
const val AUTO_SUGGEST_SORT_VARIABLE_KEY = "autoSuggestSort"
const val AUTO_COMPLETE_DEBOUNCE_TIME_VARIABLE_KEY = "debounceTime"
const val AUTO_COMPLETE_MIN_CHAR_VARIABLE_KEY = "minChar"
const val DEAL_DETAILS_REVAMP_VARIABLE_KEY = "ui_type"
const val DEAL_DETAILS_REVAMP_VARIATION_TEST2B = "test2B"
const val DEAL_DETAILS_REVAMP_VARIATION_TEST2A = "test2A"

// Feature Experiment keys for the Business Details rebranding
const val BUSINESS_DETAILS_REBRAND_VARIABLE_KEY = "ui_mode"

const val RWCC_VALIDATE_CC_VARIABLE_KEY = "shouldValidateCreditCard"
const val RWCC_ALL_STUDIO_SUPPORT_VARIABLE_KEY = "allStudiosSupport"
const val RWCC_SUPPORTED_COUNTRIES_VARIABLE_KEY = "supportedStudioCountries"
const val RWCC_EXCLUDED_STUDIOS_VARIABLE_KEY = "excludedStudioIds"
const val RWCC_SUPPORTED_STUDIOS_VARIABLE_KEY = "supportedStudioIds"
const val RWCC_INCLUDE_SUBSCRIBER_CREDIT_CARD_VARIABLE_KEY = "includeSubscriberCreditCard"
const val RWCC_MAKE_CREDIT_CARD_OPTIONAL_VARIABLE_KEY = "makeCreditCardOptional"

const val ROKT_AUTO_CLOSE_LOADER_TIME_VARIABLE_KEY = "auto_close_rokt_loader_time"
const val ROKT_CONFIRMATION_SCREEN_VARIABLE_KEY = "confirmation_screen"
const val ROKT_CONFIRMATION_SCREEN_VARIABLE_FULL_SCREEN_VALUE = "full_screen"
const val ROKT_CONFIRMATION_SCREEN_VARIABLE_FULL_SCREEN_WITH_ROKT_ADS_VALUE = "full_screen_with_rokt_ads"
const val ROKT_ENABLE_LOGGING_VARIABLE_KEY = "enable_rokt_logging"
const val ROKT_INITIALIZE_SDK_VARIABLE_KEY = "initialize_rokt_sdk"
const val ROKT_SHOW_AD_AFTER_ATTRIBUTION_SURVEY_VARIABLE_KEY = "show_rokt_ad_after_attribution_survey"
const val ROKT_TRACK_TEMPORARY_EVENTS_VARIABLE_KEY = "track_temporary_events"
const val ROKT_USE_SANDBOX_VARIABLE_KEY = "use_rokt_sandbox"

const val DISABLE_STRIPE_CALLBACK_HANDLER ="disable_stripe_callback_handler"
const val STRIPE_INITIAL_DELAY_INTERVAL = "initial_delay_interval"
const val STRIPE_MAX_RETRY_COUNT = "max_retry_count"
const val STRIPE_RETRY_DELAY_INTERVAL = "retry_delay_interval"

const val DEALS_NEAR_YOU_DESTINATION_VARIABLE_KEY = "destination"
const val DEALS_NEAR_YOU_DESTINATION_VARIABLE_SEARCH_VALUE = "search"
const val DEALS_NEAR_YOU_DESTINATION_VARIABLE_DEALS_VALUE = "deals"

const val BUCKET_NAME = "bucket_name"
