package com.mindbodyonline.framework.abvariant

import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.BuildConfig
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.toUserLocationMap
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.framework.interfaces.ABTestFramework

object ABHelperUtils {

    /**
     * In the event that an incorrect value is passed in or an Optimizely setup issue causes a
     * potential miscalculation, these values will ensure we never charge a negative fee or
     * an incredibly high fee
     */
    private const val MIN_SERVICE_FEE = 0.00
    private const val MAX_SERVICE_FEE = 5.00

    /**
     * Gets the service fee view state for the quickbook dialog.  Sets isQualified to false
     * if user not qualified for the experiment, the feature is disabled, or the [price] is zero dollars.
     * Sets isControl to true if the user is bucketed into the control variation.  Otherwise a full
     * service fee view state is returned including the calculated service fee and dynamic copy for
     * the modal dialog based on the variation the user was bucketed into.
     */
    fun getServiceFeeViewState(abTestFramework: ABTestFramework, price: Double): ServiceFeeViewState {
        // Determine if user and price are qualified for feature experiment
        if (MBAuth.isGuestUser()) {
            MBLog.d("Optimizely", "Guest user does not qualify for feature test")
            return ServiceFeeViewState(isQualified = false)
        }
        if (price == 0.0) {
            MBLog.d("Optimizely", "Zero dollar price does not qualify for feature test")
            return ServiceFeeViewState(isQualified = false)
        }
        val attributes = getUserAttributes()
        val isUserBucketed = abTestFramework.isFeatureEnabled(CONSUMER_SERVICE_FEE_ANDROID_V2, attributes)
        if (!isUserBucketed) {
            MBLog.d("Optimizely", "User does not qualify or feature experiment has been disabled")
            return ServiceFeeViewState(isQualified = false)
        }

        // Get the service fee percentage for this user's variation
        val percent = abTestFramework.getFeatureVariableDouble(CONSUMER_SERVICE_FEE_ANDROID_V2, PERCENT, attributes)
        if (percent == 0.0) {
            MBLog.d("Optimizely", "Control variation, do not show fee but still send conversion events")
            return ServiceFeeViewState(isQualified = true, isControl = true)
        }

        // User qualified and price is greater than zero dollars, calculate service fee to show
        val ceil = abTestFramework.getFeatureVariableDouble(CONSUMER_SERVICE_FEE_ANDROID_V2, CEIL, attributes)
        val floor = abTestFramework.getFeatureVariableDouble(CONSUMER_SERVICE_FEE_ANDROID_V2, FLOOR, attributes)
        val fee = calcServiceFee(price, percent ?: 0.0, floor ?: 0.0, ceil ?: 0.0)

        // Fetch dynamic copy for info dialog
        val modalBody = abTestFramework.getFeatureVariableString(CONSUMER_SERVICE_FEE_ANDROID_V2, MODAL_BODY, attributes)
        val modalHeader = abTestFramework.getFeatureVariableString(CONSUMER_SERVICE_FEE_ANDROID_V2, MODAL_HEADER, attributes)
        val quickbookLine = abTestFramework.getFeatureVariableString(CONSUMER_SERVICE_FEE_ANDROID_V2, QUICKBOOK_LINE, attributes)

        // Log view state and return view state to the quickbook dialog
        val serviceFeeViewState = ServiceFeeViewState(
                isQualified = true,
                isControl = false,
                fee = fee,
                modalBody = modalBody ?: "",
                modalHeader = modalHeader ?: "",
                quickbookLine = quickbookLine ?: ""
        )
        MBLog.d("Optimizely", serviceFeeViewState.toString())
        return serviceFeeViewState
    }

    /**
     * Calculates the convenience service fee to charge a user. The following conditions are checked:
     * If the fee is less than the minimum we would want to charge a user, than charge the [floor]
     * If the fee is greater than the maximum we would want to charge a user, then charge the [ceil]
     * If the fee falls between this range, then charge the calculated fee [price] * [percent]
     */
    fun calcServiceFee(price: Double, percent: Double, floor: Double, ceil: Double) =
        if (price == 0.0) 0.0
        else (price * percent).let { fee ->
            when {
                // Protects us from any potential bugs or setup issues
                fee < MIN_SERVICE_FEE -> MIN_SERVICE_FEE
                fee > MAX_SERVICE_FEE -> MAX_SERVICE_FEE
                // Return the valid service fee
                fee < floor -> floor
                fee < ceil -> fee
                else -> ceil
            }
        }

    data class ServiceFeeViewState(
            var isQualified: Boolean = false,
            var isControl: Boolean = false,
            var modalBody: String = "",
            var modalHeader: String = "",
            var quickbookLine: String = "",
            var fee: Double = 0.0
    )

    const val DEFAULT_CLASSPASS_LENGTH = "1 month"

    /**
     * User attributes to send on an Optimizely request.  This list should not change frequently
     * and similarly should only hold user values that do not change frequently. Failure to do so
     * will cause inconsistency in result tracking.  Buckets and click conversions previously recorded
     * for a user whose attribute now disqualifies them will be erased from the experiment results.
     *
     * Attributes can be added to an experiment activate, event track, or feature variable fetch.
     */
    @JvmStatic
    fun getUserAttributes(): MutableMap<String, Any> {
        val attributes = mutableMapOf<String, Any>()
        attributes[COUNTRY] = MBAuth.getUser()?.country ?: ""
        return attributes
    }

    /**
     * Returns all user attributes to send in an Optimizely request.
     * Includes the user ID, version name and user country.
     * Should ideally eventually replace `getUserAttributes()`.
     */
    @JvmStatic
    fun getAllUserAttributes(): MutableMap<String, Any> {
        // used as a backup if user country is not available
        val userLocationCountry = SharedPreferencesUtils
            .getGlobalIndicatorLocation()
            .toUserLocationMap()[com.mindbodyonline.connect.analytics.COUNTRY] ?: ""

        val attributes = mutableMapOf<String, Any>()
        attributes[USER_ID] = MBAuth.getUser()?.id ?: 0
        attributes[VERSION_NAME] = BuildConfig.VERSION_NAME
        attributes[COUNTRY] = MBAuth.getUser()?.country ?: userLocationCountry
        return attributes
    }

    fun isApiAutoCompleteVariationEnabled(): Boolean {
        return getAutoSuggestSortVariableValue().let {
            it == AUTO_SUGGEST_SORT_VARIABLE_DEFAULT_VALUE || it == AUTO_SUGGEST_SORT_VARIABLE_DISTANCE_VALUE
        }
    }

    fun getAutoSuggestSortVariableValue(): String {
        return ServiceLocator.abTestFramework.getFeatureVariableString(
            FeatureFlag.AUTO_SUGGEST_IMPROVEMENTS.key,
            AUTO_SUGGEST_SORT_VARIABLE_KEY
        ) ?: AUTO_SUGGEST_SORT_VARIABLE_DEFAULT_VALUE
    }

    fun getAutoCompleteDebounceAndMinChar(): Pair<Long, Int> {
        val debounce = ServiceLocator.abTestFramework.getFeatureVariableDouble(
            featureKey = FeatureFlag.AUTO_SUGGEST_IMPROVEMENTS.key,
            variableKey = AUTO_COMPLETE_DEBOUNCE_TIME_VARIABLE_KEY
        )?.toLong() ?: 350L
        val minChar = ServiceLocator.abTestFramework.getFeatureVariableDouble(
            featureKey = FeatureFlag.AUTO_SUGGEST_IMPROVEMENTS.key,
            variableKey = AUTO_COMPLETE_MIN_CHAR_VARIABLE_KEY
        )?.toInt() ?: 1
        return debounce to minChar
    }

    enum class DealDetailsVariation {
        TEST_2B,
        TEST_2A,
        LEGACY
    }

    @JvmStatic
    fun getDealDetailsVariation(): DealDetailsVariation = if (FeatureFlag.DEAL_DETAILS_UI_REVAMP.isFeatureEnabled()) {
        when (
            ServiceLocator.abTestFramework.getFeatureVariableString(
                    FeatureFlag.DEAL_DETAILS_UI_REVAMP.key,
                    DEAL_DETAILS_REVAMP_VARIABLE_KEY
            )
        ) {
            DEAL_DETAILS_REVAMP_VARIATION_TEST2B -> DealDetailsVariation.TEST_2B
            DEAL_DETAILS_REVAMP_VARIATION_TEST2A -> DealDetailsVariation.TEST_2A
            else -> DealDetailsVariation.LEGACY
        }
    } else DealDetailsVariation.LEGACY

    @JvmStatic
    fun showBusinessFocussedDealDetailsScreen(): Boolean = isBusinessDetailsRebrandActive() && when (
        getDealDetailsVariation()
    ) {
        DealDetailsVariation.TEST_2B,
        DealDetailsVariation.TEST_2A -> true

        else -> false
    }

    @JvmStatic
    fun isBusinessDetailsRebrandActive(): Boolean =
            ServiceLocator.abTestFramework.getFeatureVariableBoolean(
                    FeatureFlag.BUSINESS_DETAILS_REBRAND.key,
                    BUSINESS_DETAILS_REBRAND_VARIABLE_KEY
            ) ?: FeatureFlag.BUSINESS_DETAILS_REBRAND.isFeatureEnabled()

    @JvmStatic
    fun isCountrySupportedByRWCC(countryCode: String? = null, countryName: String? = null) : Boolean {
        if (areAllStudiosSupportedForRWCC()) return true

        val supportedCountries = getRolloutCountriesForRWCC()
        return supportedCountries.contains(countryCode) || supportedCountries.contains(countryName)
    }

    @JvmStatic
    fun isStudioSupportedByRWCC(studioId: Int? = null): Boolean {
        val studioIdString: String = studioId?.toString() ?: ""
        val excludedStudios = getExcludedStudioIdsForRWCC()
        val supportedStudios = getSupportedStudioIdsForRWCC()
        val allStudiosSupported = supportedStudios.isEmpty()
        return !excludedStudios.contains(studioIdString) && (supportedStudios.contains(studioIdString) || allStudiosSupported)
    }

    private fun getRolloutCountriesForRWCC(): List<String> {
        val countriesString: String = if (FeatureFlag.RESERVE_WITH_CREDIT_CARD.isFeatureEnabled()) {
            ServiceLocator.abTestFramework.getFeatureVariableString(
                    FeatureFlag.RESERVE_WITH_CREDIT_CARD.key,
                    RWCC_SUPPORTED_COUNTRIES_VARIABLE_KEY,
                    getAllUserAttributes(),
            ) ?: ""
        } else ""
        return countriesString.split(",").filter { it.isNotEmpty() }
    }

    private fun getExcludedStudioIdsForRWCC(): List<String> {
        val studiosIdsString: String = if (FeatureFlag.RESERVE_WITH_CREDIT_CARD.isFeatureEnabled()) {
            ServiceLocator.abTestFramework.getFeatureVariableString(
                    FeatureFlag.RESERVE_WITH_CREDIT_CARD.key,
                    RWCC_EXCLUDED_STUDIOS_VARIABLE_KEY,
                    getAllUserAttributes(),
            ) ?: ""
        } else ""
        return studiosIdsString.split(",").filter { it.isNotEmpty() }
    }

    private fun getSupportedStudioIdsForRWCC(): List<String> {
        val studiosIdsString: String = if (FeatureFlag.RESERVE_WITH_CREDIT_CARD.isFeatureEnabled()) {
            ServiceLocator.abTestFramework.getFeatureVariableString(
                    FeatureFlag.RESERVE_WITH_CREDIT_CARD.key,
                    RWCC_SUPPORTED_STUDIOS_VARIABLE_KEY,
                    getAllUserAttributes(),
            ) ?: ""
        } else ""
        return studiosIdsString.split(",").filter { it.isNotEmpty() }
    }

    fun isValidateCreditCardEnabledForRWCC(): Boolean =
            FeatureFlag.RESERVE_WITH_CREDIT_CARD.isFeatureEnabled()
                    && ServiceLocator.abTestFramework.getFeatureVariableBoolean(
                    FeatureFlag.RESERVE_WITH_CREDIT_CARD.key,
                    RWCC_VALIDATE_CC_VARIABLE_KEY,
                    getAllUserAttributes(),
            ) ?: false

    private fun areAllStudiosSupportedForRWCC(): Boolean =
            FeatureFlag.RESERVE_WITH_CREDIT_CARD.isFeatureEnabled()
                    && ServiceLocator.abTestFramework.getFeatureVariableBoolean(
                    FeatureFlag.RESERVE_WITH_CREDIT_CARD.key,
                    RWCC_ALL_STUDIO_SUPPORT_VARIABLE_KEY,
                    getAllUserAttributes(),
            ) ?: false

    fun includeSubscriberCreditCard(): Boolean =
            FeatureFlag.RESERVE_WITH_CREDIT_CARD.isFeatureEnabled()
                    && ServiceLocator.abTestFramework.getFeatureVariableBoolean(
                    FeatureFlag.RESERVE_WITH_CREDIT_CARD.key,
                    RWCC_INCLUDE_SUBSCRIBER_CREDIT_CARD_VARIABLE_KEY,
                    getAllUserAttributes(),
            ) ?: false

    fun makeCreditCardOptional(): Boolean =
            FeatureFlag.RESERVE_WITH_CREDIT_CARD.isFeatureEnabled()
                    && ServiceLocator.abTestFramework.getFeatureVariableBoolean(
                    FeatureFlag.RESERVE_WITH_CREDIT_CARD.key,
                    RWCC_MAKE_CREDIT_CARD_OPTIONAL_VARIABLE_KEY,
                    getAllUserAttributes(),
            ) ?: false

    enum class ConfirmationScreenType {
        FULL_SCREEN,
        FULL_SCREEN_WITH_ROKT_ADS,
    }

    private fun getConfirmationScreenType(): ConfirmationScreenType =
            if (FeatureFlag.ROKT.isFeatureEnabled()) {
                when (ServiceLocator.abTestFramework.getFeatureVariableString(
                        FeatureFlag.ROKT.key,
                        ROKT_CONFIRMATION_SCREEN_VARIABLE_KEY,
                        getAllUserAttributes(),
                )) {
                    ROKT_CONFIRMATION_SCREEN_VARIABLE_FULL_SCREEN_VALUE -> ConfirmationScreenType.FULL_SCREEN
                    ROKT_CONFIRMATION_SCREEN_VARIABLE_FULL_SCREEN_WITH_ROKT_ADS_VALUE -> ConfirmationScreenType.FULL_SCREEN_WITH_ROKT_ADS
                    else -> ConfirmationScreenType.FULL_SCREEN
                }
            } else ConfirmationScreenType.FULL_SCREEN

    fun showAdsOnFullScreenConfirmation(): Boolean = getConfirmationScreenType() == ConfirmationScreenType.FULL_SCREEN_WITH_ROKT_ADS

    fun autoCloseRoktLoaderAfter(): Int =
            if (FeatureFlag.ROKT.isFeatureEnabled()) {
                ServiceLocator.abTestFramework.getFeatureVariableDouble(
                        FeatureFlag.ROKT.key,
                        ROKT_AUTO_CLOSE_LOADER_TIME_VARIABLE_KEY,
                        getAllUserAttributes(),
                )?.toInt() ?: 3
            } else 3

    private fun getRoktBooleanFeatureVariable(key: String, defaultValue: Boolean): Boolean =
            FeatureFlag.ROKT.isFeatureEnabled()
                    && ServiceLocator.abTestFramework.getFeatureVariableBoolean(
                    FeatureFlag.ROKT.key,
                    key,
                    getAllUserAttributes(),
            ) ?: defaultValue

    fun getBooleanFeatureVariable(
        featureFlag: FeatureFlag,
        variableKey: String,
        defaultValue: Boolean
    ): Boolean =
        featureFlag.isFeatureEnabled(getAllUserAttributes()) && ServiceLocator.abTestFramework.getFeatureVariableBoolean(
            featureFlag.key,
            variableKey,
            getAllUserAttributes()
        ) ?: defaultValue

    fun getIntFeatureVariable(
        featureFlag: FeatureFlag,
        variableKey: String,
        defaultValue: Int
    ): Int =
        if (featureFlag.isFeatureEnabled(getAllUserAttributes())) {
            ServiceLocator.abTestFramework.getFeatureVariableInt(
                featureFlag.key,
                variableKey,
                getAllUserAttributes()
            ) ?: defaultValue
        } else defaultValue

    fun getDoubleFeatureVariable(
        featureFlag: FeatureFlag,
        variableKey: String,
        defaultValue: Double
    ): Double =

        if (featureFlag.isFeatureEnabled(getAllUserAttributes())) {
            ServiceLocator.abTestFramework.getFeatureVariableDouble(
                featureFlag.key,
                variableKey,
                getAllUserAttributes()
            ) ?: defaultValue
        } else defaultValue

    @JvmStatic
    fun enableRoktLogging(): Boolean = getRoktBooleanFeatureVariable(ROKT_ENABLE_LOGGING_VARIABLE_KEY, false)

    @JvmStatic
    fun initializeRoktSDK(): Boolean = getRoktBooleanFeatureVariable(ROKT_INITIALIZE_SDK_VARIABLE_KEY, false)

    @JvmStatic
    fun showAdAfterAttributionSurvey(): Boolean = getRoktBooleanFeatureVariable(ROKT_SHOW_AD_AFTER_ATTRIBUTION_SURVEY_VARIABLE_KEY, false)

    fun trackTemporaryRoktEvents(): Boolean = getRoktBooleanFeatureVariable(ROKT_TRACK_TEMPORARY_EVENTS_VARIABLE_KEY, false)

    fun useRoktSandbox(): Boolean = if (BuildConfig.DEBUG) true
    else getRoktBooleanFeatureVariable(key = ROKT_USE_SANDBOX_VARIABLE_KEY, defaultValue = false)

    fun shouldLogFTCAuditForUSStudio(studioCountryCode: String?): Boolean {
        return studioCountryCode.equals("US", ignoreCase = true) &&
                FeatureFlag.FTC_AUDIT_LOG_SCREENSHOT_CAPTURE.isFeatureEnabled(getAllUserAttributes())
    }

    enum class DealsNearYouDestination {
        SEARCH,
        DEALS,
    }

    fun getDealsNearYouDestination(): DealsNearYouDestination {
        return if (FeatureFlag.DEALS_NEAR_YOU_REDIRECTION.isFeatureEnabled(getAllUserAttributes())) {
            when (ServiceLocator.abTestFramework.getFeatureVariableString(
                FeatureFlag.DEALS_NEAR_YOU_REDIRECTION.key,
                DEALS_NEAR_YOU_DESTINATION_VARIABLE_KEY,
                getAllUserAttributes()
            )) {
                DEALS_NEAR_YOU_DESTINATION_VARIABLE_SEARCH_VALUE -> DealsNearYouDestination.SEARCH
                DEALS_NEAR_YOU_DESTINATION_VARIABLE_DEALS_VALUE -> DealsNearYouDestination.DEALS
                else -> DealsNearYouDestination.SEARCH
            }
        } else {
            DealsNearYouDestination.SEARCH
        }
    }

    fun getBucketingExperimentVariableValue(): String {
        return ServiceLocator.abTestFramework.getFeatureVariableString(
            FeatureFlag.BUCKETING_TEST.key,
            BUCKET_NAME,
            getAllUserAttributes()
        ) ?: ""
    }
}
