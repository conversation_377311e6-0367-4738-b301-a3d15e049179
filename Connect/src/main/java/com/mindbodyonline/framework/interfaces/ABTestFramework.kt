package com.mindbodyonline.framework.interfaces

import android.content.Context
import com.optimizely.ab.optimizelyjson.OptimizelyJSON
import org.json.JSONObject

interface ABTestFramework {

    fun initialize(context: Context)

    fun isFeatureEnabled(featureKey: String): Boolean

    fun isFeatureEnabled(featureKey: String, attributes: MutableMap<String, Any>): Boolean

    fun activate(experimentKey: String): String?

    fun activate(experimentKey: String, attributes: Map<String, Any>): String?

    fun getEnabledFeatures(userId: Int, attributes: Map<String, String>?): List<String?>?

    fun getFeatureVariableString(featureKey: String, variableKey: String): String?

    fun getFeatureVariableString(featureKey: String, variableKey: String, attributes: Map<String, Any>): String?

    fun getFeatureVariableDouble(featureKey: String, variableKey: String): Double?

    fun getFeatureVariableInt(featureKey: String, variableKey: String, attributes: Map<String, Any>): Int?

    fun getFeatureVariableDouble(featureKey: String, variableKey: String, attributes: Map<String, Any>): Double?

    fun getFeatureVariableBoolean(featureKey: String, variableKey: String): Boolean?

    fun getFeatureVariableBoolean(featureKey: String, variableKey: String, attributes: Map<String, Any>): Boolean?

    fun getFeatureVariableJSON(featureKey: String, variableKey: String): OptimizelyJSON?

    fun getAllFeatureVariables(featureKey: String): String?

    fun getAllFeatureVariables(featureKey: String, attributes: Map<String, Any>): String?

    fun getVariation(featureTestKey: String): String?

    fun track(eventName: String)

    fun track(eventName: String, attributes: Map<String, Any>)

    fun isFrameworkUp(): Boolean

    fun getRunningExperimentsWithVariations(): JSONObject
}
