package com.mindbodyonline.framework.impl

import android.content.Context
import com.mindbodyonline.framework.abvariant.CEIL
import com.mindbodyonline.framework.abvariant.CONSUMER_SERVICE_FEE_ANDROID_V2
import com.mindbodyonline.framework.abvariant.FLOOR
import com.mindbodyonline.framework.abvariant.MODAL_BODY
import com.mindbodyonline.framework.abvariant.MODAL_HEADER
import com.mindbodyonline.framework.abvariant.PERCENT
import com.mindbodyonline.framework.abvariant.QUICKBOOK_LINE
import com.mindbodyonline.framework.interfaces.ABTestFramework
import com.optimizely.ab.optimizelyjson.OptimizelyJSON
import org.json.JSONObject

class MockABTestFramework: ABTestFramework {

    val MODAL_HEADER_COPY = "Service Fee"
    val QUICKBOOK_LINE_COPY = "SERVICE FEE"
    val MODAL_BODY_COPY = "This 2% fee (minimum of \$0.25) is charged by <PERSON>body to improve your app experience."
    val COUNTRY_ATTRIBUTE = "Country"
    val COUNTRY_VALUE = "UNITED STATES"
    val PERCENT_VALUE = 0.02
    val FLOOR_VALUE = 0.25
    val CEIL_VALUE = 2.00

    override fun initialize(context: Context) { }

    override fun isFeatureEnabled(featureKey: String): Boolean {
        return featureKey == CONSUMER_SERVICE_FEE_ANDROID_V2
    }

    override fun isFeatureEnabled(featureKey: String, attributes: MutableMap<String, Any>): Boolean {
        return featureKey == CONSUMER_SERVICE_FEE_ANDROID_V2 && attributes[COUNTRY_ATTRIBUTE] == COUNTRY_VALUE
    }

    override fun activate(experimentKey: String): String? {
        return null
    }

    override fun activate(experimentKey: String, attributes: Map<String, Any>): String? {
        return null
    }

    override fun getEnabledFeatures(userId: Int, attributes: Map<String, String>?): List<String?>? {
        return null
    }

    override fun getFeatureVariableString(featureKey: String, variableKey: String): String? {
        if (featureKey == CONSUMER_SERVICE_FEE_ANDROID_V2) {
            when (variableKey) {
                MODAL_HEADER-> return MODAL_HEADER_COPY
                MODAL_BODY -> return MODAL_BODY_COPY
                QUICKBOOK_LINE -> return QUICKBOOK_LINE_COPY
            }
        }
        return null
    }

    override fun getFeatureVariableString(featureKey: String, variableKey: String, attributes: Map<String, Any>): String? {
        if (featureKey == CONSUMER_SERVICE_FEE_ANDROID_V2 && attributes[COUNTRY_ATTRIBUTE] == COUNTRY_VALUE) {
            when (variableKey) {
                MODAL_HEADER -> return MODAL_HEADER_COPY
                MODAL_BODY -> return MODAL_BODY_COPY
                QUICKBOOK_LINE -> return QUICKBOOK_LINE_COPY
            }
        }
        return null
    }

    override fun getFeatureVariableDouble(featureKey: String, variableKey: String): Double? {
        if (featureKey == CONSUMER_SERVICE_FEE_ANDROID_V2) {
            when (variableKey) {
                PERCENT -> return PERCENT_VALUE
                FLOOR -> return FLOOR_VALUE
                CEIL -> return CEIL_VALUE
            }
        }
        return null
    }

    override fun getFeatureVariableDouble(featureKey: String, variableKey: String, attributes: Map<String, Any>): Double? {
        if (featureKey == CONSUMER_SERVICE_FEE_ANDROID_V2 && attributes[COUNTRY_ATTRIBUTE] == COUNTRY_VALUE) {
            when (variableKey) {
                PERCENT -> return PERCENT_VALUE
                FLOOR -> return FLOOR_VALUE
                CEIL -> return CEIL_VALUE
            }
        }
        return null
    }

    override fun getFeatureVariableInt(featureKey: String, variableKey: String, attributes: Map<String, Any>): Int? {
        return null
    }

    override fun getFeatureVariableBoolean(featureKey: String, variableKey: String): Boolean? = null

    override fun getFeatureVariableBoolean(featureKey: String, variableKey: String, attributes: Map<String, Any>): Boolean? = null

    override fun getFeatureVariableJSON(featureKey: String, variableKey: String): OptimizelyJSON? = null

    override fun getAllFeatureVariables(featureKey: String): String? = null

    override fun getAllFeatureVariables(featureKey: String, attributes: Map<String, Any>): String? = null

    override fun getVariation(featureTestKey: String): String? = null

    override fun track(eventName: String) { }

    override fun track(eventName: String, attributes: Map<String, Any>) { }

    override fun isFrameworkUp() = true

    override fun getRunningExperimentsWithVariations(): JSONObject = JSONObject()
}
