package com.mindbodyonline.framework.impl

import android.content.Context
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.BuildConfig
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.tealium.TealiumHelper
import com.mindbodyonline.connect.utils.Constants.OPTIMIZELY_DEVELOPMENT_KEY
import com.mindbodyonline.connect.utils.Constants.OPTIMIZELY_PRODUCTION_KEY
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.framework.abvariant.DevelopmentFlag
import com.mindbodyonline.framework.abvariant.FeatureFlagData
import com.mindbodyonline.framework.abvariant.ReleaseFlagManager
import com.mindbodyonline.framework.abvariant.USER_ID
import com.mindbodyonline.framework.abvariant.VERSION_NAME
import com.mindbodyonline.framework.interfaces.ABTestFramework
import com.optimizely.ab.android.sdk.OptimizelyClient
import com.optimizely.ab.android.sdk.OptimizelyManager
import com.optimizely.ab.notification.UpdateConfigNotification
import com.optimizely.ab.optimizelyjson.OptimizelyJSON
import org.json.JSONObject
import java.util.Collections.emptyMap
import java.util.UUID

class OptimizelyImpl: ABTestFramework {

    private var optimizelyClient: OptimizelyClient? = null

    /**
     * Initializes the Optimizely service.  Called from the ServiceLocator so that we can use
     * this service from anywhere in our code. Uses the development key if buildVariant is debug
     * and the production key if buildVariant is production. The first thing initialize does is
     * downloads our Optimizely JSON settings file which can be found here:
     * https://app.optimizely.com/v2/projects/17715140215/settings/implementation
     * This file is then parsed by Optimizely in order to determine which experiments and features
     * are enabled and what variants a user should be bucketed into. This file is downloaded when
     * the app is launched and also every 15 minutes in the event the app is open for a long time.
     */
    override fun initialize(context: Context) {
        val optimizelyManager = OptimizelyManager.builder()
            .withSDKKey((when (ConnectApp.debuggableMode()) {
                true -> OPTIMIZELY_DEVELOPMENT_KEY
                false -> OPTIMIZELY_PRODUCTION_KEY
            }))
            .build(context)
        optimizelyClient = optimizelyManager.initialize(context, R.raw.optimizely_settings, true, true)
        cacheAllFeatures()
        optimizelyManager.optimizely.notificationCenter?.addNotificationHandler(UpdateConfigNotification::class.java) {
            MBLog.d("Optimizely", "Datafile updated")
        }
    }

    /**
     * Activate sends an Impression event which increments the number of Visitors for an A/B Test
     * with [experimentKey] in the Results View if the user has not been previously activated.
     *
     * If this user has not been previously activated, then the user will be bucketed into a variant.
     * If the user has been previously activated, then the variant the user has previously been bucketed
     * into will be returned and the Visitors count will remain the same.
     *
     * Returns the variant the user is bucketed into or null if the experiment key cannot be found.
     */
    override fun activate(experimentKey: String): String? =
        optimizelyClient?.activate(experimentKey, getUserId())?.key?.also {
            logActivate(experimentKey, it)
        }

    /**
     * Identical to activate(experimentKey: String) but only runs if the user [attributes]
     * qualify for the experiment audience.
     *
     * If a user has an attribute that disqualifies them, no Impression event will be sent and
     * the number of Visitors in the Results View will remain unchanged and this method will return null.
     * However if a user that has been previously bucketed with a qualifying attributes now makes this
     * call with a disqualifying attribute, then the number of Visitors in the Results View will decrement
     * but this method will still return the variant they were previously bucketed into.
     *
     * Returns the variant the user is bucketed into if the user attributes qualify for the test,
     * or null if the user attributes disqualify the user or experiment key cannot be found.
     */
    override fun activate(experimentKey: String, attributes: Map<String, Any>): String? =
        optimizelyClient?.activate(experimentKey, getUserId(), attributes)?.key?.also {
            logActivate(experimentKey, it, attributes)
        }

    /**
     * Track an event (typically a button click) with [eventName].
     *
     * Track sends a Conversion event. This will always increment the total conversion count
     * in the Results View, and only increment the unique conversion count in the Results View
     * if the user has not previously sent this event.
     *
     */
    override fun track(eventName: String) {
        if (DevelopmentFlag.DEVELOPMENT_TRACK_OPTIMIZELY_EVENTS.isFeatureEnabled()) {
            optimizelyClient?.track(eventName, getUserId())
            logTrackEvent(eventName)
        }
    }

    /**
     * Identical to track(eventName: String) but only runs if the user [attributes] qualify.
     *
     * If a user does not qualify then the event will be ignored and the Conversion count in the
     * Results View will remain unchanged. If a user who has previously qualified and sent events
     * that are counted in the Results View Conversion count and then sends a track event with a new user
     * attribute that disqualifies them, all of the Conversion counts for that variant will be
     * subtracted from the total Conversion count.
     *
     */
    override fun track(eventName: String, attributes: Map<String, Any>) {
        if (DevelopmentFlag.DEVELOPMENT_TRACK_OPTIMIZELY_EVENTS.isFeatureEnabled()) {
            optimizelyClient?.track(eventName, getUserId(), attributes)
            logTrackEvent(eventName, attributes)
        }
    }

    override fun isFrameworkUp() = optimizelyClient?.isValid ?: false

    /**
     * Sends an Impression event which increments the number of Visitors for a Feature Test
     * in the Results View if the user has not been previously counted in the Feature Test.
     *
     * Checks if a feature with [featureKey] is enabled in Optimizely.  Returns true if this
     * feature exists and is turned on.  Buckets the user for a variant in the Feature test if they
     * have not been previously bucketed and sends an Impression event for the Feature test which
     * will increment the Visitor count in the Results View.
     *
     * Returns false if the feature is disabled.
     */
    override fun isFeatureEnabled(featureKey: String): Boolean {
        val attributes = mutableMapOf<String, Any>()
        attributes[USER_ID] = MBAuth.getUser()?.id ?: 0
        attributes[VERSION_NAME] = BuildConfig.VERSION_NAME
        return optimizelyClient?.isFeatureEnabled(featureKey, getUserId(), attributes)?.also { enabled ->
            logFeatureEnabled(featureKey, enabled, attributes)
        } ?: false
    }

    /**
     * Identical to isFeatureEnabled(featureKey: String) but only buckets the user if the [attributes]
     * qualify for the feature audience.  Similar to Activate, if a user was previously bucketed
     * in the Feature test but then makes this call with an invalid attribute, their results
     * will be subtracted from the Results View but this method will still return that they are
     * bucketed in this feature.
     *
     * Returns true if the feature is enabled, false if the feature is disabled or user not qualified.
     */
    override fun isFeatureEnabled(featureKey: String, attributes: MutableMap<String, Any>): Boolean {
        attributes[USER_ID] = MBAuth.getUser()?.id ?: 0
        attributes[VERSION_NAME] = BuildConfig.VERSION_NAME
        return optimizelyClient?.isFeatureEnabled(featureKey, getUserId(), attributes)?.also { enabled ->
            logFeatureEnabled(featureKey, enabled, attributes)
        } ?: false
    }

    /**
     * Gets all of the feature flags turned on in Optimizely.  Can be used to log the list of features
     * enabled in crash reports and breadcrumbs.  If a feature filters this user by the [attributes]
     * then that feature will not be excluded from the list.
     */
    override fun getEnabledFeatures(userId: Int, attributes: Map<String, String>?): List<String?>? {
        return optimizelyClient?.getEnabledFeatures(userId.toString(), attributes ?: emptyMap<String, String>())
            ?.onEach {
                logFeatureEnabled(it, true, attributes)
            }
    }

    /**
     * Gets a feature variable string.  Feature variable strings can be used if you want to display
     * a dynamic string or make some dynamic decision that can be set by a project manager
     * in Optimizely.  For example a copy change to be shown in a new dialog or card.
     * A feature variable is setup with a [featureKey] and can have multiple feature variable values
     * are unique by [variableKey].
     */
    override fun getFeatureVariableString(featureKey: String, variableKey: String): String? {
        return optimizelyClient?.getFeatureVariableString(featureKey, variableKey, getUserId())?.also {
            logFeatureVariableString(featureKey, variableKey, it)
        }
    }

    /**
     * Identical to getFeatureVariableString(featureKey: String, variableKey: String) but again
     * similar to the other methods only fetches a variable string if the user [attributes]
     * qualify for this feature.  And again if this is sent with newly disqualifying user [attributes]
     * the results for this user will be subtracted from the Results View.
     */
    override fun getFeatureVariableString(featureKey: String, variableKey: String, attributes: Map<String, Any>): String? {
        return optimizelyClient?.getFeatureVariableString(featureKey, variableKey, getUserId(), attributes)?.also {
            logFeatureVariableString(featureKey, variableKey, it, attributes)
        }
    }

    /**
     * Gets a feature variable double.  Feature variable doubles can be used if you want to display
     * a dynamic double value, or make some dynamic decision that can be set by a project manager
     * in Optimizely.  For example a dollar amount value that could trigger different
     * logic in a checkout flow.  A feature variable is setup with a [featureKey] and can have
     * multiple feature variable values that are unique by [variableKey].
     */
    override fun getFeatureVariableDouble(featureKey: String, variableKey: String): Double? {
        return optimizelyClient?.getFeatureVariableDouble(featureKey, variableKey, getUserId())?.also {
            logFeatureVariableDouble(featureKey, variableKey, it)
        }
    }

    override fun getFeatureVariableInt(featureKey: String, variableKey: String, attributes: Map<String, Any>): Int? {
        return optimizelyClient?.getFeatureVariableInteger(featureKey, variableKey, getUserId(), attributes)?.also {
            logFeatureVariableInt(featureKey, variableKey, it, attributes)
        }
    }

    /**
     * Identical to getFeatureVariableDouble(featureKey: String, variableKey: String) but again
     * similar to the other methods only fetches a variable string if the user [attributes]
     * qualify for this feature.  And again if this is sent with newly disqualifying user [attributes]
     * the results for this user will be subtracted from the Results View.
     */
    override fun getFeatureVariableDouble(featureKey: String, variableKey: String, attributes: Map<String, Any>): Double? {
        return optimizelyClient?.getFeatureVariableDouble(featureKey, variableKey, getUserId(), attributes)?.also {
            logFeatureVariableDouble(featureKey, variableKey, it, attributes)
        }
    }

    /**
     * Gets a feature variable boolean.  Feature variable booleans can be used if you want to toggle
     * visibility of a view, or make some other dynamic decision that can be set by a project manager
     * in Optimizely.  A feature variable is setup with a [featureKey] and can have
     * multiple feature variable values that are unique by [variableKey].
     */
    override fun getFeatureVariableBoolean(featureKey: String, variableKey: String): Boolean? {
        return optimizelyClient?.getFeatureVariableBoolean(featureKey, variableKey, getUserId())?.also {
            logFeatureVariableBoolean(featureKey, variableKey, it)
        }
    }

    /**
     * Identical to getFeatureVariableBoolean(featureKey: String, variableKey: String) but again
     * similar to the other methods only fetches a variable string if the user [attributes]
     * qualify for this feature.  And again if this is sent with newly disqualifying user [attributes]
     * the results for this user will be subtracted from the Results View.
     */
    override fun getFeatureVariableBoolean(featureKey: String, variableKey: String, attributes: Map<String, Any>): Boolean? {
        return optimizelyClient?.getFeatureVariableBoolean(featureKey, variableKey, getUserId(), attributes)?.also {
            logFeatureVariableBoolean(featureKey, variableKey, it)
        }
    }

    /**
     * Gets a feature variable JSON.  Feature variable JSON can be used if you want to toggle
     * visibility of a view, or make some other dynamic decision that can be set by a project manager
     * in Optimizely.  A feature variable is setup with a [featureKey] and can have
     * multiple feature variable values that are unique by [variableKey].
     */
    override fun getFeatureVariableJSON(featureKey: String, variableKey: String): OptimizelyJSON? {
        return optimizelyClient?.getFeatureVariableJSON(featureKey, variableKey, getUserId())?.also {
            logFeatureVariableJSON(featureKey, variableKey, it)
        }
    }

    /**
     * Gets all of the feature variables for a feature with [featureKey].  Returns a string with
     * the feature variable key/value pairs.
     */
    override fun getAllFeatureVariables(featureKey: String): String? {
        return optimizelyClient?.getAllFeatureVariables(featureKey, getUserId())?.toString()?.also {
            logAllFeatureVariables(featureKey, it)
        }
    }

    /**
     * Identical to getAllFeatureVariables(featureKey: String) but only buckets the user if the [attributes]
     * qualify for the feature audience.
     */
    override fun getAllFeatureVariables(featureKey: String, attributes: Map<String, Any>): String? {
        return optimizelyClient?.getAllFeatureVariables(featureKey, getUserId(), attributes)?.toString()?.also {
            logAllFeatureVariables(featureKey, it, attributes)
        }
    }

    /**
     * Gets the variation a user is bucketed into with [featureTestKey].  Returns a string with
     * the feature variation name.  This can be used to log which variation a user is bucketed
     * into for a Feature Test with an analytics tool like Pendo.  This will enable us to lookup
     * variation by User ID for a Feature Test.
     */
    override fun getVariation(featureTestKey: String): String? {
        return optimizelyClient?.getVariation(featureTestKey, getUserId())?.key?.also {
            logFeatureTestVariation(featureTestKey, it)
        }
    }

    /**
     * Returns a JSONObject of all running experiments with the variation the user is bucketed into.
     * IMP - Update runningExperiments every time an experiment is started or stopped.
     */
    override fun getRunningExperimentsWithVariations(): JSONObject {
        val runningExperiments: List<String> = listOf("bucketing_ab_test")
        val experimentVariationsList = JSONObject()
        runningExperiments.forEach {
            getVariation(it)?.let { variation ->
                experimentVariationsList.put(it, variation)
            }
        }
        return experimentVariationsList
    }

    /**
     * Gets the User ID to send to Optimizely.  The User ID is the unique identifier to Optimizely results.
     * A User is enrolled in experiments and bucketed into variants by User ID (and also by user
     * attributes if audiences are being used)
     */
    fun getUserId() = (MBAuth.getUser()?.id ?: TealiumHelper.deviceAdId ?: UUID.randomUUID()).toString()

    private fun logActivate(experimentKey: String, variantKey: String?, attributes: Map<String, Any>? = null) {
        MBLog.d("Optimizely", "optimizely.activate($experimentKey,$variantKey)")
        attributes?.let { MBLog.d("Optimizely", "User attributes $attributes") }
        variantKey?.let { variant ->
            MBLog.d("Optimizely", "User activated in experiment $experimentKey")
            MBLog.d("Optimizely", "User bucketed into variant $variant")
        } ?: run { MBLog.d("Optimizely", "Experiment cannot be found or user attributes disqualified") }
    }

    private fun logTrackEvent(eventName: String, attributes: Map<String, Any>? = null) {
        MBLog.d("Optimizely", "optimizely.trackEvent($eventName)")
        attributes?.let { MBLog.d("Optimizely", "User attributes $attributes") }
        MBLog.d("Optimizely", "Sent conversion event $eventName")
    }

    private fun logFeatureEnabled(featureKey: String, enabled: Boolean, attributes: Map<String, Any>? = null) {
        MBLog.d("Optimizely", "optimizely.isFeatureEnabled($featureKey)")
        attributes?.let { MBLog.d("Optimizely", "User attributes $attributes") }
        (if (enabled) "enabled" else "disabled or user attributes disqualified").let {
            MBLog.d("Optimizely", "Feature $featureKey $it")
        }
    }

    private fun logFeatureVariableString(featureKey: String, variableKey:
    String, value: String?, attributes: Map<String, Any>? = null) {
        MBLog.d("Optimizely", "optimizely.getFeatureVariableString($featureKey, $variableKey)")
        attributes?.let { MBLog.d("Optimizely", "User attributes $attributes") }
        value?.let {
            MBLog.d("Optimizely", "Feature $featureKey variable $variableKey value is $value")
        } ?: run { MBLog.d("Optimizely", "Feature variable cannot be found or user attributes disqualified") }
    }

    private fun logFeatureVariableDouble(featureKey: String, variableKey:
    String, value: Double?, attributes: Map<String, Any>? = null) {
        MBLog.d("Optimizely", "optimizely.getFeatureVariableDouble($featureKey, $variableKey)")
        attributes?.let { MBLog.d("Optimizely", "User attributes $attributes") }
        value?.let {
            MBLog.d("Optimizely", "Feature $featureKey variable $variableKey value is $value")
        } ?: run { MBLog.d("Optimizely", "Feature variable cannot be found or user attributes disqualified") }
    }

    private fun logFeatureVariableInt(featureKey: String, variableKey:
    String, value: Int?, attributes: Map<String, Any>? = null) {
        MBLog.d("Optimizely", "optimizely.getFeatureVariableInt($featureKey, $variableKey)")
        attributes?.let { MBLog.d("Optimizely", "User attributes $attributes") }
        value?.let {
            MBLog.d("Optimizely", "Feature $featureKey variable $variableKey value is $value")
        } ?: run { MBLog.d("Optimizely", "Feature variable cannot be found or user attributes disqualified") }
    }

    private fun logFeatureVariableBoolean(featureKey: String, variableKey:
    String, value: Boolean?, attributes: Map<String, Any>? = null) {
        MBLog.d("Optimizely", "optimizely.getFeatureVariableBoolean($featureKey, $variableKey)")
        attributes?.let { MBLog.d("Optimizely", "User attributes $attributes") }
        value?.let {
            MBLog.d("Optimizely", "Feature $featureKey variable $variableKey value is $value")
        } ?: run { MBLog.d("Optimizely", "Feature variable cannot be found or user attributes disqualified") }
    }

    private fun logFeatureVariableJSON(featureKey: String, variableKey:
    String, value: OptimizelyJSON?, attributes: Map<String, Any>? = null) {
        MBLog.d("Optimizely", "optimizely.getFeatureVariableJSON($featureKey, $variableKey)")
        attributes?.let { MBLog.d("Optimizely", "User attributes $attributes") }
        value?.let {
            MBLog.d("Optimizely", "Feature $featureKey variable $variableKey value is $value")
        } ?: run { MBLog.d("Optimizely", "Feature variable cannot be found or user attributes disqualified") }
    }

    private fun logAllFeatureVariables(featureKey: String, value: String?, attributes: Map<String, Any>? = null) {
        MBLog.d("Optimizely", "optimizely.getAlLFeatureVariables($featureKey)")
        attributes?.let { MBLog.d("Optimizely", "User attributes $attributes") }
        value?.let {
            MBLog.d("Optimizely", "Feature variables for $featureKey for this user are $value")
        } ?: run { MBLog.d("Optimizely", "Feature variables cannot be found or user attributes disqualified") }
    }

    private fun logFeatureTestVariation(featureTestKey: String, value: String?) {
        MBLog.d("Optimizely", "optimizely.getVariation($featureTestKey)")
        value?.let {
            MBLog.d("Optimizely", "Feature variation for $featureTestKey for this user is $value")
        } ?: run { MBLog.d("Optimizely", "Feature variation cannot be found or user attributes disqualified") }
    }

    private fun cacheAllFeatures() {
        optimizelyClient?.optimizelyConfig?.featuresMap?.let { featuresMap ->
            ReleaseFlagManager.featuresMap = featuresMap.mapValues {
                FeatureFlagData(it.value.key)
            } as MutableMap<String, FeatureFlagData>?
        }
    }
}