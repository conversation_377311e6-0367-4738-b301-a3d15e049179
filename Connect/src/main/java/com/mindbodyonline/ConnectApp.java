package com.mindbodyonline;

import static com.mindbodyonline.connect.remoteconfig.RemoteConfigConstantsKt.MINIMUM_FETCH_INTERVAL_SECONDS;
import static com.mindbodyonline.connect.remoteconfig.RemoteConfigConstantsKt.ROKT_TAG_ID_KEY;

import android.app.ActivityManager;
import android.app.Application;
import android.app.NotificationManager;
import android.content.ComponentCallbacks2;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.res.Configuration;
import android.preference.PreferenceManager;
import android.util.Log;

import androidx.multidex.MultiDex;

import com.braze.BrazeActivityLifecycleCallbackListener;
import com.braze.support.BrazeLogger;
import com.google.android.libraries.places.api.Places;
import com.google.firebase.FirebaseApp;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings;
import com.mindbodyonline.android.api.clients.MbClientsAPIManager;
import com.mindbodyonline.android.api.sales.MBSalesApi;
import com.mindbodyonline.android.api.sales.model.pos.cart.CartState;
import com.mindbodyonline.android.util.SafeGson;
import com.mindbodyonline.android.util.log.MBLog;
import com.mindbodyonline.connect.BuildConfig;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.activities.TourActivity;
import com.mindbodyonline.connect.remoteconfig.RemoteConfigUtils;
import com.mindbodyonline.connect.tealium.LoginTrackingUtils;
import com.mindbodyonline.connect.tealium.TealiumHelper;
import com.mindbodyonline.connect.utils.AlarmUtils;
import com.mindbodyonline.connect.utils.AnalyticsUtils;
import com.mindbodyonline.connect.utils.BreadcrumbsUtils;
import com.mindbodyonline.connect.utils.FileUtils;
import com.mindbodyonline.connect.utils.IntentUtils;
import com.mindbodyonline.connect.utils.MBVolleyUtils;
import com.mindbodyonline.connect.utils.SharedPreferencesUtils;
import com.mindbodyonline.connect.utils.StringValue;
import com.mindbodyonline.connect.utils.StringValueDeserializer;
import com.mindbodyonline.connect.utils.Switches;
import com.mindbodyonline.connect.utils.TimeUtils;
import com.mindbodyonline.connect.utils.analytics.AnalyticsLocator;
import com.mindbodyonline.connect.utils.api.ApiCallUtils;
import com.mindbodyonline.connect.utils.api.fitnessactivity.fitbit.FitBitAPI;
import com.mindbodyonline.connect.utils.api.fitnessactivity.googlefit.GoogleFitAPI;
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI;
import com.mindbodyonline.connect.utils.api.gateway.model.CancellabilityStatusCode;
import com.mindbodyonline.connect.utils.api.gateway.model.CoursesJsonResource;
import com.mindbodyonline.connect.utils.api.gateway.model.FlexPassJsonResource;
import com.mindbodyonline.connect.utils.api.gateway.model.JsonResource;
import com.mindbodyonline.connect.utils.api.gateway.model.UserBookingStatusCode;
import com.mindbodyonline.connect.utils.api.gateway.serialization.CancellabilityStatusCodeTypeAdapter;
import com.mindbodyonline.connect.utils.api.gateway.serialization.CoursesGatewayResourceDeserializer;
import com.mindbodyonline.connect.utils.api.gateway.serialization.FlexPassGatewayResourceDeserializer;
import com.mindbodyonline.connect.utils.api.gateway.serialization.GatewayResourceDeserializer;
import com.mindbodyonline.connect.utils.api.gateway.serialization.UserBookingStatusCodeTypeAdapter;
import com.mindbodyonline.connect.utils.api.identity.IdentityAPI;
import com.mindbodyonline.connect.utils.api.strava.StravaAPI;
import com.mindbodyonline.connect.widgets.v3.WidgetUtils;
import com.mindbodyonline.data.StaticInstance;
import com.mindbodyonline.data.services.MBAuth;
import com.mindbodyonline.data.services.MBStaticCache;
import com.mindbodyonline.data.services.MbCacheService;
import com.mindbodyonline.data.services.SalesAccessParams;
import com.mindbodyonline.data.services.http.MbDataService;
import com.mindbodyonline.data.services.locator.ServiceLocator;
import com.mindbodyonline.domain.ProductIdToApptTypeIdsResponse;
import com.mindbodyonline.domain.User;
import com.mindbodyonline.domain.dataModels.SubscriptionLevel;
import com.mindbodyonline.domain.deserializer.BigDecimalDeserializer;
import com.mindbodyonline.domain.deserializer.CartStateDeserializer;
import com.mindbodyonline.domain.deserializer.GsonDateDeserializer;
import com.mindbodyonline.domain.deserializer.ProdIdToIntArrayDeserializer;
import com.mindbodyonline.domain.deserializer.SubscriptionLevelDeserializer;
import com.mindbodyonline.framework.abvariant.ABHelperUtils;
import com.mindbodyonline.framework.helpcenter.MbHelpCenter;
import com.mindbodyonline.framework.helpcenter.MbIoHelpCenter;
import com.mindbodyonline.pickaspot.api.PickASpotApi;
import com.mindbodyonline.pickaspot.ui.di.LibraryDependencies;
import com.newrelic.agent.android.NewRelic;
import com.rokt.roktsdk.Rokt;
import com.squareup.picasso.OkHttp3Downloader;
import com.squareup.picasso.Picasso;

import java.io.File;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;

import io.branch.referral.Branch;
import io.branch.referral.BranchApp;
import okhttp3.OkHttpClient;


public class ConnectApp extends BranchApp {

    /**
     * Memory class profiles for various devices:
     * <p/>
     * Galaxy S3 -      96
     * Droid Razr -     42
     * Nexus 5 -        142
     * Nexus 7 -        192
     * HTC One -        192
     * Kyocera Hydro -  36
     */

    //We're seeing issues with S3 and other lower-end devices.  Hopefully this alleviates that.
    public static final int LOW_MEMORY_THRESHOLD = 100;
    public static final String TAG = "MBConnect";
    private static final int UNACCEPTABLE_NUM_SECONDS_BETWEEN_BOOT_LOOPS = 60;
    private static final int RATE_LIMIT_PERCENTAGE = 5;
    public static int MEMORY_CLASS = 0;
    private static boolean debuggableMode = false;
    private static ConnectApp instance;
    public static boolean applicationRestarting = false;

    public static synchronized ConnectApp getInstance() {
        return instance;
    }

    // uncaught exception handler variable
    private Thread.UncaughtExceptionHandler defaultUEH;

    // handler listener
    private Thread.UncaughtExceptionHandler _unCaughtExceptionHandler =
        new Thread.UncaughtExceptionHandler() {
            @Override
            public void uncaughtException(Thread thread, Throwable ex) {
                MBLog.e(TAG, "Uncaught exception retrieved in global handler", ex);

                // Don't autorestart app if we are debugging
                if (!debuggableMode) {
                    restartApplication();
                }

                // re-throw critical exception further to the os (important)
                defaultUEH.uncaughtException(thread, ex);
            }
        };

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        applicationRestarting = false;

        /* By setting this once here using the application context we can avoid memory leaks.*/
        debuggableMode = (0 != (getApplicationInfo().flags &= ApplicationInfo.FLAG_DEBUGGABLE));

        // It is important that this is instantiated before all else.  Various analytics require us
        // having a user object, but if this is not called, our `getUser()` will return null, which
        // on the analytics side looks like the user is logged out.
        MBAuth.init(this);

        // There are sometimes scenarios where our application was broken in a way that requires
        // code to immediately be run at the first available moment.
        fixAnyOutstandingApiAndSdkIssues();

        initializeAnalyticsAndExtras();

        ActivityManager manager = (ActivityManager) getSystemService(ACTIVITY_SERVICE);
        MEMORY_CLASS = manager.getMemoryClass();

        // Initialize the analytics api session
        if (MBAuth.getUser() != null) {
            AnalyticsLocator.getAnalyticsTracker().trackSession(String.valueOf(MBAuth.getUser().getId()));
        }

        if (Switches.USER_ACTION_LINKS_ENABLED) {
            if (Switches.FORCE_PRODUCTION || !debuggableMode) {
                Branch.getInstance().setRequestMetadata("$mixpanel_distinct_id", AnalyticsLocator.getMixpanelDistinctUserId());
            } else {
                Branch.getAutoInstance(this).setRequestMetadata("$mixpanel_distinct_id", AnalyticsLocator.getMixpanelDistinctUserId());
            }
        }

        //Init dexus dependencies
        initializeDexusDependency();

        // Upgrade preferences, if needed. Must come after Dexus initialization for user access
        SharedPreferencesUtils.startupPreferenceCheck();

        //Help Center dependencies
        MbHelpCenter.setFramework(MbIoHelpCenter.INSTANCE);

        //Debug-only initializations to be done in ConnectDebugApp
        AnalyticsUtils.runConnectEventLogger();

        if (MBAuth.getUser() != null) {
            AnalyticsUtils.setBaseUserInformation(MBAuth.getUser(), false);
        }

        //All remaining analytics dependencies
        LoginTrackingUtils.setAppBoyPushNotification();

        // setup handler for uncaught exception
        defaultUEH = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(_unCaughtExceptionHandler);

        // setup memory callbacks
        registerComponentCallbacks(new MemoryCallbacks());

        // Create notification channels if they don't exist
        AlarmUtils.ensureNotificationChannels(this);

        initializeImageLibrary();

        // Initialize places sdk
        Places.initialize(this, BuildConfig.PLACES_API_KEY);

        // Initialize Firebase
        if (FirebaseApp.getApps(this).isEmpty()) {
            FirebaseApp.initializeApp(this);
        }

        // Initialize Firebase remote config once for the entire app
        initFirebaseRemoteConfig();
        initRokt();
    }

    private void initRokt() {
        if (ABHelperUtils.initializeRoktSDK()) {
            Rokt.INSTANCE.init(
                    RemoteConfigUtils.getStringParameterValue(ROKT_TAG_ID_KEY),
                    BuildConfig.VERSION_NAME,
                    this,
                    Collections.emptySet(),
                    Collections.emptyMap()
            );
            if (ABHelperUtils.enableRoktLogging()) {
                Rokt.INSTANCE.setLoggingEnabled(true);
            }
        }
    }

    private void initFirebaseRemoteConfig() {
        FirebaseRemoteConfig mFirebaseRemoteConfig = FirebaseRemoteConfig.getInstance();
        FirebaseRemoteConfigSettings configSettings = new FirebaseRemoteConfigSettings.Builder()
                .setMinimumFetchIntervalInSeconds(MINIMUM_FETCH_INTERVAL_SECONDS)
                .build();
        mFirebaseRemoteConfig.setConfigSettingsAsync(configSettings);
        mFirebaseRemoteConfig.setDefaultsAsync(R.xml.remote_config_defaults);

        // Fetch config immediately
        mFirebaseRemoteConfig.fetchAndActivate()
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        MBLog.d(TAG, "Firebase remote config parameters updated = " + task.getResult());
                    } else {
                        Exception exception = task.getException();
                        String errorMessage = exception != null ? exception.getMessage() : "Unknown error";
                        String logMessage = "Firebase remote config fetch failed - " + errorMessage;
                        MBLog.e(TAG, logMessage);
                        NewRelic.recordBreadcrumb(logMessage);
                    }
                });
    }

    private void fixAnyOutstandingApiAndSdkIssues() {
        // Breaking google maps SDK release requires us to clear a cache that is acquired
        // originally from a backend service.
        // More information: https://issuetracker.google.com/issues/154855417#comment388
        try {
            SharedPreferences googleBug = getSharedPreferences("google_bug_154855417", Context.MODE_PRIVATE);
            if (!googleBug.contains("fixed")) {
                File corruptedZoomTables = new File(getFilesDir(), "ZoomTables.data");
                //noinspection ResultOfMethodCallIgnored
                corruptedZoomTables.delete();
                googleBug.edit().putBoolean("fixed", true).apply();
            }
        } catch (Exception e) {
            // log or ignore so nothing breaks.
        }
    }

    private void initializeAnalyticsAndExtras() {
        ServiceLocator.getAbTestFramework().initialize(this);

        initializeTealium(this);
        initializeBraze();

        User currentUser = MBAuth.getUser();
        if (currentUser != null) {
            BreadcrumbsUtils.INSTANCE.breadcrumbIsGuestuser(false);
        }
    }

    private void initializeImageLibrary() {
        // Picasso needs to be enabled for TLS v1.2
        OkHttpClient.Builder okb = new OkHttpClient.Builder().sslSocketFactory(
            MBVolleyUtils.getMboSslSocketFactory(),
            Objects.requireNonNull(MBVolleyUtils.provideX509TrustManager()));

        Picasso.setSingletonInstance(
            new Picasso.Builder(this)
                .downloader(new OkHttp3Downloader(okb.build()))
                .build()
        );
    }

    private void initializeTealium(Application application) {
        TealiumHelper.INSTANCE.init(application);
    }

    private void initializeBraze() {
        BrazeLogger.setLogLevel(debuggableMode ? Log.VERBOSE : Log.ERROR);
        registerActivityLifecycleCallbacks(new BrazeActivityLifecycleCallbackListener());
    }

    /**
     * Many of our alternate service APIs use our same access token, which means we set our data service
     * as a token delegate in order to provide them that user token.  Unfortunately, our service APIs
     * do not all use the same method of anonymous access.  Some (like the REST and ConnV1 APIs) use
     * a guest token, while others use client key headers.  This method will initialize the various
     * internal service packages and SDKs for anonymous client access.
     */
    public void refreshAnonymousClientAccess() {
        // The Sales API does not support guest tokens, it instead requires an API-client header.
        // This means that we need to deregister our MBDataService as a token refresh delegate when
        // the user is going to be accessing the APIs anonymously.
        MBSalesApi.setAccessToken(MBAuth.isGuestUser() ? null : MBAuth.getUserToken());
        MBSalesApi.setTokenRefreshDelegate(MBAuth.isGuestUser() ? null : MbDataService.getServiceInstance());
        MbClientsAPIManager.INSTANCE.setAccessToken(MBAuth.isGuestUser() ? null : MBAuth.getUserToken());
        MbClientsAPIManager.INSTANCE.setTokenRefreshDelegate(MBAuth.isGuestUser() ? null : MbDataService.getServiceInstance());
        ServiceLocator.getConnV3Api().setToken(MBAuth.isGuestUser() ? null : MBAuth.getUserToken());
    }

    private void initializeDexusDependency() {
        //TODO consolidate these into a single Dexus init call
        MBLog.init(this);
        MBSalesApi.init(this, MbDataService.getServiceInstance().getRequestQueue(), new SalesAccessParams());
        MbClientsAPIManager.INSTANCE.init(this, MbDataService.getServiceInstance().getRequestQueue(), new SalesAccessParams());
        SwamisAPI.getInstance().setToken(MBAuth.getUserToken());
        IdentityAPI.getInstance().setToken(MBAuth.getUserToken());

        refreshAnonymousClientAccess();
        setDexusPickASpotDependencies();

        /**
         * Initialize our Gson deserializer so that it can produce
         * the correct data types we use.
         */

        SafeGson.getSingleton()
            .registerTypeAdapter(Date.class, new GsonDateDeserializer())
            .registerTypeAdapter(BigDecimal.class, new BigDecimalDeserializer())
            .registerTypeAdapter(CartState.class, new CartStateDeserializer())
            .registerTypeAdapter(SubscriptionLevel.class, new SubscriptionLevelDeserializer())
            .registerTypeAdapter(ProductIdToApptTypeIdsResponse.class, new ProdIdToIntArrayDeserializer())
            .registerTypeAdapter(JsonResource.class, new GatewayResourceDeserializer())
            .registerTypeAdapter(CoursesJsonResource.class, new CoursesGatewayResourceDeserializer())
            .registerTypeAdapter(FlexPassJsonResource.class, new FlexPassGatewayResourceDeserializer())
            .registerTypeAdapter(CancellabilityStatusCode.class, new CancellabilityStatusCodeTypeAdapter())
            .registerTypeAdapter(UserBookingStatusCode.class, new UserBookingStatusCodeTypeAdapter())
            .registerTypeAdapter(StringValue.class, new StringValueDeserializer())
            .build();

        ApiCallUtils.addChangeEndpointListener(() -> {
            MbCacheService.get().clearCountries();
        });

        MBAuth.setTokenUpdatedListener(token -> {
            ServiceLocator.getUserRepository().clearPASCache();
            SwamisAPI.getInstance().setToken(token);
            IdentityAPI.getInstance().setToken(token);
            refreshAnonymousClientAccess();
            setDexusPickASpotDependencies();
        });

        MBAuth.setLogoutListener(result -> {
            MBSalesApi.setAccessToken(null);
            MbClientsAPIManager.INSTANCE.setAccessToken(null);
            SwamisAPI.getInstance().setToken(null);
            IdentityAPI.getInstance().setToken(null);
            ServiceLocator.getConnV3Api().setToken(null);
            ServiceLocator.getWalletRepository().clearCache();
            ServiceLocator.getRecommendedRepository().clearCache();
            ServiceLocator.getOrderRepository().clearCache();
            //We need the user id to clear some of these, so we do it first
            SharedPreferencesUtils.clearTransientPreferences();

            // Disable push notifications for users that are logged out
            AnalyticsUtils.setBrazePushEnabled(false);

            MbDataService.getServiceInstance().clearRequests();

            SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());
            SharedPreferences.Editor prefEditor = prefs.edit();
            prefEditor.apply();

            FitBitAPI.deleteToken();
            GoogleFitAPI.getInstance().disconnectFromGoogleFit();
            StravaAPI.getInstance().deleteToken();
            AnalyticsUtils.logoutAll();

            MBStaticCache.getInstance().clearCache();
            StaticInstance.clear();

            MbHelpCenter.getInstance().clearUser();

            try {
                MbCacheService.get().clearAfterLogout();
            } catch (SQLException e) {
                MBLog.e(TAG, "Clearing cache", e);
            }

            WidgetUtils.refreshWidget(this);

            ((NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE)).cancelAll();

            // Clear the user's session in analytics api
            AnalyticsLocator.getAnalyticsTracker().trackSession(null);
        });
    }

    private void setDexusPickASpotDependencies() {
        LibraryDependencies.INSTANCE.setOkHttpClient(ServiceLocator.getPickASpotOkHttpClient());
        LibraryDependencies.INSTANCE.setApiEnvironment(PickASpotApi.Environment.PROD);
        LibraryDependencies.INSTANCE.setupLogging();
    }

    public static boolean debuggableMode() {
        return debuggableMode;
    }

    public void restartApplication() {
        BreadcrumbsUtils.logWithBreadcrumbNewRelicEventForcedLogoutWithMetadata(
                Collections.emptyMap(), "application_restart", 0);
        MBLog.d(TAG, "Restarting application");
        applicationRestarting = true;

        //Check if the application is in debuggable mode
        debuggableMode = (0 != (getApplicationInfo().flags &= ApplicationInfo.FLAG_DEBUGGABLE));

        // Get the last time there was a boot loop.
        long now = new Date().getTime();
        long loopyTime = SharedPreferencesUtils.getLastBootLoopTime();

        if (loopyTime > 0 && TimeUtils.secondsBetween(now, loopyTime) < UNACCEPTABLE_NUM_SECONDS_BETWEEN_BOOT_LOOPS) {
            //Unacceptable situation.  Clear the app cache before the next restart.
            MBAuth.logout();
            BreadcrumbsUtils.logWithBreadcrumbNewRelicEventForcedLogoutWithMetadata(
                    Collections.emptyMap(), "restart_for_boot_loops", 0);

            AnalyticsUtils.logEvent("(Settings) | User signed out",
                "Reason", "Application restart (boot loops)");
            FileUtils.deleteCache(getApplicationContext());
        } else {
            SharedPreferencesUtils.setLastBootLoopTime(now);
        }

        Intent intent = new Intent(getApplicationContext(), TourActivity.class);
//        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);

        IntentUtils.restartApplication(getApplicationContext());
    }

    //Support for multidex, necessary for kitkat
    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    class MemoryCallbacks implements ComponentCallbacks2 {
        @Override
        public void onTrimMemory(int level) {
            switch (level) {
                case TRIM_MEMORY_COMPLETE:
                case TRIM_MEMORY_RUNNING_CRITICAL:
                case TRIM_MEMORY_MODERATE:
                    MbDataService.getServiceInstance().clearCache();
                case TRIM_MEMORY_RUNNING_LOW:
                case TRIM_MEMORY_RUNNING_MODERATE:
                case TRIM_MEMORY_BACKGROUND:
                case TRIM_MEMORY_UI_HIDDEN:
                default:
                    break;
            }
        }

        @Override
        public void onConfigurationChanged(Configuration newConfig) {
        }

        @Override
        public void onLowMemory() {
            onTrimMemory(TRIM_MEMORY_COMPLETE);
        }
    }
}
