package com.mindbodyonline.connect.widgets.v3

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.datastore.preferences.core.MutablePreferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.glance.GlanceId
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.state.updateAppWidgetState
import androidx.glance.currentState

object WidgetStateManager {
    const val PREF_KEY_TAB_SELECTED = "tab_selected"
    const val PREF_KEY_IS_LOADING = "is_loading"
    const val PREF_KEY_IS_LOGGED_IN = "is_logged_in"
    const val PREF_KEY_SHOULD_REFRESH = "should_refresh"
    private val keyIsLoading = booleanPreferencesKey(PREF_KEY_IS_LOADING)
    private val keyIsLoggedIn = booleanPreferencesKey(PREF_KEY_IS_LOGGED_IN)
    private val keyTabSelected = stringPreferencesKey(PREF_KEY_TAB_SELECTED)
    private val keyShouldRefresh = booleanPreferencesKey(PREF_KEY_SHOULD_REFRESH)

    @Composable
    fun getSelectedTab(): Tab? = currentState(keyTabSelected)?.let { getTabForName(it) }

    suspend fun setSelectedTab(
        context: Context,
        glanceId: GlanceId,
        tab: Tab,
    ) {
        updateAppWidgetState(context, glanceId) {
            it[keyTabSelected] = tab.name
        }
    }

    @Composable
    fun isLoading(): Boolean = currentState(keyIsLoading) == true

    suspend fun setLoading(
        context: Context,
        glanceId: GlanceId,
        isLoading: Boolean,
    ) {
        updateAppWidgetState(context, glanceId) {
            it[keyIsLoading] = isLoading
        }
    }

    suspend fun setLoggedIn(
        context: Context,
        isLoggedIn: Boolean,
    ) {
        updatePreference(context) { preferences ->
            preferences[keyIsLoggedIn] = isLoggedIn
        }
    }

    private suspend fun updatePreference(
        context: Context,
        updatePreferences: (MutablePreferences) -> Unit,
    ) {
        val manager = GlanceAppWidgetManager(context)
        val widget = MindbodyWidget()
        val glanceIds = manager.getGlanceIds(widget.javaClass)
        glanceIds.forEach { glanceId ->
            updateAppWidgetState(context, glanceId) {
                updatePreferences(it)
            }
            MindbodyWidget().update(context, glanceId)
        }
    }

    suspend fun setRefresh(
        context: Context,
        shouldRefresh: Boolean,
    ) {
        updatePreference(context) { preferences ->
            preferences[keyShouldRefresh] = shouldRefresh
        }
    }

    @Composable
    fun shouldRefresh(): Boolean = currentState(keyShouldRefresh) == true

    @Composable
    fun isLoggedIn(): Boolean = currentState(keyIsLoggedIn) == true
}
