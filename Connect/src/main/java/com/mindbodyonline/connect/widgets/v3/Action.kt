package com.mindbodyonline.connect.widgets.v3

import android.content.Context
import android.content.Intent
import androidx.glance.GlanceId
import androidx.glance.action.ActionParameters
import androidx.glance.appwidget.action.ActionCallback
import com.google.android.material.snackbar.Snackbar
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.activities.MainActivity
import com.mindbodyonline.connect.activities.TourActivity
import com.mindbodyonline.connect.activities.details.AppointmentDetailsActivity
import com.mindbodyonline.connect.activities.details.BusinessDetailsActivity
import com.mindbodyonline.connect.activities.details.ClassTypeDetailsActivity
import com.mindbodyonline.connect.activities.info.ProxySnackbarActivity
import com.mindbodyonline.connect.analytics.OriginComponent
import com.mindbodyonline.connect.utils.Constants
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_ORIGIN_COMPONENT
import com.mindbodyonline.connect.utils.DomainObjectUtils
import com.mindbodyonline.connect.utils.ToastUtils
import com.mindbodyonline.connect.utils.api.toLocationReference
import com.mindbodyonline.data.StaticInstance
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.domain.BaseVisit
import com.mindbodyonline.domain.ClassTypeVisit
import com.mindbodyonline.domain.Location
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ScheduleTabAction : ActionCallback {
    override suspend fun onAction(
        context: Context,
        glanceId: GlanceId,
        parameters: ActionParameters,
    ) {
        WidgetStateManager.setSelectedTab(context, glanceId, Tab.SCHEDULE_TAB)
        MindbodyWidget().update(context, glanceId)
    }
}

class FavoritesTabAction : ActionCallback {
    override suspend fun onAction(
        context: Context,
        glanceId: GlanceId,
        parameters: ActionParameters,
    ) {
        WidgetStateManager.setSelectedTab(context, glanceId, Tab.FAVORITE_TAB)
        MindbodyWidget().update(context, glanceId)
    }
}

class ReloadAction : ActionCallback {
    override suspend fun onAction(
        context: Context,
        glanceId: GlanceId,
        parameters: ActionParameters,
    ) {
        withContext(Dispatchers.IO) {
            loadData(context, glanceId)
        }
    }
}

const val VISIT_TYPE_APPOINTMENT = "type_appointment"
const val VISIT_TYPE_CLASS = "type_class"

internal fun getVisitClickIntent(
    context: Context,
    visit: BaseVisit,
    visitType: String,
): Intent {
    if (visitType == VISIT_TYPE_APPOINTMENT) {
        val appointmentDetails = Intent(context, AppointmentDetailsActivity::class.java)
        appointmentDetails.putExtra(Constants.KEY_BUNDLE_VISITID, visit.BookingRefJson)
        appointmentDetails.putExtra(KEY_BUNDLE_ORIGIN_COMPONENT, OriginComponent.WIDGET)
        appointmentDetails.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        appointmentDetails.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        appointmentDetails.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        return appointmentDetails
    }
    StaticInstance.selectedClassTypeObject =
        DomainObjectUtils.convertVisitToClassTypeObject(visit as ClassTypeVisit)
    val classTypeDetails = Intent(context, ClassTypeDetailsActivity::class.java)
    classTypeDetails.putExtra(Constants.KEY_BUNDLE_CLASS_INSTANCE_ID, visit.siteVisitId)
    classTypeDetails.putExtra(KEY_BUNDLE_ORIGIN_COMPONENT, OriginComponent.WIDGET)
    classTypeDetails.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    classTypeDetails.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
    classTypeDetails.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
    return classTypeDetails
}

internal fun getFavoritesCLickIntent(
    context: Context,
    optedIn: Boolean,
    business: Location,
): Intent? {
    val locationReference = business.toLocationReference()
    return if (optedIn) {
        Intent(context, BusinessDetailsActivity::class.java).apply {
            putExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE, locationReference)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            putExtra(Constants.FROM_WIDGET, true)
        }
    } else {
        ProxySnackbarActivity
            .make(
                context,
                context.getString(R.string.business_no_longer_listed_message),
                Snackbar.LENGTH_SHORT,
            ).setAction(context.getString(R.string.remove_caps)) { _ ->
                ToastUtils.show(context.getString(R.string.removing_business_toast_message))
                removeLocation(context, business)
            }.show()
        null
    }
}

private fun removeLocation(
    context: Context,
    result: Location?,
) {
    if (result != null) {
        // Updated to fire and forget
        ServiceLocator.getFavoriteLocationRepository().removeFavorite(result)
        ToastUtils.show(context.getString(R.string.remove_from_favorites_success))
    }
}

internal fun getApplicationIntent(context: Context): Intent =
    Intent(
        context,
        if ((StaticInstance.MEMORY_DESTROYED_IF_NULL != null) && MBAuth.isLoggedIn()) {
            MainActivity::class.java
        } else {
            TourActivity::class.java
        },
    ).also {
        it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    }
