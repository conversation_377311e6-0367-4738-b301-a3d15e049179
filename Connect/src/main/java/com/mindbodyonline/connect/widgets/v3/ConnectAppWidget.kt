package com.mindbodyonline.connect.widgets.v3

import android.content.Context
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.GlanceAppWidgetReceiver
import com.mindbodyonline.connect.analytics.widget.trackWidgetEnabled
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.data.services.MBAuth.setTokenUpdatedListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

class ConnectAppWidget : GlanceAppWidgetReceiver() {
    private val coroutineScope = MainScope()

    override val glanceAppWidget: GlanceAppWidget
        get() = MindbodyWidget()

    override fun onEnabled(context: Context?) {
        if (context == null) {
            return
        }
        trackWidgetEnabled()
        coroutineScope.launch {
            WidgetStateManager.setLoggedIn(context, MBAuth.isLoggedIn())
        }
        setTokenUpdatedListener { _ ->
            coroutineScope.launch(Dispatchers.IO) {
                WidgetStateManager.setLoggedIn(context, MBAuth.isLoggedIn())
            }
        }
    }
}
