package com.mindbodyonline.connect.widgets.v3

import android.content.Context
import android.graphics.Bitmap
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceComposable
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.LocalContext
import androidx.glance.action.clickable
import androidx.glance.appwidget.CircularProgressIndicator
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.action.actionRunCallback
import androidx.glance.appwidget.action.actionStartActivity
import androidx.glance.appwidget.lazy.LazyColumn
import androidx.glance.appwidget.lazy.items
import androidx.glance.appwidget.provideContent
import androidx.glance.background
import androidx.glance.layout.Alignment
import androidx.glance.layout.Box
import androidx.glance.layout.Column
import androidx.glance.layout.ColumnScope
import androidx.glance.layout.ContentScale
import androidx.glance.layout.Row
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxHeight
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.fillMaxWidth
import androidx.glance.layout.height
import androidx.glance.layout.padding
import androidx.glance.layout.size
import androidx.glance.layout.width
import androidx.glance.layout.wrapContentHeight
import androidx.glance.text.FontFamily
import androidx.glance.text.FontWeight
import androidx.glance.text.Text
import androidx.glance.text.TextAlign
import androidx.glance.text.TextStyle
import androidx.glance.unit.ColorProvider
import com.mindbodyonline.android.util.time.FastDateFormat
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.utils.TimeUtils
import com.mindbodyonline.data.StaticInstance
import com.mindbodyonline.domain.AppointmentTypeVisit
import com.mindbodyonline.domain.BaseVisit
import com.mindbodyonline.domain.ClassTypeVisit
import com.mindbodyonline.domain.Location
import com.squareup.picasso.Picasso
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Calendar
import java.util.Locale

class MindbodyWidget : GlanceAppWidget() {
    override suspend fun provideGlance(
        context: Context,
        id: GlanceId,
    ) {
        provideContent {
            // create your AppWidget here
            GlanceTheme {
                val scope = rememberCoroutineScope()
                val context = LocalContext.current
                val isLoggedIn = WidgetStateManager.isLoggedIn()
                val selectedTab = WidgetStateManager.getSelectedTab()
                val shouldRefresh = WidgetStateManager.shouldRefresh()
                LaunchedEffect(Unit) {
                    if (isLoggedIn) {
                        if (selectedTab == null) {
                            WidgetStateManager.setSelectedTab(context, id, Tab.SCHEDULE_TAB)
                        }
                        WidgetStateManager.setRefresh(context, true)
                    }
                }
                if (shouldRefresh) {
                    SideEffect {
                        scope.launch(Dispatchers.IO) {
                            loadData(context, id)
                            WidgetStateManager.setRefresh(context, false)
                        }
                    }
                }
                WidgetLayout()
            }
        }
    }
}

@GlanceComposable
@Composable
fun WidgetLayout() {
    val isLoggedIn = WidgetStateManager.isLoggedIn()
    Column(modifier = GlanceModifier.fillMaxSize().background(R.color.white)) {
        if (isLoggedIn) {
            LoggedInView()
        } else {
            // Logged Out View
            LoggedOutView()
        }
    }
}

@Composable
@GlanceComposable
fun LoggedOutView(modifier: GlanceModifier = GlanceModifier) {
    Row(
        modifier =
            GlanceModifier
                .fillMaxWidth()
                .height(40.dp)
                .background(R.color.widget_title_bar_color)
                .padding(end = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        MindbodyLogo()
    }
    Column(
        modifier =
            modifier
                .fillMaxSize()
                .background(color = Color.White),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = LocalContext.current.getString(R.string.logged_out_action),
            style =
                TextStyle(
                    fontSize = 18.sp,
                    textAlign = TextAlign.Center,
                    fontFamily = font,
                ),
            modifier =
                GlanceModifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
        )

        Text(
            text = LocalContext.current.getString(R.string.logged_out_message),
            style =
                TextStyle(
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    fontFamily = font,
                ),
            modifier =
                GlanceModifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
        )
    }
}

@Composable
@GlanceComposable
private fun ColumnScope.LoggedInView() {
    val selectedTab = WidgetStateManager.getSelectedTab() // Retrieve the current tab state

    // Title Bar
    HeaderWidget(selectedTab)

    // Content Section
    if (!WidgetStateManager.isLoading()) {
        when (selectedTab) {
            null, Tab.SCHEDULE_TAB -> ScheduleContent()
            Tab.FAVORITE_TAB -> FavoriteContent()
        }
    } else {
        // Loading Layout
        WidgetLoader()
    }
}

@Composable
private fun WidgetLoader() {
    Column(
        modifier =
            GlanceModifier
                .fillMaxSize()
                .padding(15.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        CircularProgressIndicator(color = ColorProvider(R.color.light_gray_10lpha))
        Text(
            text = LocalContext.current.getString(R.string.loading_text),
            modifier = GlanceModifier.padding(top = 5.dp),
        )
    }
}

@Composable
private fun HeaderWidget(selectedTab: Tab?) {
    Row(
        modifier =
            GlanceModifier
                .fillMaxWidth()
                .height(40.dp)
                .background(R.color.widget_title_bar_color)
                .padding(end = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        MindbodyLogo()
        Spacer(modifier = GlanceModifier.defaultWeight())
        Column(verticalAlignment = Alignment.Bottom) {
            Image(
                provider =
                    ImageProvider(
                        if (selectedTab == Tab.SCHEDULE_TAB) R.drawable.widget_schedule_tapped else R.drawable.widget_schedule_untapped,
                    ),
                contentDescription = "Schedule",
                modifier =
                    GlanceModifier
                        .size(38.dp)
                        .padding(vertical = 7.dp, horizontal = 8.dp)
                        .clickable(actionRunCallback<ScheduleTabAction>()),
            )
            TabSelectionUnderline(condition = { selectedTab == Tab.SCHEDULE_TAB })
        }
        Column(verticalAlignment = Alignment.Bottom) {
            Image(
                provider =
                    ImageProvider(
                        if (selectedTab == Tab.FAVORITE_TAB) R.drawable.widget_heart_selected else R.drawable.widget_heart_unselected,
                    ),
                contentDescription = "Favorites",
                modifier =
                    GlanceModifier
                        .size(38.dp)
                        .padding(vertical = 7.dp, horizontal = 8.dp)
                        .clickable(actionRunCallback<FavoritesTabAction>()),
            )
            TabSelectionUnderline(condition = { selectedTab == Tab.FAVORITE_TAB })
        }
        Image(
            provider = ImageProvider(R.drawable.widget_refresh),
            contentDescription = "Reload",
            modifier =
                GlanceModifier
                    .size(38.dp)
                    .padding(horizontal = 8.dp, vertical = 6.dp)
                    .clickable(actionRunCallback<ReloadAction>()),
        )
    }
}

@Composable
private fun TabSelectionUnderline(condition: () -> Boolean) {
    Box(
        modifier =
            GlanceModifier
                .width(38.dp)
                .height(1.dp)
                .background(if (condition()) R.color.white else R.color.transparent),
    ) { }
}

@Composable
private fun MindbodyLogo() {
    val context = LocalContext.current
    Image(
        provider = ImageProvider(R.drawable.mindbody_wordmark_white),
        contentDescription = "Mindbody Logo",
        modifier =
            GlanceModifier
                .size(width = 128.dp, height = 40.dp)
                .padding(vertical = 10.dp)
                .clickable(actionStartActivity(getApplicationIntent(context))),
    )
}

@GlanceComposable
@Composable
fun HeaderRow(date: Calendar) {
    val day =
        if (isTodaysDate(date)
        ) {
            LocalContext.current.getString(R.string.today_text)
        } else {
            TimeUtils.DAY_FORMAT.format(date)
        }

    val formattedDate = "$day, ${TimeUtils.FULL_MONTH_DATE_YEAR_FORMAT.format(date)}"
    Row(
        modifier =
            GlanceModifier
                .fillMaxWidth()
                .height(32.dp)
                .background(R.color.background)
                .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = formattedDate,
            style =
                TextStyle(
                    fontSize = 14.sp,
                    color = ColorProvider(R.color.text_grey),
                    fontFamily = font,
                    textAlign = TextAlign.Center,
                ),
            modifier = GlanceModifier.wrapContentHeight(),
        )
    }
}

@GlanceComposable
@Composable
fun ScheduleItem(
    visit: BaseVisit,
    isFirstItemForDate: Boolean,
) {
    val bookingStatus =
        if (visit is ClassTypeVisit) {
            LocalContext.current.getString(if (visit.isWaitlisted) R.string.waitlisted_text else R.string.booked_text)
        } else if (visit is AppointmentTypeVisit) {
            LocalContext.current.getString(if (visit.isRequested) R.string.requested else R.string.booked_text)
        } else {
            ""
        }
    Column(modifier = GlanceModifier) {
        val context = LocalContext.current
        Row(
            modifier =
                GlanceModifier
                    .fillMaxWidth()
                    .height(78.dp)
                    .clickable(
                        actionStartActivity(
                            getVisitClickIntent(context, visit, visit.type),
                        ),
                    ).padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Time Layout
            Column(
                modifier =
                    GlanceModifier
                        .fillMaxHeight(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                if (isFirstItemForDate && isTodaysDate(visit.dateAsCal)) {
                    Text(
                        text = LocalContext.current.getString(R.string.next_up).uppercase(),
                        style =
                            TextStyle(
                                fontSize = 10.sp,
                                fontFamily = font,
                                color = ColorProvider(R.color.text_grey),
                            ),
                    )
                    Spacer(modifier = GlanceModifier.height(1.dp))
                }
                Text(
                    text =
                        timeFormat
                            .format(visit.getStartCal()),
                    style =
                        TextStyle(
                            fontSize = 14.sp,
                            fontFamily = font,
                        ),
                )
            }

            Spacer(modifier = GlanceModifier.width(32.dp))

            // Details Layout
            Column(
                modifier =
                    GlanceModifier
                        .defaultWeight()
                        .padding(end = 16.dp),
            ) {
                Text(
                    text = visit.visitName,
                    style =
                        TextStyle(
                            fontSize = 16.sp,
                            fontFamily = font,
                            fontWeight = FontWeight.Bold,
                        ),
                    maxLines = 1,
                )
                Text(
                    text =
                        LocalContext.current.getString(
                            R.string.location_text,
                            visit.locationName,
                        ),
                    style =
                        TextStyle(
                            fontSize = 12.sp,
                            fontFamily = font,
                            color = ColorProvider(R.color.text_grey),
                        ),
                    maxLines = 1,
                )
                Text(
                    text =
                        LocalContext.current.getString(
                            R.string.with_staff_phrase,
                            visit.determineStaffName(),
                        ),
                    style =
                        TextStyle(
                            fontSize = 12.sp,
                            fontFamily = font,
                            color = ColorProvider(R.color.text_grey),
                        ),
                    maxLines = 1,
                )
            }

            // Booking Status
            Text(
                text = bookingStatus.uppercase(),
                style =
                    TextStyle(
                        fontSize = 12.sp,
                        fontFamily = font,
                        color = ColorProvider(R.color.basil),
                    ),
                modifier =
                GlanceModifier,
            )
        }

        // Bottom Divider
        Box(
            modifier = GlanceModifier.fillMaxWidth().height(1.dp).background(Color.LightGray),
        ) {}
    }
}

private fun isTodaysDate(date: Calendar): Boolean {
    val today = Calendar.getInstance()
    return today.get(Calendar.DAY_OF_YEAR) == date.get(Calendar.DAY_OF_YEAR) &&
        today.get(Calendar.YEAR) == date.get(Calendar.YEAR)
}

@GlanceComposable
@Composable
fun ScheduleContent() {
    val visits = StaticInstance.widgetVisits.sorted()
    var lastDate: Calendar? = null
    var isFirstItemForDate = true
    if (visits.isNotEmpty()) {
        LazyColumn(modifier = GlanceModifier.fillMaxSize()) {
            visits.forEach { visit ->
                val currentDate = visit.getStartCal()
                if (lastDate == null ||
                    currentDate.get(Calendar.DAY_OF_YEAR) !=
                    lastDate!!.get(
                        Calendar.DAY_OF_YEAR,
                    )
                ) {
                    item {
                        HeaderRow(currentDate)
                    }
                    isFirstItemForDate = true
                }
                item {
                    ScheduleItem(visit, isFirstItemForDate)
                    isFirstItemForDate = false
                }
                lastDate = currentDate
            }
        }
    } else {
        NoDataView()
    }
}

@GlanceComposable
@Composable
fun NoDataView() {
    Text(
        text = LocalContext.current.getString(R.string.widget_no_schedule_items),
        modifier =
            GlanceModifier
                .fillMaxSize()
                .background(Color.White)
                .padding(20.dp),
        style =
            TextStyle(
                fontSize = 16.sp,
                fontFamily = font,
                textAlign = TextAlign.Center,
            ),
    )
}

@Composable
fun BusinessRow(location: Location) {
    val context = LocalContext.current
    Box(modifier = GlanceModifier.wrapContentHeight()) {
        Column(modifier = GlanceModifier.wrapContentHeight()) {
            Row(
                modifier =
                    GlanceModifier
                        .fillMaxWidth()
                        .padding(16.dp)
                        .let {
                            val favoriteClickIntent =
                                getFavoritesCLickIntent(
                                    context,
                                    location.isOptedIntoConnect,
                                    location,
                                )
                            if (favoriteClickIntent != null) {
                                it.clickable(actionStartActivity(favoriteClickIntent))
                            } else {
                                it.clickable(actionRunCallback<ReloadAction>())
                            }
                        },
            ) {
                val imageUrl = location.studioImageUrl
                var loadedBitmap by remember(imageUrl) { mutableStateOf<Bitmap?>(null) }
                loadedBitmap?.let {
                    Image(
                        provider = ImageProvider(it),
                        contentDescription = null,
                        contentScale = ContentScale.FillBounds,
                        modifier =
                            GlanceModifier
                                .size(50.dp),
                    )
                } ?: if (imageUrl != null) {
                    LaunchedEffect(imageUrl) {
                        withContext(Dispatchers.IO) {
                            try {
                                loadedBitmap = Picasso.get().load(imageUrl).get()
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }
                    }
                } else {
                    DefaultFavoriteStudioLogo()
                }

                Spacer(modifier = GlanceModifier.width(16.dp))

                // Business Details
                Column(modifier = GlanceModifier.defaultWeight()) {
                    Text(
                        text = location.studioName ?: "Unknown Studio",
                        style =
                            TextStyle(
                                fontSize = 16.sp,
                                color = ColorProvider(Color.Black),
                                fontFamily = font,
                                fontWeight = FontWeight.Bold,
                            ),
                    )
                    val fullAddress = location.getFullAddress(true)
                    if (!fullAddress.isNullOrEmpty()) {
                        Text(
                            text = fullAddress,
                            style =
                                TextStyle(
                                    fontSize = 14.sp,
                                    color = ColorProvider(R.color.text_grey),
                                    fontFamily = font,
                                ),
                        )
                    }
                }
            }
            Box(
                modifier =
                    GlanceModifier
                        .fillMaxWidth()
                        .height(1.dp)
                        .background(Color.LightGray),
            ) { }
        }
        // Overlay for opted-out businesses
        if (!location.isOptedIntoConnect) {
            Box(
                modifier =
                    GlanceModifier
                        .fillMaxSize()
                        .background(Color(0x80FFFFFF)), // Semi-transparent white overlay
            ) {}
        }
    }
}

@Composable
private fun DefaultFavoriteStudioLogo() {
    Image(
        ImageProvider(R.drawable.no_biz_logo),
        contentDescription = "Business Logo",
        modifier =
            GlanceModifier
                .size(50.dp),
    )
}

@Composable
fun FavoriteContent() {
    val locations = StaticInstance.widgetLocations
    if (locations.isNullOrEmpty()) {
        NoDataView()
    } else {
        LazyColumn(
            modifier = GlanceModifier.fillMaxSize(),
        ) {
            items(locations) { location ->
                BusinessRow(location)
            }
        }
    }
}

private val BaseVisit.type
    get() =
        when (this) {
            is ClassTypeVisit -> VISIT_TYPE_CLASS
            is AppointmentTypeVisit -> VISIT_TYPE_APPOINTMENT
            else -> throw IllegalArgumentException("Unknown visit type")
        }

private val font = FontFamily.SansSerif
private val timeFormat
    get() = FastDateFormat.getInstance("h:mm a", Locale.getDefault())
