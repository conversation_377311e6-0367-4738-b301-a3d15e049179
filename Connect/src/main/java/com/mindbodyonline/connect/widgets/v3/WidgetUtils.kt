@file:Jvm<PERSON><PERSON>("WidgetUtils")

package com.mindbodyonline.connect.widgets.v3

import android.app.Application
import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

fun LifecycleOwner.refreshWidget(context: Context) {
    lifecycleScope.launch(Dispatchers.IO) {
        WidgetStateManager.setRefresh(context, true)
    }
}

fun ViewModel.refreshWidget(context: Context) {
    viewModelScope.launch(Dispatchers.IO) {
        WidgetStateManager.setRefresh(context, true)
    }
}

fun Application.refreshWidget() {
    ProcessLifecycleOwner.get().refreshWidget(this)
}
