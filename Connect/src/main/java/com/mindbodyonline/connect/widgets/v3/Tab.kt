package com.mindbodyonline.connect.widgets.v3

import com.mindbodyonline.connect.widgets.v3.Tab.FAVORITE_TAB
import com.mindbodyonline.connect.widgets.v3.Tab.SCHEDULE_TAB

enum class Tab {
    SCHEDULE_TAB,
    FAVORITE_TAB,
}

fun getTabForName(name: String): Tab {
    return when (name) {
        SCHEDULE_TAB.name -> SCHEDULE_TAB
        FAVORITE_TAB.name -> FAVORITE_TAB
        else -> throw IllegalArgumentException("No Tab with name $name exists")
    }
}
