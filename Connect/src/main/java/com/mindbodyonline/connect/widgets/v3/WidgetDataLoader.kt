package com.mindbodyonline.connect.widgets.v3

import android.content.Context
import androidx.glance.GlanceId
import androidx.glance.appwidget.updateAll
import com.mindbodyonline.connect.analytics.widget.trackWidgetReloaded
import com.mindbodyonline.data.StaticInstance

suspend fun loadData(
    context: Context,
    glanceId: GlanceId,
) {
    trackWidgetReloaded()
    WidgetStateManager.setLoading(context, glanceId, true)
    MindbodyWidget().updateAll(context)

    // Simulate API call

    fetchDataFromApi()
    // Disable loading and refresh widget
    WidgetStateManager.setLoading(context, glanceId, false)
    MindbodyWidget().update(context, glanceId)
}

private suspend fun fetchDataFromApi() {
    // Fetch data for schedule and favorites
    try {
        val scheduleData = ApiClient.fetchScheduleData()
        val favoriteData = ApiClient.fetchFavoriteData()

        // Update StaticInstance with new data
        StaticInstance.widgetVisits = scheduleData
        StaticInstance.widgetLocations = favoriteData?.toTypedArray()
    } catch (
        e: Exception,
    ) {
        // Handle error
        e.printStackTrace()
        // Optionally, show a message to the user or log the error
    }
}
