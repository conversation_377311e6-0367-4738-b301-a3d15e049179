package com.mindbodyonline.connect.widgets.v3

import com.mindbodyonline.connect.utils.DomainObjectUtils
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI.Companion.INSTANCE
import com.mindbodyonline.connect.utils.api.gateway.util.getAttributes
import com.mindbodyonline.connect.utils.api.toDomainModel
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.domain.BaseVisit
import com.mindbodyonline.domain.Location
import com.mindbodyonline.domain.connv1.Visit
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resumeWithException

object ApiClient {
    const val NUM_VISITS_TO_GET = 20

    suspend fun fetchScheduleData(): List<BaseVisit>? {
        // Simulate REST API call for schedule data
        return ServiceLocator.userRepository
            .getUpcomingBookings(
                true,
                NUM_VISITS_TO_GET,
            )?.map { visit: Visit ->
                DomainObjectUtils.convertToBaseVisit(visit)
            }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun fetchFavoriteData(): List<Location>? {
        // Simulate REST API call for favorite data    return suspendCancellableCoroutine { continuation ->
        return suspendCancellableCoroutine { continuation ->
            INSTANCE.getFavoriteLocations(
                successListener = { response ->
                    val locations =
                        response.getAttributes()?.mapNotNull {
                            it?.toDomainModel()
                        }
                    // Resume the coroutine with the list of locations
                    continuation.resume(locations, null)
                },
                errorListener = { error ->
                    // Resume the coroutine with an exception if an error occurs
                    continuation.resumeWithException(error)
                },
            )
        }
    }
}
