package com.mindbodyonline.connect.profile.schedule.cancellation

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.connect.common.repository.LocationDataSource
import com.mindbodyonline.connect.common.repository.SwamiApiDataSource
import com.mindbodyonline.connect.common.utilities.removeBookingFromCache
import com.mindbodyonline.connect.utils.MBPhoneUtils
import com.mindbodyonline.connect.utils.api.gateway.model.CancellabilityStatus
import com.mindbodyonline.connect.utils.api.gateway.model.CancellabilityStatusCode
import com.mindbodyonline.connect.utils.api.toLocationReference
import com.mindbodyonline.connect.widgets.v3.refreshWidget
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.domain.VisitCancelStatus
import com.mindbodyonline.domain.connv1.Visit
import com.mindbodyonline.framework.abvariant.ABHelperUtils
import com.mindbodyonline.framework.abvariant.FeatureFlag
import kotlinx.coroutines.launch

class VisitCancellationViewModel(val swamiApiDataSource: SwamiApiDataSource,
                                 val locationDataSource: LocationDataSource) : ViewModel() {

    private var visit: Visit? = null

    private val _cancelStatus = MutableLiveData<VisitCancelStatus>()
    val cancelStatus: LiveData<VisitCancelStatus> = _cancelStatus

    private val _phoneNumber = MutableLiveData<String>()
    val phoneNumber: LiveData<String?> = _phoneNumber

    private val _visitCancelResult = MutableLiveData<Boolean>()
    val visitCancelResult: LiveData<Boolean> = _visitCancelResult

    fun queryVisitCancelStatus(visit: Visit) {
        this.visit = visit

        visit.BookingRefJson?.let { bookingRefJson ->
            swamiApiDataSource.getCancellabilityStatus(
                    bookingRefJson,
                    successListener = {
                        if (it.data?.attributes?.status?.code == CancellabilityStatusCode.DP_CANCELLABLE) {
                            _cancelStatus.postValue(VisitCancelStatus.CANCELLABLE_LATE)
                        } else {
                            _cancelStatus.postValue(it.data?.attributes?.status.toVisitCancelStatus())
                        }
                    },
                    errorListener = {
                        _cancelStatus.postValue(VisitCancelStatus.NON_CANCELLABLE)
                    })
        } ?: _cancelStatus.postValue(VisitCancelStatus.NON_CANCELLABLE)
    }

    fun queryPhoneNumber() = viewModelScope.launch {
        _phoneNumber.postValue(
                visit?.toLocationReference()?.let {
                    locationDataSource.getLocation(it)?.let { location ->
                        MBPhoneUtils.getInternationalPhoneNumber(location,
                                ServiceLocator.deviceRepository.getNetworkCountryCode())
                    }
                }
        )
    }

    fun cancelVisit() {
        visit?.BookingRefJson?.let { bookingRefJson ->
            swamiApiDataSource.deleteBooking(
                    bookingRefJson = bookingRefJson,
                    successListener = {
                        visit?.removeBookingFromCache()
                        _visitCancelResult.postValue(true)
                        ConnectApp.getInstance()?.let {
                            refreshWidget(it)
                        }
                    },
                    errorListener = {
                        _visitCancelResult.postValue(false)
                    }
            )
        } ?: _visitCancelResult.postValue(false)
    }

    private fun CancellabilityStatus?.toVisitCancelStatus(): VisitCancelStatus =
            if (this?.code == CancellabilityStatusCode.UNPAID_LATE_CANCELLABLE
                    && ABHelperUtils.isCountrySupportedByRWCC(
                            countryCode = visit?.AppointmentDetails?.LocationCountryCode,
                            countryName = visit?.AppointmentDetails?.LocationCountryName
                    )
            ) {
                VisitCancelStatus.UNPAID_CANCELLABLE_LATE
            } else {
                when (this?.code) {
                    CancellabilityStatusCode.CANCELLABLE -> VisitCancelStatus.CANCELLABLE
                    CancellabilityStatusCode.LATE_CANCELLABLE -> VisitCancelStatus.CANCELLABLE_LATE
                    else -> VisitCancelStatus.NON_CANCELLABLE
                }
            }

}
