package com.mindbodyonline.connect.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import androidx.fragment.app.FragmentManager
import com.google.android.play.core.review.ReviewManagerFactory
import com.mindbodyonline.android.util.TaskCallback
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.analytics.CANCEL_BUTTON
import com.mindbodyonline.connect.analytics.LIKE_IT_BUTTON
import com.mindbodyonline.connect.analytics.NEEDS_IMPROVEMENT_BUTTON
import com.mindbodyonline.connect.analytics.NOT_NOW_BUTTON
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.analytics.rokt.AppFeedbackDialogEventTracker
import com.mindbodyonline.connect.utils.DialogUtils.REQUEST_DIALOG_SHOW_TIME_IN_MS
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference
import com.mindbodyonline.data.services.locator.ServiceLocator.locationRepository
import com.mindbodyonline.domain.BaseVisit
import com.mindbodyonline.domain.BookabilityStatus
import com.mindbodyonline.domain.ClassStatusMessage
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.framework.abvariant.ABHelperUtils
import com.mindbodyonline.views.dialog.CTAButtonData
import com.mindbodyonline.views.dialog.ConfirmationHUD
import com.mindbodyonline.views.dialog.GenericInformationDialog
import com.mindbodyonline.views.dialog.GenericVerticalCtaDialog
import com.mindbodyonline.views.dialog.MaterialOptionDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.util.Calendar


fun showContractConfirmation(fragmentManager: FragmentManager, onComplete :(() -> Unit)? = null) {
    val dialog = ConfirmationHUD().apply {
        setMode(ConfirmationHUD.CONTRACT_MODE)
        setCancelCallback {
            onComplete?.invoke()
        }
        setOnDismissListener {
            onComplete?.invoke()
        }
    }
    dialog.show(fragmentManager, ConfirmationHUD::class.java.simpleName)
    dialog.startTimer()
}

fun ConfirmationHUD.startTimer() {
    if (!Switches.AUTODISMISS_CONFIRMATION_DIALOG) return
    Handler(Looper.getMainLooper()).postDelayed({
        this.takeUnless { isPerformingLongRunningTask }
            ?.takeUnless { activity?.supportFragmentManager?.isDestroyed == true }?.also {
                dismissAllowingStateLoss()
            }
    }, REQUEST_DIALOG_SHOW_TIME_IN_MS.toLong())
}

fun createApplicationFeedbackDialog(context: Activity, locationReference: LocationReference?, dialogInstanceCallback: ((GenericVerticalCtaDialog) -> Unit)?) {
    CoroutineScope(Dispatchers.IO).launch {
        try {
            val locationDetails = locationReference?.let { locationRepository.fetchLocationDetails(locationReference) }

            AppFeedbackDialogEventTracker.trackAppFeedbackViewedEvent(
                    originView = OriginView.POST_TRANSACTION_CONFIRMATION,
                    isRoktAdEnabled = locationDetails?.showAds
            )
            val dialog = GenericVerticalCtaDialog.newInstance(
                    message = context.getString(R.string.message_rate_dialog),
                    ctaButtons = listOf(
                            CTAButtonData(
                                    text = context.getString(R.string.rate_love_it),
                                    action = {
                                        AppFeedbackDialogEventTracker.trackAppFeedbackInteractedEvent(
                                                key = LIKE_IT_BUTTON,
                                                originView = OriginView.APP_RATING_POPUP,
                                                isRoktAdEnabled = locationDetails?.showAds
                                        )
                                        //Adding 20 years to calendar to avoid showing the dialog again and also to avoid adding another flag
                                        SharedPreferencesUtils.setLastAskedAppFeedback(
                                                Calendar.getInstance().apply { add(Calendar.YEAR, 20) })
                                        launchInAppPlaystoreReview(context)
                                    }
                            ),
                            CTAButtonData(
                                    text = context.getString(R.string.rate_hate_it),
                                    action = {
                                        AppFeedbackDialogEventTracker.trackAppFeedbackInteractedEvent(
                                                key = NEEDS_IMPROVEMENT_BUTTON,
                                                originView = OriginView.APP_RATING_POPUP,
                                                isRoktAdEnabled = locationDetails?.showAds
                                        )
                                        SharedPreferencesUtils.setLastAskedAppFeedback(Calendar.getInstance())
                                        IntentUtils.feedbackIntent(context)
                                    }
                            ),
                            CTAButtonData(
                                    text = context.getString(R.string.rate_ask_later),
                                    action = {
                                        AppFeedbackDialogEventTracker.trackAppFeedbackInteractedEvent(
                                                key = NOT_NOW_BUTTON,
                                                originView = OriginView.APP_RATING_POPUP,
                                                isRoktAdEnabled = locationDetails?.showAds
                                        )
                                        SharedPreferencesUtils.setLastAskedAppFeedback(Calendar.getInstance())
                                    }
                            )
                    )
            ) {
                AppFeedbackDialogEventTracker.trackAppFeedbackInteractedEvent(
                        key = CANCEL_BUTTON,
                        originView = OriginView.APP_RATING_POPUP,
                        isRoktAdEnabled = locationDetails?.showAds
                )
            }

            dialogInstanceCallback?.let { (it(dialog)) }
        } finally {
            cancel()
        }
    }
}

fun launchInAppPlaystoreReview(context: Activity) {
    val manager = ReviewManagerFactory.create(context)
    val request = manager.requestReviewFlow()
    request.addOnCompleteListener { request ->
        if (request.isSuccessful) {
            val reviewInfo = request.result
            val flow = manager.launchReviewFlow(context, reviewInfo)
            flow.addOnCompleteListener { _ ->
                //Adding 20 years to calendar to avoid showing the dialog again and also to avoid adding another flag
                SharedPreferencesUtils.setLastAskedAppFeedback(Calendar.getInstance().apply { add(Calendar.YEAR, 20) })
            }
        }
    }
}

fun launchBookabilityBlockedInformation(fragmentManager: FragmentManager, cto: ClassTypeObject?, context: Context?) {
    cto?.status?.id?.takeIf { cto.bookabilityIsBlocked()}?.takeIf { context != null }?.let { bookabilityStatusCode ->
        val classStatusMessage = if (cto?.status?.id == BookabilityStatus.BOOKED) {
            R.string.event_booked_header_text
        } else {
            ClassStatusMessage.getClassStatusMessageFromBookabilityStatus(bookabilityStatusCode)
        }
        val messageText = context?.getString(classStatusMessage)?.let {
            when {
                it.contains("%s") || it.contains("%1\$s") -> String.format(it, cto.location?.name)
                else -> it
        } } ?: ""
        val dialog = GenericInformationDialog.newInstance(
                message = messageText
        )
        dialog.show(fragmentManager, GenericInformationDialog::class.java.simpleName)
    }
}

fun FragmentManager?.showNotificationPermissionRequiredDialog(listener: RequestPushNotificationPermissionDialogClickListener? = null) {
    this?.let {
        MaterialOptionDialog().apply {
            setText(R.string.notification_permission_denied_title, R.string.gcm_consent_message, R.string.goto_connection_settings_button_text, R.string.cancel)
            setButton1Callback {
                listener?.onPositiveButtonClicked()
                val intent = Intent()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS)
                    intent.putExtra(Settings.EXTRA_APP_PACKAGE, context!!.packageName)
                } else
                    intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS")
                intent.putExtra("app_package", context!!.packageName)
                intent.putExtra("app_uid", context!!.applicationInfo.uid)
                context!!.startActivity(intent)
                it?.dismiss()
            }
            setButton2Callback {
                listener?.onNegativeButtonClicked()
                it?.dismiss()
            }
            setHorizontalButtonStyle(true)
            isCancelable = false
            show(it, "LocationPermissionRequiredDialog")
        }
    }
}

fun promptQuickSignInDialog(context: Context, manager: FragmentManager, visit: BaseVisit, onSignInCallback: TaskCallback<Void>) {
    val dialog = MaterialOptionDialog()
    dialog.setText(
        R.string.signin_query_w_classname,
        R.string.signin_dialog_message,
        R.string.sign_in_notification_action,
        R.string.no_thanks_cancel_action
    )
    dialog.setHeaderText(context.getString(R.string.signin_query_w_classname, visit.serviceName))
    dialog.setHorizontalButtonStyle(true)
    dialog.setButton1Callback {
        onSignInCallback.onTaskComplete(null)
        dialog.dismiss()
    }
    dialog.show(manager, "SignInActionDialog")

}
