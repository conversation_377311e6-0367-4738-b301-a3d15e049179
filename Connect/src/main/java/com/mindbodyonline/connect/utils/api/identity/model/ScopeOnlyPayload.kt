package com.mindbodyonline.connect.utils.api.identity.model


import com.google.gson.annotations.SerializedName
/**
 * Created by <PERSON><PERSON><PERSON> on 04/04/25.
 */
data class ScopeOnlyPayload(
    @SerializedName("scope") val scope: List<String>,
    override val issuer: String?,
    override val subject: String?,
    override val audience: String?,
    override val expiresAt: String?,
    override val notBefore: String?,
    override val issuedAt: String?,
    override val id: String?
) : JwtPayload

interface JwtPayload {
    val issuer: String?
    val subject: String?
    val audience: String?
    val expiresAt: String?
    val notBefore: String?
    val issuedAt: String?
    val id: String?
}
