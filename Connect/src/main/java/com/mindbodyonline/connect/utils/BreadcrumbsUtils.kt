package com.mindbodyonline.connect.utils

import android.net.Uri
import com.android.volley.VolleyError
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.common.repository.PerformanceName
import com.mindbodyonline.domain.BookabilityStatus
import com.mindbodyonline.domain.ClassStatusMessage
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.framework.abvariant.DevelopmentFlag
import com.newrelic.agent.android.NewRelic
import com.newrelic.agent.android.logging.LogLevel
import kotlin.math.roundToInt

object BreadcrumbsUtils {

    const val TAG = "BreadcrumbUtils"
    private const val GATEWAY_V1_PREFIX = "/v1/"

    /**
     * Breadcrumb in onCreate for activities and fragments, or show for Dialogs.  This can be queried
     * in New Relic under BreadCrumbName.ENABLED_FEATURES and will also show up in crash reports
     */
    fun breadcrumbEnabledFeature(enabledFeature: String?) {
        enabledFeature?.let {
            val data = EnabledFeaturesBreadcrumbData(EnabledFeatures(it)).convertDataToMap()
            NewRelic.recordBreadcrumb(BreadcrumbName.ENABLED_FEATURES.displayName, data)
        }
    }

    fun breadcrumbBookabilityBlocker(cto: ClassTypeObject?, sourceLocation: BookabilitySourceScreen){
        val classStatusMessage: ClassStatusMessage = cto?.status?.id?.let {
            when(it) {
                BookabilityStatus.BOOKED_AT_THIS_TIME -> ClassStatusMessage.BOOKED_AT_THIS_TIME
                BookabilityStatus.PREREQUISITES_NOT_MET -> ClassStatusMessage.PREREQUISITES_NOT_MET
                BookabilityStatus.NO_ONLINE_BOOKING -> ClassStatusMessage.NO_ONLINE_BOOKING
                BookabilityStatus.ONLINE_CAPACITY_FULL -> ClassStatusMessage.ONLINE_BOOKING_CAPACITY_FULL
                BookabilityStatus.BOOKED ,BookabilityStatus.CANNOT_CANCEL -> ClassStatusMessage.BOOKED_AT_THIS_TIME
                BookabilityStatus.CLASS_FULL -> ClassStatusMessage.CLASS_FULL
                BookabilityStatus.OVERLAPPING_WAITLIST_RESTRICTION -> ClassStatusMessage.OVERLAPPING_WAITLIST_RESTRICTION
                BookabilityStatus.OUTSIDE_BOOKING_WINDOW -> ClassStatusMessage.OUTSIDE_BOOKING_WINDOW
                else -> ClassStatusMessage.UNKNOWN
            }
        } ?: ClassStatusMessage.UNKNOWN
        val data = BookabilityBlockerBreadcrumbData(BookingBlockerValue(classStatusMessage), BookabilityScreenSource(sourceLocation.screenName)).convertDataToMap()
        NewRelic.recordBreadcrumb(BreadcrumbName.BOOKING_BLOCKER.displayName, data)
    }

    /**
     * Breadcrumb in onCreate for activities and fragments, or show for Dialogs.  This can be queried
     * in New Relic under BreadCrumbName.PERFORMANCE_METRIC and will also show up in crash reports
     * @param time The time at which the performance measurement should end in milliseconds
     * (Usually just using System.currentTimeMillis()). Must be greater than 0.
     * @param metric The name of the measurement. From the [PerformanceName] enum.
     * @param additionalData Additional data to be added to the breadcrumb.  This is optional.
     */
    @JvmOverloads
    fun breadcrumbPerformanceMetric(time: Long, metric: PerformanceName, additionalData: List<BreadcrumbsDataLayerValue>? = null) {
        val timeConverted = (time / 1000.0).roundToInt()
        val data = PerformanceBreadcrumbData(PerformanceTimeValue(timeConverted), PerformanceNameValue(metric), additionalData).convertDataToMap()
        NewRelic.recordBreadcrumb(BreadcrumbName.PERFORMANCE_METRIC.displayName, data)
        MBLog.d(TAG, "BreadcrumbPerformanceMetric being sent: $metric: $data")
    }

    /**
     * Breadcrumb parsing of a valid deeplink prior to navigation off that same deeplink.
     */
    fun breadcrumbDeeplink(deeplinkString: String?) {
        deeplinkString?.let {
            val data = DeeplinkBreadcrumbData(DeeplinkUri(it)).convertDataToMap()
            NewRelic.recordBreadcrumb(BreadcrumbName.DEEPLINK_DATA.displayName, data)
        }
    }

    /**
     * Breadcrumb in onResume for activities and fragments, or show for Dialogs.  This can be queried
     * in New Relic under BreadCrumbName.USER_FLOW and will also show up in crash reports
     */
    fun breadcrumbUserScreenFlow(className: String) {
        val data = ScreenBreadcrumbData(ScreenClassName(className)).convertDataToMap()
        NewRelic.recordBreadcrumb(BreadcrumbName.USER_FLOW.displayName, data)
        FirebaseCrashlytics.getInstance().log(data.toString())
    }

    /**
     * Breadcrumb to track Gateway API calls.  This can be queried
     * in New Relic under BreadCrumbName.GATEWAY_API and will also show up in crash reports
     *
     * @param uriBuilder Uri builder passed into the Gateway request. The trimmed encoded path will
     * show up as a data point in the breadcrumb under the key 'path'.
     */
    fun breadcrumbGatewayApiCall(uriBuilder: Uri.Builder) {
        uriBuilder.build().encodedPath?.replaceFirst(GATEWAY_V1_PREFIX, "")?.let {
            val data = ApiCallBreadcrumbData(RestPath(it)).convertDataToMap()
            NewRelic.recordBreadcrumb(BreadcrumbName.GATEWAY_API.displayName, data)
            FirebaseCrashlytics.getInstance().log(data.toString())
        }
    }

    /**
     *  This is going to be left as a reminder that it would be better if we sent this at a lower level
     *  when we attempt to call the api, however abandoned this approach at least for now because
     *  we just add to the volley request queue and the volley framework does the actual sending.
     *  Contemplated putting this inside a Volley response listener but decided to go the simpler method
     *  albeit with way more lines of code of sending prior to adding to the request queue.
     */
    fun breadcrumbApiCall(uriString: String) {
        java.net.URI(uriString).path?.let {
            NewRelic.recordBreadcrumb(
                    BreadcrumbName.GATEWAY_API.displayName,
                    ApiCallBreadcrumbData(RestPath(it)).convertDataToMap()
            )
        }
    }

    fun breadcrumbGenericError(error: String) {
        val data = GenericErrorData(errorMessage = ErrorMessage(error)).convertDataToMap()
        NewRelic.recordBreadcrumb(BreadcrumbName.GENERIC_ERROR.displayName, data)
        FirebaseCrashlytics.getInstance().log(
                BreadcrumbName.GENERIC_ERROR.displayName + ": " + data.toString()
        )
    }

    fun breadcrumbTriggerVolleyError(error: VolleyError?, url: String) {
        val apiErrorBreadcrumbData = ApiErrorBreadcrumbData(
                errorCode = ErrorCode(error?.networkResponse?.statusCode ?: 0),
                errorMessage = ErrorMessage(error?.message ?: "None"),
                restPath = RestPath(url)).convertDataToMap()
        NewRelic.recordBreadcrumb(BreadcrumbName.API_ERROR.displayName, apiErrorBreadcrumbData)
        FirebaseCrashlytics.getInstance().log(BreadcrumbName.API_ERROR.displayName + ": " + apiErrorBreadcrumbData.toString())
    }

    fun breadcrumbBadRequestError(error: VolleyError?, url: String, payload: String?) {
        val apiBadRequestBreadcrumbData = ApiBadRequestBreadcrumbData(
                errorCode = ErrorCode(error?.networkResponse?.statusCode ?: 0),
                errorMessage = ErrorMessage(error?.message ?: "None"),
                restPath = RestPath(url),
                payload = payload?.let { ApiPayload(it) }).convertDataToMap()
        NewRelic.recordBreadcrumb(BreadcrumbName.API_BAD_REQUEST.displayName, apiBadRequestBreadcrumbData)
        FirebaseCrashlytics.getInstance().log(BreadcrumbName.API_BAD_REQUEST.displayName + ": " + apiBadRequestBreadcrumbData.toString())
    }


    fun breadCrumbUITimeout(className: String) {
        val data = UITimeoutBreadcrumbData(ScreenClassName(className)).convertDataToMap()
        NewRelic.recordBreadcrumb(BreadcrumbName.UI_TIMEOUT.displayName, data)
        FirebaseCrashlytics.getInstance().log(
                BreadcrumbName.UI_TIMEOUT.displayName + ": " + data.toString()
        )
    }

    fun breadcrumbUserEvent(eventName: String) {
        val data = EventBreadcrumbData(EventName(eventName)).convertDataToMap()
        NewRelic.recordBreadcrumb(BreadcrumbName.USER_EVENT.displayName, data)
        FirebaseCrashlytics.getInstance().log(
                BreadcrumbName.USER_EVENT.displayName + ": " + data.toString()
        )
    }

    fun breadcrumbIsGuestuser(isGuestUser: Boolean?) {
        isGuestUser?.let {
            NewRelic.recordBreadcrumb(
                    BreadcrumbName.IS_GUEST_DATA.displayName,
                    UserDataBreadcrumbData(UserDataValue(it.toString())).convertDataToMap()
            )
        }
    }

    /**
     * Breadcrumb to record bad attribution links passed in from Braze.  Currently only checking for a value
     * of 0, but that may expand in the future
     */
    fun breadcrumbBadAttributionLinks(referringParams: String?) {
        NewRelic.recordBreadcrumb(
                BreadcrumbName.ATTRIBUTION_LINK_ERROR.displayName,
                AttributionErrorData(ReferringParams(referringParams ?: "")).convertDataToMap()
        )
    }

    /**
     * Records a handled exception. Use this for recording stack stack traces, log messages,
     * and optional key/pair values when a problem that doesn't crash the app occurs.
     */
    fun recordHandledException(ex: Exception, logMessage: String? = null, customKeys: Map<String, String>? = null) {
        // Adds a log message to the Crashlytics handled exception event viewer
        logMessage?.let {
            FirebaseCrashlytics.getInstance().log(it)
            MBLog.d(TAG, logMessage)
        }
        // Use custom key/value pairs to record user data at the point the exception occurred
        customKeys?.forEach {
            FirebaseCrashlytics.getInstance().setCustomKey(it.key, it.value)
            MBLog.d(TAG, "${it.key}: ${it.value}")
        }
        // Send the handled exception to Crashlytics
        FirebaseCrashlytics.getInstance().recordException(ex)
        MBLog.d(TAG, ex.toString())
        // Send the handled exception to NewRelic. This automatically includes an entity guuid
        // that can be used to lookup associated custom events in the NRQL viewer
        customKeys?.let {
            NewRelic.recordHandledException(ex, it)
        } ?: NewRelic.recordHandledException(ex)

    }

    fun recordStripePollingBreadCrumb(attributes: Map<String, Any?>) {
        NewRelic.recordBreadcrumb(BreadcrumbName.STRIPE_POLLING_TASK.displayName, attributes)
    }

    fun recordStripeUPEBreadCrumb(attributes: Map<String, Any?>) {
        NewRelic.recordBreadcrumb(BreadcrumbName.STRIPE_UPE_CHECKOUT.displayName, attributes)
    }

    fun recordStripeWebViewBreadCrumb(attributes: Map<String, Any?>) {
        NewRelic.recordBreadcrumb(BreadcrumbName.STRIPE_WEB_VIEW_CHECKOUT.displayName, attributes)
    }

    fun breadcrumbAppLaunchSteps(appLaunchSteps: String) {
        val attributes: MutableMap<String, Any> = mutableMapOf("Steps" to appLaunchSteps)
        NewRelic.recordBreadcrumb(BreadcrumbName.APP_LAUNCH_STEPS.displayName, attributes)
    }

    fun recordFTCAuditLogBreadCrumb(attributes: Map<String, Any?>) {
        NewRelic.recordBreadcrumb(BreadcrumbName.FTC_AUDIT_LOG.displayName, attributes)
    }

    fun recordTokenDelegationBreadCrumb(attributes: Map<String, Any?>) {
        NewRelic.recordBreadcrumb(BreadcrumbName.TOKEN_DELEGATION.displayName, attributes)
    }

    fun recordAuthTokenMissingBreadCrumb(attributes: Map<String, Any?>) {
        NewRelic.recordBreadcrumb(BreadcrumbName.AUTH_TOKEN_MISSING.displayName, attributes)
    }

    /**
     * Logs the NewRelic breadcrumb for a forced logout event with metadata.
     * @see logWithBreadcrumbNewRelicEventWithMetadata
     *
     * @param metadata A map of metadata to include with the event.
     * @param label An optional label for the event.
     * @param value An optional value for the event.
     */
    @JvmStatic
    @JvmOverloads
    fun logWithBreadcrumbNewRelicEventForcedLogoutWithMetadata(
        metadata: Map<Any?, Any?>,
        label: String?,
        value: Int,
    ) {
        logWithBreadcrumbNewRelicEventWithMetadata(
            metadata = metadata,
            label = label,
            value = value,
            event = BreadcrumbName.FORCED_LOGOUT,
        )
    }

    /**
     * Records a NewRelic breadcrumb with metadata and logs the same if logging is available.
     * This is used to log/breadcrumb custom events with additional information.
     *
     * @param event The name of the event to log.
     * @param metadata A map of metadata to include with the event.
     * @param label An optional label for the event.
     * @param value An optional value for the event.
     */
    @JvmStatic
    fun logWithBreadcrumbNewRelicEventWithMetadata(
        metadata: Map<Any?, Any?>,
        label: String?,
        value: Int,
        event: BreadcrumbName,
    ) {
        if (!DevelopmentFlag.DEVELOPMENT_LOG_NEWRELIC_BREADCRUMBS.isFeatureEnabled()) return
        val data: MutableMap<String?, Any?> = HashMap<String?, Any?>()
        if (label != null) {
            data.put("label", label)
        }
        data.put("value", value)
        data.put("metadata", metadata)
        data.put("level", LogLevel.DEBUG)
        data.put("breadcrumbType", BreadcrumbType.USER_FLOW.name)
        val eventName = event.displayName
        NewRelic.recordBreadcrumb(eventName, data)
        data.put("event_name", eventName)
        NewRelic.logAttributes(data)
    }
}
