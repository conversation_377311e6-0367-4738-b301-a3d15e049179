package com.mindbodyonline.connect.utils.analytics

import com.mindbodyonline.ConnectApp
import com.mindbodyonline.analytics.AnalyticsApi
import com.mindbodyonline.analytics.Config
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.BuildConfig
import com.mindbodyonline.connect.analytics.APP
import com.mindbodyonline.connect.analytics.MINDBODY_APP
import com.mindbodyonline.framework.abvariant.DevelopmentFlag

const val TAG = "Analytics"
object AnalyticsLocator {

	private val appContext : ConnectApp = ConnectApp.getInstance()
	private val analyticsConfigEnv : Config.Environment = if (BuildConfig.DEBUG)
		Config.Environment.Development else Config.Environment.Production

	private val config : Config = Config(appContext, analyticsConfigEnv) {
		MBLog.d(TAG, it)
	}

	private val analyticsApi : AnalyticsApi? = AnalyticsApi.getInstance(
		config = config,
		superProperties = mapOf(APP to MINDBODY_APP)
	)

	@JvmStatic
	val analyticsTracker: AnalyticsTracker by lazy {
		analyticsApi?.let { AnalyticsTrackerImpl(it) } ?: FallbackAnalyticsTrackerImpl()
	}

	@JvmStatic
	fun getMixpanelDistinctUserId(): String = analyticsApi?.getMixpanelDistinctUserId() ?: ""
}
