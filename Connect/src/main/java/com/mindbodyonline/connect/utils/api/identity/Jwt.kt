package com.mindbodyonline.connect.utils.api.identity

import com.mindbodyonline.connect.utils.api.identity.model.JwtPayload
import android.util.Base64
import com.google.gson.reflect.TypeToken
import com.mindbodyonline.android.util.SafeGson
import java.lang.reflect.Type
/**
 * Created by <PERSON><PERSON><PERSON> on 04/04/25.
 */
data class Jwt<Payload : JwtPayload>(
    val headers: Map<String, String>,
    val payload: Payload
) {
    companion object {
        private const val DELIMITER = "."
        private const val HEADER_INDEX = 0
        private const val PAYLOAD_INDEX = 1


        private fun String.jwtSplit(): List<String> = split(DELIMITER)

        private fun String.jwtHeader(): String {
            return jwtSplit().takeIf { it.isNotEmpty() }?.get(HEADER_INDEX)?.decodeBase64() ?: ""
        }

        private fun String.jwtPayload(): String =
            jwtSplit().takeIf { it.size > 1 }?.get(PAYLOAD_INDEX)?.decodeBase64() ?: ""

        private fun String.decodeBase64() : String =
            String(Base64.decode(this, Base64.NO_WRAP))

        @JvmStatic
        fun <Payload> decode(
            jwt: String,
            payloadClass: Class<Payload>
        ): Jwt<Payload>? where Payload : JwtPayload {
            val mapType : Type = (object : TypeToken<Map<String, String>>() {}).type
            val headers : Map<String, String>? = SafeGson.fromJson<Map<String, String>>(jwt.jwtHeader(), mapType)
            val payload : Payload? = SafeGson.fromJson(jwt.jwtPayload(), payloadClass)
            if (headers == null || payload == null) {
                return null
            }
            return Jwt(headers, payload)
        }
    }
}
