package com.mindbodyonline.connect.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.mindbodyonline.android.util.SafeGson;
import com.mindbodyonline.android.util.log.MBLog;
import com.mindbodyonline.android.util.time.DateParser;
import com.mindbodyonline.connect.activities.MainActivity;
import com.mindbodyonline.connect.activities.SettingsActivity;
import com.mindbodyonline.connect.activities.details.AppointmentDetailsActivity;
import com.mindbodyonline.connect.activities.details.AppointmentTypeDetailsActivity;
import com.mindbodyonline.connect.activities.details.BusinessDetailsActivity;
import com.mindbodyonline.connect.activities.details.ClassTypeDetailsActivity;
import com.mindbodyonline.connect.activities.details.DealDetailsActivity;
import com.mindbodyonline.connect.activities.details.ReviewDetailsActivity;
import com.mindbodyonline.connect.activities.list.ReviewListActivity;
import com.mindbodyonline.connect.activities.list.search.BusinessListActivity;
import com.mindbodyonline.connect.activities.list.search.SearchDealsListActivity;
import com.mindbodyonline.connect.activities.list.services.BusinessDealsListActivity;
import com.mindbodyonline.connect.activities.list.services.EventListActivity;
import com.mindbodyonline.connect.activities.list.services.PricingListActivity;
import com.mindbodyonline.connect.activities.list.services.RoutineServicesActivity;
import com.mindbodyonline.connect.activities.list.services.classes.ClassStaffDetailsActivity;
import com.mindbodyonline.connect.activities.schedule.BusinessScheduleActivity;
import com.mindbodyonline.connect.activities.workflow.FitbitAccessTokenActivity;
import com.mindbodyonline.connect.activities.workflow.GoogleFitAccessTokenActivity;
import com.mindbodyonline.connect.activities.workflow.StravaAccessTokenActivity;
import com.mindbodyonline.connect.analytics.OriginComponent;
import com.mindbodyonline.connect.appointments.AppointmentCategoryListActivity;
import com.mindbodyonline.connect.giftcards.GiftCardPickerActivity;
import com.mindbodyonline.connect.login.AccountWorkflow;
import com.mindbodyonline.connect.profile.schedule.ScheduleFragment.ScheduleType;
import com.mindbodyonline.connect.utils.api.GatewayWorkflowUtilsKt;
import com.mindbodyonline.connect.utils.api.ModelTranslationKt;
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI;
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationRefJson;
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference;
import com.mindbodyonline.connect.utils.time.DateFormatUtils;
import com.mindbodyonline.data.StaticInstance;
import com.mindbodyonline.data.services.MBAuth;
import com.mindbodyonline.data.services.locator.ServiceLocator;
import com.mindbodyonline.domain.Location;
import com.mindbodyonline.domain.User;

import com.google.android.gms.maps.model.LatLng;
import com.mindbodyonline.framework.abvariant.ABHelperUtils;
import com.mindbodyonline.framework.abvariant.FeatureFlag;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import androidx.annotation.IntDef;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import static com.mindbodyonline.connect.profile.ScheduleProfileContainerFragment.POSITION_ACTIVITY;
import static com.mindbodyonline.connect.profile.ScheduleProfileContainerFragment.POSITION_PASSES;
import static com.mindbodyonline.connect.profile.ScheduleProfileContainerFragment.POSITION_SCHEDULE;
import static com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_EXPLORE_PARAMETER_FRAGMENT_TAG;
import static com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_LOCATION_REFERENCE;
import static com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_ORIGIN_COMPONENT;
import static com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_SELECTED_TAB;
import static com.mindbodyonline.connect.utils.DealDetailsUtils.getLocationFromMasterLocationId;

import kotlin.Unit;

// TODO: remove these
// TODO: end

/**
 * Created by anthony.lipscomb on 8/19/2014.
 */
public class DeepLinkUtils {
    // Uri Scheme wiki: https://wiki.mbodev.me/wiki/9100/connect-uri-scheme

    @Retention(RetentionPolicy.SOURCE)
    public @IntDef({TAB_BUSINESSES, TAB_CLASSES, TAB_STAFF}) @interface FavoritesTab {}

    public static final int TAB_BUSINESSES = 0;
    public static final int TAB_CLASSES = 1;
    public static final int TAB_STAFF = 2;

    //Host
    public static final String DEEPLINK_SCHEME = "mindbodyconnect://";

    //Location
    public static final String LOCATION_CONTROLLER = "location";
    public static final String EXPLORE_CONTROLLER = "explore";
    public static final String APPOINTMENT_TYPE_SUBCONTROLLER = "appointmenttype";
    public static final String CLASS_SUBCONTROLLER = "class";
    public static final String STAFF_SUBCONTROLLER = "staff";
    public static final String APPT_SUBCONTROLLER = "appointment";
    public static final String SCHEDULE_SUBCONTROLLER = "schedule";
    public static final String GIFTCARD_SUBCONTROLLER = "giftcards";
    public static final String REVIEW_SUBCONTROLLER = "review";
    //Note that we have deal and deals (One is for the deals tab and the other is for deal search, etc..)
    public static final String DEAL_CONTROLLER = "deal";
    public static final String PRICING_SUBCONTROLLER = "pricing";
    public static final String EVENT_SUBCONTROLLER = "event";
    public static final String EXPLORE_WELLNESS_SUBCONTROLLER = "wellness";
    public static final String EXPLORE_FITNESS_SUBCONTROLLER = "fitness";
    public static final String EXPLORE_BEAUTY_SUBCONTROLLER = "beauty";

    //search
    public static final String SEARCH_CONTROLLER = "search";

    //Favorite
    public static final String FAVORITE_CONTROLLER = "favorite";

    //User
    public static final String USER_CONTROLLER = "user";
    public static final String UPCOMING_SUBCONTROLLER = "upcoming";
    public static final String HISTORY_SUBCONTROLLER = "history";
    public static final String PROFILE_SUBCONTROLLER = "profile";
    public static final String ACTIVITY_DASHBOARD_SUBCONTROLLER = "activity";
    public static final String APPOINTMENT_ACTION = "appointment";
    public static final String PASSES_ACTION = "passes";
    public static final String CLASS_ACTION /*_LAWSUIT*/ = "class";

    //Settings and support
    public static final String SUPPORT_CONTROLLER = "support";
    public static final String SETTINGS_CONTROLLER = "settings";
    public static final String FITBIT_CONTROLLER = "fitbit";
    public static final String GOOGLEFIT_CONTROLLER = "googlefit";
    public static final String STRAVA_CONTROLLER = "strava";
    public static final String NOTIFICATIONS_SUBCONTROLLER = "notifications";
    public static final String CHAT_SUBCONTROLLER = "chat";

    //Note that we have deal and deals (One is for the deals tab and the other is for deal search, etc..)
    //Deals page
    public static final String DEALS_CONTROLLER = "deals";

    // Application variables
    // Names taken from mvc routing scheme (controller/action/id)
    public static final int CONTROLLER_ID_INDEX = 0;
    public static final int SUBCONTROLLER_INDEX = 1;     // corresponds to /location/:id/SUBCONTROLLER
    public static final int SUBCONTROLLER_ID_INDEX = 2;  // corresponds to /location/:id/class/SUBCONTROLLER_ID
    public static final int ACTION_INDEX = 1;            // corresponds to /user/upcoming/ACTION
    public static final int ACTION_ID_INDEX = 2;         // corresponds to /user/upcoming/appointment/ACTION_ID

    public static final String DEEP_LINK_URI_KEY = "uri";

    //Booker studio id prefix
    public static final String BOOKER_ID_PREFIX = "b_";  // corresponds to /location/:b_<id>/SUBCONTROLLER

    public static final String USER_ACTION_LINK_CONTROLLER = "useractionlink";

    /*
        Handles all navigating of deep linking including different layers of the routing based on
        the activity is showing when this method is called
     */

    public static void deepLink(final Context context) throws NumberFormatException {
        // If we don't have deep link information or a host, no point in trying
        if (StaticInstance.getDeepLinkedData() != null && StaticInstance.getDeepLinkedData().getHost() != null) {
            String deeplinkData = StaticInstance.getDeepLinkedData().toString();
            MBLog.d("MBDeepLink", "Acting on deep link: " + deeplinkData);
            BreadcrumbsUtils.INSTANCE.breadcrumbDeeplink(deeplinkData);
            Uri data = StaticInstance.getDeepLinkedData();
            StaticInstance.setDeepLinkedData(null);
            String controller = data.getHost();
            List<String> segments = data.getPathSegments();

            final List<Intent> backStack = new ArrayList<>();

            // mindbodyconnect://favorite/class
            // mindbodyconnect://favorite/:path0
            if (controller.equalsIgnoreCase(FAVORITE_CONTROLLER)) {
                StaticInstance.setSelectedFragment(Constants.FAVORITES_FRAGMENT_TAG);

                if (data.getLastPathSegment() != null && data.getLastPathSegment().equalsIgnoreCase(STAFF_SUBCONTROLLER)) {
                    StaticInstance.setSelectedFragmentTabIndex(2);
                    return;
                } else if (data.getLastPathSegment() != null && data.getLastPathSegment().equalsIgnoreCase(CLASS_SUBCONTROLLER)) {
                    StaticInstance.setSelectedFragmentTabIndex(1);
                    return;
                } else {
                    StaticInstance.setSelectedFragmentTabIndex(0);
                    return;
                }
            }

            // mindbodyconnect://support/chat
            if (controller.equalsIgnoreCase(SUPPORT_CONTROLLER) && !Utils.isEmpty(data.getPathSegments()) &&
                data.getPathSegments().get(0).equals(CHAT_SUBCONTROLLER)) {
                User user = MBAuth.getUser();
                if (user != null) {
                    IntentUtils.launchWebsite(context, Constants.CONTACT_SUPPORT_URL);
                }
            }

            // mindbodyconnect://user/schedule/upcoming
            // mindbodyconnect://user/:path0/:path1
            if (controller.equalsIgnoreCase(USER_CONTROLLER)) {
                if (!Utils.isEmpty(data.getPathSegments())) {
                    String subController = data.getPathSegments().get(0);

                    if (subController.equalsIgnoreCase(SCHEDULE_SUBCONTROLLER)) {
                        StaticInstance.setSelectedFragment(Constants.MY_PROFILE_FRAGMENT_TAG);
                        StaticInstance.setSelectedFragmentTabIndex(POSITION_SCHEDULE);
                        if (data.getPathSegments().size() > 1) {
                            String scheduleTab = data.getPathSegments().get(1);
                            if (scheduleTab.equalsIgnoreCase(UPCOMING_SUBCONTROLLER)) {
                                StaticInstance.setScheduleType(ScheduleType.UPCOMING);
                                return;
                            } else if (scheduleTab.equalsIgnoreCase(HISTORY_SUBCONTROLLER)) {
                                StaticInstance.setScheduleType(ScheduleType.HISTORY);
                                return;
                            }
                        }

                        StaticInstance.setSelectedFragmentTabIndex(0);
                        return;
                    } else if (subController.equalsIgnoreCase(PROFILE_SUBCONTROLLER)) {
                        StaticInstance.setSelectedFragment(Constants.MY_PROFILE_FRAGMENT_TAG);
                        if (data.getPathSegments().size() > ACTION_INDEX) {
                            String action = data.getPathSegments().get(ACTION_INDEX);
                            if (action.equals(PASSES_ACTION)) {
                                StaticInstance.setSelectedFragmentTabIndex(POSITION_PASSES);
                            }
                        }
                    } else if (subController.equalsIgnoreCase(ACTIVITY_DASHBOARD_SUBCONTROLLER)) {
                        StaticInstance.setSelectedFragment(Constants.MY_PROFILE_FRAGMENT_TAG);
                        StaticInstance.setSelectedFragmentTabIndex(POSITION_ACTIVITY);
                        return;
                    } else if (subController.equalsIgnoreCase(UPCOMING_SUBCONTROLLER) &&
                        data.getPathSegments().size() > ACTION_ID_INDEX) {
                        try {
                            String action = data.getPathSegments().get(ACTION_INDEX);

                            if (action.equalsIgnoreCase(CLASS_ACTION)) {
                                long actionId = Long.parseLong(data.getPathSegments().get(ACTION_ID_INDEX));
                                Intent classDetails = new Intent(context, ClassTypeDetailsActivity.class);
                                classDetails.putExtra(Constants.KEY_BUNDLE_CLASS_INSTANCE_ID, actionId);
                                backStack.add(classDetails);
                            }

                            if (action.equalsIgnoreCase(APPOINTMENT_ACTION) && !MBAuth.isGuestUser()) {
                                String actionId = data.getPathSegments().get(ACTION_ID_INDEX);
                                Intent appointmentDetails = new Intent(context, AppointmentDetailsActivity.class);
                                appointmentDetails.putExtra(Constants.KEY_BUNDLE_VISITID, actionId);
                                appointmentDetails.putExtra(KEY_BUNDLE_ORIGIN_COMPONENT, OriginComponent.USER_ACTION_LINK);
                                backStack.add(appointmentDetails);
                            }
                        } catch (NumberFormatException ignored) {
                        }
                    }
                } else {
                    StaticInstance.setSelectedFragment(Constants.MY_PROFILE_FRAGMENT_TAG);
                    return;
                }
            }

            // mindbodyconnect://explore
            // mindbodyconnect://explore/fitness
            // mindbodyconnect://explore/wellness
            // mindbodyconnect://explore/beauty
            if (controller.equalsIgnoreCase(EXPLORE_CONTROLLER)) {
                StaticInstance.setSelectedFragment(Constants.EXPLORE_FRAGMENT_TAG);

                if (!Utils.isEmpty(data.getPathSegments())) {
                    String subController = data.getPathSegments().get(0);
                    if (subController.equalsIgnoreCase(EXPLORE_FITNESS_SUBCONTROLLER)) {
                        StaticInstance.setSelectedFragmentTabIndex(Constants.FITNESS_VERTICAL_ID);
                        if (context instanceof Activity) {
                            attachExploreFitnessQueryParams(data, ((Activity) context).getIntent());
                        }
                    } else if (subController.equalsIgnoreCase(EXPLORE_BEAUTY_SUBCONTROLLER)) {
                        StaticInstance.setSelectedFragmentTabIndex(Constants.BEAUTY_VERTICAL_ID);
                    } else if (subController.equalsIgnoreCase(EXPLORE_WELLNESS_SUBCONTROLLER)) {
                        StaticInstance.setSelectedFragmentTabIndex(Constants.WELLNESS_VERTICAL_ID);
                    }
                }
                return;
            }

            // mindbodyconnect://deal?q=Yoga
            if (controller.equalsIgnoreCase(DEAL_CONTROLLER)) {
                Intent searchActivityIntent = new Intent(context, SearchDealsListActivity.class);
                attachSearchQueryParams(data, searchActivityIntent);
                attachDealsV2SearchQueryParams(data, searchActivityIntent);
                backStack.add(searchActivityIntent);
            }

            // mindbodyconnect://location/:id/:SUBCONTROLLER/:SUBCONTROLLER_ID
            if (controller.equalsIgnoreCase(LOCATION_CONTROLLER)) {
                if (segments.size() >= 1) {
                    //First add the business details to the back stack (check for master ID)
                    Intent businessIntent = new Intent(context, BusinessDetailsActivity.class);

                    // Check if Booker studio
                    String inventorySource = SwamisAPI.MB_INVENTORY_SOURCE;
                    String locationId = segments.get(0);
                    int masterLocationId;
                    if (locationId.startsWith(BOOKER_ID_PREFIX)) {
                        inventorySource = SwamisAPI.BOOKER_INVENTORY_SOURCE;
                        locationId = locationId.replace(BOOKER_ID_PREFIX, "");
                    }
                    try {
                        masterLocationId = Integer.parseInt(locationId);
                    } catch (NumberFormatException ex) {
                        //Can't launch business details without a master loc ID
                        return;
                    }

                    businessIntent.putExtra(KEY_BUNDLE_ORIGIN_COMPONENT, OriginComponent.USER_ACTION_LINK.getComponentName());

                    //Add the business details to the back stack
                    businessIntent.putExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE,
                        GatewayWorkflowUtilsKt.buildLocationRefById(masterLocationId, inventorySource));
                    backStack.add(businessIntent);

                    // Then see if there's anything else on the URI path
                    if (segments.size() > 1) {
                        String subController = segments.get(SUBCONTROLLER_INDEX);

                        if (MBAuth.isGuestUser()) {
                            switch (subController.toLowerCase()) {
                                case PRICING_SUBCONTROLLER:
                                case GIFTCARD_SUBCONTROLLER:
                                    showLoginDialog(context, data);
                                    backStack.clear();
                                    return;
                            }
                        }

                        // Launches full list activities associated with a location
                        // mindbodyconnect://location/:id/SUBCONTROLLER
                        if (segments.size() == 2) {
                            // Schedule for the business
                            Intent subcontrollerIntent = getListIntentForSubcontroller(context, subController, masterLocationId, inventorySource);

                            if (subcontrollerIntent != null) {
                                backStack.add(subcontrollerIntent);
                            }
                        } else { //size is at least 3
                            // Go into a details view for the subcontroller / ID
                            long subcontrollerItemId = 0;
                            try {
                                subcontrollerItemId = Long.parseLong(segments.get(SUBCONTROLLER_ID_INDEX));
                            } catch (NumberFormatException ignored) {
                            }

                            // If we have a valid ID, go to some details
                            if (subcontrollerItemId != 0) {
                                Intent subDetailsIntent = null;
                                switch (subController.toLowerCase()) {
                                    case CLASS_SUBCONTROLLER:
                                        //TODO: Gateway V3 will have LocationReference support for classes
                                        subDetailsIntent = new Intent(context, ClassTypeDetailsActivity.class);
                                        subDetailsIntent.putExtra(ClassTypeDetailsActivity.TYPE_EXTRA_STRING, ClassTypeDetailsActivity.Type.CLASS.ordinal());
                                        subDetailsIntent.putExtra(Constants.KEY_BUNDLE_CLASS_INSTANCE_ID, subcontrollerItemId);
                                        break;
                                    case STAFF_SUBCONTROLLER:
                                        //TODO: Gateway V3 will have LocationReference support for classes
                                        subDetailsIntent = ClassStaffDetailsActivity.newIntent(context, subcontrollerItemId, masterLocationId,
                                            null, OriginComponent.USER_ACTION_LINK);
                                        break;
                                    case EVENT_SUBCONTROLLER:
                                        //TODO: Gateway V3 will have LocationReference support for enrollments
                                        subDetailsIntent = new Intent(context, ClassTypeDetailsActivity.class);
                                        subDetailsIntent.putExtra(ClassTypeDetailsActivity.TYPE_EXTRA_STRING, ClassTypeDetailsActivity.Type.ENROLLMENT.ordinal());
                                        subDetailsIntent.putExtra(Constants.KEY_BUNDLE_CLASS_INSTANCE_ID, subcontrollerItemId);
                                        break;
                                    case APPT_SUBCONTROLLER:
                                    case APPOINTMENT_TYPE_SUBCONTROLLER:
                                        Intent apptTypeListIntent = AppointmentCategoryListActivity.newIntent(context,
                                            GatewayWorkflowUtilsKt.buildLocationRefById(masterLocationId, inventorySource));
                                        backStack.add(apptTypeListIntent);
                                        subDetailsIntent = new Intent(context, AppointmentTypeDetailsActivity.class);
                                        subDetailsIntent.putExtra(Constants.KEY_BUNDLE_APPOINTMENT_SERVICE_REF,
                                            GatewayWorkflowUtilsKt.buildAppointmentRefById((int) subcontrollerItemId, masterLocationId, inventorySource));
                                        subDetailsIntent.putExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE,
                                            GatewayWorkflowUtilsKt.buildLocationRefById(masterLocationId, inventorySource));
                                        break;
                                    case REVIEW_SUBCONTROLLER:
                                        //TODO: AB-740714 hook up gateway API and LocationReference to ReviewDetailsActivity
                                        subDetailsIntent = new Intent(context, ReviewDetailsActivity.class);
                                        subDetailsIntent.putExtra(Constants.KEY_BUNDLE_REVIEW_ID, (int) subcontrollerItemId);
                                        break;
                                    case DEAL_CONTROLLER:
                                        if (MBAuth.isGuestUser()) {
                                            backStack.clear();
                                            showLoginDialog(context, data);
                                        } else {
                                            if (ABHelperUtils.showBusinessFocussedDealDetailsScreen()) {
                                                // Remove business details screen from the backstack
                                                backStack.clear();

                                                Location location = getLocationFromMasterLocationId(masterLocationId);
                                                if (location != null) {
                                                    LocationReference locationReference = ModelTranslationKt.toLocationReference(location);
                                                    subDetailsIntent = BusinessDetailsActivity.newIntent(
                                                            context,
                                                            locationReference,
                                                            (int) subcontrollerItemId,
                                                            OriginComponent.USER_ACTION_LINK
                                                    );
                                                } else {
                                                    subDetailsIntent = BusinessDetailsActivity.newIntent(context, null);
                                                    subDetailsIntent.putExtra(KEY_BUNDLE_ORIGIN_COMPONENT, OriginComponent.USER_ACTION_LINK);
                                                }
                                            } else {
                                                subDetailsIntent = new Intent(context, DealDetailsActivity.class);
                                                subDetailsIntent.putExtra(KEY_BUNDLE_ORIGIN_COMPONENT, OriginComponent.USER_ACTION_LINK);
                                            }
                                            subDetailsIntent.putExtra(Constants.KEY_BUNDLE_PRODUCT_ID, (int) subcontrollerItemId);
                                        }
                                        break;
                                    default:  //ignore if you can't follow the rules
                                }

                                if (subDetailsIntent != null) {
                                    subDetailsIntent.putExtra(Constants.KEY_BUNDLE_MASTER_LOCATION_ID, masterLocationId);
                                    backStack.add(subDetailsIntent);
                                }
                            } else {
                                //Not a valid ID, but can still direct in certain situations
                                Intent subcontrollerIntent = getListIntentForSubcontroller(context, subController, masterLocationId, inventorySource);

                                if (subcontrollerIntent != null) {
                                    backStack.add(subcontrollerIntent);
                                }
                            }
                        }
                    }
                } else {
                    // Search locations on the fitness tab
                    StaticInstance.setSelectedFragment(Constants.EXPLORE_FRAGMENT_TAG);
                    StaticInstance.setSelectedFragmentTabIndex(Constants.FITNESS_VERTICAL_ID);
                    StaticInstance.selectedFragmentShowSecondTab = true;
                    if (context instanceof Activity) {
                        attachExploreFitnessQueryParams(data, ((Activity) context).getIntent());
                    }
                }
            }

            if (controller.equalsIgnoreCase(SETTINGS_CONTROLLER)) {
                backStack.add(new Intent(context, SettingsActivity.class));

                if (!Utils.isEmpty(data.getPathSegments())) {
                    String subController = data.getPathSegments().get(0);

                    if (subController.equals(NOTIFICATIONS_SUBCONTROLLER)) {
                        Intent notificationsIntent = new Intent(context, SettingsActivity.class);
                        notificationsIntent.setAction(SharedPreferencesUtils.NOTIFICATIONS_PREFERENCE);
                        backStack.add(notificationsIntent);
                    }
                }
            }

            if (controller.equalsIgnoreCase(FITBIT_CONTROLLER)) {
                if (MBAuth.isGuestUser()) {
                    showLoginDialog(context, data);
                } else {
                    backStack.add(new Intent(context, FitbitAccessTokenActivity.class));
                }
            }

            if (controller.equalsIgnoreCase(GOOGLEFIT_CONTROLLER)) {
                if (MBAuth.isGuestUser()) {
                    showLoginDialog(context, data);
                } else {
                    backStack.add(new Intent(context, GoogleFitAccessTokenActivity.class));
                }
            }

            if (controller.equalsIgnoreCase(STRAVA_CONTROLLER)) {
                if (MBAuth.isGuestUser()) {
                    showLoginDialog(context, data);
                } else {
                    backStack.add(new Intent(context, StravaAccessTokenActivity.class));
                }
            }

            // mindbodyconnect://deals/
            if (controller.equalsIgnoreCase(DEALS_CONTROLLER)) {
                StaticInstance.setSelectedFragment(Constants.DEALS_FRAGMENT_TAG);
                return;
            }

            // mindbodyconnect://search/
            if (controller.equalsIgnoreCase(SEARCH_CONTROLLER)) {
                Intent intent = new Intent(context, MainActivity.class);
                intent.putExtra(KEY_BUNDLE_EXPLORE_PARAMETER_FRAGMENT_TAG, Constants.EXPLORE_FRAGMENT_TAG);
                intent.putExtra(KEY_BUNDLE_SELECTED_TAB, 1);
                attachExploreFitnessQueryParams(data, intent);
                backStack.add(intent);
            }

            // mindbodyconnect://useractionlink/
            if (controller.equalsIgnoreCase(USER_ACTION_LINK_CONTROLLER)) {
                // ?studioID={studio_id}
                if (data.getQueryParameter("studioID") != null || data.getQueryParameter("studioId") != null) {
                    String studioId = data.getQueryParameter("studioID");
                    if (studioId == null) {
                        studioId = data.getQueryParameter("studioId");
                    }
                    try {
                        int parsedStudioId = Integer.parseInt(studioId.trim());

                        ServiceLocator.getLocationRepository().getAllLocationsForSite(parsedStudioId,
                                response -> {
                                    if (response != null) {
                                        if (response.size() == 1) {
                                            // Directly open the business details screen
                                            Intent businessDetailsIntent = new Intent(context, BusinessDetailsActivity.class);
                                            businessDetailsIntent.putExtra(KEY_BUNDLE_LOCATION_REFERENCE,
                                                    new LocationReference(SwamisAPI.MB_INVENTORY_SOURCE,
                                                            SafeGson.toJson(new LocationRefJson(null,
                                                                    parsedStudioId, null, null,
                                                                    SwamisAPI.MB_INVENTORY_SOURCE))));
                                            businessDetailsIntent.putExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT, OriginComponent.USER_ACTION_LINK.getComponentName());
                                            backStack.add(businessDetailsIntent);
                                        } else if (response.size() > 1) {
                                            // Business has multiple locations, show business list
                                            Intent businessListIntent = new Intent(context, BusinessListActivity.class);
                                            businessListIntent.putExtra(Constants.KEY_BUNDLE_SITEID, parsedStudioId);
                                            backStack.add(businessListIntent);
                                        }
                                    }
                                    // Need to start activities here too as this response is received after the last startActivities is called
                                    if (!Utils.isEmpty(backStack)) {
                                        ContextCompat.startActivities(context, backStack.toArray(new Intent[backStack.size()]));
                                    }
                                    return Unit.INSTANCE;
                                }, error -> {
                                    BreadcrumbsUtils.INSTANCE.breadcrumbDeeplink("Failed to fetch locations for deeplink studio id: " + parsedStudioId);
                                    return Unit.INSTANCE;
                                });
                    } catch (NumberFormatException ignored) {
                        BreadcrumbsUtils.INSTANCE.breadcrumbDeeplink("Failed to parse deeplink studio id: " + studioId);
                    }
                    // To ensure startActivities is only called once for this scenario
                    return;
                }
            }

            if (!Utils.isEmpty(backStack)) {
                ContextCompat.startActivities(context, backStack.toArray(new Intent[backStack.size()]));
            }
        }
    }

    @SuppressLint("TypeCastDetector.CONTEXT_CAST_ISSUE")
    private static void showLoginDialog(Context context, Uri data) {
        if (context instanceof FragmentActivity) {
            AccountWorkflow.showLoginRequiredDialog((FragmentActivity) context, data, "Deep link");
        }
        // If the context is not a fragment activity, then we can ignore.  Main activity will
        //  pick up the deep link when the user enters the app
    }

    public static void attachSearchQueryParams(Uri data, Intent searchActivityIntent) {
        if (data == null || searchActivityIntent == null) return;
        if (data.getQueryParameter("q") != null) {
            String query = data.getQueryParameter("q").replace("+", " ").trim();
            searchActivityIntent.putExtra(Constants.KEY_BUNDLE_SEARCHTERM, query);
            searchActivityIntent.putExtra(Constants.KEY_BUNDLE_DEAL_PARAMETER_SEARCH_TEXT, query);
        }

        if (data.getQueryParameter("lat") != null && data.getQueryParameter("long") != null) {
            try {
                searchActivityIntent.putExtra(Constants.KEY_BUNDLE_LAT_LNG,
                    new LatLng(Double.valueOf(data.getQueryParameter("lat")),
                        Double.valueOf(data.getQueryParameter("long"))));
            } catch (Exception ignored) {
            }
        } else if (data.getQueryParameter("address") != null) {
            searchActivityIntent.putExtra(Constants.KEY_BUNDLE_CURRENT_ADDRESS_TEXT,
                data.getQueryParameter("address"));
        }

        searchActivityIntent.putExtra(Constants.KEY_BUNDLE_DEFAULT_SEARCH, true);
    }

    // mimics the endpoint
    public static void attachDealsV2SearchQueryParams(Uri data, Intent searchActivityIntent) {
        if (data == null || searchActivityIntent == null) return;

        IntentBuilder builder = new IntentBuilder(searchActivityIntent, data);

        builder.attachStringParam("searchText", Constants.KEY_BUNDLE_DEAL_PARAMETER_SEARCH_TEXT, true);
        builder.attachCalendarParam("createdStart", DateFormatUtils.ISO_DATE_FORMAT, Constants.KEY_BUNDLE_DEAL_PARAMETER_CREATED_START);
        builder.attachCalendarParam("createdEnd", DateFormatUtils.ISO_DATE_FORMAT, Constants.KEY_BUNDLE_DEAL_PARAMETER_CREATED_END);
        builder.attachIntegerParam("numberOfDealsPerSubscriber", Constants.KEY_BUNDLE_DEAL_PARAMETER_NUMBER_OF_DEALS_PER_SUBSCRIBER);
        builder.attachBooleanParam("limitDealsToFavoriteSubscribers", Constants.KEY_BUNDLE_DEAL_PARAMETER_LIMIT_DEALS_TO_FAVORITE_SUBSCRIBERS);
        builder.attachBooleanParam("includeUnqualified", Constants.KEY_BUNDLE_DEAL_PARAMETER_INCLUDE_UNQUALIFIED);
        builder.attachStringListParams("businessTags", Constants.KEY_BUNDLE_DEAL_PARAMETER_BUSINESS_TAGS);
        builder.attachStringListParams("dealTags", Constants.KEY_BUNDLE_DEAL_PARAMETER_DEAL_TAGS);
        builder.attachIntegerParam("skip", Constants.KEY_BUNDLE_DEAL_PARAMETER_SKIP);
        builder.attachIntegerParam("top", Constants.KEY_BUNDLE_DEAL_PARAMETER_TOP);
        builder.attachDoubleParam("priceLow", Constants.KEY_BUNDLE_DEAL_PARAMETER_PRICE_LOW);
        builder.attachDoubleParam("priceHigh", Constants.KEY_BUNDLE_DEAL_PARAMETER_PRICE_HIGH);
        builder.attachIntegerListParams("subscriberIds", Constants.KEY_BUNDLE_DEAL_PARAMETER_SUBSCRIBER_IDS);
        builder.attachIntegerListParams("masterLocationIds", Constants.KEY_BUNDLE_DEAL_PARAMETER_MASTERLOCATION_IDS);
        builder.attachStringParam("sort", Constants.KEY_BUNDLE_DEAL_PARAMETER_SORT, true);
    }

    // mimics the endpoint
    public static void attachExploreFitnessQueryParams(Uri data, Intent searchActivityIntent) {
        if (data == null || searchActivityIntent == null) return;

        IntentBuilder builder = new IntentBuilder(searchActivityIntent, data);

        builder.attachStringParam("q", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_QUERY, true);
        builder.attachStringListParams("categories", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_CATEGORIES);
        builder.attachBooleanParam("flexiblePricingOnly", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_FLEXIBLE_PRICING_ONLY);
        builder.attachBooleanParam("virtualOnly", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_VIRTUAL_ONLY);
        builder.attachBooleanParam("favoritesOnly", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_FAVORITES_ONLY);
        builder.attachBooleanParam("mindbodymembershiponly", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_FLEX_ONLY);
        builder.attachDoubleParam("lat", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_LATITUDE);
        builder.attachDoubleParam("lon", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_LONGITUDE);
        builder.attachDoubleParam("long", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_LONGITUDE);
        builder.attachDoubleParam("radius", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_RADIUS);
        builder.attachStringParam("start_time", Constants.KEY_BUNDLE_CLASS_START_TIME);
        builder.attachStringParam("end_time", Constants.KEY_BUNDLE_CLASS_END_TIME);
        builder.attachBooleanParam("showClassSearchResultList", Constants.KEY_BUNDLE_SHOW_CLASSES);
        builder.attachCommaSeparatedValuesIfPossible("categories", Constants.KEY_BUNDLE_EXPLORE_PARAMETER_CATEGORIES);
    }

    private static Intent getListIntentForSubcontroller(Context context, String subController, Integer masterLocationId, String inventorySource) {
        Intent subcontrollerIntent = null;
        switch (subController.toLowerCase()) {
            case CLASS_SUBCONTROLLER:
            case SCHEDULE_SUBCONTROLLER:
                if (FeatureFlag.CLASS_SCHEDULE_UI_REFACTOR.isFeatureEnabled()) {
                    subcontrollerIntent = BusinessScheduleActivity.newIntent(context,
                            GatewayWorkflowUtilsKt.buildLocationRefById(masterLocationId, inventorySource),
                            null, OriginComponent.USER_ACTION_LINK);
                } else {
                    subcontrollerIntent = RoutineServicesActivity.newIntent(context,
                            GatewayWorkflowUtilsKt.buildLocationRefById(masterLocationId, inventorySource),
                            null, OriginComponent.USER_ACTION_LINK);
                }
                break;
            case APPT_SUBCONTROLLER:
            case APPOINTMENT_TYPE_SUBCONTROLLER:
                subcontrollerIntent = AppointmentCategoryListActivity.newIntent(context,
                    GatewayWorkflowUtilsKt.buildLocationRefById(masterLocationId, inventorySource));
                break;
            case REVIEW_SUBCONTROLLER:
                //TODO: AB-740714 hook up gateway API and LocationReference to ReviewDetailsActivity
                subcontrollerIntent = new Intent(context, ReviewListActivity.class);
                subcontrollerIntent.putExtra(Constants.KEY_BUNDLE_MASTER_LOCATION_ID, masterLocationId);
                break;
            case GIFTCARD_SUBCONTROLLER:
                //TODO: Gateway will support Gift Cards for V3
                subcontrollerIntent = new Intent(context, GiftCardPickerActivity.class);
                subcontrollerIntent.putExtra(Constants.KEY_BUNDLE_MASTER_LOCATION_ID, masterLocationId);
                break;
            case DEAL_CONTROLLER:
                subcontrollerIntent = new Intent(context, BusinessDealsListActivity.class);
                subcontrollerIntent.putExtra(Constants.KEY_BUNDLE_MASTER_LOCATION_ID, masterLocationId);
                break;
            case PRICING_SUBCONTROLLER:
                subcontrollerIntent = new Intent(context, PricingListActivity.class);
                subcontrollerIntent.putExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE,
                    GatewayWorkflowUtilsKt.buildLocationRefById(masterLocationId, inventorySource));
                break;
            case EVENT_SUBCONTROLLER:
                subcontrollerIntent = new Intent(context, EventListActivity.class);
                subcontrollerIntent.putExtra(Constants.KEY_BUNDLE_MASTER_LOCATION_ID, masterLocationId);
                break;
            default:
        }
        return subcontrollerIntent;
    }

    public static String buildClassDetailsUri(int userId, long classVisitId, int studioId) {
        return DEEPLINK_SCHEME + "user/" + userId + "/schedule/class" + classVisitId + "?studioId=" + studioId;
    }

    public static String buildLocationPricingUri(int masterLocationId) {
        return DEEPLINK_SCHEME + LOCATION_CONTROLLER + "/" + masterLocationId + "/" + PRICING_SUBCONTROLLER;
    }

    // mindbodyconnect://deals
    public static Uri getDealsTabUri() {
        return Uri.parse(DEEPLINK_SCHEME + DEALS_CONTROLLER);
    }

    public static Uri getClassUri(int masterLocationId, Integer classid) {
        Uri.Builder link = Uri.parse(DEEPLINK_SCHEME + LOCATION_CONTROLLER).buildUpon();
        link.appendPath("" + masterLocationId)
            .appendPath(CLASS_SUBCONTROLLER);

        if (classid != null) {
            link.appendPath("" + classid);
        }

        return link.build();
    }

    public static Uri getAppointmentShareUri(int masterLocationId, Integer appointmentTypeId, String inventorySource) {
        Uri.Builder link = Uri.parse(DEEPLINK_SCHEME + LOCATION_CONTROLLER).buildUpon();
        link.appendPath((SwamisAPI.BOOKER_INVENTORY_SOURCE.equalsIgnoreCase(inventorySource) ? BOOKER_ID_PREFIX : "") + masterLocationId);
        link.appendPath(APPOINTMENT_TYPE_SUBCONTROLLER);
        if (appointmentTypeId != null) {
            link.appendPath("" + appointmentTypeId);
        }
        return link.build();
    }

    /**
     * Generates a {@link Uri} to the user's favorites
     *
     * @param tab which favorite tab to land on. See {@link com.mindbodyonline.connect.favorites.FavoritesFragment}
     * @return a link that is parsable by {@link DeepLinkUtils#deepLink(Context)}
     */
    public static Uri buildFavoritesDeepLink(@FavoritesTab int tab) {
        Uri.Builder builder = Uri.parse(DEEPLINK_SCHEME + FAVORITE_CONTROLLER)
            .buildUpon();

        if (tab == 1) {
            builder.appendPath(CLASS_SUBCONTROLLER);
        } else if (tab == 2) {
            builder.appendPath(STAFF_SUBCONTROLLER);
        }

        return builder.build();
    }

    /**
     * Generates a {@link Uri} to the user's profile
     *
     * @return a link that is parsable by {@link DeepLinkUtils#deepLink(Context)}
     */
    public static Uri buildProfileDeepLink() {
        return Uri.parse(DEEPLINK_SCHEME + USER_CONTROLLER).buildUpon()
            .appendPath(PROFILE_SUBCONTROLLER)
            .build();
    }

    /**
     * Generates a {@link Uri} to the user's passes
     *
     * @return a link that is parsable by {@link DeepLinkUtils#deepLink(Context)}
     */
    public static Uri buildPassesDeepLink() {
        return Uri.parse(DEEPLINK_SCHEME + USER_CONTROLLER).buildUpon()
            .appendPath(PROFILE_SUBCONTROLLER)
            .appendPath(PASSES_ACTION)
            .build();
    }

    /**
     * Generates a {@link Uri} to the user's schedule
     *
     * @param tab which schedule tab to land on.
     * @return a link that is parsable by {@link DeepLinkUtils#deepLink(Context)}
     */
    public static Uri buildScheduleDeepLink(int tab) {
        return Uri.parse(DEEPLINK_SCHEME + USER_CONTROLLER).buildUpon()
            .appendPath(SCHEDULE_SUBCONTROLLER)
            .appendPath(tab == 0 ? UPCOMING_SUBCONTROLLER : HISTORY_SUBCONTROLLER)
            .build();
    }

    public static Uri getActivityDashboardDeepLink() {
        return Uri.parse(DEEPLINK_SCHEME + USER_CONTROLLER).buildUpon()
            .appendPath(ACTIVITY_DASHBOARD_SUBCONTROLLER)
            .build();
    }

    /**
     * Generates a {@link Uri} to a studio
     * @param masterLocationId is the MB master location ID or the Booker location ID, set in {@link ModelTranslationKt}
     * @param inventorySource of the studio, either "MB" or "BOOKER"
     * @return a link that is parsable by {@link DeepLinkUtils#deepLink(Context)}
     */
    public static Uri getLocationShareUri(int masterLocationId, String inventorySource) {
        Uri.Builder link = Uri.parse(DEEPLINK_SCHEME + LOCATION_CONTROLLER).buildUpon();
        link.appendPath((SwamisAPI.BOOKER_INVENTORY_SOURCE.equalsIgnoreCase(inventorySource) ? BOOKER_ID_PREFIX : "") + masterLocationId);
        return link.build();
    }

    public static Uri getPricingShareUri(int masterLocationId) {
        Uri.Builder link = Uri.parse(DEEPLINK_SCHEME + LOCATION_CONTROLLER).buildUpon();
        link.appendPath("" + masterLocationId)
            .appendPath(PRICING_SUBCONTROLLER);
        return link.build();
    }

    public static Uri getDealSearchUri() {
        return Uri.parse(DEEPLINK_SCHEME + DEAL_CONTROLLER);
    }

    /**
     * Generates a {@link Uri} to a deal
     * @param masterLocationId is the MB master location ID
     * @return a link that is parsable by {@link DeepLinkUtils#deepLink(Context)}
     */
    public static Uri getProductDetailsUri(int masterLocationId, int productId) {
        return getLocationShareUri(masterLocationId, SwamisAPI.MB_INVENTORY_SOURCE).buildUpon()
            .appendPath(DEAL_CONTROLLER)
            .appendPath("" + productId)
            .build();
    }

    public static Uri getStaffDetailsUri(long masterLocationId, long staffID) {
        Uri.Builder link = Uri.parse(DeepLinkUtils.DEEPLINK_SCHEME + LOCATION_CONTROLLER).buildUpon();
        link.appendPath("" + masterLocationId)
            .appendPath(DeepLinkUtils.STAFF_SUBCONTROLLER)
            .appendPath("" + staffID);

        return link.build();
    }

    private static class IntentBuilder {
        private final Intent intent;
        private final Uri uri;

        public IntentBuilder(Intent intent, Uri uri) {
            this.intent = intent;
            this.uri = uri;
        }

        public void attachStringParam(String paramName, String key) {
            attachStringParam(paramName, key, false);
        }

        public void attachStringParam(String paramName, String key, boolean replaceAndTrim) {
            String param = uri.getQueryParameter(paramName);
            if (param != null) {
                intent.putExtra(key, replaceAndTrim ? param.replaceAll("\\+", " ") : param);
            }
        }

        public void attachCalendarParam(String paramName, DateParser parser, String key) {
            try {
                String param = uri.getQueryParameter(paramName);

                if (param != null) {
                    Calendar calendar = Calendar.getInstance();
                    Date date = parser.parse(param);

                    calendar.setTime(date);
                    intent.putExtra(key, calendar);
                }
            } catch (ParseException ignored) {
            }
        }

        public void attachIntegerParam(String paramName, String key) {
            try {
                String param = uri.getQueryParameter(paramName);
                int integer = Integer.parseInt(param);
                intent.putExtra(key, integer);
            } catch (NumberFormatException ignored) {
            }
        }

        public void attachBooleanParam(String paramName, String key) {
            String param = uri.getQueryParameter(paramName);
            if (param != null) {
                intent.putExtra(key, Boolean.valueOf(param));
            }
        }

        public void attachStringListParams(String paramName, String key) {
            List<String> params = uri.getQueryParameters(paramName);
            if (!Utils.isEmpty(params)) {
                intent.putStringArrayListExtra(key, new ArrayList<>(params));
            }
        }

        // expects format -> "[item1,item2,item3]"
        public void attachCommaSeparatedValuesIfPossible(String paramName, String key) {
            String commaSeparatedValues = uri.getQueryParameter(paramName);
            if (commaSeparatedValues == null) return;

            commaSeparatedValues = commaSeparatedValues.substring(1, commaSeparatedValues.length()-1);
            String[] params = commaSeparatedValues.split(",");
            if (!Utils.isEmpty(params)) {
                intent.putStringArrayListExtra(key, new ArrayList<>(Arrays.asList(params)));
            }
        }

        public void attachDoubleParam(String paramName, String key) {
            try {
                String param = uri.getQueryParameter(paramName);
                if (param != null) {
                    intent.putExtra(key, Double.parseDouble(param));
                }
            } catch (NumberFormatException ignored) {
            }
        }

        public void attachIntegerListParams(String paramName, String key) {
            List<String> params = uri.getQueryParameters(paramName);
            if (!Utils.isEmpty(params)) {
                ArrayList<Integer> integers = new ArrayList<>(params.size());

                for (String param : params) {
                    try {
                        Integer integer = Integer.parseInt(param);
                        integers.add(integer);
                    } catch (NumberFormatException ignored) {
                    }
                }

                intent.putExtra(key, integers);
            }
        }
    }

    public static @Nullable Uri getDeeplinkFromIntent(@NotNull Intent intent) {
        Uri uri = intent.getData();
        if (uri == null) {
            String uriString = intent.getStringExtra(DeepLinkUtils.DEEP_LINK_URI_KEY);
            if (uriString != null) {
                return Uri.parse(uriString);
            }
        }
        return uri;
    }
}
