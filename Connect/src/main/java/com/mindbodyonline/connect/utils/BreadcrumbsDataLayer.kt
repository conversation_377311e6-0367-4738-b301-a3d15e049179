package com.mindbodyonline.connect.utils

import com.mindbodyonline.connect.common.repository.BookingResult
import com.mindbodyonline.connect.common.repository.PerformanceName
import com.mindbodyonline.domain.ClassStatusMessage


sealed class BreadcrumbsDataLayerValue(val fieldKey: String, val value: Any?)
class RestPath(value: String?) : BreadcrumbsDataLayerValue("path", value)
class BreadcrumbTypeValue(value: BreadcrumbType) : BreadcrumbsDataLayerValue("breadcrumbType", value.name)
class ErrorCode(value: Int) : BreadcrumbsDataLayerValue("errorCode", value)
class ErrorMessage(value: String?) : BreadcrumbsDataLayerValue("errorMessage", value)
class ScreenClassName(value: String) : BreadcrumbsDataLayerValue("screenClassName", value)
class DeeplinkUri(value: String) : BreadcrumbsDataLayerValue("deeplinkUri", value)
class ApiPayload(value: String) : BreadcrumbsDataLayerValue("apiPayLoad", value)
class EventName(value: String) : BreadcrumbsDataLayerValue("eventName", value)
class EnabledFeatures(value: String) : BreadcrumbsDataLayerValue("enabledFeatures", value)
class ReferringParams(value: String) : BreadcrumbsDataLayerValue("referringParams", value)
class UserDataValue(value: String) : BreadcrumbsDataLayerValue("userDataValue", value)
class PerformanceTimeValue(value: Int) : BreadcrumbsDataLayerValue("performanceTime", value)
class PerformanceNameValue(value: PerformanceName) : BreadcrumbsDataLayerValue("performanceName", value.name)
class GuestUser(value: Boolean) : BreadcrumbsDataLayerValue("isGuestUser", value)
class BookingResultValue(value: BookingResult) : BreadcrumbsDataLayerValue("bookingResult", value.name)
class BookingBlockerValue(value: ClassStatusMessage) : BreadcrumbsDataLayerValue("bookingBlockerStatus", value.name)
class BookabilityScreenSource(value: String) : BreadcrumbsDataLayerValue("page_name", value) //Not my idea for the fieldKey but using it for consistency with iOS


open class DataFieldsMapping {
    lateinit var dataFields: Collection<BreadcrumbsDataLayerValue?>
    fun convertDataToMap(): Map<String, Any> {
        val dataFieldsMap = mutableMapOf<String, Any>()
        dataFields.filterNotNull().forEach { data ->
            data.value?.let {
                dataFieldsMap.put(data.fieldKey, it)
            }
        }
        return dataFieldsMap
    }
}

class ApiCallBreadcrumbData(restPath: RestPath? = null) : DataFieldsMapping() {
    init {
        dataFields = listOf(restPath, BreadcrumbTypeValue(BreadcrumbType.API_CALL))
    }
}

class DeeplinkBreadcrumbData(deepLinkString: DeeplinkUri? = null) : DataFieldsMapping() {
    init {
        dataFields = listOf(deepLinkString, BreadcrumbTypeValue(BreadcrumbType.DATA))
    }
}

class EnabledFeaturesBreadcrumbData(enabledFeatures: EnabledFeatures? = null) : DataFieldsMapping() {
    init {
        dataFields = listOf(enabledFeatures, BreadcrumbTypeValue(BreadcrumbType.FEATURE))
    }
}

class BookabilityBlockerBreadcrumbData(blockingReason: BookingBlockerValue, source: BookabilityScreenSource) : DataFieldsMapping() {
    init {
        dataFields = listOf(blockingReason, source, BreadcrumbTypeValue(BreadcrumbType.BOOKING_BLOCKER))
    }
}

class PerformanceBreadcrumbData(performanceTime: PerformanceTimeValue? = null, performanceName: PerformanceNameValue? = null,
                                additional: List<BreadcrumbsDataLayerValue>? = null) : DataFieldsMapping() {
    init {
        dataFields = listOf(performanceTime, performanceName, BreadcrumbTypeValue(BreadcrumbType.PERFORMANCE))
        additional?.forEach { dataFields += it }
    }
}

class ScreenBreadcrumbData(screenClassName: ScreenClassName? = null) : DataFieldsMapping() {
    init {
        dataFields = listOf(screenClassName, BreadcrumbTypeValue(BreadcrumbType.USER_FLOW))
    }
}

class EventBreadcrumbData(eventName: EventName? = null) : DataFieldsMapping() {
    init {
        dataFields = listOf(eventName, BreadcrumbTypeValue(BreadcrumbType.EVENT))
    }
}

class UserDataBreadcrumbData(userDataValue: UserDataValue?) : DataFieldsMapping() {
    init {
        dataFields = listOf(userDataValue, BreadcrumbTypeValue(BreadcrumbType.DATA))
    }
}

//region Trigger breadcrumbs - Events that indicate issues that should be examined further.
class ApiErrorBreadcrumbData(errorCode: ErrorCode? = null, errorMessage: ErrorMessage? = null, restPath: RestPath? = null) : DataFieldsMapping() {
    init {
        dataFields = listOf(errorCode, errorMessage, restPath, BreadcrumbTypeValue(BreadcrumbType.ERROR))
    }
}

class ApiBadRequestBreadcrumbData(errorCode: ErrorCode? = null, errorMessage: ErrorMessage? = null, restPath: RestPath? = null, payload: ApiPayload?) : DataFieldsMapping() {
    init {
        dataFields = listOf(errorCode, errorMessage, restPath, payload, BreadcrumbTypeValue(BreadcrumbType.ERROR))
    }
}

class UITimeoutBreadcrumbData(screenClassName: ScreenClassName?) : DataFieldsMapping() {
    init {
        dataFields = listOf(screenClassName, BreadcrumbTypeValue(BreadcrumbType.TIMEOUT))
    }
}

class GenericErrorData(errorMessage: ErrorMessage? = null) : DataFieldsMapping() {
    init {
        dataFields = listOf(errorMessage, BreadcrumbTypeValue(BreadcrumbType.ERROR))
    }
}

class AttributionErrorData(referringParams: ReferringParams? = null) : DataFieldsMapping() {
    init {
        dataFields = listOf(referringParams, BreadcrumbTypeValue(BreadcrumbType.ERROR))
    }
}
//end region

enum class BreadcrumbName(val displayName: String) {
    USER_FLOW("UserFlow"),
    API_ERROR("APIError"),
    GATEWAY_API("GatewayApiCall"),
    UI_TIMEOUT("UITimeout"),
    USER_EVENT("UserEvent"),
    GENERIC_ERROR("GenericError"),
    ENABLED_FEATURES("EnabledFeatures"),
    API_BAD_REQUEST("BadRequest"),
    ATTRIBUTION_LINK_ERROR("AttributionLinkError"),
    DEEPLINK_DATA("DeeplinkData"),
    IS_GUEST_DATA("IsGuestUser"),
    PERFORMANCE_METRIC("PerformanceMetric"),
    BOOKING_BLOCKER("BookingBlockerReason"),
    STRIPE_POLLING_TASK("StripePollingTask"),
    STRIPE_UPE_CHECKOUT("StripeUPECheckout"),
    STRIPE_WEB_VIEW_CHECKOUT("StripeWebViewCheckout"),
    APP_LAUNCH_STEPS("AppLaunchSteps"),
    FTC_AUDIT_LOG("FTCAuditLog"),
    TOKEN_DELEGATION("TokenDelegation"),
    AUTH_TOKEN_MISSING("AuthTokenMissing"),
    FORCED_LOGOUT("ForcedLogout"),
}

enum class BreadcrumbType { ERROR, FEATURE, API_CALL, TIMEOUT, USER_FLOW, EVENT, DATA, PERFORMANCE, BOOKING_BLOCKER }

/**
 * The name of the screen that the user is on when a bookability breadcrumb is triggered. Do not change
 * as they are consistent with iOS.
 */
enum class BookabilitySourceScreen(val screenName: String) {
    FavoriteStaffClassList("Favorite Staff Class List"),
    LocationScheduleClassList("Location Schedule Class List"),
    FavoriteClassList("Favorite Class List"),
    ExploreScreen("Explore"),
    ClassDetails("Class Details")
}

