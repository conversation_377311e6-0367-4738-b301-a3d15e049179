package com.mindbodyonline.connect.utils.api.identity.model

data class DelegationTokenRequest(
    val clientId: String,
    val clientSecret: String,
    val grantType: String = "delegation",
    val scope: String,
    val token: String
) {
    fun formParams(): Map<String, String> {
        return mapOf(
            "client_id" to clientId,
            "grant_type" to grantType,
            "scope" to scope,
            "token" to token,
            "client_secret" to clientSecret
        )
    }
}
