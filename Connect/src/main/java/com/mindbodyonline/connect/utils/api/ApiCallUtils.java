package com.mindbodyonline.connect.utils.api;

import android.net.Uri;

import com.mindbodyonline.ConnectApp;
import com.mindbodyonline.android.api.clients.MbClientsAPIManager;
import com.mindbodyonline.android.api.clients.api.MbClientsEndPoint;
import com.mindbodyonline.android.api.sales.MBSalesApi;
import com.mindbodyonline.android.api.sales.params.SalesEndpoint;
import com.mindbodyonline.android.util.log.MBLog;
import com.mindbodyonline.android.util.time.FastDateFormat;
import com.mindbodyonline.connect.utils.Endpoint;
import com.mindbodyonline.connect.utils.FamilyAccountsUtilsKt;
import com.mindbodyonline.connect.utils.Switches;
import com.mindbodyonline.connect.utils.api.dynamicpricing.DynamicPricingApi;
import com.mindbodyonline.connect.utils.time.DateFormatUtils;
import com.mindbodyonline.data.services.OAuth2Params;
import com.mindbodyonline.data.services.http.MbDataService;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Created by anthony.lipscomb on 7/4/13.
 */
public class ApiCallUtils {
    private static Endpoint endpoint = Endpoint.PRODUCTION;
    private static List<Runnable> changeEndpointListeners = new ArrayList<>();

    public static void setEndpoint(Endpoint endpoint) {
        if (ApiCallUtils.endpoint == endpoint) return;

        if (!Switches.FORCE_PRODUCTION && endpoint != null) {
            ApiCallUtils.endpoint = endpoint;

            for (Runnable runnable : changeEndpointListeners) {
                runnable.run();
            }

            //Also set the sales endpoints
            switch (endpoint) {
                case DEVELOPMENT:
                    MBSalesApi.setSalesEndpoint(SalesEndpoint.DEVELOPMENT);
                    MbClientsAPIManager.INSTANCE.setMbClientsEndPoint(MbClientsEndPoint.STAGING);
                    DynamicPricingApi.setAccessParams(OAuth2Params.LYMBER_OAUTH_DEV);
                    break;
                case PRODUCTION:
                    MBSalesApi.setSalesEndpoint(SalesEndpoint.PRODUCTION);
                    MbClientsAPIManager.INSTANCE.setMbClientsEndPoint(MbClientsEndPoint.PRODUCTION);
                    DynamicPricingApi.setAccessParams(OAuth2Params.LYMBER_OAUTH_PROD);
                    break;
                case DISASTER_RECOVERY:
                    MBSalesApi.setSalesEndpoint(SalesEndpoint.DISASTER_RECOVERY);
                    break;
                case STAGING:
                    MBSalesApi.setSalesEndpoint(SalesEndpoint.STAGING);
                    MbClientsAPIManager.INSTANCE.setMbClientsEndPoint(MbClientsEndPoint.STAGING);
                    break;
                case INTEGRATION:
                    MBSalesApi.setSalesEndpoint(SalesEndpoint.INTEGRATION);
                    break;

            }
        }
    }

    public static Endpoint getEndpoint() {
        return endpoint;
    }

    public static void addChangeEndpointListener(@NotNull Runnable changeEndpointListener) {
        ApiCallUtils.changeEndpointListeners.add(changeEndpointListener);
    }

    public static String resendUserVerificationEmailURL(long userId) {
        return String.format(Locale.US, "%srest/user/%d/verify", endpoint.connectServer, userId);
    }

    public static String getSyncUserURL(long userId) {
        return String.format(Locale.US, "%srest/user/%d/sync", endpoint.connectServer, userId);
    }

    /**
     * @param classDescriptionIds results will only include classes with one of the given
     * classDescriptionIds. Excluded from query if null
     */
    public static String getBusinessClassesUrl(int businessId, Integer userId, Calendar startDate, @Nullable Calendar endDate, int count, @Nullable int[] classDescriptionIds) {
        Uri.Builder builder = Uri.parse(String.format(Locale.US, "%srest/class", endpoint.connectServer))
            .buildUpon()
            .appendQueryParameter("locationId", String.valueOf(businessId))
            .appendQueryParameter("startRange", DateFormatUtils.ISO_DATETIME_FORMAT.format(startDate))
            .appendQueryParameter("count", String.valueOf(count));

        if (userId != null) {
            builder.appendQueryParameter("userId", String.valueOf(userId));
        }

        if (endDate != null) {
            builder.appendQueryParameter("endRange", DateFormatUtils.ISO_DATETIME_FORMAT.format(endDate));
        }

        if (classDescriptionIds != null) {
            for (int classDescriptionId : classDescriptionIds) {
                builder.appendQueryParameter("classDescriptionIds", String.valueOf(classDescriptionId));
            }
        }

        return builder.toString();
    }

    public static String getClassInformationUrl(long classInstanceId, Integer userId) {
        Uri.Builder builder = Uri.parse(String.format(Locale.US, endpoint.connectServer + "rest/class/%d",
            classInstanceId)).buildUpon();

        String selectedUserId = getSelectedUserId(userId);
        if (selectedUserId != null) {
            builder.appendQueryParameter("userId", selectedUserId);
        }

        return builder.toString();
    }

    public static String getClassPassOptionsUrl(long classInstanceId) {
        return appendDependentUserIdInUrl(String.format(Locale.US, endpoint.connectServer + "rest/class/%d/PaymentOptions",
            classInstanceId));
    }

    public static String getBillingInfoUrl(long userId) {
        return String.format(Locale.US, endpoint.connectServer + "rest/user/%s/billinginfo", getSelectedUserId((int) userId));
    }

    public static String removeBillingInfoUrl(long userId, long cardId) {
        return String.format(Locale.US, endpoint.connectServer + "rest/user/%s/billinginfo/%d", getSelectedUserId((int) userId), cardId);
    }

    public static String addClientToClassUrl(long userId) {
        int searchId = MbDataService.getSearchId();

        Uri.Builder builder = Uri.parse(String.format(Locale.US, endpoint.connectServer + "rest/user/%s/visits", getSelectedUserId((int) userId))
            + (MbDataService.isSearchIdActive() && searchId != -1 ? ("?searchId=" + searchId) : "")).buildUpon();

        return builder.toString();
    }

    public static String addClientToWaitlistUrl(long userid) {
        int searchId = MbDataService.getSearchId();
        return String.format(Locale.US, endpoint.connectServer + "rest/user/%d/waitlist", userid)
            + (MbDataService.isSearchIdActive() && searchId != -1 ? ("?searchId=" + searchId) : "");
    }

    public static String removeClientFromWaitlistUrl(long userId, long waitlistId) {
        return String.format(Locale.US, endpoint.connectServer + "rest/user/%d/waitlist/%d",
            userId, waitlistId);
    }

    public static String getProfileImageUrl(long userId) {
        return String.format(Locale.US, "%srest/user/%d/profileimage", endpoint.connectServer, userId);
    }

    public static String requestPasswordChange() {
        return endpoint.connect + "Users/ChangeRequest/Password";
    }

    public static String doesUserExistUrl(String username) {
        try {
            return String.format(Locale.US, endpoint.api + "users?email=%s", URLEncoder.encode(username, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            MBLog.e(ConnectApp.TAG, e.getMessage(), e);
        }
        return null;
    }

    public static String getReviewsBySpecificIdUrl(final long reviewId) {
        return String.format(Locale.US, endpoint.connectServer + "rest/reviews/%d", reviewId);
    }

    public static String getReviewByLocationSetUrl(final int masterLocationId, final int pageIndex, final int count,
        int[] classDescriptionIds, int[] staffIds) {

        Uri uri = Uri.parse(String.format(Locale.US, "%srest/reviews", endpoint.connectServer));
        Uri.Builder builder = uri.buildUpon();

        builder.appendQueryParameter("masterlocationid", String.valueOf(masterLocationId));
        builder.appendQueryParameter("pageindex", String.valueOf(pageIndex));
        builder.appendQueryParameter("count", String.valueOf(count));

        if (classDescriptionIds != null) {
            for (int id : classDescriptionIds) {
                builder.appendQueryParameter("classDescriptionIDs", String.valueOf(id));
            }
        }

        if (staffIds != null) {
            for (int id : staffIds) {
                builder.appendQueryParameter("staffIDs", String.valueOf(id));
            }
        }

        return builder.toString();
    }

    public static String upvoteSpecificReviewUrl(final long reviewId) {
        return String.format(Locale.US, endpoint.connectServer + "rest/reviews/%d/upvote", reviewId);
    }

    public static String downvoteSpecificReviewUrl(final long reviewId) {
        return String.format(Locale.US, endpoint.connectServer + "rest/reviews/%d/downvote", reviewId);
    }

    public static String deleteReviewUrl(final long reviewId) {
        return String.format(Locale.US, endpoint.connectServer + "rest/reviews/%d", reviewId);
    }

    public static String getUsersRelationToReviewUrl(final long reviewId) {
        return String.format(Locale.US, endpoint.connectServer + "rest/reviews/%d/status", reviewId);
    }

    public static String signClientIntoClassUrl(final long userId) {
        return String.format(Locale.US, endpoint.connectServer + "rest/user/%d/visits/signin", userId);
    }

    public static String getServiceCategoryUrl() {
        return endpoint.connectServer + "rest/servicecategory/fetchactive";
    }

    public static String getBookAppointmentUrl(int locationId) {
        int searchId = MbDataService.getSearchId();

        return String.format(Locale.US, endpoint.api + "Consumer/Schedules/Location/%d/Appointments", locationId)
            + (MbDataService.isSearchIdActive() && searchId != -1 ? ("?searchId=" + searchId) : "");
    }

    public static String getBookAppointmentUrl(int locationId, boolean isVerified, String creditCardToken) {
        int searchId = MbDataService.getSearchId();

        return String.format(
                Locale.US,
                endpoint.api + "Consumer/Schedules/Location/%d/Appointments?checkVerified=%s&creditCardTokenId=%s",
                locationId,
                isVerified,
                creditCardToken
        )
            + (MbDataService.isSearchIdActive() && searchId != -1 ? ("?searchId=" + searchId) : "");
    }

    public static String getAppointmentVisits(int userId, int count, boolean isAscending, Calendar startDateTime) {
        FastDateFormat toDateTimeFormat = DateFormatUtils.ISO_DATETIME_FORMAT;

        return String.format(Locale.US, "%srest/user/%d/appointmentVisits?count=%d&isAscending=%b&startDateTime=%s", endpoint.connectServer, userId, count, isAscending, toDateTimeFormat.format(startDateTime));

    }

    public static String getClassVisits(int userId, int count, boolean isAscending, Calendar startDateTime, boolean includeWaitlist, boolean includeEnrollments) {
        FastDateFormat toDateTimeFormat = DateFormatUtils.ISO_DATETIME_FORMAT;
        return String.format(Locale.US, "%srest/user/%d/classVisits?count=%d&isAscending=%b&startDateTime=%s&includeWaitlisted=%b&includeEnrollments=%b", endpoint.connectServer, userId, count, isAscending, toDateTimeFormat.format(startDateTime), includeWaitlist, includeEnrollments);
    }

    public static String getRemainingClientSeriesUrl(int clientId) {
        return String.format(Locale.US, endpoint.api + "Users/ClientSeries?clientId=%d", clientId);
    }

    public static String getUserSitesUrl(int userId) {
        return String.format(Locale.US, "%srest/user/%d/sites", endpoint.connectServer, userId);
    }

    public static String getClassPaymentStatusUrl(int classInstanceId, Integer userId) {
        Uri.Builder builder = Uri.parse(String.format(Locale.US,
            "%srest/class/%d/PaymentStatus", endpoint.connectServer, classInstanceId)).buildUpon();

        String selectedUserId = getSelectedUserId(userId);
        if (selectedUserId != null) {
            builder.appendQueryParameter("userId", selectedUserId);
        }

        return builder.toString();
    }

    public static String getQualifiedClassesUrl(int locationId, Integer count, int[] programIds,
        Integer classInstanceId, Calendar startDate, Calendar endDate, Integer userId) {
        Uri.Builder builder = Uri.parse(String.format(Locale.US, "%srest/class", endpoint.connectServer)).buildUpon();

        builder.appendQueryParameter("locationId", String.valueOf(locationId));

        if (userId != null) {
            builder.appendQueryParameter("userid", userId.toString());
        }

        if (count != null && count > 0) {
            builder.appendQueryParameter("count", String.valueOf(count));
        }

        if (programIds != null && programIds.length > 0) {
            for (int id : programIds) {
                builder.appendQueryParameter("ProgramIds", String.valueOf(id));
            }
        }

        if (classInstanceId != null) {
            builder.appendQueryParameter("Id", String.valueOf(classInstanceId));
        }

        if (startDate != null) {
            builder.appendQueryParameter("startRange", DateFormatUtils.ISO_DATETIME_FORMAT.format(startDate));
        }

        if (endDate != null) {
            builder.appendQueryParameter("endRange", DateFormatUtils.ISO_DATETIME_FORMAT.format(endDate));
        }

        return builder.toString();
    }

    public static String getQualifiedEnrollmentsUrl(int locationId, Integer count, int[] programIds, Integer[] productIds,
        Integer classInstanceId, Calendar startDate, Calendar endDate, Integer userId) {
        Uri.Builder builder = Uri.parse(String.format(Locale.US, "%srest/enrollment", endpoint.connectServer)).buildUpon();

        builder.appendQueryParameter("locationId", String.valueOf(locationId));

        String dependentUserId = FamilyAccountsUtilsKt.getUserIdForDependentUser();
        if (dependentUserId != null) {
            builder.appendQueryParameter("userId", dependentUserId);
        } else if (userId != null) {
            builder.appendQueryParameter("userid", userId.toString());
        }

        if (count != null && count > 0) {
            builder.appendQueryParameter("count", String.valueOf(count));
        }

        if (programIds != null && programIds.length > 0) {
            for (int id : programIds) {
                builder.appendQueryParameter("ProgramIds", String.valueOf(id));
            }
        }

        if (classInstanceId != null) {
            builder.appendQueryParameter("Id", String.valueOf(classInstanceId));
        }

        if (startDate != null) {
            builder.appendQueryParameter("startRange", DateFormatUtils.ISO_DATETIME_FORMAT.format(startDate));
        }

        if (endDate != null) {
            builder.appendQueryParameter("endRange", DateFormatUtils.ISO_DATETIME_FORMAT.format(endDate));
        }

        return builder.toString();
    }

    public static final Map<String, String> DEFAULT_AUTHORIZATION_HEADER = Collections.singletonMap(MbDataService.AUTHORIZATION_HEADER_KEY, "Basic YW5kcm9pZGNvbm5lY3Q6ZG9kZ2Vycw==");

    public static String getAllLocationsForStudio() {
        return String.format(Locale.US, "%srest/Location/", endpoint.connectServer);
    }

    public static String getEnrollmentsUrl(Integer userId, List<Integer> programIds, Long enrollmentId, Calendar startRange, Calendar endRange, Integer locationId, Integer count) {
        Uri.Builder builder = Uri.parse(String.format(Locale.US, "%srest/enrollment", endpoint.connectServer)).buildUpon();

        if (locationId != null) {
            builder.appendQueryParameter("locationId", String.valueOf(locationId));
        }
        String dependentUserId = FamilyAccountsUtilsKt.getUserIdForDependentUser();
        if (dependentUserId != null) {
            builder.appendQueryParameter("userId", dependentUserId);
        } else if (userId != null) {
            builder.appendQueryParameter("userid", String.valueOf(userId));
        }

        if (count != null && count > 0) {
            builder.appendQueryParameter("count", String.valueOf(count));
        }

        if (programIds != null && programIds.size() > 0) {
            for (int id : programIds) {
                builder.appendQueryParameter("ProgramIds", String.valueOf(id));
            }
        }

        if (enrollmentId != null) {
            builder.appendQueryParameter("Id", String.valueOf(enrollmentId));
        }

        if (startRange != null) {
            builder.appendQueryParameter("startRange", DateFormatUtils.ISO_DATETIME_FORMAT.format(startRange));
        }

        if (endRange != null) {
            builder.appendQueryParameter("endRange", DateFormatUtils.ISO_DATETIME_FORMAT.format(endRange));
        }

        return builder.toString();
    }

    public static String connectEventURL() {
        return String.format(Locale.US, "%srest/connectevent", endpoint.connectServer);
    }

    public static String getUserActionLinkUrl(String guid) {
        return String.format(Locale.US, "%srest/UserActionLink/%s/Fetch", endpoint.connectServer, guid);
    }

    public static String postUserSiteUrl(String guid) {
        return String.format(Locale.US, "%srest/UserActionLink/%s/CreateUserSiteLink", endpoint.connectServer, guid);
    }

    public static String getMobileAppVersionsUrl() {
        return endpoint.api + "mobileappversions/?product=connect&platform=android";
    }

    // The connect user agreement is always 1
    public static String getConsumerAgreementUrl() {
        return String.format(Locale.US, "%srest/documents/agreements/1/latest", endpoint.connectServer);
    }

    // The connect user agreement is always 1
    public static String postAgreementSessionStateUrl() {
        return String.format(Locale.US, "%srest/documents/agreements/sessionstate", endpoint.connectServer);
    }

    public static String getStaffForSiteUrl(long staffID) {
        return Uri.parse(endpoint.api).buildUpon()
            .appendPath("Users")
            .appendPath("Staff")
            .appendQueryParameter("request.staffIds", String.valueOf(staffID))
            .toString();
    }

    //This Post review url is used only for Enrollment/Appointment
    public static String postReviewUrl() {
        return String.format(Locale.US, "%srest/Reviews", endpoint.connectServer);
    }

    // This function will append query param userId in the request Url for dependent users only
    public static String appendDependentUserIdInUrl(String requestUrl) {
        String dependentUserId = FamilyAccountsUtilsKt.getUserIdForDependentUser();
        if (dependentUserId == null) {
            return requestUrl;
        } else {
            return Uri.parse(requestUrl).buildUpon().appendQueryParameter("userId", dependentUserId).toString();
        }
    }

    // This function will return last selected userId
    private static String getSelectedUserId(Integer userId) {
        String dependentUserId = FamilyAccountsUtilsKt.getUserIdForDependentUser();
        if (dependentUserId != null) {
            return dependentUserId;
        } else {
            return userId == null ? null : String.valueOf(userId);
        }
    }


    public static String addClientToVisitsUrl(long userId, boolean includeWaitlist, boolean includeEnrollments, boolean includeClasses, boolean includeAppointments, Calendar before) {
        try {
            FastDateFormat toDateTimeFormat = DateFormatUtils.ISO_DATETIME_TIME_ZONE_FORMAT;
            return String.format(Locale.US, endpoint.connectServer + "rest/user/%s/visits?before=%s&includeWaitlisted=%b&includeEnrollments=%b&includeClasses=%b&includeAppointments=%b", getSelectedUserId((int) userId), URLEncoder.encode(toDateTimeFormat.format(before), "UTF-8"), includeWaitlist, includeEnrollments, includeClasses, includeAppointments);
        } catch (UnsupportedEncodingException e) {
            MBLog.e(ConnectApp.TAG, e.getMessage(), e);
        }
        return null;
    }

}
