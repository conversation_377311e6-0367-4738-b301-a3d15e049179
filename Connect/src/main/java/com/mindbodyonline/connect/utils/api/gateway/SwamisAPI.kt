package com.mindbodyonline.connect.utils.api.gateway

import android.net.Uri
import androidx.annotation.StringDef
import com.android.volley.Request
import com.android.volley.Response
import com.android.volley.VolleyError
import com.google.android.gms.maps.model.LatLng
import com.google.gson.JsonObject
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.OpenWhileTesting
import com.mindbodyonline.android.util.api.request.GsonRequest
import com.mindbodyonline.android.util.api.request.MBRequest
import com.mindbodyonline.android.util.api.service.oauth.OAuthAccessToken
import com.mindbodyonline.android.util.api.service.oauth.OAuthDataService
import com.mindbodyonline.android.util.time.FastDateFormat
import com.mindbodyonline.connect.BuildConfig
import com.mindbodyonline.connect.adapters.filters.BookableOnlineClassFilter
import com.mindbodyonline.connect.adapters.filters.IFilter
import com.mindbodyonline.connect.adapters.filters.QualifiedClassFilter
import com.mindbodyonline.connect.analytics.classes.isContaining
import com.mindbodyonline.connect.common.repository.SwamiApiDataSource
import com.mindbodyonline.connect.common.utilities.toJson
import com.mindbodyonline.connect.utils.BreadcrumbsUtils
import com.mindbodyonline.connect.utils.Constants
import com.mindbodyonline.connect.utils.Constants.SORTING_OPTION_DISTANCE
import com.mindbodyonline.connect.utils.Constants.SORTING_OPTION_RATINGS
import com.mindbodyonline.connect.utils.Endpoint
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.api.ApiCallUtils
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_ATTRIBUTION
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_BILLING_INFO
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_CANCELLATION_POLICY
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_DELETE_REVIEW
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_LOCATION_APPOINTMENT_SETTINGS
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_LOCATION_DETAILS
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_POPULAR_KEY_WORDS
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_PRIVACY_PREFERENCE_URL
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_REVIEWS_BY_COURSE
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_REVIEWS_BY_LOCATION
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_REVIEWS_REPORT_ABUSE
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_REVIEW_SUMMARY_BY_COURSE
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_REVIEW_SUMMARY_BY_LOCATION
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_USER_ASSOCIATE_USERS
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_USER_PASSES
import com.mindbodyonline.connect.utils.api.PATH_GATEWAY_VALIDATE_CREDIT_CARD
import com.mindbodyonline.connect.utils.api.RecommenderType
import com.mindbodyonline.connect.utils.api.RecommenderType.LMO
import com.mindbodyonline.connect.utils.api.RecommenderType.SIMILAR_HISTORY
import com.mindbodyonline.connect.utils.api.ReviewRefType
import com.mindbodyonline.connect.utils.api.common.ApiErrorCodes
import com.mindbodyonline.connect.utils.api.common.GatewayAPIErrorInterceptor
import com.mindbodyonline.connect.utils.api.common.ResponseErrorInterceptor
import com.mindbodyonline.connect.utils.api.common.VolleyLambdaErrorInterceptor
import com.mindbodyonline.connect.utils.api.gateway.model.AppointmentAvailabilityResponse
import com.mindbodyonline.connect.utils.api.gateway.model.AppointmentBookabilityResponse
import com.mindbodyonline.connect.utils.api.gateway.model.AppointmentIdentifierRequest
import com.mindbodyonline.connect.utils.api.gateway.model.AppointmentServiceRefJson
import com.mindbodyonline.connect.utils.api.gateway.model.AppointmentServiceResponse
import com.mindbodyonline.connect.utils.api.gateway.model.AppointmentStaffResponse
import com.mindbodyonline.connect.utils.api.gateway.model.AssociatedUserResponse
import com.mindbodyonline.connect.utils.api.gateway.model.AttributionReference
import com.mindbodyonline.connect.utils.api.gateway.model.AutoCompleteDefinition
import com.mindbodyonline.connect.utils.api.gateway.model.AutoCompleteFilter
import com.mindbodyonline.connect.utils.api.gateway.model.AutoCompletePage
import com.mindbodyonline.connect.utils.api.gateway.model.BillingAttributes
import com.mindbodyonline.connect.utils.api.gateway.model.BillingResponse
import com.mindbodyonline.connect.utils.api.gateway.model.BookingConfiguration
import com.mindbodyonline.connect.utils.api.gateway.model.BookingRefJsonModel
import com.mindbodyonline.connect.utils.api.gateway.model.BookingReference
import com.mindbodyonline.connect.utils.api.gateway.model.CancellabilityResponse
import com.mindbodyonline.connect.utils.api.gateway.model.CancellationPolicyDetailsResponse
import com.mindbodyonline.connect.utils.api.gateway.model.CancellationPolicyRequest
import com.mindbodyonline.connect.utils.api.gateway.model.ClassStatusResponse
import com.mindbodyonline.connect.utils.api.gateway.model.ClassTimeInventoryReference
import com.mindbodyonline.connect.utils.api.gateway.model.ClassTimesBookabilityRequest
import com.mindbodyonline.connect.utils.api.gateway.model.ClassTimesBookabilityResponse
import com.mindbodyonline.connect.utils.api.gateway.model.DealInventoryReference
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayAutoCompleteResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayBusinessSchedulesResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayClassTimesResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayCountryResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayDealResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayEnhanceReviewsResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayFetchReviewsRequest
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayLocationResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayProvincesResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayTimeRange
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayUserInfoResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayUserPassesResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayUserPurchaseInfoResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayUserReviewsResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GenericLocationReferenceRequest
import com.mindbodyonline.connect.utils.api.gateway.model.GeolocationResponse
import com.mindbodyonline.connect.utils.api.gateway.model.InventoryRefJsonModel
import com.mindbodyonline.connect.utils.api.gateway.model.InventoryReference
import com.mindbodyonline.connect.utils.api.gateway.model.JsonApiDocumentSingle
import com.mindbodyonline.connect.utils.api.gateway.model.JsonResource
import com.mindbodyonline.connect.utils.api.gateway.model.LiabilityReleaseResponse
import com.mindbodyonline.connect.utils.api.gateway.model.LocationAppointmentSettingsRequest
import com.mindbodyonline.connect.utils.api.gateway.model.LocationAppointmentSettingsResponse
import com.mindbodyonline.connect.utils.api.gateway.model.LocationDetailsRequest
import com.mindbodyonline.connect.utils.api.gateway.model.LocationDetailsResponse
import com.mindbodyonline.connect.utils.api.gateway.model.OrderBookingItem
import com.mindbodyonline.connect.utils.api.gateway.model.OrderPaymentRequestObject
import com.mindbodyonline.connect.utils.api.gateway.model.OrderResponse
import com.mindbodyonline.connect.utils.api.gateway.model.PageAttributes
import com.mindbodyonline.connect.utils.api.gateway.model.PaymentInstrumentRequestObject
import com.mindbodyonline.connect.utils.api.gateway.model.PaymentMethodConfigurationResponse
import com.mindbodyonline.connect.utils.api.gateway.model.PaymentMethodCreditCardsResponse
import com.mindbodyonline.connect.utils.api.gateway.model.PaymentMethodGiftCardsResponse
import com.mindbodyonline.connect.utils.api.gateway.model.PaymentMethodPassesResponse
import com.mindbodyonline.connect.utils.api.gateway.model.PaymentMethodsClassPassRequest
import com.mindbodyonline.connect.utils.api.gateway.model.PaymentMethodsPassRequest
import com.mindbodyonline.connect.utils.api.gateway.model.PopularKeywordsResponse
import com.mindbodyonline.connect.utils.api.gateway.model.PrivacyPreferenceUrlResponse
import com.mindbodyonline.connect.utils.api.gateway.model.PurchaseItem
import com.mindbodyonline.connect.utils.api.gateway.model.PurchaseOptionResponse
import com.mindbodyonline.connect.utils.api.gateway.model.ResponseAttributes
import com.mindbodyonline.connect.utils.api.gateway.model.ReviewFilterAttributes
import com.mindbodyonline.connect.utils.api.gateway.model.ReviewPromptRequest
import com.mindbodyonline.connect.utils.api.gateway.model.ReviewPromptResponse
import com.mindbodyonline.connect.utils.api.gateway.model.ReviewSummaryResponse
import com.mindbodyonline.connect.utils.api.gateway.model.ScheduleCancellableResponse
import com.mindbodyonline.connect.utils.api.gateway.model.SearchClassTimesDefinition
import com.mindbodyonline.connect.utils.api.gateway.model.SearchClassTimesPageDefinition
import com.mindbodyonline.connect.utils.api.gateway.model.SinglePurchaseOptionResponse
import com.mindbodyonline.connect.utils.api.gateway.model.UserBookingsResponse
import com.mindbodyonline.connect.utils.api.gateway.model.UserInfoRequestObject
import com.mindbodyonline.connect.utils.api.gateway.model.ValidateCreditCardRequest
import com.mindbodyonline.connect.utils.api.gateway.model.ValidateCreditCardResponse
import com.mindbodyonline.connect.utils.api.gateway.model.VisitReviewDefinition
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationRefJson
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference
import com.mindbodyonline.connect.utils.getGuestUserId
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.data.services.OAuth2Params
import com.mindbodyonline.data.services.http.MbDataService
import com.mindbodyonline.data.services.http.MbDataService.AUTHORIZATION_HEADER_KEY
import com.mindbodyonline.data.services.http.MbDataService.BEARER_TOKEN_PREFIX
import com.mindbodyonline.data.services.http.MbDataService.GUEST_USER_ID_HEADER_KEY
import com.mindbodyonline.domain.BaseVisit
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.Error
import com.mindbodyonline.domain.apiModels.VisitReview
import com.mindbodyonline.framework.abvariant.ABHelperUtils
import com.mindbodyonline.framework.abvariant.DevelopmentFlag
import com.mindbodyonline.framework.abvariant.FeatureFlag
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone

private const val FILTER_REQUEST_TYPE_QUERY_PARAM = "filter.request_type"
private const val FILTER_REQUEST_TARGET_QUERY_PARAM = "filter.request_target"
private const val FILTER_REQUEST_TYPE_QUERY_VALUE = "MbUserPreferencesPage"
private const val FILTER_REQUEST_TARGET_QUERY_VALUE = "PreferencesPage"

@Suppress("unused")
@OpenWhileTesting
class SwamisAPI : GeolocationService, SwamiApiDataSource {

    companion object {
        private const val GATEWAY_ENDPOINT_DEV = "https://dev-mkt-gateway.mindbody.io"
        private const val GATEWAY_ENDPOINT_STAGING = "https://stg-mkt-gateway.mindbody.io"
        private const val GATEWAY_ENDPOINT_PROD = "https://prod-mkt-gateway.mindbody.io"
        private const val WAITLIST_URI_PATH = "v1/waitlist"

        @Retention(AnnotationRetention.SOURCE)
        @StringDef(MB_INVENTORY_SOURCE, BOOKER_INVENTORY_SOURCE)
        annotation class InventorySource

        //Search constants
        const val MB_INVENTORY_SOURCE = "MB" //We may want to move this once we have what API wants for Booker source
        const val BOOKER_INVENTORY_SOURCE = "BOOKER"
        const val SEARCH_SORT_DISTANCE = "distance"
        const val SEARCH_SORT_SCORE = "score"
        private const val SEARCH_SORT_AVERAGE_RATING = "averageRating"
        private const val SEARCH_SORT_START_TIME = "start_time"
        private const val EMPTY = ""
        const val PARAMETER_MODIFIER_DESCENDING = "-"
        private const val COMMA_DISTANCE = ",distance"
        private const val DISTANCE_COMMA = "distance,"
        private const val RELEVANCE_SORT_VALUE = "-score,start_time,distance"
        private const val RATINGS_SORT_VALUE = "-averageRating,start_time,-score"
        private const val DISTANCE_SORT_VALUE = "distance,start_time,-score"

        private const val LOCATION_REF_JSON = "location_ref_json"
        private const val START_TIME_FROM = "start_time_from"
        private const val START_TIME_TO = "start_time_to"
        private const val ONLINE_BOOKABLE = "online_bookable"
        private const val BOOKABLE_WITH_PASSES = "bookable_with_passes"

        private val ISO_8601 = FastDateFormat.getInstance(
                "yyyy-MM-dd'T'HH:mm:ss'Z'", TimeZone.getTimeZone("UTC"))
        private val ISO_DATETIME_FORMAT = FastDateFormat.getInstance(
                "yyyy-MM-dd'T'HH:mm:ss", Locale.US)

        @JvmStatic
        var INSTANCE = SwamisAPI().apply {
            oauthService.setTokenRefreshDelegate(MbDataService.getServiceInstance())
        }
            @JvmName("getInstance")
            get
            @JvmName("setInstance")
            set
    }

    init {
        ApiCallUtils.addChangeEndpointListener {
            currentEndpoint = determineCurrentEndpoint()
        }
    }

    private var currentEndpoint = determineCurrentEndpoint()

    // Use the MBDataService request queue to get built-in chuck and stetho
    private var requestQueue = MbDataService.getServiceInstance().requestQueue

    // This oauthService shares token management with our main code base
    private val oauthService = OAuthDataService.getInstance(OAuth2Params.GATEWAY_OAUTH, requestQueue, ConnectApp.getInstance())

    /**
     * There is no staging or integration endpoint, so use production outside of dev.
     *
     * Note that these endpoints will eventually be replaced with a single endpoint that routes
     * to each service behind the scenes.
     */
    private fun determineCurrentEndpoint() = when (ApiCallUtils.getEndpoint()) {
        Endpoint.DEVELOPMENT -> Uri.parse(GATEWAY_ENDPOINT_DEV)
        Endpoint.STAGING -> Uri.parse(GATEWAY_ENDPOINT_STAGING)
        else -> Uri.parse(GATEWAY_ENDPOINT_PROD)
    }

    /**
     * The base headers to be sent along with every request.  Swamis API requires no authentication,
     * but we are tracking the client in Newrelic with these base headers.
     *
     * Use this method for all requests that do not have a body.  Otherwise, the content type
     * will need to be specified, as in [getContentHeaders].
     */
    private fun getBaseHeaders() = mutableMapOf(
            "X-MB-APP-NAME" to MbDataService.USER_AGENT_HEADER_VALUE,
            "X-MB-APP-VERSION" to BuildConfig.VERSION_NAME,
            "X-MB-APP-BUILD" to BuildConfig.VERSION_CODE.toString(),
            "Accept" to "application/vnd.api+json",
            "charset" to "utf-8"
    ).apply {
        if (MBAuth.isGuestUser()) {
            put(GUEST_USER_ID_HEADER_KEY, getGuestUserId())
        } else {
            MBAuth.getUserToken()?.let { token -> put(AUTHORIZATION_HEADER_KEY, BEARER_TOKEN_PREFIX + token.accessToken) }
        }
    }

    /**
     * This function will add selected userId to be sent for family accounts
     */
    private fun addFamilyAccountsHeaders(headersMap: MutableMap<String, String>) = headersMap.apply {
        if (!MBAuth.isGuestUser() && SharedPreferencesUtils.getSelectedUserId().isNotEmpty()) {
            put("X-MB-ON-BEHALF-OF", SharedPreferencesUtils.getSelectedUserId())
        }
    }

    /**
     * Swamis API does not interpret JSON requests correctly in some cases, so we have
     * to manually declare the Content-Type here to amend that.
     *
     * Use this for all JSON-formatted requests that are sent.
     */
    private fun getContentHeaders() = getBaseHeaders().apply {
        put("Content-Type", "application/json")
    }

    /**
     * Sets the MB User ID in order to get this user's ranked results according to the AI/ML Recommender.
     */
    private fun getRecommenderHeaders() = getContentHeaders().apply {
        this["X-MB-USER-SESSION-ID"] = MBAuth.getUser()?.id.toString()
    }

    /**
     * For use in allowing the client to control the authentication token that this API
     * instance will use for authenticated requests.
     */
    fun setToken(token: OAuthAccessToken?) {
        oauthService.token = token
    }

    /**
     * This should be the default method to handle all tokenized requests.  If the user is in
     * guest mode, we should not be providing a guest token to those APIs, since the gateway API
     * will handle anonymous access for us automatically.
     */
    private fun <T> attachTokenIfNotGuestAndSend(request: Request<T>) {
        BreadcrumbsUtils.logWithBreadcrumbNewRelicEventForcedLogoutWithMetadata(
            mapOf(
                "url" to request.url,
                "is_guest_user" to MBAuth.isGuestUser(),
                "is_user_available" to (MBAuth.getUser() != null),
                "is_logged_in" to MBAuth.isLoggedIn(),
                "token_needs_refresh" to MBAuth.getUserToken()?.needsRefresh(),
            ),
            "attach_token_if_not_guest_swamis_api",
            0,
        )
        if (MBAuth.isGuestUser() || !MBAuth.isLoggedIn()) {
            requestQueue.add(request)
        } else {
            oauthService.wrapAndSendRequest(request)
        }
    }

    /**
     * Retrieves a geolocation response, given some or no criteria.  If no parameters
     * are supplied, a geolocation response will be issued given the user's
     * IP address.
     *
     * @param latLon an optional lat/lon to retrieve geolocation data from
     * @param query an optional query to search upon for geolocation data
     * @param successListener success callback
     * @param errorListener failure callback
     */
    override fun geolocate(latLon: LatLng?,
                           query: String?,
                           successListener: ((GeolocationResponse?) -> Unit)?,
                           errorListener: ((Throwable?) -> Unit)?) {
        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/geolocate").also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        latLon?.let {
            url.appendQueryParameter("filter.latitude", latLon.latitude.toString())
            url.appendQueryParameter("filter.longitude", latLon.longitude.toString())
        }

        query?.let {
            url.appendQueryParameter("filter.q", it)
        }

        val req = GsonRequest(Request.Method.GET, url.toString(), GeolocationResponse::class.java,
            getBaseHeaders(), successListener,
            ResponseErrorInterceptor(passedValue = errorListener, url = url.toString())
        )

        requestQueue.add(req)
    }

    //region Class information APIs
    /**
     * Search interface for the AWS ClassTimes endpoint.  Searches upon all available classes
     * based on criteria provided by the client.  Basically a proxy for our old CloudSearchUtils
     * methods of querying DSPOs.  This allows us to search without embedding an AWS client and
     * secret within our client application.
     *
     * @param page (Optional) Page of results to return from paginated set.
     *             Default value : 1
     * @param radiusInMeters Radius in meters. Example: 40233.6 (25mi in m).
     *                       Default value : 16093.44
     * @param latitude Latitude for search center. Example: 32.9194673.
     * @param longitude Longitude for search center. Example: -117.2365862.
     * @param query (Optional) Search text
     * @param categories (Optional) Categories to include in results
     * @param subcategories (Optional) Subcategories to include in results
     * @param inventory (Optional) Default includes all inventory
     *                  Available values : MB, Booker
     * @param startTimeRanges (Optional) ISO8601 UTC timestamp ranges specifing the earliest startTime
     *                  until latest startTime search criteria.
     * @param dynamicPricingOnly (Optional) Filters by dynamic priceability. Boolean values correspond to:
     *                         any (does not filter by dynamic priceability), only (only dynamic pricing
     *                         inventory), and exclude (only non-dynamic priceable inventory).
     *                         Available values : any, only, exclude
     *                         Default value : any
     * @param includePricingTokens whether or not to include the DSPO tokens as part of the response.
     *                             defaults to false.
     * @param virtualOnly whether or not to return only virtual (online) classes
     * @param flexOnly whether or not to return only flex eligible classes
     * @param recommenderType determines which recommender type (if needed) to request from AI/ML Recommender
     */
    fun searchClasses(
            page: Int = 1,
            pageSize: Int = SearchClassTimesPageDefinition.DEFAULT_PAGE_SIZE,
            radiusInMeters: Number? = null,
            latitude: Number? = null,
            longitude: Number? = null,
            query: String? = null,
            categories: List<String>? = null,
            subcategories: List<String>? = null,
            inventory: List<InventoryReference>? = null,
            startTimeRanges: List<Pair<Calendar, Calendar?>>? = null,
            dynamicPricingOnly: Boolean = false,
            includePricingTokens: Boolean? = null,
            virtualOnly: Boolean = false,
            // Flex Deprecation changes - This value is no longer used in this function, so passing it does not affect the logic.
            flexOnly: Boolean = false,
            category_types: List<String>? = null,
            recommenderType: RecommenderType? = null,
            sortType: String = Constants.SORTING_OPTION_RECOMMENDED,
            successListener: ((GatewayClassTimesResponse) -> Unit)? = null,
            errorListener: (VolleyError) -> Unit): MBRequest<GatewayClassTimesResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/search/class_times")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val body = SearchClassTimesDefinition(SearchClassTimesPageDefinition(size = pageSize, number = page))

        // These are required
        body.filter.radius = radiusInMeters?.toLong()
        body.filter.latitude = latitude?.toDouble()
        body.filter.longitude = longitude?.toDouble()

        categories?.let { body.filter.categories = it.toTypedArray() }
        subcategories?.let { body.filter.subcategories = it.toTypedArray() }
        inventory?.let {
            val listOfLocationReferences = mutableListOf<String>()
            it.forEach { inventoryReference ->
                inventoryReference.inventory_ref_jsons?.toList()?.let { listOfLocationReferences.addAll(it) }
            }
            body.filter.inventory_ref_jsons = listOfLocationReferences.toTypedArray()
        }
        startTimeRanges?.let {
            body.filter.start_time_ranges = startTimeRanges.map {
                GatewayTimeRange(ISO_8601.format(
                        it.first.timeInMillis),
                        it.second?.let { endTime -> ISO_8601.format(endTime.timeInMillis) })
            }.toTypedArray()
        }

        category_types?.let { body.filter.category_types = it.toTypedArray() }

        when (recommenderType) {
            SIMILAR_HISTORY -> body.filter.recommender_ranking_type = "similar_history"
            LMO -> {
                body.filter.dynamic_priceable = "only"
                body.filter.include_dynamic_pricing = "true"
                body.filter.recommender_ranking_type = "lmo"
            }
            else -> {
                query?.takeIf { it.isNotBlank() }?.let { body.filter.term = it }
                body.filter.dynamic_priceable = if (dynamicPricingOnly) "only" else "any"
                body.filter.virtual = if (virtualOnly) "only" else "any"
                body.filter.cm_membership_bookable = "any"
                includePricingTokens?.let { body.filter.include_dynamic_pricing = it.toString() }
            }
        }

        val sortValue = when (sortType) {
                SORTING_OPTION_DISTANCE -> if (latitude != null) DISTANCE_SORT_VALUE else DISTANCE_SORT_VALUE.replace(DISTANCE_COMMA, "")
                SORTING_OPTION_RATINGS -> RATINGS_SORT_VALUE
                else -> if (latitude != null) RELEVANCE_SORT_VALUE else RELEVANCE_SORT_VALUE.replace(COMMA_DISTANCE, "")
        }

        body.sort = sortValue
        val headers = addFamilyAccountsHeaders(if (recommenderType == null) getContentHeaders() else getRecommenderHeaders())

        val req = GsonRequest(Request.Method.POST,
                url.toString(),
                GatewayClassTimesResponse::class.java,
                headers,
                body.toJson(), successListener, VolleyLambdaErrorInterceptor(passedLambda = errorListener,
                url = url.toString())).apply { setShouldCache(false) }
        requestQueue.add(req)
        return req
    }

    /**
     * Endpoint to fetch schedule data i.e. scheduled classes and enrollments for a given business.
     *
     * @param startTime ISO8601 UTC timestamp specifying the start time from which the schedule data is requested.
     * @param endTime ISO8601 UTC timestamp specifying the end time till which the schedule data is requested.
     * @param locationReference business' location reference object for which the schedule is being requested.
     */
    fun fetchBusinessSchedules(
        startTime: String,
        endTime: String,
        locationReference: LocationRefJson?,
        filters: MutableSet<IFilter<ClassTypeObject>>?,
        successListener: ((GatewayBusinessSchedulesResponse) -> Unit)? = null,
        errorListener: (VolleyError) -> Unit
    ): MBRequest<GatewayBusinessSchedulesResponse> {
        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/location/schedules")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val headers = getRecommenderHeaders()

        val body = mapOf(
            LOCATION_REF_JSON to locationReference.toJson(),
            START_TIME_FROM to startTime,
            START_TIME_TO to endTime,
            ONLINE_BOOKABLE to if (filters?.isContaining(BookableOnlineClassFilter()) == true) "only" else "any",
            BOOKABLE_WITH_PASSES to if (filters?.isContaining(QualifiedClassFilter()) == true) "only" else "any",
        )
        val req = GsonRequest(
            Request.Method.POST,
            url.toString(),
            GatewayBusinessSchedulesResponse::class.java,
            headers,
            body.toJson(),
            successListener,
            ResponseErrorInterceptor(errorListener)
        )

        requestQueue.add(req)
        return req
    }

    /**
     * Convenience method to get a studio's class schedule from our gateway / cloudsearch endpoints.
     * This will be used to populate the pricing information and eventually hopefully the class
     * list information as well.
     */
    override fun getClassPricingInformation(
            inventory: List<InventoryReference>?,
            successListener: ((GatewayClassTimesResponse) -> Unit)?,
            errorListener: Response.ErrorListener): MBRequest<GatewayClassTimesResponse> {
        return searchClasses(
                inventory = inventory, includePricingTokens = true,
                successListener = successListener, errorListener = {
            errorListener.onErrorResponse(it)
        })
    }

    /**
     * Fetches bookability statuses for a given location's class times. Uses [locationReference] to
     * lookup the location, and optional class times rangte from [startTimeFrom] to [startTimeTo].
     * If [startTimeTo] is not provided, Gateway returns the range from [startTimeFrom] to the current time.
     * If [startTimeTo] is provided, a value for [startTimeFrom] must be provided.
     */
    fun getClassTimesBookability(
            locationReference: LocationReference,
            startTimeFrom: Calendar?,
            startTimeTo: Calendar?,
            successListener: ((ClassTimesBookabilityResponse) -> Unit)? = null,
            errorListener: ((Throwable) -> Unit)?): MBRequest<ClassTimesBookabilityResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/class_times/bookability")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val body = ClassTimesBookabilityRequest(location_ref_json = locationReference.location_ref_json)

        when {
            startTimeFrom != null && startTimeTo != null -> {
                body.start_time_from = ISO_8601.format(startTimeFrom.timeInMillis)
                body.start_time_to = ISO_8601.format(startTimeTo.timeInMillis)
            }
            startTimeFrom != null && startTimeTo == null -> body.start_time_from = ISO_8601.format(startTimeFrom.timeInMillis)
        }

        val req = GsonRequest(
            Request.Method.POST,
            url.toString(),
            ClassTimesBookabilityResponse::class.java,
            getContentHeaders(),
            body.toJson(),
            successListener,
            ResponseErrorInterceptor(passedValue = errorListener, url = url.toString())
        )

        attachTokenIfNotGuestAndSend(req)
        return req
    }

    /**
     * Swamis API call to return the status of a class instance
     *
     * @param successListener success callback which will return a ClassStatusResponse containing the ClassStatusAttributes
     * @param errorListener failure callback
     */
    fun getClassStatus(
            inventorySource: String? = null,
            inventoryReference: String?,
            authorizedUser: Boolean? = null,
            successListener: ((ClassStatusResponse) -> Unit)? = null,
            errorListener: ((Throwable) -> Unit)? = null): MBRequest<ClassStatusResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/class/status")
            .appendQueryParameter("filter.inventory_source", inventorySource)
            .appendQueryParameter("filter.inventory_ref_json", inventoryReference).also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        authorizedUser?.let { url.appendQueryParameter("filter.authorized_user", it.toString()) }

        val req = GsonRequest(Request.Method.GET, url.toString(), ClassStatusResponse::class.java,
                getBaseHeaders(), successListener,
                ResponseErrorInterceptor(passedValue = { errorListener?.invoke(it) }, url = url.toString()))

        attachTokenIfNotGuestAndSend(req)
        return req
    }

    /**
     * Swamis API call to get alacarte purchase options
     */
    fun getDealPurchaseOption(
            locationRefJson: String,
            passRefJson: String,
            successListener: ((SinglePurchaseOptionResponse) -> Unit)? = null,
            errorListener: ((Throwable) -> Unit)?): MBRequest<SinglePurchaseOptionResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/deals/purchase_option")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val body = DealInventoryReference(locationRefJson, passRefJson)

        val request = GsonRequest(Request.Method.POST, url.toString(), SinglePurchaseOptionResponse::class.java,
                getBaseHeaders(), body.toJson(), successListener,
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to get class purchase options
     */
    fun getClassPurchaseOptions(
            locationRefJson: String,
            classTimeRefJson: String,
            successListener: ((PurchaseOptionResponse) -> Unit)? = null,
            errorListener: ((Throwable) -> Unit)?): MBRequest<PurchaseOptionResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/class_times/purchase_options")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val body = ClassTimeInventoryReference(locationRefJson, classTimeRefJson)

        val request = GsonRequest(Request.Method.POST, url.toString(), PurchaseOptionResponse::class.java,
                getBaseHeaders(), body.toJson(), successListener,
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }
    //endregion

    //region Location information APIs
    /**
     * Search endpoint against swamis pointing to the location database in AWS.  This is essentially
     * an identical call to [searchClasses], but the object returned has aggregated location
     * information.
     *
     * @param page (Optional) Page of results to return from paginated set.
     *             Default value : 1
     * @param radiusInMeters Radius in meters. Example: 40233.6 (25mi in m).
     *                       Default value : 16093.44
     * @param latitude Latitude for search center. Example: 32.9194673.
     * @param longitude Longitude for search center. Example: -117.2365862.
     * @param query (Optional) Search text
     * @param categories (Optional) Categories to include in results
     * @param subcategories (Optional) Subcategories to include in results
     * @param inventory (Optional) Default includes all inventory
     *                  Available values : MB, Booker
     */
    override fun searchLocations(
            page: Int,
            pageSize: Int,
            radiusInMeters: Number?,
            latitude: Number?,
            longitude: Number?,
            query: String?,
            categories: List<String>?,
            categoryTypes: List<String>?,
            subcategories: List<String>?,
            inventory: List<InventoryReference>?,
            sortType: String,
            successListener: ((GatewayLocationResponse) -> Unit)?,
            errorListener: (VolleyError) -> Unit): MBRequest<GatewayLocationResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/search/locations")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val body = SearchClassTimesDefinition(SearchClassTimesPageDefinition(size = pageSize, number = page))

        // These are required
        body.filter.radius = radiusInMeters?.toLong()
        body.filter.latitude = latitude?.toDouble()
        body.filter.longitude = longitude?.toDouble()

        if (body.filter.latitude != null) {
            val sortValue = when (sortType) {
                SORTING_OPTION_RATINGS -> "$PARAMETER_MODIFIER_DESCENDING$SEARCH_SORT_AVERAGE_RATING,$PARAMETER_MODIFIER_DESCENDING$SEARCH_SORT_SCORE"
                SORTING_OPTION_DISTANCE -> "$SEARCH_SORT_DISTANCE,$PARAMETER_MODIFIER_DESCENDING$SEARCH_SORT_SCORE"
                //We want distance as the default sort, and can only be used when a lat/lon is provided
                else -> if (query?.isNotBlank() == true)
                    "$PARAMETER_MODIFIER_DESCENDING$SEARCH_SORT_SCORE,$SEARCH_SORT_DISTANCE"
                else SEARCH_SORT_DISTANCE
            }
            body.sort = sortValue
        }

        query?.takeIf { it.isNotBlank() }.let { body.filter.term = it }
        categories?.let { body.filter.categories = it.toTypedArray() }
        categoryTypes?.let { body.filter.category_types = it.toTypedArray() }
        subcategories?.let { body.filter.subcategories = it.toTypedArray() }
        inventory?.let { body.filter.inventory_refs = it.toTypedArray() }

        val req = GsonRequest(Request.Method.POST, url.toString(),
                getContentHeaders(), GatewayLocationResponse::class.java, body.toJson(), successListener,
                VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        requestQueue.add(req)
        return req
    }

    /**
     * Swamis API call to get a list of favorite businesses for a user
     *
     * @param successListener success callback - Void return type
     * @param errorListener failure callback - Void return type
     */
    fun getFavoriteLocations(
            successListener: ((GatewayLocationResponse) -> Unit)? = null,
            errorListener: (VolleyError) -> Unit): MBRequest<GatewayLocationResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/user/favorite_locations")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val req = GsonRequest(Request.Method.GET, url.toString(),
                getBaseHeaders(), GatewayLocationResponse::class.java, null, successListener,
                VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString())).apply { setShouldCache(false) }

        attachTokenIfNotGuestAndSend(req)
        return req
    }

    /**
     * Swamis API call to delete a favorite business
     *
     * @param inventoryReference - the location inventory reference
     * @param successListener success callback - Void return type
     * @param errorListener failure callback - Void return type
     */
    fun removeFavoriteLocation(
            inventoryReference: LocationReference,
            successListener: Response.Listener<Void>? = null,
            errorListener: Response.ErrorListener): MBRequest<Void> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/user/favorite_locations")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val request = GsonRequest(Request.Method.DELETE, url.toString(), Void::class.java,
                getBaseHeaders(), inventoryReference.toJson(), successListener,
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swami API call to add a favorite business
     *
     * @param inventoryReference - The location inventory reference
     * @param successListener success callback - Void return type
     * @param errorListener failure callback - Void return type
     */
    fun addFavoriteLocation(
            inventoryReference: LocationReference,
            successListener: Response.Listener<Void>? = null,
            errorListener: Response.ErrorListener): MBRequest<Void> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/user/favorite_locations")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val request = GsonRequest(Request.Method.POST, url.toString(), Void::class.java,
                getBaseHeaders(), inventoryReference.toJson(), successListener,
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }
    //endregion

    /**
     * API to get the user's booking schedule (either past or future).
     *
     * Requests the user's schedule, which allows you to specify:
     * - whether or not to [includeWaitlisted]
     * - whether or not to [includeClasses] (also includes enrollments)
     * - whether or not to [includeAppointments]
     * - whether or not the result set [isAscendingOrder].  Ascending will mean from earliest date to latest.
     * - whether to only return results [before] a given date
     * - whether to only return results [after] a given date
     * - returns [size] results, gateway default and max value is 100
     */
    fun getUserBookings(
            inventorySource: String? = null,
            includeWaitlisted: Boolean? = null,
            includeClasses: Boolean? = null,
            includeAppointments: Boolean? = null,
            isAscendingOrder: Boolean? = null,
            before: Calendar? = null,
            after: Calendar? = null,
            size: Int? = null,
            successListener: ((UserBookingsResponse) -> Unit)? = null,
            errorListener: (VolleyError) -> Unit): MBRequest<UserBookingsResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/user/bookings").also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        inventorySource?.let { url.appendQueryParameter("filter.inventory_source", it) }
        includeWaitlisted?.let { url.appendQueryParameter("filter.include_waitlist", it.toString()) }
        includeClasses?.let { url.appendQueryParameter("filter.include_classes", it.toString()) }
        includeAppointments?.let { url.appendQueryParameter("filter.include_appointments", it.toString()) }
        isAscendingOrder?.let { url.appendQueryParameter("filter.ascending", it.toString()) }
        before?.let { url.appendQueryParameter("filter.before", ISO_8601.format(it)) }
        after?.let { url.appendQueryParameter("filter.after", ISO_8601.format(it)) }
        size?.let { url.appendQueryParameter("page.size", it.toString()) }

        val req = GsonRequest(Request.Method.GET, url.toString(),
                addFamilyAccountsHeaders(getBaseHeaders()), UserBookingsResponse::class.java, null, successListener,
                VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(req)
        return req
    }
    //endregion

    //region User info APIs
    /**
     * Swamis API call to get a user's profile info
     *
     * @param successListener Success callback that will return a UserInfoAttributes model
     * @param errorListener Failure callback that will return a VolleyError
     */
    fun getUserInfo(
            successListener: ((GatewayUserInfoResponse) -> Unit)? = null,
            errorListener: (VolleyError) -> Unit): MBRequest<GatewayUserInfoResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/user/info")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val headers = getBaseHeaders()
        if (interceptAndStopRequest(url.toString(), headers)) {
            errorListener(VolleyError("Missing Authorization or X-MB-GUEST-USER-ID in headers"))
            return GsonRequest(
                Request.Method.GET,
                url.toString(),
                GatewayUserInfoResponse::class.java,
                headers,
                successListener,
                errorListener
            )
        }

        val req = GsonRequest(Request.Method.GET, url.toString(),
                headers, GatewayUserInfoResponse::class.java, successListener,
                VolleyLambdaErrorInterceptor(passedLambda = errorListener,
                        passThroughCodes = listOf(ApiErrorCodes.UNAUTHORIZED_ACCESS), url = url.toString()))

        // We may not have the user object yet, so we need to keep this as oauthService.wrap()
        oauthService.wrapAndSendRequest(req)
        return req
    }

    /**
     * Swamis API call to save the cached user's profile
     *
     * @param user UserInfoAttributes object to save to user's profile
     * @param successListener Success callback returning Void (server response has no body)
     * @param errorListener Failure callback that will return a Volley Response.ErrorListener
     */
    fun saveUserInfo(
            user: UserInfoRequestObject,
            successListener: Response.Listener<Void>?,
            errorListener: Response.ErrorListener): MBRequest<Void> {

        val body = JsonApiDocumentSingle(data = JsonResource(type = "users", attributes = user))

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/user")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val request = GsonRequest(Request.Method.PUT, url.toString(), Void::class.java, getBaseHeaders(),
                body.toJson(), successListener,
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        // We may not have the user object yet, so we need to keep this as oauthService.wrap()
        oauthService.wrapAndSendRequest(request)
        return request

    }

    /**
     * Swamis API call to get a user's purchase information for a specific studio using [locationReference].
     * For example if they are purchasing at that studio for the first time
     *
     * @param successListener Success callback that will return a UserPurchaseInfoAttributes model
     * @param errorListener Failure callback that will return a VolleyError
     *
     * @return [GatewayUserPurchaseInfoResponse]
     */
    fun getUserPurchaseInfo(
            locationReference: LocationReference,
            successListener: ((GatewayUserPurchaseInfoResponse) -> Unit)? = null,
            errorListener: (VolleyError) -> Unit): MBRequest<GatewayUserPurchaseInfoResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/user/purchase_info")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val body = GenericLocationReferenceRequest(location_ref_json = locationReference.location_ref_json)
        val req = GsonRequest(Request.Method.POST, url.toString(), GatewayUserPurchaseInfoResponse::class.java,
                getContentHeaders(), body.toJson(), successListener,
                VolleyLambdaErrorInterceptor(passedLambda = errorListener,
                        passThroughCodes = listOf(ApiErrorCodes.UNAUTHORIZED_ACCESS),
                        url = url.toString()))

        oauthService.wrapAndSendRequest(req)
        return req
    }

    //endregion

    //region Deal Search and Deal information
    /**
     * Search endpoint against swamis pointing to the aggregate deals Connv1 API.  This is essentially
     * an identical call to [searchClasses], but the object returned has aggregated deal / pricing
     * information.
     *
     * @param page (Optional) Page of results to return from paginated set.
     *             Default value : 1
     * @param radiusInMeters Radius in meters. Example: 40233.6 (25mi in m).
     *                       Default value : 16093.44
     * @param latitude Latitude for search center. Example: 32.9194673.
     * @param longitude Longitude for search center. Example: -117.2365862.
     * @param query (Optional) Search text
     */
    override fun searchDeals(
            page: Int,
            pageSize: Int,
            radiusInMeters: Number?,
            latitude: Number?,
            longitude: Number?,
            query: String?,
            minPrice: Number?,
            maxPrice: Number?,
            dealsPerSubscriber: Int?,
            onlyFavoriteSubscribers: Boolean?,
            onlyQualifiedDeals: Boolean?,
            successListener: ((GatewayDealResponse) -> Unit)?,
            errorListener: (VolleyError) -> Unit): MBRequest<GatewayDealResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/search/deals")
            .appendQueryParameter("page.number", page.toString())
            .appendQueryParameter("page.size", pageSize.toString()).also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        radiusInMeters?.let { url.appendQueryParameter("filter.radius", it.toString()) }
        latitude?.let { url.appendQueryParameter("filter.latitude", it.toString()) }
        longitude?.let { url.appendQueryParameter("filter.longitude", it.toString()) }
        query?.let { url.appendQueryParameter("filter.term", it) }
        minPrice?.let { url.appendQueryParameter("filter.min_price", it.toString()) }
        maxPrice?.let { url.appendQueryParameter("filter.max_price", it.toString()) }
        dealsPerSubscriber?.let { url.appendQueryParameter("filter.deals_per_subscriber", it.toString()) }
        onlyFavoriteSubscribers?.let { url.appendQueryParameter("filter.only_favorite_subscribers", it.toString()) }
        onlyQualifiedDeals?.let { url.appendQueryParameter("filter.only_qualified", it.toString()) }

        val req = GsonRequest<GatewayDealResponse>(Request.Method.GET, url.toString(),
                getContentHeaders(), GatewayDealResponse::class.java, { response ->
            successListener?.invoke(response)
        }, VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString())).apply { setShouldCache(false) }

        requestQueue.add(req)
        return req
    }

    /**
     * Identical to [searchDeals], but filters down to a single inventory reference (such as
     * a location or site)
     */
    override fun searchDealsByInventoryReference(
            page: Int,
            pageSize: Int,
            inventorySource: String?,
            inventoryReference: InventoryRefJsonModel,
            minPrice: Number?,
            maxPrice: Number?,
            onlyQualifiedDeals: Boolean?,
            successListener: ((GatewayDealResponse) -> Unit)?,
            errorListener: (VolleyError) -> Unit): MBRequest<GatewayDealResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/search/deals/by_inventory_ref")
            .appendQueryParameter("page.number", page.toString())
            .appendQueryParameter("page.size", pageSize.toString())
            .appendQueryParameter("filter.inventory_source", inventorySource)
            .appendQueryParameter("filter.inventory_ref_json", inventoryReference.toJson()).also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        minPrice?.let { url.appendQueryParameter("filter.min_price", it.toString()) }
        maxPrice?.let { url.appendQueryParameter("filter.max_price", it.toString()) }
        onlyQualifiedDeals?.let { url.appendQueryParameter("filter.only_qualified", it.toString()) }

        val req = GsonRequest<GatewayDealResponse>(Request.Method.GET, url.toString(),
                getContentHeaders(), GatewayDealResponse::class.java, { response ->
            successListener?.invoke(response)
        }, VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        requestQueue.add(req)
        return req
    }
    //endregion

    //region User Review APIs
    /**
     * Swamis API call to get user reviews. Takes an optional parameter which will filter to a specific,
     *  otherwise will get all the users reviews
     */
    fun getUserReviews(
            visitId: String? = null,
            successListener: ((GatewayUserReviewsResponse) -> Unit)? = null,
            errorListener: (VolleyError) -> Unit): MBRequest<GatewayUserReviewsResponse> {
        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/user/reviews").also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        visitId?.let { url.appendQueryParameter("filter.mb_visit_id", visitId) }

        val request = GsonRequest(Request.Method.GET, url.toString(),
                GatewayUserReviewsResponse::class.java, addFamilyAccountsHeaders(getContentHeaders()), successListener,
                VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)

        return request
    }

    fun postReview(review: VisitReviewDefinition,
                   successListener: Response.Listener<Void>?,
                   errorListener: Response.ErrorListener): MBRequest<Void> {

        val body = JsonApiDocumentSingle(data = JsonResource(type = "reviews", attributes = review))

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/reviews")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val request = GsonRequest(Request.Method.POST, url.toString(), Void::class.java, getBaseHeaders(),
                body.toJson(), successListener,
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request

    }
    //endregion

    //region Countries and Provinces APIs
    /**
     * Swamis API call to get all countries we currently support
     */
    fun getCountries(
            successListener: ((GatewayCountryResponse) -> Unit)? = null,
            errorListener: (VolleyError) -> Unit): MBRequest<GatewayCountryResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/countries")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val request = GsonRequest<GatewayCountryResponse>(Request.Method.GET, url.toString(),
                getBaseHeaders(), GatewayCountryResponse::class.java, { response ->
            successListener?.invoke(response)
        }, VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        requestQueue.add(request)

        return request
    }

    /**
     * Swamis API call to get provinces of a country. Takes a country ID and appends it as a URL param.
     */
    override fun getProvinces(
            countryId: String,
            successListener: ((GatewayProvincesResponse) -> Unit)?,
            errorListener: (VolleyError) -> Unit): MBRequest<GatewayProvincesResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/countries")
            .appendEncodedPath(countryId).appendEncodedPath("provinces")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest<GatewayProvincesResponse>(Request.Method.GET, url.toString(),
                getBaseHeaders(), GatewayProvincesResponse::class.java, { response ->
            successListener?.invoke(response)
        }, VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        requestQueue.add(request)

        return request
    }

    /**
     * Swamis API call to get liability release for an authenticated user at a site.
     */
    fun getLiabilityRelease(
            siteId: Int,
            inventorySource: String = MB_INVENTORY_SOURCE,
            successListener: ((LiabilityReleaseResponse) -> Unit)? = null,
            errorListener: (VolleyError) -> Unit): MBRequest<LiabilityReleaseResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/liability_releases")
            .appendQueryParameter("filter.inventory_ref_json", InventoryRefJsonModel(mb_site_id = siteId).toJson())
            .appendQueryParameter("filter.inventory_source", inventorySource)?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest<LiabilityReleaseResponse>(Request.Method.GET, url.toString(),
                addFamilyAccountsHeaders(getContentHeaders()), LiabilityReleaseResponse::class.java, { response ->
            successListener?.invoke(response)
        }, VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to post liability release for an authenticated user at a site. There is no
     * body required for this post. The response object is the same as the get liability release, but
     * the success should be indicated by the consumerHasAccountAtLocation attribute now being true.
     */
    override fun postLiabilityRelease(
            inventoryReference: InventoryRefJsonModel,
            inventorySource: String,
            successListener: ((LiabilityReleaseResponse) -> Unit)?,
            errorListener: (VolleyError) -> Unit): MBRequest<LiabilityReleaseResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/liability_releases")
            .appendQueryParameter("filter.inventory_ref_json", inventoryReference.toJson())
            .appendQueryParameter("filter.inventory_source", inventorySource)?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest<LiabilityReleaseResponse>(Request.Method.POST, url.toString(),
                addFamilyAccountsHeaders(getContentHeaders()), LiabilityReleaseResponse::class.java, { response ->
            successListener?.invoke(response)
        }, VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }
    //endregion

    //region Schedule Cancel APIs
    /**
     * Swamis API call to get the cancellable status of a booking
     *
     * NOTE:  Deprecated, please use [getCancellabilityStatus]
     */
    @Deprecated("Deprecated in favor of getCancellabilityStatus")
    fun getScheduleCancellable(
            inventorySource: String = MB_INVENTORY_SOURCE,
            bookingRefJsonModel: BookingRefJsonModel,
            successListener: ((ScheduleCancellableResponse) -> Unit)? = null,
            errorListener: ((VolleyError) -> Unit)? = null): MBRequest<ScheduleCancellableResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/schedule/cancellable")
            .appendQueryParameter("filter.inventory_source", inventorySource)
            .appendQueryParameter("filter.booking_ref_json", bookingRefJsonModel.toJson())?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest<ScheduleCancellableResponse>(Request.Method.GET, url.toString(),
                getContentHeaders(), ScheduleCancellableResponse::class.java, { response ->
            successListener?.invoke(response)
        }, VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to get the cancellable status of a booking
     */
    override fun getCancellabilityStatus(
            bookingRefJson: String,
            successListener: ((CancellabilityResponse) -> Unit)?,
            errorListener: (VolleyError) -> Unit): MBRequest<CancellabilityResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/user/bookings/cancellability")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val req = GsonRequest(Request.Method.POST, url.toString(),
                addFamilyAccountsHeaders(getContentHeaders()), CancellabilityResponse::class.java,
                BookingReference(booking_ref_json = bookingRefJson).toJson(),
                successListener, VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(req)
        return req
    }

    /**
     * Swamis API call to delete a booking from the user's schedule
     */
    override fun deleteBooking(
            bookingRefJson: String,
            successListener: ((Void?) -> Unit)?,
            errorListener: ((VolleyError) -> Unit)?): MBRequest<Void> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/user/bookings")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val body = BookingReference(booking_ref_json = bookingRefJson)

        val request = GsonRequest(Request.Method.DELETE, url.toString(), Void::class.java,
                addFamilyAccountsHeaders(getContentHeaders()), body.toJson(), successListener,
                VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))
        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to request a refund for a [bookingRefJson].  If the request is granted the user
     * is refunded a pass, for example if a flex class is cancelled a pass is added to their membership.
     * Returns 200 when the request is accepted, 409 error if a previous request for this
     * [bookingRefJson] already exists, or 422 error if this [bookingRefJson] cannot be found or
     * cannot be cancelled for some other reason.
     */
    override fun requestRefund(
            bookingRefJson: String,
            successListener: ((Void?) -> Unit)?,
            errorListener: ((VolleyError) -> Unit)?): MBRequest<Void> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/user/bookings/refund_request")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val body = BookingReference(booking_ref_json = bookingRefJson)

        val request = GsonRequest(Request.Method.POST, url.toString(), Void::class.java,
                getContentHeaders(), body.toJson(), successListener,
                VolleyLambdaErrorInterceptor(passedLambda = errorListener, url = url.toString()))
        attachTokenIfNotGuestAndSend(request)
        return request
    }
    //endregion

    //region Appointment APIs
    /**
     * Swamis API call to get the appointment services, given a [locationReference].  This
     * call is meant to replace the service categories and appointment types call from the
     * MB REST API.
     */
    fun getAppointmentServices(
            locationReference: LocationReference,
            successListener: ((AppointmentServiceResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<AppointmentServiceResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/location/appointment_services")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val request = GsonRequest(Request.Method.POST, url.toString(), AppointmentServiceResponse::class.java,
                getBaseHeaders(), locationReference.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        requestQueue.add(request)
        return request
    }

    /**
     * Swamis API call to get the appointment staff, given an appointment and location reference.
     */
    fun getAppointmentStaff(
            inventorySource: String,
            locationRefJson: String,
            appointmentServiceRefJson: String,
            successListener: ((AppointmentStaffResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<AppointmentStaffResponse> {

        val body = AppointmentIdentifierRequest(inventorySource, locationRefJson, appointmentServiceRefJson)

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/location/appointment_services/staff")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val request = GsonRequest(Request.Method.POST, url.toString(), AppointmentStaffResponse::class.java,
                getBaseHeaders(), body.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        requestQueue.add(request)
        return request
    }

    /**
     * Swamis API call to get the appointment staff, given an appointment and location reference.
     */
    fun getAppointmentBookability(
            locationReference: LocationReference,
            appointmentServiceRefJson: String,
            successListener: ((AppointmentBookabilityResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<AppointmentBookabilityResponse> {

        val body = AppointmentIdentifierRequest(locationReference.inventory_source, locationReference.location_ref_json, appointmentServiceRefJson)

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/appointment_services/bookability")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), AppointmentBookabilityResponse::class.java,
                getBaseHeaders(), body.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to get the availability for a set of appointment bookability criteria.
     *
     * The [minStartTime] is the earliest possible appointment time, and [maxStartTime] is the
     * latest time an appointment can start.  The [staffRefJson] is optional, and if it is null,
     * it will return availability for all staff (it will show all open time slots for the location)
     */
    override fun getAppointmentAvailability(
            locationReference: LocationReference,
            appointmentServiceRefJson: String,
            staffRefJson: String?,
            minStartTime: Calendar,
            maxStartTime: Calendar,
            successListener: ((AppointmentAvailabilityResponse) -> Unit)?,
            errorListener: Response.ErrorListener?): MBRequest<AppointmentAvailabilityResponse> {

        val body = AppointmentIdentifierRequest(
                locationReference.inventory_source, locationReference.location_ref_json, appointmentServiceRefJson,
                staffRefJson, ISO_8601.format(minStartTime.timeInMillis), ISO_8601.format(maxStartTime.timeInMillis))

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/appointment_services/availability")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), AppointmentAvailabilityResponse::class.java,
                getBaseHeaders(), body.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to get appointment purchase options
     */
    fun getAppointmentPurchaseOptions(
            locationReference: LocationReference,
            appointmentServiceRefJson: String,
            successListener: ((PurchaseOptionResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<PurchaseOptionResponse> {

        val body = AppointmentIdentifierRequest(locationReference.inventory_source, locationReference.location_ref_json, appointmentServiceRefJson)

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/appointment_services/purchase_options")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), PurchaseOptionResponse::class.java,
                getBaseHeaders(), body.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }
    //endregion

    //region Location payment methods
    /**
     * Swamis API call to fetch a user’s applicable passes for the given appointment service and location
     */
    fun getPasses(
            locationReference: LocationReference,
            appointmentServiceRefJson: String,
            startTimeFrom: Calendar,
            startTimeTo: Calendar,
            successListener: ((PaymentMethodPassesResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<PaymentMethodPassesResponse> {

        val body = PaymentMethodsPassRequest(locationReference.inventory_source, locationReference.location_ref_json,
                appointmentServiceRefJson, ISO_8601.format(startTimeFrom), ISO_8601.format(startTimeTo))

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/payment_methods/passes")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), PaymentMethodPassesResponse::class.java,
                getBaseHeaders(), body.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to fetch a user’s applicable class passes for the given location
     */
    fun getClassPasses(
            locationReference: LocationReference,
            classTimeRefJson: String,
            successListener: ((PaymentMethodPassesResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<PaymentMethodPassesResponse> {

        val body = PaymentMethodsClassPassRequest(locationReference.location_ref_json, classTimeRefJson)

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/payment_methods/class_time_passes")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), PaymentMethodPassesResponse::class.java,
                addFamilyAccountsHeaders(getBaseHeaders()), body.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to fetch a user’s applicable gift cards for the given location.
     */
    fun getGiftCards(
            locationReference: LocationReference,
            successListener: ((PaymentMethodGiftCardsResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<PaymentMethodGiftCardsResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/payment_methods/gift_cards")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), PaymentMethodGiftCardsResponse::class.java,
                getBaseHeaders(), locationReference.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to fetch a user’s applicable credit cards for the given location.
     */
    fun getCreditCards(
            locationReference: LocationReference,
            successListener: ((PaymentMethodCreditCardsResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<PaymentMethodCreditCardsResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/payment_methods/credit_cards")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), PaymentMethodCreditCardsResponse::class.java,
                getBaseHeaders(), locationReference.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to fetch a location's payment method configuration
     */
    fun getPaymentMethodConfiguration(
            locationReference: LocationReference,
            successListener: ((PaymentMethodConfigurationResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<PaymentMethodConfigurationResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/location/payment_methods/configuration")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), PaymentMethodConfigurationResponse::class.java,
                getBaseHeaders(), locationReference.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }
    //endregion

    //region Order Management / Line items
    /**
     * Create a new order for the selected [locationReference].
     */
    fun createOrder(
            locationReference: LocationReference,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val body = mapOf("location_ref_json" to locationReference.location_ref_json)

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val request = GsonRequest(Request.Method.POST, url.toString(), OrderResponse::class.java,
                getBaseHeaders(), body.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Abandon an order, provided the [orderId].  An optional [abandonReason] is used on the
     * backend for analytics purposes.
     */
    fun abandonOrder(
            orderId: String,
            abandonReason: String,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val body = mapOf("abandonment_reason" to abandonReason)

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("abandon")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.PUT, url.toString(), OrderResponse::class.java,
                getBaseHeaders(), body.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Retrieve the order via the [orderId].  Should be used as a checkpoint for the cart state.
     */
    fun getOrder(
            orderId: String,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.GET, url.toString(), OrderResponse::class.java,
                getBaseHeaders(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Compute a total for an existing order attached to the [orderId]
     */
    fun computeTotal(
            orderId: String,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("compute_total")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), OrderResponse::class.java,
                getContentHeaders(), successListener, {
            ResponseErrorInterceptor(passedValue = errorListener,
                    url = url.toString())
        })

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Process an existing order attached to the [orderId]
     */
    fun processOrder(
            orderId: String,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("process")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), OrderResponse::class.java,
                getContentHeaders(), successListener, {
            ResponseErrorInterceptor(passedValue = errorListener,
                    url = url.toString())
        })

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Create the booking item in the order attached to the [orderId].  If the [inventoryItemRefJson]
     * corresponds to an appointment, then the [staffRefJson], [startTime] and [endTime] are
     * required for the response.
     */
    fun createOrderBookingItem(
            orderId: String,
            inventoryItemRefJson: String,
            staffRefJson: String? = null,
            startTime: Calendar?,
            endTime: Calendar?,
            notes: String? = null,
            bookingConfiguration: BookingConfiguration = BookingConfiguration.BOOK,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val body = OrderBookingItem(
                startTime = startTime?.timeInMillis?.let(ISO_8601::format),
                endTime = endTime?.timeInMillis?.let(ISO_8601::format),
                inventoryItemRefJson = inventoryItemRefJson,
                staffRefJson = staffRefJson,
                bookingConfiguration = bookingConfiguration.bookingConfiguration,
                notes = notes)

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("booking_items")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), OrderResponse::class.java,
                getContentHeaders(), body.toJson(), Response.Listener {
            successListener?.invoke(it)
        }, ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Updates a booking item with [bookingItemId] for an existing order with [orderId].
     * If the [bookingItemId] corresponds to an appointment, then the [staffRefJson],
     * [startTime] and [endTime] are required.
     */
    fun updateBookingItem(
            orderId: String,
            bookingItemId: String,
            staffRefJson: String? = null,
            startTime: Calendar?,
            endTime: Calendar?,
            notes: String? = null,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val body = OrderBookingItem(
                startTime = startTime?.timeInMillis?.let(ISO_8601::format),
                endTime = endTime?.timeInMillis?.let(ISO_8601::format),
                staffRefJson = staffRefJson,
                notes = notes)

        //TODO CK - This does not look right where we are passing in the body.toString for the url.  Investigate
        val request = GsonRequest(Request.Method.PUT, body.toString(), OrderResponse::class.java,
                getContentHeaders(), body.toJson(), Response.Listener {
            successListener?.invoke(it)
        }, ResponseErrorInterceptor(passedValue = errorListener, url = body.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Deletes a booking item with [bookingItemId] from an existing order with [orderId]
     */
    fun deleteBookingItem(
            orderId: String,
            bookingItemId: String,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("booking_items")
            .appendEncodedPath(bookingItemId)?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.DELETE, url.toString(), OrderResponse::class.java,
                getContentHeaders(), successListener, {
            ResponseErrorInterceptor(passedValue = errorListener,
                    url = url.toString())
        })

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Adds a payment [amount] with [paymentMethodUuid] to an existing order with [orderId].
     * When booking an item with a pass payment method, a [bookingItemUuid] must be provided to
     * specify how to apply the pass.
     */
    fun postOrderPayment(
            orderId: String,
            paymentMethodUuid: String,
            bookingItemUuid: String? = null,
            amount: Number? = null,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val body = OrderPaymentRequestObject(paymentMethodUuid, bookingItemUuid, amount)

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("payments")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), OrderResponse::class.java,
                getContentHeaders(), body.toJson(), { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = body.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Updates the [amount] to be charged to the payment that has [paymentId] for the
     * currently ongoing order associated with [orderId].
     */
    fun updatePayment(
            amount: Number,
            orderId: String,
            paymentId: String,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val body = mapOf("amount" to amount)

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("payments")
            .appendEncodedPath(paymentId)?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.PUT, url.toString(), OrderResponse::class.java,
                getContentHeaders(), body.toJson(), successListener, {
            ResponseErrorInterceptor(passedValue = errorListener,
                    url = url.toString())
        })

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Deletes a payment that has [paymentId] from an existing order attached to [orderId]
     */
    fun deletePayment(
            orderId: String,
            paymentId: String,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("payments")
            .appendEncodedPath(paymentId)?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.DELETE, url.toString(), OrderResponse::class.java,
                getContentHeaders(), successListener, {
            ResponseErrorInterceptor(passedValue = errorListener,
                    url = url.toString())
        })

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Adds a purchaseable item with [purchaseOptionUuid] to an existing order with [orderId].
     * A purchaseable item is the entity that is bought and used to reserve a spot for a service
     * (Drop-In, Multi-Session Packs).  When buying and booking in a single order transaction,
     * a [bookingItemUuid] must be provided to this endpoint. Note that in these cases a booking
     * item must be added to the order first.
     */
    fun createPurchaseItem(
            orderId: String,
            purchaseOptionUuid: String,
            bookingItemUuid: String? = null,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val body = PurchaseItem(
                purchaseOptionUuid,
                bookingItemUuid
        )

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("purchase_items")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), OrderResponse::class.java,
                getContentHeaders(), body.toJson(), Response.Listener {
            successListener?.invoke(it)
        }, ResponseErrorInterceptor(passedValue = errorListener, url = body.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Deletes a purchase item with [purchaseItemId] from an existing order with [orderId]
     */
    fun deletePurchaseItem(
            orderId: String,
            purchaseItemId: String,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {
        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("purchase_items")
            .appendEncodedPath(purchaseItemId)?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.DELETE, url.toString(), OrderResponse::class.java,
                getContentHeaders(), successListener, {
            ResponseErrorInterceptor(passedValue = errorListener,
                    url = url.toString())
        })
        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     *Creates a temporary payment method and association with a given order by UUID.
     *The payment method will only persist to the user’s account if store_card is true.
     */
    fun createPaymentInstrument(
            orderId: String,
            paymentRequestObject: PaymentInstrumentRequestObject,
            successListener: ((OrderResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<OrderResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath("v1/orders")
            .appendEncodedPath(orderId)
            .appendEncodedPath("payment/instrument")?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(Request.Method.POST, url.toString(), OrderResponse::class.java,
                getBaseHeaders(), paymentRequestObject.toJson(), Response.Listener { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }
    //endregion

    /**
     * Swamis API call to fetch a billing info to display cards on wallet screen from settings menu
     */
    fun getBilling(
            successListener: ((BillingResponse) -> Unit)? = null,
            errorListener: Response.ErrorListener? = null): MBRequest<BillingResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath(PATH_GATEWAY_BILLING_INFO)

        val request = GsonRequest(Request.Method.GET, url.toString(), BillingResponse::class.java,
                addFamilyAccountsHeaders(getRecommenderHeaders()), successListener,
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call delete billing info from wallet screen in settings menu
     */
    fun removeBilling(
            billingId: Long?,
            successListener: ((Void) -> Unit)? = null,
            errorListener: ((Throwable) -> Unit)? = null): MBRequest<Void> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath(PATH_GATEWAY_BILLING_INFO)
            .appendEncodedPath(billingId.toString())
        val request = GsonRequest(Request.Method.DELETE, url.toString(), Void::class.java,
                addFamilyAccountsHeaders(getBaseHeaders()), successListener, VolleyLambdaErrorInterceptor(errorListener))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Gets all passes for logged in user. This will return a [GatewayUserPassesResponse]
     */
    fun getUserPasses(successListener: ((GatewayUserPassesResponse) -> Unit)? = null,
                      errorListener: ((VolleyError) -> Unit)? = null): MBRequest<GatewayUserPassesResponse> {
        val url = currentEndpoint.buildUpon()
            .appendEncodedPath(PATH_GATEWAY_USER_PASSES)

        val headers = addFamilyAccountsHeaders(getRecommenderHeaders())
        if (interceptAndStopRequest(url.toString(), headers)) {
            errorListener?.let {
                it(VolleyError("Missing AUTHORIZATION_HEADER_KEY in headers"))
            }
            return GsonRequest(
                Request.Method.GET,
                url.toString(),
                GatewayUserPassesResponse::class.java,
                headers,
                successListener,
                errorListener
            )
        }
        val request = GsonRequest(Request.Method.GET, url.toString(), GatewayUserPassesResponse::class.java,
               headers, successListener, ResponseErrorInterceptor(errorListener))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to add cards
     */
    fun addBilling(billingAttributes: BillingAttributes,
                   successListener: Response.Listener<Void>?,
                   errorListener: ((Error?) -> Unit?)? = null): MBRequest<Void> {

        val body = JsonApiDocumentSingle(data = JsonResource(type = "userBilling", attributes = billingAttributes))

        val url = currentEndpoint.buildUpon().appendEncodedPath(PATH_GATEWAY_BILLING_INFO)?.also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val request = GsonRequest(Request.Method.POST, url.toString(), Void::class.java, addFamilyAccountsHeaders(getRecommenderHeaders()),
                body.toJson(), successListener,
                GatewayAPIErrorInterceptor(passedLambda = errorListener, url = url.toString()))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to fetch cancellation policy details for an appointment.
     * @param successListener Success callback returning [CancellationPolicyDetailsResponse]
     * @param locationReference is an [LocationReference] for the particular business
     * @param appointmentServiceRef is [AppointmentServiceRefJson] for the appointment the cancellation
     *  policy applies to
     */
    override fun fetchCancellationPolicyDetails(
            locationReference: LocationReference?,
            appointmentServiceRef: AppointmentServiceRefJson?,
            successListener: ((CancellationPolicyDetailsResponse) -> Unit)?,
            errorListener: ((VolleyError) -> Unit)?): MBRequest<CancellationPolicyDetailsResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath(PATH_GATEWAY_CANCELLATION_POLICY)

        val body = CancellationPolicyRequest(
                location_ref_json = locationReference?.location_ref_json,
                appointment_service_ref_json = appointmentServiceRef.toJson(),
                inventory_source = appointmentServiceRef?.inventory_source
        )

        val request = GsonRequest(
                Request.Method.POST,
                url.toString(),
                CancellationPolicyDetailsResponse::class.java,
                getContentHeaders(),
                body.toJson(),
                successListener,
                ResponseErrorInterceptor(errorListener))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to fetch appointment settings for a location.
     * @param successListener Success callback returning [LocationAppointmentSettingsResponse]
     * @param locationReference is a [LocationReference] of the business/location
     */
    override fun fetchLocationAppointmentSettings(
            locationReference: LocationReference?,
            successListener: ((LocationAppointmentSettingsResponse) -> Unit)?,
            errorListener: ((VolleyError) -> Unit)?): MBRequest<LocationAppointmentSettingsResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath(PATH_GATEWAY_LOCATION_APPOINTMENT_SETTINGS)

        val body = LocationAppointmentSettingsRequest(
                location_ref_json = locationReference?.location_ref_json,
                inventory_source = locationReference?.inventory_source
        )

        val request = GsonRequest(
                Request.Method.POST,
                url.toString(),
                LocationAppointmentSettingsResponse::class.java,
                getContentHeaders(),
                body.toJson(),
                successListener,
                ResponseErrorInterceptor(errorListener))

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Gets all associated family member for logged in user. This will return a [AssociatedUserResponse]
     */
    fun getAssociatedUsers(
            successListener: ((AssociatedUserResponse) -> Unit)? = null,
            errorListener: ((VolleyError) -> Unit)? = null
    ): MBRequest<AssociatedUserResponse> {
        val url = currentEndpoint.buildUpon()
            .appendEncodedPath(PATH_GATEWAY_USER_ASSOCIATE_USERS)?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(
                Request.Method.GET, url.toString(), AssociatedUserResponse::class.java,
                getRecommenderHeaders(), successListener, ResponseErrorInterceptor(errorListener)
        )
        attachTokenIfNotGuestAndSend(request)
        return request
    }
    //endregion

    /**
     * Swamis API call to add attributions to a user. Gateway will handle if we send duplicates and
     * will take care of timestamping, etc..
     *
     * @param attributions One or more attributions wrapped in the AttributionReference class
     * @param successListener Success callback returning Void (server response has no body).
     *   SharedPrefs should be cleared so that we dont continue to send attributions
     * @param errorListener Failure callback that will return a Volley Response.ErrorListener
     */
    fun postAttributionValues(
            attributions: AttributionReference,
            successListener: Response.Listener<Void>?,
            errorListener: Response.ErrorListener): MBRequest<Void> {

        val url = currentEndpoint.buildUpon().appendEncodedPath(PATH_GATEWAY_ATTRIBUTION).also {
            BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
        }

        val request = GsonRequest(Request.Method.POST, url.toString(), Void::class.java,
                getBaseHeaders(), attributions.toJson(), successListener,
                ResponseErrorInterceptor(passedValue = errorListener, url = url.toString())).apply {
            setResponseCanSafelyBeNull(true)
        }

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to get Enhance review prompt data.
     *
     * @param successListener success callback which will return a ClassStatusResponse containing the ClassStatusAttributes
     * @param errorListener failure callback
     */
    fun getEnhanceReviewPrompt(
        bookingRefJson: String?,
        authorizedUser: Boolean? = null,
        successListener: ((ReviewPromptResponse) -> Unit)? = null,
        errorListener: ((VolleyError) -> Unit)? = null
    ): MBRequest<ReviewPromptResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("reviews/review_prompts/by_booking_ref").also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        authorizedUser?.let { url.appendQueryParameter("filter.authorized_user", it.toString()) }

        val body = BookingReference(booking_ref_json = bookingRefJson)

        val req = GsonRequest(
            Request.Method.POST, url.toString(), ReviewPromptResponse::class.java,
            getBaseHeaders(), body.toJson(), successListener,
            ResponseErrorInterceptor(errorListener)
        )

        attachTokenIfNotGuestAndSend(req)
        return req
    }


    /**
     * Swamis API call to submit enhance review prompt.
     *
     * @param successListener success callback which is Empty
     * @param errorListener failure callback
     */
    fun postEnhanceReviewPrompt(
            reviewId: String,
            visitReview: VisitReview,
            attributes: List<ResponseAttributes>?,
            submittedDate: String?,
            id: Long?,
            successListener: Response.Listener<Void>?,
            errorListener: Response.ErrorListener): MBRequest<Void> {

        var method: Int= Request.Method.POST

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("reviews/reviews")

        /**
         * Id will be null in case of submit review and
         * for edit review Id would be connect Id to perform edit operation.
         */
        id?.let {
            method=Request.Method.PUT
            url.appendEncodedPath(id.toString())
        }

        BreadcrumbsUtils.breadcrumbGatewayApiCall(url)

        val body = ReviewPromptRequest().apply {
            reviewPromptId = reviewId
            submitted = submittedDate
            bookingRefJson = BookingRefJsonModel().apply {
                mb_site_visit_id = visitReview.visitId
                mb_site_id = visitReview.siteId
                inventory_source = MB_INVENTORY_SOURCE
                mb_program_type = visitReview.programType
            }.toJson()
            responsesAttributes = attributes
        }

        val req = GsonRequest(
            method, url.toString(), Void::class.java,
            getBaseHeaders(), body.toJson(), successListener,
            ResponseErrorInterceptor(passedValue = errorListener, url = url.toString())
        )

        attachTokenIfNotGuestAndSend(req)
        return req
    }

    /**
     * Swami API call to return popular keywords list.
     * @param successListener success callback with popular keyword response
     * @param errorListener failure callback
     */
    override fun getPopularKeywords(
            successListener: ((PopularKeywordsResponse) -> Unit)?,
            errorListener: ((VolleyError) -> Unit)?
    ): MBRequest<PopularKeywordsResponse> {
        val url = currentEndpoint.buildUpon()
            .appendEncodedPath(PATH_GATEWAY_POPULAR_KEY_WORDS)?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val request = GsonRequest(
                Request.Method.GET, url.toString(), PopularKeywordsResponse::class.java,
                getBaseHeaders(), successListener, ResponseErrorInterceptor(errorListener)
        )
        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call get all review summary by_course
     *
     * @param successListener success callback which is Empty
     * @param errorListener failure callback
     */
    fun getEnhanceReviewSummaryByCourseResponse(
            inventoryReference: String,
            successListener: ((ReviewSummaryResponse) -> Unit)? = null,
            errorListener: ((VolleyError) -> Unit)? = null): MBRequest<ReviewSummaryResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath(PATH_GATEWAY_REVIEW_SUMMARY_BY_COURSE).also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val body= JsonObject().apply {
            addProperty("inventory_ref_json", inventoryReference)
        }

        val req = GsonRequest(
                Request.Method.POST, url.toString(), ReviewSummaryResponse::class.java,
                getBaseHeaders(), body.toJson(), successListener,
                ResponseErrorInterceptor(errorListener)
        )

        attachTokenIfNotGuestAndSend(req)
        return req
    }

    /**
     * Swamis API call get all review summary by_location
     *
     * @param successListener success callback which is Empty
     * @param errorListener failure callback
     */
    override fun getEnhanceReviewSummaryByLocationResponse(
            locationReference: String,
            successListener: ((ReviewSummaryResponse) -> Unit)?,
            errorListener: ((VolleyError) -> Unit)?): MBRequest<ReviewSummaryResponse> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath(PATH_GATEWAY_REVIEW_SUMMARY_BY_LOCATION).also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val body= JsonObject().apply {
            addProperty("location_ref_json", locationReference)
        }

        val req = GsonRequest(
                Request.Method.POST, url.toString(), ReviewSummaryResponse::class.java,
                getBaseHeaders(), body.toJson(), successListener,
                ResponseErrorInterceptor(errorListener)
        )

        attachTokenIfNotGuestAndSend(req)
        return req
    }

    override fun getEnhanceReviewsResponse(
        userId: Int?,
        visitId:Long?,
        size: Int, number: Int,
        referenceJson: String,
        reviewRefType: ReviewRefType,
        successListener: ((GatewayEnhanceReviewsResponse) -> Unit)?,
        errorListener: ((VolleyError) -> Unit)?
    ): MBRequest<GatewayEnhanceReviewsResponse> {
        val reviewRefTypeURL: String
        val reviewFilterAttributes: ReviewFilterAttributes
        when (reviewRefType) {
            ReviewRefType.REVIEW_BY_LOCATION -> {
                reviewRefTypeURL = PATH_GATEWAY_REVIEWS_BY_LOCATION
                reviewFilterAttributes = ReviewFilterAttributes(locationRefJson = referenceJson)
            }
            ReviewRefType.REVIEW_BY_COURSE -> {
                reviewRefTypeURL = PATH_GATEWAY_REVIEWS_BY_COURSE
                reviewFilterAttributes = ReviewFilterAttributes(userId= userId,visitId= visitId, inventoryRefJson = referenceJson)
            }
        }

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath(reviewRefTypeURL).also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val body = GatewayFetchReviewsRequest().apply {
            this.page = PageAttributes(size, number)
            this.filter = reviewFilterAttributes
        }

        val req = GsonRequest(
                Request.Method.POST, url.toString(), GatewayEnhanceReviewsResponse::class.java,
                getBaseHeaders(), body.toJson(), successListener,
                VolleyLambdaErrorInterceptor(errorListener)
        )

        attachTokenIfNotGuestAndSend(req)
        return req
    }

    override fun deleteEnhanceReview(
            reviewId: String,
            visit: BaseVisit,
            submittedDate: String?,
            id: Long?,
            successListener: ((Void?) -> Unit)?,
            errorListener: ((VolleyError) -> Unit)?): MBRequest<Void> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath(PATH_GATEWAY_DELETE_REVIEW)?.also {
                it.appendEncodedPath(id.toString())
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val body = ReviewPromptRequest().apply {
            reviewPromptId = reviewId
            submitted = submittedDate
            bookingRefJson = BookingRefJsonModel().apply {
                mb_site_visit_id = visit.siteVisitId
                mb_site_id = visit.siteId
                inventory_source = MB_INVENTORY_SOURCE
                mb_program_type = visit.ProgramType
            }.toJson()
        }

        val request = GsonRequest(
                Request.Method.DELETE, url.toString(), Void::class.java,
                getBaseHeaders(), body.toJson(), successListener, ResponseErrorInterceptor(errorListener)
        )
        attachTokenIfNotGuestAndSend(request)
        return request
    }


    override fun reviewReportAbuse(reviewId: Long,
                                   successListener: ((Void?) -> Unit)?,
                                   errorListener: ((VolleyError) -> Unit)?): MBRequest<Void> {

        val url = currentEndpoint.buildUpon()
            .appendEncodedPath(PATH_GATEWAY_REVIEWS_REPORT_ABUSE)?.also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val body = JsonObject().apply {
            addProperty("review_ref_json", JsonObject().apply { addProperty("review_id", reviewId)}.toJson())
        }

        val request = GsonRequest(
                Request.Method.POST, url.toString(), Void::class.java,
                getBaseHeaders(), body.toJson(), successListener, ResponseErrorInterceptor(errorListener)
        )
        attachTokenIfNotGuestAndSend(request)
        return request

    }

    /**
     * API Call to get the auto complete results for,
     * @param keyword the keyword to search for
     * @param latitude the latitude of the location
     * @param longitude the longitude of the location
     * @param radius the radius to search within
     */
    override fun getAutoCompleteResults(keyword: String, latitude: Double, longitude: Double, radius: Int,
                                        successListener: ((GatewayAutoCompleteResponse) -> Unit)?,
                                        errorListener: ((VolleyError) -> Unit)?)
    : MBRequest<GatewayAutoCompleteResponse> {
        val url = currentEndpoint.buildUpon()
            .appendEncodedPath("v1/auto_suggest")
            .appendQueryParameter(
                "allowSpellCorrect",
                FeatureFlag.AUTO_SUGGEST_SPELL_CORRECT.isFeatureEnabled().toString()
            )
            .also {
                BreadcrumbsUtils.breadcrumbGatewayApiCall(it)
            }

        val locationSort = ABHelperUtils.getAutoSuggestSortVariableValue()
        val body = AutoCompleteDefinition(
                locationSort = locationSort,
                page = AutoCompletePage(20, 1),
                filter = AutoCompleteFilter(latitude, longitude, radius, keyword)
        )

        val req = GsonRequest(
                Request.Method.POST, url.toString(), GatewayAutoCompleteResponse::class.java,
                mapOf("Content-Type" to "application/json", "accept" to "application/vnd.api+json",
                ), body.toJson(), successListener,
                ResponseErrorInterceptor(errorListener)
        )

        requestQueue.add(req)
        return req
    }

    /**
     * Swamis API call to validate a credit card against a studio.
     * @param successListener Success callback returning [ValidateCreditCardResponse]
     * @param locationReference is the [LocationReference] of a particular business
     */
    override fun validateCreditCard(
            locationReference: LocationReference?,
            cardToken: String?,
            successListener: ((ValidateCreditCardResponse) -> Unit)?,
            errorListener: ((VolleyError) -> Unit)?): MBRequest<ValidateCreditCardResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath(PATH_GATEWAY_VALIDATE_CREDIT_CARD)

        val body = ValidateCreditCardRequest(
                location_ref_json = locationReference?.location_ref_json,
                inventory_source = locationReference?.inventory_source,
                card_id = cardToken ?: "",
        )

        val request = GsonRequest(
                Request.Method.POST,
                url.toString(),
                ValidateCreditCardResponse::class.java,
                getContentHeaders(),
                body.toJson(),
                successListener,
                errorListener,
        )

        attachTokenIfNotGuestAndSend(request)
        return request
    }

    /**
     * Swamis API call to get location details
     * @param locationReference is the [LocationReference] of a particular business
     * @param successListener Success callback returning [LocationDetailsResponse]
     */
    override fun fetchLocationDetails(
            locationReference: LocationReference?,
            successListener: ((LocationDetailsResponse) -> Unit)?,
            errorListener: ((VolleyError) -> Unit)?): MBRequest<LocationDetailsResponse> {

        val url = currentEndpoint.buildUpon().appendEncodedPath(PATH_GATEWAY_LOCATION_DETAILS)

        val body = LocationDetailsRequest(
                location_ref_json = locationReference?.location_ref_json,
                inventory_source = locationReference?.inventory_source,
        )

        val request = GsonRequest(
                Request.Method.POST,
                url.toString(),
                LocationDetailsResponse::class.java,
                getContentHeaders(),
                body.toJson(),
                successListener,
                errorListener,
        )

        attachTokenIfNotGuestAndSend(request)
        return request
    }

	override fun getPrivacyPreferenceUrl(
		successListener: ((PrivacyPreferenceUrlResponse) -> Unit)?,
		errorListener: ((VolleyError) -> Unit)?
	): MBRequest<PrivacyPreferenceUrlResponse> {
		val url = currentEndpoint.buildUpon()
			.appendEncodedPath(PATH_GATEWAY_PRIVACY_PREFERENCE_URL)
			.appendQueryParameter(FILTER_REQUEST_TYPE_QUERY_PARAM, FILTER_REQUEST_TYPE_QUERY_VALUE)
			.appendQueryParameter(FILTER_REQUEST_TARGET_QUERY_PARAM, FILTER_REQUEST_TARGET_QUERY_VALUE)
			.toString()
		val request = GsonRequest(
			Request.Method.GET,
			url,
			PrivacyPreferenceUrlResponse::class.java,
			getContentHeaders(),
			null,
			successListener,
			errorListener
		)
		attachTokenIfNotGuestAndSend(request)
		return request
	}

    private fun interceptAndStopRequest(url: String, headers: MutableMap<String, String>) : Boolean {
        if (DevelopmentFlag.DEVELOPMENT_ANDROID_NO_AUTH_TOKEN_INTERCEPT.isFeatureEnabled()) {
            if (!headers.containsKey(AUTHORIZATION_HEADER_KEY) && !headers.containsKey(GUEST_USER_ID_HEADER_KEY)) {
                BreadcrumbsUtils.recordAuthTokenMissingBreadCrumb(
                    mutableMapOf(
                        "url" to url,
                        "headers" to headers.toJson(),
                    )
                )
                return true
            }
        }
        return false
    }
}
