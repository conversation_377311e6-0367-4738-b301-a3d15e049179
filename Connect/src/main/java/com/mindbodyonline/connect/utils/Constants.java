package com.mindbodyonline.connect.utils;

import com.mindbodyonline.connect.BuildConfig;

/**
 * Created by john.leehey on 8/1/13.
 */
public class Constants {
    public static final String PREF_LAST_LOC_LATITUDE = "PREF_LAST_LOC_LATITUDE";
    public static final String PREF_LAST_LOC_LONGITUDE = "PREF_LAST_LOC_LONGITUDE";
    public static final String PREF_LAST_LOC_ACCURACY = "PREF_LAST_LOC_ACCURACY";
    public static final String PREF_LAST_LOC_TIME = "PREF_LAST_LOC_TIME";

    public static final String PREF_LAST_SEARCHED_LOC_LATITUDE = "PREF_LAST_SEARCHED_LOC_LATITUDE";
    public static final String PREF_LAST_SEARCHED_LOC_LONGITUDE = "PREF_LAST_SEARCHED_LOC_LONGITUDE";
    public static final String PREF_LAST_SEARCHED_LOC_ACCURACY = "PREF_LAST_SEARCHED_LOC_ACCURACY";
    public static final String PREF_LAST_SEARCHED_LOC_TIME = "PREF_LAST_SEARCHED_LOC_TIME";

    public static final String PREF_GLOBAL_INDICATOR_LOC_LATITUDE = "PREF_GLOBAL_INDICATOR_LOC_LATITUDE";
    public static final String PREF_GLOBAL_INDICATOR_LOC_LONGITUDE = "PREF_GLOBAL_INDICATOR_LOC_LONGITUDE";
    public static final String PREF_GLOBAL_INDICATOR_LOC_ACCURACY = "PREF_GLOBAL_INDICATOR_LOC_ACCURACY";
    public static final String PREF_GLOBAL_INDICATOR_LOC_TIME = "PREF_GLOBAL_INDICATOR_LOC_TIME";

    public static final String PREF_LOCATION_PROFILE_ID_MAP = "PREF_LOCATION_PROFILE_ID_MAP";

    public static final String SEARCH_FRAGMENT_TAG = "com.mindbodyonline.connect.fragments.SearchFragment";
    public static final String MY_SCHEDULE_FRAGMENT_TAG = "com.mindbodyonline.connect.profile.schedule.ScheduleFragment";
    public static final String FAVORITES_FRAGMENT_TAG = "com.mindbodyonline.connect.fragments.FavoritesFragment";
    public static final String MY_PROFILE_FRAGMENT_TAG = "com.mindbodyonline.connect.profile.ScheduleProfileContainerFragment";
    public static final String DEALS_FRAGMENT_TAG = "com.mindbodyonline.connect.deals.DealsFragment";

    public static final String PAS_FRAGMENT_TAG = "com.mindbodyonline.pickaspot.ui.PickASpotFragment";
    public static final String BROWSE_FRAGMENT_TAG = "com.mindbodyonline.connect.fragments.BrowseFragment";
    public static final String EXPLORE_FRAGMENT_TAG = "com.mindbodyonline.connect.fragments.ExploreFragment";
    public static final String EXPLORE_HOME_FRAGMENT_TAG = "com.mindbodyonline.connect.explore.home.ExploreHomeFragment";
    public static final String HOME_FRAGMENT_TAG = "com.mindbodyonline.connect.fragments.HomeFragment";
    public static final String QUICKBOOK_CONTEXT_HELPER_TAG = "QuickbookContextHelper";
    public static final String CALENDAR_DAY_PICKER_TAG = "CalendarDayPicker";
    public static final String RATE_DIALOG_FRAGMENT_TAG = "RateDialog";
    public static final String PRIVACY_DIALOG_TAG = "privacyDialog";
    public static final String QB_ORDER_SUMMARY_DIALOG = "QuickBookOrderSummaryDialog";


    public static final int NUM_LOCATION_FILTER_HEADERS = 2;
    public static final String KEY_BUNDLE_SITEID = "KEY_SITE_ID";
    public static final String KEY_BUNDLE_SITELOCATIONID = "KEY_SITE_LOCATION_ID";
    public static final String KEY_BUNDLE_VISITID = "KEY_VISIT_ID";
    public static final String KEY_BUNDLE_PROGRAM_TYPE = "KEY_VISIT_PROGRAM_TYPE";
    public static final String KEY_BUNDLE_PRICING_REFERENCE = "KEY_BUNDLE_PRICING_REFERENCE";
    public static final String KEY_BUNDLE_CART = "KEY_BUNDLE_CART";
    public static final String KEY_BUNDLE_PAYMENT_METHOD = "KEY_BUNDLE_PAYMENT_METHOD";
    public static final String KEY_BUNDLE_SITEID2 = "KEY_BUNDLE_SITEID2";
    public static final String KEY_BUNDLE_VISITID2 = "KEY_BUNDLE_VISITID2";
    public static final String KEY_BUNDLE_CLASSNAME = "KEY_CLASS_NAME";
    public static final String KEY_BUNDLE_CLASS_TYPE_OBJECT = "KEY_BUNDLE_CLASS_TYPE_OBJECT";
    public static final String KEY_BUNDLE_IS_PAS_CLASS = "KEY_BUNDLE_IS_PAS_CLASS";
    public static final String KEY_BUNDLE_QB_VIEW_STATE = "KEY_BUNDLE_QB_VIEW_STATE";
    public static final String KEY_BUNDLE_LATITUDE = "KEY_BUNDLE_LATITUDE";
    public static final String KEY_BUNDLE_LONGITUDE = "KEY_BUNDLE_LONGITUDE";
    public static final String KEY_BUNDLE_SHOW_CLASSES = "KEY_BUNDLE_SHOW_CLASSES";
    public static final String KEY_BUNDLE_CLASS_DATE = "KEY_BUNDLE_CLASS_DATE";
    public static final String KEY_BUNDLE_CLASS_END_TIME = "KEY_BUNDLE_CLASS_END_TIME";
    public static final String KEY_BUNDLE_CLASS_START_TIME = "KEY_BUNDLE_CLASS_START_TIME";
    public static final String KEY_BUNDLE_LOCATION = "KEY_BUNDLE_LOCATION";
    public static final String KEY_BUNDLE_LOCATION_NAME = "KEY_BUNDLE_LOCATION_NAME";
    public static final String KEY_BUNDLE_VISIT_TYPE_ID = "KEY_BUNDLE_VISIT_TYPE_ID";
    public static final String KEY_BUNDLE_FROM_BUSINESS_LINK = "KEY_BUNDLE_FROM_BUSINESS_LINK";
    public static final String KEY_BUNDLE_SEARCHTERM = "KEY_BUNDLE_SEARCHTERM";
    public static final String KEY_BUNDLE_SEARCH_FOCUS = "KEY_BUNDLE_SEARCH_FOCUS";
    public static final String SEARCH_FOCUS_QUERY = "SEARCH_FOCUS_QUERY";
    public static final String SEARCH_FOCUS_LOCATION = "SEARCH_FOCUS_LOCATION";
    public static final String KEY_BUNDLE_LOCATION_SEARCHTERM = "KEY_BUNDLE_LOCATION_SEARCHTERM";
    public static final String KEY_BUNDLE_DISTANCE_VALUE = "KEY_BUNDLE_DISTANCE_VALUE";
    public static final String KEY_BUNDLE_DISTANCE_CHANGED = "KEY_BUNDLE_DISTANCE_CHANGED";
    public static final String KEY_BUNDLE_IS_IN_MILES = "KEY_BUNDLE_IS_IN_MILES";
    public static final String KEY_BUNDLE_DEFAULT_SEARCH = "KEY_BUNDLE_DEFAULT_SEARCH";
    public static final String KEY_BUNDLE_SEARCH_MODEL = "KEY_BUNDLE_SEARCH_MODEL";
    public static final String KEY_BUNDLE_STRING_COLOR_THEME = "KEY_BUNDLE_STRING_COLOR_THEME";
    public static final String KEY_BUNDLE_SHOW_UNLIMITED_RANGE = "KEY_BUNDLE_SHOW_UNLIMITED_RANGE";
    public static final String KEY_BUNDLE_SHOW_PASSES = "KEY_BUNDLE_SHOW_PASSES";
    public static final String KEY_BUNDLE_SECTION_NAME = "KEY_BUNDLE_SECTION_NAME";
    public static final String KEY_BUNDLE_IS_FROM_NEW_SEARCH_HOMEPAGE = "KEY_BUNDLE_IS_FROM_NEW_SEARCH_HOMEPAGE";
    public static final String KEY_BUNDLE_SELECTED_CATEGORY = "KEY_BUNDLE_SELECTED_CATEGORY";
    public static final String KEY_BUNDLE_SELECTED_VERTICAL = "KEY_BUNDLE_SELECTED_VERTICAL";
    public static final String KEY_BUNDLE_HAS_SEARCHED_TERM_CHANGED = "KEY_BUNDLE_HAS_SEARCHED_TERM_CHANGED";
    public static final String KEY_BUNDLE_SHOULD_SEARCH_WITH_LOCATION_ONLY = "KEY_BUNDLE_SHOULD_SEARCH_WITH_LOCATION_ONLY";
    public static final String KEY_BUNDLE_NO_RESULTS_UI_TEXT_FOR_SPELL_CORRECT = "KEY_BUNDLE_NO_RESULTS_UI_TEXT_FOR_SPELL_CORRECT";


    //Deal search parameter bundle keys
    public static final String KEY_BUNDLE_DEAL_PARAMETER_SEARCH_TEXT = "KEY_BUNDLE_DEAL_PARAMETER_SEARCH_TEXT";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_CREATED_START = "KEY_BUNDLE_DEAL_PARAMETER_CREATED_START";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_CREATED_END = "KEY_BUNDLE_DEAL_PARAMETER_CREATED_END";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_NUMBER_OF_DEALS_PER_SUBSCRIBER = "KEY_BUNDLE_DEAL_PARAMETER_NUMBER_OF_DEALS_PER_SUBSCRIBER";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_LIMIT_DEALS_TO_FAVORITE_SUBSCRIBERS = "KEY_BUNDLE_DEAL_PARAMETER_LIMIT_DEALS_TO_FAVORITE_SUBSCRIBERS";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_INCLUDE_UNQUALIFIED = "KEY_BUNDLE_DEAL_PARAMETER_INCLUDE_UNQUALIFIED";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_BUSINESS_TAGS = "KEY_BUNDLE_DEAL_PARAMETER_BUSINESS_TAGS";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_DEAL_TAGS = "KEY_BUNDLE_DEAL_PARAMETER_DEAL_TAGS";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_SKIP = "KEY_BUNDLE_DEAL_PARAMETER_SKIP";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_TOP = "KEY_BUNDLE_DEAL_PARAMETER_TOP";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_LOCATION = "KEY_BUNDLE_DEAL_PARAMETER_LOCATION";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_PRICE_LOW = "KEY_BUNDLE_DEAL_PARAMETER_PRICE_LOW";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_PRICE_HIGH = "KEY_BUNDLE_DEAL_PARAMETER_PRICE_HIGH";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_SUBSCRIBER_IDS = "KEY_BUNDLE_DEAL_PARAMETER_SUBSCRIBER_IDS";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_MASTERLOCATION_IDS = "KEY_BUNDLE_DEAL_PARAMETER_MASTERLOCATION_IDS";
    public static final String KEY_BUNDLE_DEAL_PARAMETER_SORT = "KEY_BUNDLE_DEAL_PARAMETER_SORT";

    //Explore search parameter bundle keys
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_QUERY = "KEY_BUNDLE_EXPLORE_PARAMETER_QUERY";
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_CATEGORIES = "KEY_BUNDLE_EXPLORE_PARAMETER_CATEGORIES";
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_FLEXIBLE_PRICING_ONLY = "KEY_BUNDLE_EXPLORE_PARAMETER_FLEXIBLE_PRICING_ONLY";
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_VIRTUAL_ONLY = "KEY_BUNDLE_EXPLORE_PARAMETER_VIRTUAL_ONLY";
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_FLEX_ONLY = "KEY_BUNDLE_EXPLORE_PARAMETER_FLEX_ONLY";
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_CLEAR_VIRTUAL_AND_SEARCH = "KEY_BUNDLE_EXPLORE_PARAMETER_CLEAR_VIRTUAL";
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_FAVORITES_ONLY = "KEY_BUNDLE_EXPLORE_PARAMETER_FAVORITES_ONLY";
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_LATITUDE = "KEY_BUNDLE_EXPLORE_PARAMETER_LATITUDE";
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_LONGITUDE = "KEY_BUNDLE_EXPLORE_PARAMETER_LONGITUDE";
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_RADIUS = "KEY_BUNDLE_EXPLORE_PARAMETER_RADIUS";
    public static final String KEY_BUNDLE_EXPLORE_PARAMETER_FRAGMENT_TAG = "KEY_BUNDLE_EXPLORE_PARAMETER_FRAGMENT_TAG";

    // Miscellaneous bundle keys
    public static final String KEY_BUNDLE_CLASSDESC_FAILOVER = "KEY_BUNDLE_CLASSDESC_FAILOVER";
    public static final String KEY_BUNDLE_LAT_LNG = "KEY_BUNDLE_LAT_LNG";
    public static final String KEY_USER_ID = "KEY_USER_ID";
    public static final String KEY_BUNDLE_PRICING_OPTION_ID = "KEY_BUNDLE_PRICING_OPTION_ID";
    public static final String KEY_BUNDLE_CHECKOUT_MODE = "KEY_BUNDLE_CHECKOUT_MODE";
    public static final String KEY_BUNDLE_NOTIFICATIONID = "KEY_BUNDLE_NOTIFICATIONID";
    public static final String KEY_BUNDLE_PRODUCT_ID = "KEY_BUNDLE_PRODUCT_ID";
    public static final String KEY_BUNDLE_SERVICE_CATEGORY_NAME = "KEY_BUNDLE_SERVICE_CATEGORY_NAME";
    public static final String KEY_BUNDLE_SERVICE_CATEGORY_PROGRAM_TYPE = "KEY_BUNDLE_SERVICE_CATEGORY_PROGRAM_TYPE";
    public static final String KEY_BUNDLE_SERVICE_CATEGORY_ID = "KEY_BUNDLE_SERVICE_CATEGORY_ID";
    public static final String KEY_BUNDLE_SERVICE_CATEGORY_IDS = "KEY_BUNDLE_SERVICE_CATEGORY_IDS";
    public static final String KEY_LOCATION_STUDIO_NAME = "locationStudioName";
    public static final String KEY_BUNDLE_CALENDAR_START_TIME = "KEY_BUNDLE_CALENDAR_START_TIME";
    public static final String KEY_BUNDLE_CALENDAR_END_TIME = "KEY_BUNDLE_CALENDAR_END_TIME";
    public static final String KEY_BUNDLE_CALENDAR_DATE = "KEY_BUNDLE_CALENDAR_DATE";
    public static final String KEY_BUNDLE_DURATION = "KEY_BUNDLE_DURATION";
    public static final String KEY_BUNDLE_HAS_JUST_LOGGED_OUT = "hasJustLoggedOut";
    public static final String KEY_BUNDLE_HAS_GPS_STATE_LOGGED = "hasGpsStateLogged";
    public static final String KEY_BUNDLE_SELECTED_ANY_STAFF = "selectedAnyStaff";
    public static final String KEY_VERTICAL_ID = "KEY_VERTICAL_ID";
    public static final String KEY_BUNDLE_UA_LINK_AUTOFAVORITE = "userActionLinkAutoFavorite";
    public static final String KEY_BUNDLE_UA_LINK_SOURCE = "fromUserActionLink";
    public static final String KEY_BUNDLE_TRIGGER_SIGN_IN = "KEY_BUNDLE_TRIGGER_SIGN_IN";
    public static final String KEY_BUNDLE_SHOW_ACTIVITY_DASHBOARD = "KEY_BUNDLE_SHOW_ACTIVITY_DASHBOARD";
    public static final String KEY_BUNDLE_SHOW_TOMORROW = "KEY_BUNDLE_SHOW_TOMORROW";
    public static final String KEY_BUNDLE_NOTES_AND_INSTRUCTIONS = "KEY_BUNDLE_NOTES_AND_INSTRUCTIONS";
    public static final String KEY_BUNDLE_CATALOG_ITEMS = "KEY_BUNDLE_CATALOG_ITEMS";
    public static final String KEY_BUNDLE_APPT_RESPONSE = "KEY_BUNDLE_APPT_RESPONSE";
    public static final String KEY_BUNDLE_ERROR = "KEY_BUNDLE_ERROR";
    public static final String KEY_BUNDLE_FORCE_LOGIN = "KEY_BUNDLE_FORCE_LOGIN";
    public static final String KEY_BUNDLE_DEEP_LINK_AFTER_LOGIN = "KEY_BUNDLE_DEEP_LINK_AFTER_LOGIN";
    public static final String KEY_BUNDLE_SELECTED_FAMILY_ACCOUNT = "KEY_BUNDLE_SELECTED_FAMILY_ACCOUNT";
    public static final String KEY_BUNDLE_CLASS_TIME_ZONE_ID = "KEY_BUNDLE_CLASS_TIME_ZONE_ID";
    public static final String KEY_BUNDLE_SELECTED_KEYWORD = "KEY_BUNDLE_SELECTED_KEYWORD";
    public static final String KEY_BUNDLE_FULL_SCREEN_CONFIRMATION_SCREEN_DATA = "KEY_BUNDLE_FULL_SCREEN_CONFIRMATION_SCREEN_DATA";
    public static final String KEY_BUNDLE_GIFT_CARD_FLOW = "KEY_BUNDLE_GIFT_CARD_FLOW";

    public static final String APPOINTMENT_TYPE_ID = "APPOINTMENT_TYPE_ID";

    public static final String BUNDLE_LOGIN_STATE = "BUNDLE_LOGIN_STATE";
    public static final String BUNDLE_EMAIL_ADDRESS = "BUNDLE_EMAIL_ADDRESS";
    public static final String BUNDLE_PASSWORD = "BUNDLE_PASSWORD";
    public static final String BUNDLE_PASSWORD_CONFIRM = "BUNDLE_PASSWORD_CONFIRM";
    public static final String BUNDLE_FIRST_NAME = "BUNDLE_FIRST_NAME";
    public static final String BUNDLE_LAST_NAME = "BUNDLE_LAST_NAME";
    public static final String BUNDLE_COUNTRY = "BUNDLE_COUNTRY";

    public static final String ALARM_ACTION_REVIEW = "rate_review_notification";
    public static final String ALARM_ACTION_GEOFENCE_SIGNIN = "alarm_action_geofence_signin";

    public static final float DEFAULT_LATITUDE = BuildConfig.DEBUG ? 48.8566f : 35.275714f;
    public static final float DEFAULT_LONGITUDE = BuildConfig.DEBUG ? 2.3522f : -120.662147f;
    public static final float DEFAULT_ACCURACY = 10_000; // meters
    public static final float DEFAULT_DISTANCE_VALUE = 25f;
    //When user select 50+ on seek bar we are considering which is more than 50 i.e 51
    public static final float MAXIMUM_DISTANCE_VALUE_50_PLUS = 51f;
    //Extended search will work at max 50 Miles.
    public static final float MAXIMUM_DISTANCE_VALUE_FOR_EXTENDED_SEARCH = 50f;
    public static final String KEY_BUNDLE_STAFF_NAME = "staffname";
    public static final String CL_LAST_ACTIVITY_STARTED = "last_activity_started";
    public static final String FROM_OFFER = "from_offer";
    public static final int MINUTES_BEFORE_SIGNIN = 15;

    public static final String INSTALL_REFERRER = "install_referrer";
    public static final int DEFAULT_MAX_DESCRIPTION_LINES = 2;
    public static final String SHOW_EVENTS = "show_events";
    public static final String CHECKOUT_FAILED = "checkout_failed";
    public static final String EXTRA_TYPE = "extra_type";
    public static final String VISIT_JSON = "visit_json";
    public static final String AMEX_LONG = "americanexpress";
    public static final int REQUEST_CODE_REQUIRED_FIELDS = 54545;
    public static final String KEY_BUNDLE_MASTER_LOCATION_ID = "location_master_id";
    public static final String KEY_BUNDLE_LOCATION_REFERENCE = "key_bundle_locationReference";
    public static final String KEY_BUNDLE_ORIGIN_VIEW = "key_bundle_origin_view";
    public static final String KEY_BUNDLE_ORIGIN_COMPONENT = "key_bundle_origin_component";
    public static final String KEY_BUNDLE_TRACKER_EVENT_PATH_IDENTIFIER = "key_bundle_event_path_identifier";
    public static final String KEY_BUNDLE_CLASS_REF_JSON = "key_bundle_classReference";
    public static final String KEY_BUNDLE_INVENTORY_SOURCE = "key_bundle_inventory_source";
    public static final String KEY_BUNDLE_CONFIRMATION_FLOW = "key_bundle_confirmation_flow";
    public static final String KEY_BUNDLE_SHOW_ATTRIBUTION_SURVEY = "key_bundle_show_attribution_survey";
    public static final String KEY_BUNDLE_ORDER_ID = "key_bundle_order_id";
    public static final String KEY_BUNDLE_PAYMENT_REQUIRED = "key_bundle_payment_required";
    public static final String KEY_TOTAL_REVIEWS = "key_total_reviews";
    public static final String OPTIMIZELY_DEVELOPMENT_KEY = "QDH6vA8H3ZNFdneFymxzqc";
    public static final String OPTIMIZELY_DEVELOPMENT_URL = "https://cdn.optimizely.com/datafiles/QDH6vA8H3ZNFdneFymxzqc.json";
    public static final String OPTIMIZELY_PRODUCTION_KEY = "Ff7G1aosbAWaK3NYF8iNSs";
    public static final String OPTIMIZELY_PRODUCTION_URL = "https://cdn.optimizely.com/datafiles/Ff7G1aosbAWaK3NYF8iNSs.json";

    public static final String CONTACT_SUPPORT_URL = "https://support.mindbody.io/s/contactsupport";
    public static final String LOGIN_HELP_URL = "https://support.mindbody.io/s/contactsupport";
    public static final String FLEX_FAQ_ARTICLE = "https://support.mindbodyonline.com/s/article/Mindbody-Flex-FAQ";
    public static final String PRIVACY_POLICY_URL = "https://company.mindbodyonline.com/legal/privacy-policy";
    public static final String GENDER_OPTIONS_INFO_URL = "https://support.mindbodyonline.com/s/article/Gender-options-in-the-Mindbody-app";
    public static final String MANAGE_MEMBER_ACCOUNTS_URL = "https://account.mindbodyonline.com";
    public static final String LEARN_MORE_CLASSPASS_URL = "https://classpass.com/try/mbfriend";
    public static final String SHARE_CLASSPASS_URL = "https://classpass.com/walkthrough/mindbodyspecial";
    public static final String LOTTIE_FITNESS_PATH = "lottie/fitness/";
    public static final String ENROLLMENT = "enrollment";
    public static final String APPOINTMENT = "appointment";
    public static final String CLASS = "class";
    public static final String FROM_WIDGET = "from_widget";
    public static final String OPTED_INTO_CONNECT = "opted_into_connect";
    public static final String GOOGLE_MAPS_URL = "http://maps.google.com/maps?daddr=";
    public static final String KEY_BUNDLE_DISPLAY_MODE = "KEY_BUNDLE_DISPLAY_MODE";
    public static final String KEY_BUNDLE_CLASS_DESCRIPTION_ID = "KEY_BUNDLE_CLASS_DESCRIPTION_ID";
    public static final String KEY_BUNDLE_CLASS_TYPE_ID = "KEY_BUNDLE_CLASS_TYPE_ID";
    public static final String KEY_BUNDLE_CLASS_INSTANCE_ID = "KEY_BUNDLE_CLASS_INSTANCE_ID";
    public static final String KEY_BUNDLE_CLASS_TITLE = "KEY_BUNDLE_CLASS_TITLE";
    public static final String KEY_BUNDLE_REVIEW_ID = "KEY_BUNDLE_REVIEW_ID";
    public static final String KEY_BUNDLE_STAFF_ID = "KEY_BUNDLE_STAFF_ID";
    public static final String KEY_BUNDLE_SEARCH_LOCATION_LATLONG = "KEY_BUNDLE_SEARCH_LOCATION_LATLONG";
    public static final int REQUEST_CODE_CHECKOUT = 4444;
    public static final int UNLIMITED_SESSION_THRESHOLD = 1000;
    public static final String KEY_OAUTH2_AUTHORIZATION_CODE = "KEY_OAUTH2_AUTHORIZATION_CODE";
    public static final String KEY_BUNDLE_SKIP_PROMPT = "KEY_BUNDLE_SKIP_PROMPT";
    public static final String KEY_BUNDLE_CATALOG_ITEM_ID = "KEY_BUNDLE_CATALOG_ITEM_ID";
    public static final String KEY_BUNDLE_CATALOG_ITEM = "KEY_BUNDLE_CATALOG_ITEM";
    public static final String KEY_BUNDLE_CATALOG_PACKAGE = "KEY_BUNDLE_CATALOG_PACKAGE";
    public static final String KEY_BUNDLE_APPOINTMENT_TYPE = "KEY_BUNDLE_APPOINTMENT_TYPE";
    public static final String KEY_BUNDLE_APPOINTMENT_BOOKABILITY_STATUS = "KEY_BUNDLE_APPOINTMENT_BOOKABILITY_STATUS";
    public static final String KEY_BUNDLE_STAFF_REF_JSON = "KEY_BUNDLE_STAFF_REF";
    public static final String KEY_BUNDLE_STAFF_DISPLAY_REF_JSON = "KEY_BUNDLE_STAFF_DISPLAY_REF_JSON";
    public static final String KEY_BUNDLE_APPOINTMENT_SERVICE_REF = "KEY_BUNDLE_APPOINTMENT_SERVICE_REF";
    public static final String KEY_BUNDLE_APPOINTMENT_NAME = "KEY_BUNDLE_APPOINTMENT_NAME";
    public static final String KEY_BUNDLE_APPOINTMENT_ID = "KEY_BUNDLE_APPOINTMENT_ID";
    public static final String KEY_BUNDLE_CATEGORY_VERTICAL = "KEY_BUNDLE_CATEGORY_VERTICAL";
    public static final String KEY_BUNDLE_FILTER_TYPE = "KEY_BUNDLE_FILTER_TYPE";

    public static final int LISTVIEW_DIVIDER_HEIGHT = 3;
    public static final String KEY_BUNDLE_SELECTED_TAB = "selectedTab";
    public static final int READ_CALENDAR_PERMISSIONS_CODE = 65;
    public static final int WRITE_CALENDAR_PERMISSIONS_CODE = 66;
    public static final String KEY_BUNDLE_SEARCH_STATE = "KEY_BUNDLE_SEARCH_STATE";
    public static final String KEY_BUNDLE_CURRENT_ADDRESS_TEXT = "KEY_BUNDLE_CURRENT_ADDRESS_TEXT";
    public static final int RESULT_BOOKED = 47129;
    public static final int RESULT_BOOKED_WAITLIST = 47130;
    public static final int RESULT_BOOKED_DEAL = 47131;
    public static final int RESULT_UNBOOKED = 47128;
    public static final String KEY_BUNDLE_ANALYTICS_PREFIX = "KEY_BUNDLE_ANALYTICS_PREFIX";
    public static final String KEY_BUNDLE_IS_MARKETPLACE = "KEY_BUNDLE_IS_MARKETPLACE";
    public static final String KEY_BUNDLE_IS_REQUEST = "KEY_BUNDLE_IS_REQUEST";
    public static final String KEY_BUNDLE_HIDE_APPOINTMENT_BOOKING_CONFIRMATION_DIALOG = "KEY_BUNDLE_HIDE_APPOINTMENT_BOOKING_CONFIRMATION_DIALOG";
    public static final int DEAL_CHECKOUT_REQUESTCODE = 156;
    public static final int RESULT_FAILURE = 9;
    public static final int APPOINTMENT_BOOK_REQUEST_CODE = 716;
    public static final int FLEX_CREDIT_REQUEST_CODE = 717;
    public static final String SHOW_BUSINESS_DETAILS = "SHOW_BUSINESS_DETAILS";
    public static final String KEY_BUNDLE_DEAL = "KEY_BUNDLE_DEAL";
    public static final String KEY_BUNDLE_IS_LMO_WORKFLOW = "KEY_BUNDLE_IS_LMO_WORKFLOW";
    public static final String KEY_BUNDLE_DROP_IN_PRICE = "KEY_BUNDLE_DROP_IN_PRICE";
    public static final String KEY_BUNDLE_SECONDARY_CATEGORIES = "KEY_BUNDLE_SECONDARY_CATEGORIES";

    public static final float TOOLBAR_ELEVATION = 4.0f;
    public static final String PAYMENT_METHODS_ID = "PaymentMethods";
    public static final String VIRTUAL_SEARCH_KEY = "Virtual";
    public static final float VIRTUAL_SEARCH_DEFAULT_RADIUS = 50f;
    public static final String KEY_BUNDLE_PHONE = "KEY_BUNDLE_PHONE";
    public static final String KEY_BUNDLE_VISIT = "KEY_BUNDLE_VISIT";

    public class Location {
        public static final String MY_LOCATION = "My Location";
        public static final String CURRENT_AREA = "Current map area";
    }

    public static final String MASTERCARD = "MasterCard";
    public static final String VISA = "Visa";
    public static final String AMEX = "AMEX";
    public static final String AMERICAN_EXPRESS = "AmericanExpress";
    public static final String DISCOVER = "Discover";
    public static final String EXCHANGE = "Exchange";
    public static final String EXCHANGE_CARD = "ExchangeCard";
    public static final String IDEAL="Ideal";
    public static final String BANCONTACT="Bancontact";
    public static final String KLARNA="Klarna";
    public static final String GOOGLE_PAY="GooglePay";
    public static final String TWINT="Twint";
    public static final String FPX="Fpx";
    public static final String ALIPAY="Alipay";
    public static final String PAYNOW = "PayNow";
    public static final String WECHATPAY = "WeChatPay";
    public static final String MERCHANT_DISPLAY_NAME="Mindbody, Inc.";
    public static final String MERCHANT_COUNTRY_CODE="US";
    public static final String SWEDEN="Sweden";
    public static final String NETHERLANDS="Netherlands";
    public static final String SWITZERLAND="Switzerland";
    public static final String MALAYSIA="Malaysia";
    public static final String HONGKONG="Hongkong";
    public static final String US="US";
    public static final String DEVELOPMENT="development";
    public static final String PRODUCTION="production";
    public static final int MORNING_CUTOFF_HOUR = 11;
    public static final int AFTERNOON_CUTOFF_HOUR = 16;
    public static final int FIFTEEN_MIN_IN_MILLS = 900000;
    public static final int TWENTY_MIN_IN_MILLS = 1200000; // Signin notification timeout
    public static final int GEOFENCE_START_WINDOW_IN_MILLS = 900000;
    public static final int ACTIONBAR_ITEM_DISABLE_TIME_IN_MS = 2000;
    public static final int LIABILITY_WIDTH_IN_PIXELS = 500;
    public static final int LIVESTREAM_BUTTON_ENABLED_WINDOW_MINUTES = 60;

    public static final long KEYBOARD_WAIT = 500; // ms

    public static final int MORNING_BUTTON_ID = 1;
    public static final int AFTERNOON_BUTTON_ID = 2;
    public static final int EVENING_BUTTON_ID = 3;

    public static final int FITNESS_VERTICAL_ID = 0;
    public static final int WELLNESS_VERTICAL_ID = 1;
    public static final int BEAUTY_VERTICAL_ID = 2;
    public static final int EXPLORE_FRAGMENT_ID = 3;

    public static final int SCHEDULE_TAB = 0;
    public static final int PASSES_TAB = 1;

    public static final String APPS_FLYERS_APP_DEV_KEY = "7WpvSBoS65Fzh6LvyxCLBn";

    public static final boolean DEFAULT_WORKPLACE_FILTER_ENABLED = false;
    public static final float[] SEARCH_DISTANCE_STEPS_IN_MILES = {0.5f, 2f, 5f, 10f, 25f};
    public static final float[] SEARCH_DISTANCE_STEPS_IN_KILOMETERS = {1f, 5f, 8f, 15f, 40f};
    public static final String AUTOMATIC_PREF_VALUE = "auto";
    public static final String MILES_PREF_VALUE = "mi";
    public static final String KILOMETERS_PREF_VALUE = "km";
    public static final String PLAY_FLAVOR = "play";

    // Notification Channel IDs
    public static final String CHANNEL_ID_REVIEW = "channel_id_review";
    public static final String CHANNEL_ID_SIGN_IN = "channel_id_sign_in";
    public static final String CHANNEL_ID_ACTIVITY = "channel_id_activity";

    // which fitness tracker the user prefers
    public static final String PREF_FITNESS_TRACKER = "PREF_FITNESS_TRACKER";

    // Avoids multiple DSPO supported calls if still within 300 feet of last API call
    public static final double DSPO_SUPPORTED_APPROX = 1e3;
    // Minimum number of tiles needed to display carousel at all
    public static final int MIN_HOME_PAGE_TILES = 3;
    // Maximum number of tiles to show in carousel
    public static final int MAX_HOME_PAGE_TILES = 10;
    // Similar history recommender time range to set on Explore page when See All clicked
    public static final int SIMILAR_SEE_ALL_START = 6;
    public static final int SIMILAR_SEE_ALL_END = 21;
    //TODO: 1040032 Maximum number of intro offers to show in carousel

    // Flex starting soon time range is now until 3 hours from now
    public static final int STARTING_SOON_NUM_HOURS = 3;
    // Flex early bird time range is 5am - 10am
    public static final int EARLY_BIRD_START_HOUR = 5;
    public static final int EARLY_BIRD_END_HOUR = 10;
    // Flex night owl time range is 7pm - 10pm
    public static final int NIGHT_OWL_START_HOUR = 19;
    public static final int NIGHT_OWL_END_HOUR = 22;

    public static final int MAX_DEALS_CAROUSEL = 5;

    public static final String KEY_BUNDLE_NAVIGATE_FROM_PROFILE_TAB = "navigateFromProfileTab";
    public static final String KEY_BUNDLE_SELECTED_DEPENDENT_USER_ID = "selectedDependentUserId";

    public static final String KEY_BUNDLE_DEPENDENT_USER_FIRST_NAME = "dependent_user_firstname";
    public static final String KEY_BUNDLE_DEPENDENT_USER_LAST_NAME = "dependent_user_lastname";


    //Referral link constants
    public static final String KEY_ATTRIBUTION_SOURCE = "studioId";


    public static final int FILTER_CATEGORY_OFFSET_LIMIT = 2;
    public static final int FILTER_CATEGORY_GRID_NUM_COLUMNS = 3;
    public static final int FILTER_CATEGORY_GRID_COLUMNS_SPACING_DP = 0;


    //Home Screen Headline Experiment
    public static final String FEATURE_KEY = "Feature_Key";
    public static final String FEATURE_VARIABLES = "Feature_Variables";

    public static final String TOTAL_DEALS = "Total deals";
    public static final String TOTAL_RATINGS = "Total Ratings";
    public static final String ALLOW_MOBILE_CHECKIN = "Allow Mobile Checkin";
    public static final String AVERAGE_RATING = "Average Rating";
    public static final String ONLINE_STORE_ACTIVE = "Online Store Active";

    public static final int MAX_UPCOMING_HOME_V2 = 7;


    public static final int DEFAULT_REVIEW_MAX_DESCRIPTION_LINES = 3;

    //Received mapping of review type from backend team as below
    public enum ReviewType {
        UNKNOWN,
        CLASS,
        APPOINTMENT,
        LOCATION,
        STAFF,
        WORKSHOP,
        ENROLLMENT
    }

    public static final int REVIEW_TEXT_MIN_CHAR_LIMIT = 50;
    public static final int MAX_CHARS = 500;

    public static final String PREF_LAST_HOME_MAP_LOC_LATITUDE = "PREF_LAST_HOME_MAP_LOC_LATITUDE";
    public static final String PREF_LAST_HOME_MAP_LOC_LONGITUDE = "PREF_LAST_HOME_MAP_LOC_LONGITUDE";
    public static final int MAX_MARKERS_NEAR_YOU = 5;
    public static final double NEAR_YOU_MAP_ZOOM_LEVEL = 0.5;

    public static final String CLASS_VALUE = "Class";

    public static final int DISTANCE_ONE = 1;
    public static final int DISTANCE_THREE = 3;
    public static final int DISTANCE_FIVE = 5;
    public static final int DISTANCE_TEN = 10;
    public static final int DISTANCE_FIFTEEN = 15;
    public static final int DISTANCE_THIRTY = 30;
    public static final int DISTANCE_FIFTY = 50;

    public static final String SORTING_OPTION_RECOMMENDED = "Recommended";
    public static final String SORTING_OPTION_RATINGS = "Ratings";
    public static final String SORTING_OPTION_DISTANCE = "Distance";
    // TODO: Need to use sealed class later for this sorting option

    public static final String LOCATION_BUSINESS_IDENTIFIES = "location_business_identifies";
    public static final String LOCATION_CROWD_SAFES = "location_crowd_safes";

    public static final String HOME_INTRO_OFFER_CONTEXT = "HOME_INTRO_OFFER_CONTEXT";
    public static final String HOME_CAROUSEL_CONTEXT = "HOME_CAROUSEL_CONTEXT";

    public static final String DEV_BUILD_SUFFIX = "-dev";

    public static final String KEY_SEARCH_TAB_TYPE_FOR_EVENT_TRACKING = "search_tab_type_for_event_tracking";

    public static final String STUDIO_GALLERY_SOURCE_DEFAULT = "default";

    public static final String DEAL_SERVICE_PRICING_OPTION_TYPE = "ServicePricingOptionType";

    public static final String SMS_TEXTING_TERMS = "SMS_TEXTING_TERMS";

    public static final String CONTRACT_CANCELLATION_URL = "https://clients.mindbodyonline.com/launch";

}

