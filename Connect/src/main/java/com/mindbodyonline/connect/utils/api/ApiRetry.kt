package com.mindbodyonline.connect.utils.api

import com.android.volley.VolleyError
import com.mindbodyonline.android.util.log.MBLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeout

/**
 * Generic function to execute suspending operations with retry capability
 */
suspend fun <T> withRetry(
    maxRetries: Int = 3,
    timeoutMs: Long = 30000,
    backoffMultiplier: Double = 1.0,
    retryDelayMs: Long = 300,
    block: suspend () -> T
): T {
    var currentAttempt = 0
    var lastException: Throwable? = null
    var currentDelay = retryDelayMs

    while (currentAttempt <= maxRetries) {
        try {
            return withTimeout(timeoutMs) {
                // Initial call without delay
                block()
            }
        } catch (e: Throwable) {
            if (!shouldRetry(e)) {
                throw e
            }
            lastException = e
            currentAttempt++

            if (currentAttempt > maxRetries) {
                MBLog.e("FTCAuditLog", "All retry attempts failed after $maxRetries retries", e)
                break
            }

            MBLog.d(
                "FTCAuditLog",
                "Retry attempt $currentAttempt/$maxRetries after $currentDelay ms",
                e
            )
            // Delay for the retry call
            delay(currentDelay)

            // Calculate next delay with backoff multiplier
            currentDelay = (currentDelay * backoffMultiplier).toLong()
        }
    }

    throw lastException ?: IllegalStateException("Unknown error during retry")
}

fun shouldRetry(e: Throwable): Boolean {
    return when (e) {
        is VolleyError -> {
            val statusCode = e.networkResponse?.statusCode ?: 0
            statusCode >= 500 || statusCode == 444
        }

        else -> true
    }
}
