package com.mindbodyonline.connect.utils

import com.mindbodyonline.android.util.log.MBLog

object AppLaunchTracker {

    enum class AppLaunchStep {
        APP_LAUNCH,
        TOUR_ACTIVITY_ON_CREATE,
        CHECK_<PERSON><PERSON>CH_AND_USER_ACTION_LINKS,
        FORCE_UPDATE_VERSIONS_API_CALL,
        FAV_BUSINESSES_OBSERVER,
        ON_FETCH_USER_ERROR,
        ON_FETCH_TOKEN_ERROR,
        HAND<PERSON>_AUTH_CODE_RESPONSE,
        CHECK_IF_USER_OUTSIDE_US,
        SAVE_USER_LOCATION,
        HANDLE_UPDATE_REQUIREMENT,
        CHECK_FOR_UPDATES_AND_CONTINUE,
        BEGIN_LOGIN_WORKFLOW,
        CONTINUE_LOGIN,
        ON_LOGGED_BACK_IN_ERROR,
        ON_LOGGED_BACK_IN_SUCCESS,
        CHECK_TERMS_OF_USE_AND_FINISH_LOGIN,
        RETRIEVE_GUEST_TOKEN_AND_GO_TO_MAIN,
        RUN_BA<PERSON>KGROUND_TASKS,
        UPDATE_MARKETING_OPT_IN,
        UPDATE_USER_SITES,
        CONTINUE_AS_GUEST,
        FIND_LANDING_FRAGMENT_AND_NAVIGATE,
        ON_FETCH_USER_BEFORE_NAV_ERROR,
        ON_FETCH_USER_BEFORE_NAV_SUCCESS,
        LAND_ON_DEFAULT_FRAGMENT,
        LAUNCH_MAIN_ACTIVITY,
        MAIN_ACTIVITY_ON_CREATE,
        HOME_FRAGMENT_ON_VIEW_CREATED,
        END_LAUNCH,
    }

    @JvmStatic
    fun trackAndClearPreviousSessionAppLaunchSteps() {
        val appLaunchSteps = SharedPreferencesUtils.getAppLaunchSteps()
        if (appLaunchSteps.isNotEmpty()) {
            BreadcrumbsUtils.breadcrumbAppLaunchSteps(appLaunchSteps)
            MBLog.d(
                BreadcrumbName.APP_LAUNCH_STEPS.displayName,
                "Previous session app launch steps = $appLaunchSteps"
            )
            SharedPreferencesUtils.removeAppLaunchSteps()
        }
    }

    @JvmStatic
    fun addLaunchStep(step: AppLaunchStep) {
        val currentAppLaunchSteps = SharedPreferencesUtils.getAppLaunchSteps()
        val updatedAppLaunchSteps =
            if (currentAppLaunchSteps.isEmpty()) step.name.lowercase() else "$currentAppLaunchSteps,${step.name.lowercase()}"
        MBLog.d(
            BreadcrumbName.APP_LAUNCH_STEPS.displayName,
            "Updated app launch steps = $updatedAppLaunchSteps"
        )
        SharedPreferencesUtils.putAppLaunchSteps(updatedAppLaunchSteps)
    }
}
