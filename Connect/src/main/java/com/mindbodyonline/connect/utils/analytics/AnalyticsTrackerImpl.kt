package com.mindbodyonline.connect.utils.analytics

import com.mindbodyonline.analytics.AnalyticsApi
import com.mindbodyonline.analytics.Event
import com.mindbodyonline.connect.analytics.APP
import com.mindbodyonline.connect.analytics.CLASS_ALGORITHM
import com.mindbodyonline.connect.analytics.CLASS_SEARCH_ENGINE
import com.mindbodyonline.connect.analytics.COUNTRY
import com.mindbodyonline.connect.analytics.DEALS_NEAR_YOU_DESTINATION
import com.mindbodyonline.connect.analytics.DEVICE_ID
import com.mindbodyonline.connect.analytics.EXPERIMENTS
import com.mindbodyonline.connect.analytics.IS_LOGGED_IN
import com.mindbodyonline.connect.analytics.IS_VERIFIED
import com.mindbodyonline.connect.analytics.LOCATION_ALGORITHM
import com.mindbodyonline.connect.analytics.LOCATION_SEARCH_ENGINE
import com.mindbodyonline.connect.analytics.LOGGED_IN_USER
import com.mindbodyonline.connect.analytics.MINDBODY_APP
import com.mindbodyonline.connect.analytics.SHOULD_VALIDATE_CREDIT_CARD
import com.mindbodyonline.connect.analytics.UNIVERSAL_ID
import com.mindbodyonline.connect.analytics.USER_COUNTRY
import com.mindbodyonline.connect.analytics.USER_ID
import com.mindbodyonline.connect.analytics.USER_LOCATION
import com.mindbodyonline.connect.tealium.TealiumHelper
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.toUserLocationMap
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.framework.abvariant.ABHelperUtils
import com.mindbodyonline.framework.abvariant.BUCKET_NAME

class AnalyticsTrackerImpl(private val analyticsApi: AnalyticsApi) : AnalyticsTracker {

	private fun getGlobalProperties(): Map<String, Any> {
		val userLocation = SharedPreferencesUtils.getGlobalIndicatorLocation().toUserLocationMap()

		val loggedInUser = MBAuth.getUser()?.let { user ->
			mapOf(
				UNIVERSAL_ID to (user.identityUserId ?: ""),
				USER_COUNTRY to (user.country ?: userLocation[COUNTRY]),
				USER_ID to user.id
			)
		} ?: emptyMap()

		return getSearchEngineAndAlgorithmProperties() + mapOf(
				APP to MINDBODY_APP,
				EXPERIMENTS to ServiceLocator.abTestFramework.getRunningExperimentsWithVariations(),
				IS_LOGGED_IN to !MBAuth.isGuestUser(),
				IS_VERIFIED to (MBAuth.getUser()?.isVerified ?: false),
				SHOULD_VALIDATE_CREDIT_CARD to ABHelperUtils.isValidateCreditCardEnabledForRWCC(),
				DEALS_NEAR_YOU_DESTINATION to ABHelperUtils.getDealsNearYouDestination().name.lowercase(),
				BUCKET_NAME to ABHelperUtils.getBucketingExperimentVariableValue(),
				LOGGED_IN_USER to loggedInUser,
				USER_LOCATION to userLocation,
				DEVICE_ID to (TealiumHelper.deviceAdId ?: ""),
		)
	}

	private fun getSearchEngineAndAlgorithmProperties(): Map<String, String> {
		return mapOf(
				CLASS_ALGORITHM to SharedPreferencesUtils.getAlgorithmUsedInClassTimes(),
				LOCATION_ALGORITHM to SharedPreferencesUtils.getAlgorithmUsedInLocations(),
				CLASS_SEARCH_ENGINE to SharedPreferencesUtils.getSearchEngineUsedInClassTimes(),
				LOCATION_SEARCH_ENGINE to SharedPreferencesUtils.getSearchEngineUsedInLocations()
		)
	}

	override fun track(event: Event) {
		analyticsApi.send(Event(event.name, event.type, event.data + getGlobalProperties()))
	}

	override fun trackSession(userId: String?) {
		analyticsApi.setUserId(userId)
	}

	override fun stitchAlias(alias: String) {
		analyticsApi.setAlias(alias)
	}
}
