package com.mindbodyonline.connect.utils

import android.app.Dialog
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.view.View
import androidx.core.widget.NestedScrollView
import com.mindbodyonline.android.util.log.MBLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

/**
 * Utility class for capturing screenshots of views and dialogs.
 * capture individual views, nested scroll views,and full dialogs, saving them as JPEG files.
 */
object ScreenshotUtil {
    private const val TAG = "FTCAuditLog"
    private const val DEFAULT_COMPRESSION_QUALITY = 50
    const val FILE_EXTENSION = ".jpeg"
    private var bitmapConfig: Bitmap.Config = Bitmap.Config.RGB_565

    //Captures a full dialog screenshot (including dialogs and popups if possible)
    @Throws(Exception::class)
    fun captureFullDialogScreenShot(dialog: Dialog, dialogView: View): Bitmap {
        try {
            // Try capturing the full screenshot of the screen
            return FullScreenScreenshotUtil.takeScreenshotBitmap(dialog.ownerActivity)
        } catch (e1: Exception) {
            MBLog.d(
                TAG, "Full screenshot failed: ${e1.message}, falling back to dialog view capture"
            )
            return captureViewToBitmap(dialogView)
        }
    }

    // Captures a complete dialog with scrollable content and text at bottom
    @Throws(Exception::class)
    fun captureFullDialogWithTextAtBottom(
        rootView: View,
        textView: View,
        nestedScrollView: NestedScrollView,
        callback: (Bitmap) -> Unit
    ) {
        val textViewBitmap = captureViewToBitmap(textView)
        captureNestedScrollViewFull(nestedScrollView) { scrollBitmap: Bitmap ->
            // Create a combined bitmap
            val totalHeight = scrollBitmap.height + textViewBitmap.height
            val resultBitmap = Bitmap.createBitmap(
                rootView.width, totalHeight, bitmapConfig
            ).apply {
                Canvas(this).also { canvas ->
                    canvas.drawBitmap(scrollBitmap, 0f, 0f, null)
                    canvas.drawBitmap(textViewBitmap, 0f, scrollBitmap.height.toFloat(), null)
                }
            }

            textViewBitmap.recycle()
            scrollBitmap.recycle()
            callback(resultBitmap)
        }
    }

    // Captures a nested scroll view fully, including content not visible on screen.
    @Throws(IllegalStateException::class)
    private fun captureNestedScrollViewFull(
        scrollView: NestedScrollView, callback: (Bitmap) -> Unit
    ) {
        val child = scrollView.getChildAt(0)
            ?: throw IllegalStateException("NestedScrollView has no child view")

        val originalScrollY = scrollView.scrollY

        scrollView.scrollTo(0, 0)

        // Use post to wait until scrollTo takes effect
        scrollView.post {
            try {
                val bitmap = Bitmap.createBitmap(
                    scrollView.width, child.height, bitmapConfig
                ).apply {
                    Canvas(this).also { canvas ->
                        child.draw(canvas)
                    }
                }
                callback(bitmap)
            } finally {
                // Restore original scroll position
                scrollView.scrollTo(0, originalScrollY)
            }
        }
    }

    @Throws(IllegalStateException::class)
    fun captureViewToBitmap(view: View): Bitmap {
        if (view.width == 0 || view.height == 0) {
            throw IllegalStateException("View has not been laid out yet")
        }
        return Bitmap.createBitmap(view.width, view.height, bitmapConfig).apply {
            Canvas(this).also { canvas -> view.draw(canvas) }
        }
    }

    suspend fun bitmapToFileNew(
        context: Context, bitmap: Bitmap,
        filename: String, quality: Int = DEFAULT_COMPRESSION_QUALITY
    ): File = withContext(Dispatchers.IO) {
        val file = File(context.cacheDir, filename)
        try {
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, out)
                out.flush()
            }
            MBLog.d(TAG, "Screenshot saved:${file.length()} bytes")
            return@withContext file
        } catch (e: Exception) {
            MBLog.e(TAG, "Error saving bitmap to file", e)
            throw e
        } finally {
            bitmap.recycle()
        }
    }
}
