package com.mindbodyonline.connect.utils.api.identity

import android.net.Uri
import com.android.volley.Request
import com.android.volley.Response
import com.android.volley.VolleyError
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.OpenWhileTesting
import com.mindbodyonline.android.util.api.request.FormDataRequest
import com.mindbodyonline.android.util.api.request.GsonRequest
import com.mindbodyonline.android.util.api.service.oauth.OAuthAccessParams
import com.mindbodyonline.android.util.api.service.oauth.OAuthAccessToken
import com.mindbodyonline.android.util.api.service.oauth.OAuthDataService
import com.mindbodyonline.connect.BuildConfig
import com.mindbodyonline.connect.common.utilities.fromJson
import com.mindbodyonline.connect.common.utilities.toJson
import com.mindbodyonline.connect.utils.Endpoint
import com.mindbodyonline.connect.utils.api.ApiCallUtils
import com.mindbodyonline.connect.utils.api.common.ApiErrorCodes
import com.mindbodyonline.connect.utils.api.common.ProblemDetailsInterceptor
import com.mindbodyonline.connect.utils.api.common.ResponseErrorInterceptor
import com.mindbodyonline.connect.utils.api.identity.model.*
import com.mindbodyonline.data.services.OAuth2Params
import com.mindbodyonline.data.services.http.MbDataService
import com.mindbodyonline.domain.Token

@Suppress("unused")
@OpenWhileTesting
class IdentityAPI {

    companion object {
        const val AUTH_PROMPT_VALUE = "login"

        @JvmStatic
        var INSTANCE = IdentityAPI().apply {
            oauthService.setTokenRefreshDelegate(MbDataService.getServiceInstance())
        }
            @JvmName("getInstance")
            get
            @JvmName("setInstance")
            set
    }

    // Use the MBDataService request queue to get built-in chuck and stetho
    private var requestQueue = MbDataService.getServiceInstance().requestQueue

    // This oauthService shares token management with our main code base
    private val oauthService = OAuthDataService.getInstance(OAuth2Params.IDENTITY_PROD, requestQueue, ConnectApp.getInstance())

    /**
     * These params are used to provide the client ID, secret, redirect uri, etc.
     */
    val oauthParams: OAuthAccessParams
        get() {
            return when (ApiCallUtils.getEndpoint()) {
                Endpoint.DEVELOPMENT -> OAuth2Params.IDENTITY_DEV
                else -> OAuth2Params.IDENTITY_PROD
            }
        }

    /**
     * Note that these endpoints will eventually be replaced with a single endpoint that routes
     * to each service behind the scenes.
     */
    private val currentEndpoint: IdentityEndpoint
        get() {
            return when (ApiCallUtils.getEndpoint()) {
                Endpoint.DEVELOPMENT -> IdentityEndpoint.DEVELOPMENT
                Endpoint.STAGING -> IdentityEndpoint.STAGING
                else -> IdentityEndpoint.PRODUCTION
            }
        }

    /**
     * Swamis API requires no authentication, but we are tracking the client in Newrelic
     * with these base headers.
     */
    private fun getBaseHeaders() = mutableMapOf(
            "X-MB-APP-NAME" to MbDataService.USER_AGENT_HEADER_VALUE,
            "X-MB-APP-VERSION" to BuildConfig.VERSION_NAME,
            "X-MB-APP-BUILD" to BuildConfig.VERSION_CODE.toString()
    )

    /**
     * Some requests in the user APIs utilize JSON as the body content type.
     */
    private fun getJsonHeaders() = getBaseHeaders().apply {
        put("Content-Type", "application/json")
    }

    /**
     * This is only really needed due to faulty logic in the OAuthDataService.  Once we are able to
     * remove that from this API (i.e. transition to Dexus), this can be removed.  We can
     * also just take in a token parameter for most of these API calls.
     */
    fun setToken(token: OAuthAccessToken?) {
        oauthService.token = token
    }

    /**
     * Retrieves the Authrorization URL to send the user to in a browser.
     * The [loginType] needs to be set if you are looking to bypass the email login prompt -
     * for instance, if you are directing the user straight to "log in with facebook".
     */
    @JvmOverloads
    fun getAuthorizationUrl(loginType: LoginProviders = LoginProviders.MINDBODY): Uri {
        val url = currentEndpoint.identity.buildUpon()
            .appendEncodedPath("connect/authorize")
            .appendQueryParameter("client_id", oauthParams.clientId)
            .appendQueryParameter("scope", oauthParams.scope)
            .appendQueryParameter("response_type", "code")
            .appendQueryParameter("redirect_uri", oauthParams.redirectUri)
            .appendQueryParameter("client_secret", oauthParams.clientSecret)
            .appendQueryParameter("prompt", AUTH_PROMPT_VALUE)

        // If we are not using the default login, we need to specify the ACR values for other providers
        if (loginType != LoginProviders.MINDBODY) {
            loginType.acr?.let { url.appendQueryParameter("acr_values", it) }
        }
        return url.build()
    }

    /**
     * This will get an Identity access token based on a [username] and [password].
     */
    fun getToken(username: String,
                 password: String,
                 callerHandleTheseErrors: List<ApiErrorCodes>? = null,
                 successListener: ((Token) -> Unit)?,
                 errorListener: ((ProblemDetails?) -> Unit)?) {

        val url = currentEndpoint.identity.buildUpon()
            .appendEncodedPath("connect/token")

        val params = mutableMapOf(
                "client_id" to oauthParams.clientId,
                "grant_type" to "password",
                "username" to username,
                "password" to password,
                "scope" to oauthParams.scope,
                "client_secret" to oauthParams.clientSecret)


        val req = FormDataRequest(Request.Method.POST,
                url.toString(), Token::class.java, getBaseHeaders(), params,
                { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = { errorListener?.invoke(it.asProblemDetails()) },
                        passThroughCodes = callerHandleTheseErrors, url = url.toString()))

        requestQueue.add(req)
    }

    /**
     * Technically the same endpoint as [getToken], this will refresh the token.  The scopes
     * of the original token request will be carried over, unless it is a v1 token, in which case
     * the scopes will be automatically applied.
     */
    fun refreshToken(token: Token,
                     successListener: ((Token) -> Unit)?,
                     errorListener: ((ProblemDetails?) -> Unit)?) {

        val url = currentEndpoint.identity.buildUpon()
            .appendEncodedPath("connect/token")

        val params = mutableMapOf(
                "client_id" to oauthParams.clientId,
                "grant_type" to "refresh_token",
                "refresh_token" to token.refreshToken,
                "client_secret" to oauthParams.clientSecret)

        val req = FormDataRequest(Request.Method.POST,
                url.toString(), Token::class.java, getBaseHeaders(), params,
                {
                    oauthService.token = it
                    successListener?.invoke(it)
                },
                ResponseErrorInterceptor(passedValue = { errorListener?.invoke(it.asProblemDetails()) },
                        url = url.toString()))
        requestQueue.add(req)
    }

    /**
     * Technically the same endpoint as [getToken], this will refresh the token when the scopes are
     * differed with grantType as delegate to fetch updated token with proper scopes.
     */
    fun refreshTokenWithDelegate(token: Token,
                     successListener: ((Token) -> Unit)?,
                     errorListener: ((ProblemDetails?) -> Unit)?) {

        val url = currentEndpoint.identity.buildUpon()
            .appendEncodedPath("connect/token")
        val delegationTokenRequest = DelegationTokenRequest(
            clientId = oauthParams.clientId,
            clientSecret = oauthParams.clientSecret,
            scope = oauthParams.scope,
            token = token.accessToken
        )

        val req = FormDataRequest(Request.Method.POST,
            url.toString(), Token::class.java, getBaseHeaders(), delegationTokenRequest.formParams(),
            {
                oauthService.token = it
                successListener?.invoke(it)
            },
            ResponseErrorInterceptor(passedValue = { errorListener?.invoke(it.asProblemDetails()) },
                url = url.toString()))
        requestQueue.add(req)
    }

    /**
     * This will provide an access token given an authorization [code].  This [code]
     * is retrieved directly from the Identity API frontend - or via a third party provider
     * routed through the Identity API (i.e. facebook).
     */
    fun retrieveTokenFromAuthCode(code: String,
                                  successListener: ((Token) -> Unit)?,
                                  errorListener: ((Throwable) -> Unit)?) {

        val url = currentEndpoint.identity.buildUpon()
            .appendEncodedPath("connect/token")

        val params = mutableMapOf(
                "client_id" to oauthParams.clientId,
                "grant_type" to "authorization_code",
                "code" to code,
                "client_secret" to oauthParams.clientSecret,
                "redirect_uri" to oauthParams.redirectUri)

        val req = FormDataRequest(Request.Method.POST,
                url.toString(), Token::class.java, getBaseHeaders(), params,
                { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = { errorListener?.invoke(it) }, url = url.toString()))
        requestQueue.add(req)
    }

    /**
     *  This will create an unverified MB App account against the Identity user account APIs.
     *
     *  Note that to retrieve a user token, the [getToken] endpoint will need to be called
     *  after the verification step is complete.
     */
    fun createUserAccount(
            firstName: String,
            lastName: String,
            email: String,
            password: String,
            countryCode: String,
            successListener: ((IdentityUser) -> Unit)? = null,
            errorListener: ((ProblemDetails?) -> Unit)?) {

        val body = CreateUserRequest(firstName, lastName, email, password, countryCode)

        val url = currentEndpoint.users

        val req = GsonRequest(Request.Method.POST,
                url.toString(), IdentityUser::class.java, getJsonHeaders(), body.toJson(),
                { successListener?.invoke(it) }, ProblemDetailsInterceptor(passedLambda = errorListener, url = url.toString()))

        requestQueue.add(req)
    }

    /**
     * Retrieve the current user, given nothing but the access token.  This will grant us
     * the Identity user ID, which we need for the rest of the Identity endpoints.
     */
    fun getUser(successListener: ((IdentityUser) -> Unit)? = null,
                errorListener: ((ProblemDetails?) -> Unit)?) {

        val url = currentEndpoint.users.buildUpon().appendPath("Me")

        val req = GsonRequest(Request.Method.GET,
                url.toString(), IdentityUser::class.java, getJsonHeaders(),
                { successListener?.invoke(it) }, ProblemDetailsInterceptor(passedLambda = errorListener, url = url.toString()))

        oauthService.wrapAndSendRequest(req)
    }

    /**
     *  Check a user's verification status by their user ID.
     */
    fun queryVerificationStatus(
            userId: String,
            successListener: ((UserVerificationStatusResponse) -> Unit)? = null,
            errorListener: ((ProblemDetails?) -> Unit)?) {

        val url = currentEndpoint.users.buildUpon()
            .appendEncodedPath(userId)
            .appendPath("VerificationStatus")

        val req = GsonRequest(Request.Method.GET,
                url.toString(), UserVerificationStatusResponse::class.java, getJsonHeaders(),
                { successListener?.invoke(it) }, ProblemDetailsInterceptor(passedLambda = errorListener, url = url.toString()))

        requestQueue.add(req)
    }

    /**
     *  Trigger an email to be resent to the user for verification. No matter the status of the user
     *  (even if the user doesn't exist), the result will always be the same, a 204 success.  The
     *  only exceptions are due to network communication errors.
     */
    fun resendVerificationEmail(
            email: String,
            successListener: (() -> Unit)? = null,
            errorListener: ((ProblemDetails?) -> Unit)?) {

        val body = SendVerificationEmailRequest(email)

        val url = currentEndpoint.users.buildUpon()
            .appendPath("VerificationRequests")

        val req = GsonRequest(Request.Method.POST,
                url.toString(), Void::class.java, getJsonHeaders(), body.toJson(),
                { successListener?.invoke() }, ProblemDetailsInterceptor(passedLambda = errorListener, url = url.toString()))

        requestQueue.add(req)
    }

    /**
     * Sends a verification email to the unconfirmed email address associated with an existing user account
     * and a warning email to the current email address that is associated with the user account,
     * more detail at https://mindbody.visualstudio.com/MBScrum/_wiki/wikis/MBScrum.wiki/1679/Integrating-with-Edit-Consumer-Email-Address
     */
    fun changeUserEmail(
            accountId: String,
            emailToChangeTo: String,
            successListener: (() -> Unit)? = null,
            errorListener: ((ProblemDetails?) -> Unit)?) {

        val body = ChangeEmailRequest(emailToChangeTo)

        val url = currentEndpoint.users.buildUpon()
            .appendPath(accountId)
            .appendPath("ChangeEmailRequests")

        val req = GsonRequest(Request.Method.POST,
                url.toString(), Void::class.java, getJsonHeaders(), body.toJson(),
                { successListener?.invoke() }, ProblemDetailsInterceptor(passedLambda = errorListener, url = url.toString()))

        oauthService.wrapAndSendRequest(req)

    }

    /**
     *  Trigger an email to be sent to the user which allows for password reset.  Success returns an empty
     *  body, Failure has a few codes.
     */
    fun requestPasswordReset(email: String,
                             subscriberId: String? = null,
                             successListener: Response.Listener<Void>? = null,
                             errorListener: ((ProblemDetails?) -> Unit)?) {
        val body = ResetPasswordEmailRequest(email, subscriberId)
        val url = currentEndpoint.users.buildUpon().appendPath("ResetPasswordRequests")

        val request = GsonRequest(Request.Method.POST,
                url.toString(), Void::class.java, getJsonHeaders(), body.toJson(), successListener,
                ProblemDetailsInterceptor(passedLambda = errorListener, url = url.toString()))
        requestQueue.add(request)
    }

    /**
     *  Update a user's information.
     *
     *  Note that if you are updating to a [newPassword], the [oldPassword] field is required.
     *  The [userId] corresponds to the Identity user ID, not the ConnV1 user ID, and is required.
     */
    fun updateUserAccount(userId: String,
                          firstName: String? = null,
                          lastName: String? = null,
                          oldPassword: String? = null,
                          newPassword: String? = null,
                          countryCode: String? = null,
                          successListener: ((IdentityUser?) -> Unit)? = null,
                          errorListener: ((ProblemDetails?) -> Unit)? = null) {

        val body = UpdateIdentityUserRequest(firstName, lastName, oldPassword, newPassword, countryCode)
        val url = currentEndpoint.users.buildUpon().appendPath(userId)

        val request = GsonRequest(Request.Method.PATCH,
                url.toString(), IdentityUser::class.java, getJsonHeaders(), body.toJson(),
                { successListener?.invoke(it) }, ProblemDetailsInterceptor(passedLambda = errorListener, url = url.toString()))

        oauthService.wrapAndSendRequest(request)
    }

    /**
     * This will get an Identity guest token.  If a user selects Continue as Guest from the Login page,
     * they will still need a token on API calls that require an authorization bearer header, for
     * example checking if the user exists by email address or checking if a user is a Flex user.
     */
    fun getGuestToken(
                 successListener: ((Token) -> Unit)?,
                 errorListener: ((ProblemDetails?) -> Unit)?) {

        val url = currentEndpoint.identity.buildUpon()
            .appendEncodedPath("connect/token")

        // Generic parameters required to fetch a guest token, these values should never change
        val params = mutableMapOf(
                "client_id" to oauthParams.clientId,
                "grant_type" to "password",
                "username" to "api_user",
                "password" to "user1234",
                "scope" to oauthParams.scope,
                "client_secret" to oauthParams.clientSecret)

        val req = FormDataRequest(Request.Method.POST,
                url.toString(), Token::class.java, getBaseHeaders(), params,
                { successListener?.invoke(it) },
                ResponseErrorInterceptor(passedValue = { errorListener?.invoke(it.asProblemDetails()) },
                        url = url.toString()))

        requestQueue.add(req)
    }
}

private fun VolleyError.asProblemDetails(): ProblemDetails {
    // If our response is null, then we can create a ProblemDetails with the status code as the Code
    return networkResponse?.data
        ?.let { String(it) }
        ?.fromJson<ProblemDetails>()
        ?.takeIf { it.Code != 0 } ?: ProblemDetails(Code = networkResponse?.statusCode ?: 500)
}

//region Identity service endpoints
private val IDENTITY_ENDPOINT_DEV = Uri.parse("https://signin-sandbox.staging.arcusplatform.io")
private val IDENTITY_ENDPOINT_STAGING = Uri.parse("https://signin-sandbox.staging.arcusplatform.io")
private val IDENTITY_ENDPOINT_PROD = Uri.parse("https://signin.mindbodyonline.com")

private val USERS_ENDPOINT_DEV = Uri.parse("https://www.staging.arcusplatform.io/identity-sandbox/gateway/V2/Users")
private val USERS_ENDPOINT_STAGING = Uri.parse("https://www.staging.arcusplatform.io/identity/gateway/V2/Users")
private val USERS_ENDPOINT_PROD = Uri.parse("https://www.mindbodyapis.com/identity/gateway/V2/Users")

private enum class IdentityEndpoint(val identity: Uri, val users: Uri) {
    DEVELOPMENT(IDENTITY_ENDPOINT_DEV, USERS_ENDPOINT_DEV),
    STAGING(IDENTITY_ENDPOINT_STAGING, USERS_ENDPOINT_STAGING),
    PRODUCTION(IDENTITY_ENDPOINT_PROD, USERS_ENDPOINT_PROD);
}

enum class LoginProviders(val acr: String? = null) {
    MINDBODY,
    FACEBOOK("idp:Facebook"),
    GOOGLE("idp:Google"),
    APPLE("idp:Apple")
}
//endregion