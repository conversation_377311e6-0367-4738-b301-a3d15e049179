package com.mindbodyonline.connect.utils

import com.mindbodyonline.android.util.log.MBLog
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileInputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

//Creates a ZIP file in memory from a list of files
fun createZipFromFiles(files: List<File>): ByteArray {
    val byteArrayOutputStream = ByteArrayOutputStream()
    ZipOutputStream(byteArrayOutputStream).use { zipOutputStream ->
        // Buffer for reading data
        val buffer = ByteArray(1024)

        // Add each file to the ZIP
        files.forEachIndexed { _, file ->
            if (!file.exists()) {
                MBLog.w("FTCAuditLog", "File does not exist: ${file.absolutePath}")
                return@forEachIndexed
            }

            try {
                val zipEntry = ZipEntry(file.name)
                zipOutputStream.putNextEntry(zipEntry)

                // Read the file and write it to the ZIP
                FileInputStream(file).use { fileInputStream ->
                    var len: Int
                    while (fileInputStream.read(buffer).also { len = it } > 0) {
                        zipOutputStream.write(buffer, 0, len)
                    }
                }

                zipOutputStream.closeEntry()
            } catch (e: Exception) {
                MBLog.e("FTCAuditLog", "Error adding file to ZIP: ${e.message}")
                throw e
            }
        }
    }

    return byteArrayOutputStream.toByteArray()
}
