package com.mindbodyonline.connect.utils;

import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.media.RingtoneManager;
import android.os.Build;
import android.os.Build.VERSION;
import android.os.Bundle;

import com.mindbodyonline.ConnectApp;
import com.mindbodyonline.android.util.log.MBLog;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.common.utilities.IntentUtil;
import com.mindbodyonline.connect.services.AlarmReceiver;
import com.mindbodyonline.connect.services.StravaSyncReceiver;
import com.mindbodyonline.data.services.MBAuth;
import com.mindbodyonline.data.services.MBStaticCache;
import com.mindbodyonline.data.services.MbCacheService;
import com.mindbodyonline.domain.BaseVisit;
import com.mindbodyonline.domain.ClassTypeObject;
import com.mindbodyonline.domain.ClassTypeVisit;
import com.mindbodyonline.domain.FavoriteClass;
import com.mindbodyonline.domain.Location;
import com.mindbodyonline.domain.User;
import com.mindbodyonline.domain.Visit;
import com.mindbodyonline.domain.dataModels.AlarmDataViewModel;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import androidx.annotation.DrawableRes;
import androidx.core.app.NotificationCompat;

/**
 * Created by reece.engle on 10/31/13.
 */
public class AlarmUtils {
    private static final String TAG = AlarmUtils.class.getName();

    private static final long FIVE_MINUTES_IN_MS = TimeUnit.MINUTES.toMillis(5);

    public static void restartReviewNotificationAlarm(Context context, AlarmDataViewModel alarmDataViewModel) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        Intent intent = new Intent(context, AlarmReceiver.class).setAction("rate_review_notification");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        Bundle extras = new Bundle();
        extras.putString(Constants.KEY_BUNDLE_CLASSNAME, alarmDataViewModel.getClassName());
        extras.putInt(Constants.KEY_BUNDLE_SITEID, alarmDataViewModel.getSiteId());
        extras.putLong(Constants.KEY_BUNDLE_VISITID, alarmDataViewModel.getVisitId());
        intent.putExtras(extras);

        Calendar notificationAlarm = Calendar.getInstance();
        notificationAlarm.setTime(alarmDataViewModel.getAlarmDate());

        int intentID = (int) new Date().getTime();
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context, intentID, intent,
            IntentUtil.getFlagUpdateCurrent());

        MBLog.d("Connect:MainActivity", "Setup the alarm for " + notificationAlarm.getTime().toString());

        alarmManager.set(AlarmManager.RTC_WAKEUP, notificationAlarm.getTimeInMillis(), pendingIntent);
    }

    /**
     * Sets up the alarm
     */
    public static void addReviewNotificationAlarm(final Context context, final Visit visit, final ClassTypeObject favoriteClass) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        Intent intent = new Intent(context, AlarmReceiver.class).setAction(Constants.ALARM_ACTION_REVIEW);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        User user = MBAuth.getUser();

        Bundle extras = new Bundle();
        extras.putString(Constants.KEY_BUNDLE_CLASSNAME, favoriteClass.getName());
        extras.putInt(Constants.KEY_BUNDLE_SITEID, favoriteClass.getLocation() != null ?
            favoriteClass.getLocation().getSiteId() : visit.getSiteId());
        extras.putLong(Constants.KEY_BUNDLE_VISITID, visit.getSiteVisitId());
        extras.putString(Constants.KEY_BUNDLE_PROGRAM_TYPE, visit.getProgramType());
        extras.putInt(Constants.KEY_USER_ID, user.getId());

        if (favoriteClass.getStaff() != null) {
            String staffName = favoriteClass.getStaff().getDisplayName();
            extras.putString(Constants.KEY_BUNDLE_STAFF_NAME, staffName);
        }
        intent.putExtras(extras);

        Calendar notificationAlarm = Calendar.getInstance();
        notificationAlarm.setTime(favoriteClass.getEndDate());
        notificationAlarm.add(Calendar.MINUTE, 3);

        int intentID = (int) new Date().getTime();
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context, intentID, intent,
            IntentUtil.getFlagUpdateCurrent());

        MBLog.d("Connect:MainActivity", "Setup the alarm for " + notificationAlarm.getTime().toString());

        alarmManager.set(AlarmManager.RTC_WAKEUP, notificationAlarm.getTimeInMillis(), pendingIntent);
        AlarmDataViewModel alarmDataViewModel =
            new AlarmDataViewModel(Constants.ALARM_ACTION_REVIEW, intentID, favoriteClass.getId(),
                visit.getSiteVisitId(), visit.getSiteId(), favoriteClass.getName(),
                notificationAlarm.getTime());
        MbCacheService.get().addAlarm(alarmDataViewModel);
    }

    public static void removeNotificationAlarm(Context context, Visit visit, FavoriteClass favoriteClass, int intentID) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        Intent intent = new Intent(context, AlarmReceiver.class).setAction(Constants.ALARM_ACTION_REVIEW);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        Bundle extras = new Bundle();
        extras.putString(Constants.KEY_BUNDLE_CLASSNAME, favoriteClass.getName());
        extras.putInt(Constants.KEY_BUNDLE_SITEID, favoriteClass.getLocation() != null ?
            favoriteClass.getLocation().getSiteId() : visit.getSiteId());
        extras.putLong(Constants.KEY_BUNDLE_VISITID, visit.getSiteVisitId());
        intent.putExtras(extras);

        Calendar notificationAlarm = Calendar.getInstance();
        notificationAlarm.setTime(favoriteClass.getEndDate());
        notificationAlarm.add(Calendar.MINUTE, 3);

        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context, intentID, intent,
            IntentUtil.getFlagUpdateCurrent());

        alarmManager.cancel(pendingIntent);

        AlarmDataViewModel alarmDataViewModel =
            new AlarmDataViewModel(Constants.ALARM_ACTION_REVIEW, intentID, favoriteClass.getId(),
                visit.getSiteVisitId(), visit.getSiteId(), favoriteClass.getName(),
                notificationAlarm.getTime());
        MbCacheService.get().removeAlarm(alarmDataViewModel);
    }

    public static void addSigninNotificationAlarm(final Context context, final ClassTypeObject favoriteClass) {

        if (favoriteClass.getLocation().getStudioAllowsGeofenceCheckins()) {
            new Thread(() -> {
                Location location = favoriteClass.getLocation();

                AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
                Intent intent = new Intent(context, AlarmReceiver.class).setAction(Constants.ALARM_ACTION_GEOFENCE_SIGNIN);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                User user = MBAuth.getUser();

                Bundle extras = new Bundle();
                extras.putDouble(Constants.KEY_BUNDLE_LATITUDE, location.getLatitude());
                extras.putDouble(Constants.KEY_BUNDLE_LONGITUDE, location.getLongitude());
                extras.putLong(Constants.KEY_BUNDLE_VISITID, favoriteClass.getVisits()[0].getSiteVisitId());
                extras.putInt(Constants.KEY_BUNDLE_SITEID, location.getSiteId());
                extras.putString(Constants.KEY_BUNDLE_CLASS_DATE, favoriteClass.getDate());
                extras.putString(Constants.KEY_BUNDLE_CLASSNAME, favoriteClass.getName());
                extras.putString(Constants.KEY_BUNDLE_CLASS_END_TIME, favoriteClass.getEndTime());
                extras.putString(Constants.KEY_BUNDLE_CLASS_START_TIME, favoriteClass.getStartTime());
                extras.putInt(Constants.KEY_USER_ID, user.getId());
                intent.putExtras(extras);

                Calendar notificationAlarm = Calendar.getInstance();
                notificationAlarm.setTime(favoriteClass.getStartDate());
                notificationAlarm.add(Calendar.MINUTE, 0 - Constants.MINUTES_BEFORE_SIGNIN);

                MBLog.d("Connect:MainActivity", "Setup the alarm for " + notificationAlarm.getTime().toString());

                int intentID = ((int) new Date().getTime()) / 2;
                PendingIntent pendingIntent = PendingIntent.getBroadcast(
                    context, intentID, intent,
                    IntentUtil.getFlagUpdateCurrent());
                alarmManager.set(AlarmManager.RTC, notificationAlarm.getTimeInMillis(), pendingIntent);
            }).start();
        }
    }

    public static void scheduleNotificationsForClass(ClassTypeObject classTypeObject) {
        //Registers a notification for reviewing the event that has taken place.
        AlarmUtils.addReviewNotificationAlarm(ConnectApp.getInstance(), classTypeObject.getVisits()[0], classTypeObject);

        //Registers a notification for signing in to the event that is about to take place.
        AlarmUtils.addSigninNotificationAlarm(ConnectApp.getInstance(), classTypeObject);

        //Registers an alarm that triggers a background service for uploading classes to Strava
        AlarmUtils.addStravaBackgroundSyncAlarm(classTypeObject);
    }

    /**
     * This will register an alarm that will trigger the sync to Strava, via calling the
     * StravaSyncReceiver broadcast receiver.
     *
     * @param classTypeObject The class to sync to strava
     * @param timeToTrigger The system time to initiate the sync
     */
    public static void addStravaBackgroundSyncAlarm(@NotNull ClassTypeObject classTypeObject, long timeToTrigger) {
        addStravaBackgroundSyncAlarm(classTypeObject.getId(), classTypeObject.getLocation().getSiteId(), timeToTrigger);
    }

    private static void addStravaBackgroundSyncAlarm(@NotNull ClassTypeVisit classVisit, long timeToTrigger) {
        addStravaBackgroundSyncAlarm(classVisit.ClassInstanceId, classVisit.SiteID, timeToTrigger);
    }

    public static void addStravaBackgroundSyncAlarm(long classInstanceID, int siteID, long timeToTrigger) {
        Context context = ConnectApp.getInstance();
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        User user = MBAuth.getUser();
        if (alarmManager == null || user == null) return; //There is no alarm manager service on this device

        Intent intent = new Intent(context, StravaSyncReceiver.class);
        intent.putExtra(Constants.KEY_BUNDLE_CLASS_INSTANCE_ID, (long) classInstanceID);
        intent.putExtra(Constants.KEY_BUNDLE_SITEID, siteID);
        intent.putExtra(Constants.KEY_USER_ID, user.getId());

        // Passing the class instance ID as the request code to allow for multiple alarms
        PendingIntent pendingIntent = PendingIntent.getBroadcast(ConnectApp.getInstance(),
            (int) classInstanceID, intent,
            IntentUtil.getFlagUpdateCurrent());

        alarmManager.set(AlarmManager.RTC_WAKEUP, timeToTrigger, pendingIntent);

        MBLog.d("StravaAlarm", "Added alarm for Strava, Class ID " + classInstanceID + ", Site ID " + siteID);
    }

    private static void addStravaBackgroundSyncAlarm(@NotNull ClassTypeObject classTypeObject) {
        //Sync starts 5 minutes after the class has ended
        long startTime = classTypeObject.getEndDate().getTime() + FIVE_MINUTES_IN_MS;

        addStravaBackgroundSyncAlarm(classTypeObject, startTime);
    }

    public static void addStravaBackgroundSyncAlarm(@NotNull ClassTypeVisit classVisit) {
        //Sync starts 5 minutes after the class has ended
        long startTime = classVisit.getEndCal().getTimeInMillis() + FIVE_MINUTES_IN_MS;

        addStravaBackgroundSyncAlarm(classVisit, startTime);
    }

    public static void syncUserVisitsForStrava() {
        for (BaseVisit visit : MBStaticCache.getInstance().getUpcomingVisits()) {
            if (visit instanceof ClassTypeVisit && !visit.hasPassed()) {
                AlarmUtils.addStravaBackgroundSyncAlarm((ClassTypeVisit) visit);
            }
        }
    }

    public static void ensureNotificationChannels(Context context) {
        // No channels pre-Oreo
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) return;

        NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        if (manager == null) return; // Shouldn't happen, give up to prevent NPE

        // Create channels
        String reviewName = context.getString(R.string.notification_channel_review_title);
        NotificationChannel reviewChannel = new NotificationChannel(Constants.CHANNEL_ID_REVIEW, reviewName, NotificationManager.IMPORTANCE_DEFAULT);
        reviewChannel.setDescription(context.getString(R.string.notification_channel_review_description));

        String signInName = context.getString(R.string.notification_channel_sign_in_title);
        NotificationChannel signInChannel = new NotificationChannel(Constants.CHANNEL_ID_SIGN_IN, signInName, NotificationManager.IMPORTANCE_DEFAULT);
        signInChannel.setDescription(context.getString(R.string.notification_channel_sign_in_description));

        String activityName = context.getString(R.string.notification_channel_activity_title);
        NotificationChannel activityChannel = new NotificationChannel(Constants.CHANNEL_ID_ACTIVITY, activityName, NotificationManager.IMPORTANCE_DEFAULT);
        activityChannel.setDescription(context.getString(R.string.notification_channel_activity_description));

        // Register the channels
        manager.createNotificationChannel(reviewChannel);
        manager.createNotificationChannel(signInChannel);
        manager.createNotificationChannel(activityChannel);
    }

    public static @NotNull Notification constructNotification(@NotNull String channelId, @Nullable String title,
        @NotNull String subtitle, @NotNull PendingIntent pendingIntent, @Nullable Integer timeoutMs,
        @Nullable @DrawableRes Integer wearableDrawableRes, @Nullable String wearableActionString,
        @Nullable @DrawableRes Integer notificationActionRes, @Nullable String notificationActionString,
        @Nullable PendingIntent actionPendingIntent) {

        Context context = ConnectApp.getInstance();
        Notification notification;

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, channelId)
            .setContentTitle(title)
            .setLargeIcon(BitmapFactory.decodeResource(context.getResources(),
                R.mipmap.ic_launcher))
            .setSmallIcon(R.drawable.mindbody_logo_white)
            .setContentText(subtitle)
            .setStyle(new NotificationCompat.BigTextStyle().bigText(subtitle))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true);

        if (timeoutMs != null) {
            builder.setTimeoutAfter(timeoutMs);
        }

        //If SDK Version is above 16 then we can add actions
        if (VERSION.SDK_INT >= 16) {
            if (notificationActionRes != null && notificationActionString != null && actionPendingIntent != null) {
                builder.addAction(notificationActionRes, notificationActionString, actionPendingIntent);
            }

            if (wearableDrawableRes != null && wearableActionString != null) {
                NotificationCompat.Action wearAction =
                    new NotificationCompat.Action.Builder(wearableDrawableRes,
                        wearableActionString,
                        pendingIntent).build();
                builder.extend(new NotificationCompat.WearableExtender().addAction(wearAction));
            }

            notification = builder.build();
        } else {
            //Had to specify builder.build() in conditional or else IDE highlights warning
            // (jleehey) Clarification:  Builder.build() does not exist pre-API 16.  Changed to
            // use getNotification().
            notification = builder.getNotification();
        }

        if (SharedPreferencesUtils.isPhoneLEDNotificationsEnabled()) {
            notification.flags = Notification.FLAG_SHOW_LIGHTS | Notification.FLAG_AUTO_CANCEL;
            notification.ledARGB = Color.rgb(255, 103, 0);
            notification.ledOnMS = 1000;
            notification.ledOffMS = 300;
        }

        if (SharedPreferencesUtils.isVibrateNotificationsEnabled()) {
            notification.vibrate = new long[]{0, 300, 400, 300};
        }

        if (SharedPreferencesUtils.isSoundNotificationsEnabled()) {
            try {
                notification.sound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
            } catch (Exception ignored) {
            }
        }

        return notification;
    }
}
