package com.mindbodyonline.connect.ftc

import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbStatus
import com.mindbodyonline.connect.ftc.logs.ParamConstants
import com.mindbodyonline.connect.utils.BreadcrumbsUtils
import com.mindbodyonline.connect.utils.api.identity.IdentityAPI
import com.mindbodyonline.connect.utils.api.identity.Jwt
import com.mindbodyonline.connect.utils.api.identity.model.ScopeOnlyPayload
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.data.services.OAuth2Params
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

object DelegateTokenRefreshService {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private const val LOG_TAG = "DelegateTokenRefresh"

    fun refreshTokenWithDelegate() {
        scope.launch(Dispatchers.IO) {
            try {
                val token = MBAuth.getUserToken() ?: return@launch

                val acquiredScopes =
                    Jwt.decode(
                        token.accessToken,
                        ScopeOnlyPayload::class.java
                    )?.payload?.scope?.toSet()
                        ?: emptyList<String>().toSet()

                val requiredScopes =
                    OAuth2Params.IDENTITY_PROD.scope.split("\\s+".toRegex()).toSet()

                if (acquiredScopes != requiredScopes) {
                    MBLog.i(LOG_TAG, "Scopes are different")
                    logEvent(FTCBreadCrumbStatus.STARTED.name)
                    val result = CompletableDeferred<Boolean>()
                    IdentityAPI.INSTANCE.refreshTokenWithDelegate(
                        token,
                        { newToken ->
                            MBLog.i(LOG_TAG, "Token refreshed successfully")
                            logEvent(FTCBreadCrumbStatus.COMPLETED.name)
                            MBAuth.setToken(newToken)
                            result.complete(true)
                        },
                        { error ->
                            logEvent(FTCBreadCrumbStatus.FAILED.name, error?.Title)
                            MBLog.i(LOG_TAG, "Token refresh failed, $error")
                            result.complete(false)
                        }
                    )
                    result.await()
                } else {
                    MBLog.i(LOG_TAG, "Scopes are same, no need for refresh")
                }
            } catch (exception: Exception) {
                logEvent(FTCBreadCrumbStatus.FAILED.name, exception.localizedMessage)
                MBLog.e(LOG_TAG, "Error refreshing token with delegate", exception)
            }
        }
    }

    private fun logEvent(status: String, errorMessage: String? = null) {
        BreadcrumbsUtils.recordTokenDelegationBreadCrumb(
            mutableMapOf(
                Pair(ParamConstants.STATUS.name, status),
            ).apply {
                errorMessage?.let {
                    Pair(ParamConstants.ERROR_MESSAGE.name, it)
                }
            }
        )
    }
}