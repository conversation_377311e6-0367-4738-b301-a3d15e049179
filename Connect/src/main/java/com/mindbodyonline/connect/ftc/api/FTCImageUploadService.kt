package com.mindbodyonline.connect.ftc.api

import com.mindbodyonline.android.api.clients.model.FTCImageUrlRequest
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.ftc.FTCOperationHandler
import com.mindbodyonline.connect.ftc.S3ZipUploader
import com.mindbodyonline.connect.ftc.data.FTCImageUploadMetadata
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbAction
import com.mindbodyonline.connect.utils.createZipFromFiles
import java.io.File

/**
 * Service class handling image upload operations
 */
class FTCImageUploadService(private val repository: FTCRepository) {
    private val logTag = "FTCAuditLog"

    suspend fun createZipOutOfImages(
        images: List<File>,
        metadata: FTCImageUploadMetadata
    ): ByteArray = FTCOperationHandler.executeOperation(
        action = FTCBreadCrumbAction.GENERATE_ARCHIVE,
        metadata = metadata
    ) {
        return@executeOperation createZipFromFiles(images)
    }

    suspend fun uploadZipFileToS3(
        zipBytes: ByteArray,
        metadata: FTCImageUploadMetadata,
        request: FTCImageUrlRequest
    ): String = FTCOperationHandler.executeOperation(
        action = FTCBreadCrumbAction.FETCH_PRESIGNED_URL,
        metadata = metadata
    ) {
        // Get presigned URL
        val ftcImageUrlResponse = repository.fetchPreSignedURL(
            metadata.subscriberId,
            metadata.contractId,
            request
        )

        // Upload to S3
        return@executeOperation FTCOperationHandler.executeOperation(
            action = FTCBreadCrumbAction.UPLOAD_SCREENSHOTS_ARCHIVE,
            metadata = metadata
        ) {
            val uploadResult = S3ZipUploader.create(
                maxRetries = 3,
                baseRetryDelayMs = 300,
                useExponentialBackoff = false
            ).uploadImagesAsZip(ftcImageUrlResponse.presignedUrl, zipBytes)

            if (uploadResult is S3ZipUploader.UploadResult.Success) {
                MBLog.d(logTag, "S3 upload successful for cartId-${metadata.cartId}")
                ftcImageUrlResponse.recordId
            } else {
                throw Exception("Failed to upload image to S3: ${(uploadResult as S3ZipUploader.UploadResult.Error).message}")
            }
        }
    }
}
