package com.mindbodyonline.connect.ftc.logs

/**
 * Created by <PERSON><PERSON><PERSON> on 26/03/25.
 */

enum class FTCBreadCrumbAction(action: String) {
    CAPTURE_SCREENSHOT("capture_screenshot"),
    FETCH_PRESIGNED_URL("fetch_preSigned_url"),
    GENERATE_ARCHIVE("generate_archive"),
    UPLOAD_SCREENSHOTS_ARCHIVE("upload_screenshots_archive"),
    CONFIRM_CONTRACT_STATUS("confirm_contract_status")
}

enum class FTCBreadCrumbStatus(status: String) {
    STARTED("started"),
    FAILED("failed"),
    COMPLETED("completed"),
    IMAGE_UPLOAD_IN_PROGRESS("images_zip_upload_in_progress")
}

enum class ParamConstants(param: String) {
    ACTION("action"),
    STATUS("status"),
    USER_ID("userId"),
    CART_ID("cartId"),
    SOURCE("source"),
    STUDIO_ID("studioId"),
    CONTRACT_ID("contractId"),
    RECORD_ID("recordId"),
    ORDER_ID("orderId"),
    ERROR_MESSAGE("errorMessage"),
    LOCATION("location")
}
