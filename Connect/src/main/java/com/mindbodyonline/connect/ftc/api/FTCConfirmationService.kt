package com.mindbodyonline.connect.ftc.api

import com.mindbodyonline.android.api.clients.model.FTCUploadConfirmationRequest
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.ftc.FTCOperationHandler
import com.mindbodyonline.connect.ftc.data.FTCImageUploadMetadata
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbAction

/**
 * Service class handling confirmation operations
 */
class FTCConfirmationService(private val repository: FTCRepository) {
    private val logTag = "FTCAuditLog"

    suspend fun confirmUpload(
        metadata: FTCImageUploadMetadata,
        recordId: String
    ): Boolean = FTCOperationHandler.executeOperation(
        action = FTCBreadCrumbAction.CONFIRM_CONTRACT_STATUS,
        metadata = metadata,
        recordId = recordId
    ) {
        val request = FTCUploadConfirmationRequest(
            mbUserId = metadata.userId,
            recordId = recordId,
            orderId = metadata.orderId ?: "",
            uploadSuccessful = true,
            verifiedClientContract = true,
            metadata = mapOf("cartId" to metadata.cartId)
        )

        val result = repository.confirmUpload(
            metadata.subscriberId,
            metadata.contractId,
            request
        )
        val message = if (result) "Upload confirmation successful" else "Upload confirmation failed"
        MBLog.d(logTag, "$message for cart: ${metadata.cartId}")

        result
    }
}
