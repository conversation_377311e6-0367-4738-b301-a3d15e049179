package com.mindbodyonline.connect.ftc

import com.mindbodyonline.android.util.log.MBLog
import kotlinx.coroutines.delay
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import java.io.IOException
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

/**
 * Created by Rohit Vundigala on 07/04/25.
 */
class S3ZipUploader(
    private val okHttpClient: OkHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build(),
    private val config: S3UploaderConfig = S3UploaderConfig()
) {

    data class S3UploaderConfig(
        val maxRetries: Int = 3,
        val baseRetryDelayMs: Long = 1000,
        val useExponentialBackoff: Boolean = true,
    )

    sealed class UploadResult {
        data object Success : UploadResult()
        data class Error(
            val code: ErrorCode,
            val message: String,
            val exception: Exception? = null
        ) : UploadResult()
    }

    /**
     * Error codes for upload failures
     */
    enum class ErrorCode {
        MAX_RETRIES_EXCEEDED,
        EMPTY_ZIP
    }

    suspend fun uploadImagesAsZip(
        preSignedUrl: String,
        zipBytes: ByteArray
    ): UploadResult {
        if (zipBytes.isEmpty()) {
            return UploadResult.Error(
                code = ErrorCode.EMPTY_ZIP,
                message = "No bytes provided for zipping",
                exception = null
            )
        }

        val zipMediaType = "application/zip".toMediaType()

        return uploadWithRetry(
            preSignedUrl = preSignedUrl,
            mediaType = zipMediaType,
            zipBytes = zipBytes,
        )
    }

    private suspend fun uploadWithRetry(
        preSignedUrl: String,
        mediaType: MediaType,
        zipBytes: ByteArray
    ): UploadResult {
        var retryCount = 0
        var lastException: Exception? = null

        while (retryCount <= config.maxRetries) {
            try {
                val result = putFileToS3(preSignedUrl, zipBytes, mediaType)
                if (result) {
                    return UploadResult.Success
                } else {
                    lastException = IOException("Upload failed with unknown error")
                }
            } catch (e: Exception) {
                lastException = e
                MBLog.e("FTCAuditLog", "Upload attempt $retryCount failed: ${e.message}")
            }

            if (retryCount == config.maxRetries) {
                break
            }

            val retryDelay = if (config.useExponentialBackoff) {
                config.baseRetryDelayMs * (1 shl retryCount)
            } else {
                config.baseRetryDelayMs
            }

            delay(retryDelay)
            retryCount++
        }

        return UploadResult.Error(
            code = ErrorCode.MAX_RETRIES_EXCEEDED,
            message = "Failed to upload after ${config.maxRetries} attempts",
            exception = lastException
        )
    }

    private suspend fun putFileToS3(
        preSignedUrl: String,
        imageBytes: ByteArray,
        mediaType: MediaType
    ): Boolean {
        return suspendCoroutine { continuation ->
            try {
                val requestBody = imageBytes.toRequestBody(mediaType, 0, imageBytes.size)

                val request = Request.Builder()
                    .url(preSignedUrl)
                    .put(requestBody)
                    .build()

                okHttpClient.newCall(request).enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        continuation.resumeWithException(e)
                    }

                    override fun onResponse(call: Call, response: Response) {
                        response.use {
                            if (response.isSuccessful) {
                                continuation.resume(true)
                            } else {
                                val errorBody = response.body?.string() ?: "No error body"
                                continuation.resumeWithException(
                                    IOException("S3 upload failed with status ${response.code}: $errorBody")
                                )
                            }
                        }
                    }
                })
            } catch (e: Exception) {
                continuation.resumeWithException(e)
            }
        }
    }

    companion object {
        fun create(
            maxRetries: Int = 3,
            baseRetryDelayMs: Long = 1000,
            useExponentialBackoff: Boolean = true
        ): S3ZipUploader {
            return S3ZipUploader(
                config = S3UploaderConfig(
                    maxRetries = maxRetries,
                    baseRetryDelayMs = baseRetryDelayMs,
                    useExponentialBackoff = useExponentialBackoff
                )
            )
        }
    }
}
