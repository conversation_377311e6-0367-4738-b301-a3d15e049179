package com.mindbodyonline.connect.ftc

import android.view.View
import androidx.core.widget.NestedScrollView
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.ftc.data.FTCScreenShotSource
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbAction
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbLogger
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbStatus
import com.mindbodyonline.connect.utils.ScreenshotUtil

/**
 * Utility class for handling contract screenshot capturing for FTC compliance
 */
object ContractScreenshotHelper {
    private const val TAG = "FTCAuditLog"

    fun captureDialogScreenshot(
        source: FTCScreenShotSource,
        contractId: String?,
        studioId: String?,
        view: View,
        location: OriginView,
        scrollView: NestedScrollView? = null,
        bottomView: View? = null,
        onComplete: (() -> Unit?)? = null
    ) {
        try {
            logScreenshotEvent(FTCBreadCrumbStatus.STARTED, contractId, studioId, location)
            // For scrollable content with bottom text (like contract details)
            if (scrollView != null && bottomView != null) {
                ScreenshotUtil.captureFullDialogWithTextAtBottom(
                    view, bottomView, scrollView
                ) { bitmap ->
                    FTCContractScreenshotManager.addScreenshot(
                        bitmap, view.context, contractId.toString(), source
                    )
                    onComplete?.invoke()
                }
            } else {
                val bitmap = ScreenshotUtil.captureViewToBitmap(view)
                FTCContractScreenshotManager.addScreenshot(
                    bitmap, view.context, contractId.toString(), source
                )
            }

            logScreenshotEvent(FTCBreadCrumbStatus.COMPLETED, contractId, studioId, location)
            MBLog.i(TAG, "Screenshot captured successfully for ${location.viewName}")
        } catch (e: Exception) {
            logScreenshotEvent(FTCBreadCrumbStatus.FAILED, contractId, studioId, location, e)
            MBLog.e(TAG, "Failed to capture screenshot for ${location.viewName}: ${e.message}")
        }
    }

    private fun logScreenshotEvent(
        status: FTCBreadCrumbStatus,
        contractId: String?,
        studioId: String?,
        location: OriginView,
        exception: Exception? = null
    ) {
        FTCBreadCrumbLogger.logFTCBreadCrumb(
            action = FTCBreadCrumbAction.CAPTURE_SCREENSHOT,
            status = status,
            location = location.viewName,
            contractID = contractId,
            studioId = studioId,
            errorMessage = exception?.message
        )
    }
}
