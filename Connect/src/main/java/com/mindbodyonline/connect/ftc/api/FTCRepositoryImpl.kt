package com.mindbodyonline.connect.ftc.api

import com.android.volley.Response
import com.mindbodyonline.android.api.clients.MbClientsAPIManager
import com.mindbodyonline.android.api.clients.model.FTCImageUrlRequest
import com.mindbodyonline.android.api.clients.model.FTCImageUrlResponse
import com.mindbodyonline.android.api.clients.model.FTCUploadConfirmationRequest
import com.mindbodyonline.android.api.clients.model.FTCUploadConfirmationResponse
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.utils.api.withRetry
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

interface FTCRepository {
    suspend fun fetchPreSignedURL(
        subscriberId: String,
        contractId: String,
        request: FTCImageUrlRequest
    ): FTCImageUrlResponse

    suspend fun confirmUpload(
        subscriberId: String,
        contractId: String,
        request: FTCUploadConfirmationRequest
    ): Boolean
}

class FTCRepositoryImpl : FTCRepository {
    private val logTag = "FTCAuditLog"

    override suspend fun fetchPreSignedURL(
        subscriberId: String,
        contractId: String,
        request: FTCImageUrlRequest
    ): FTCImageUrlResponse {
        return withRetry(
            maxRetries = 3,
            timeoutMs = 30000,
            backoffMultiplier = 1.0,
            retryDelayMs = 300
        ) {
            suspendCancellableCoroutine { continuation ->
                val successListener = Response.Listener<FTCImageUrlResponse> { response ->
                    continuation.resume(response)
                }

                val errorListener = Response.ErrorListener { error ->
                    MBLog.e(logTag, "Error fetching presigned URL", error)
                    continuation.resumeWithException(
                        error
                    )
                }

                MbClientsAPIManager.fetchPreSignedURL(
                    subscriberId,
                    contractId,
                    request,
                    successListener,
                    errorListener
                )
            }
        }
    }

    override suspend fun confirmUpload(
        subscriberId: String,
        contractId: String,
        request: FTCUploadConfirmationRequest
    ): Boolean {
        return withRetry(
            maxRetries = 3,
            timeoutMs = 30000,
            backoffMultiplier = 1.0,
            retryDelayMs = 300
        ) {
            suspendCancellableCoroutine { continuation ->
                val successListener =
                    Response.Listener<FTCUploadConfirmationResponse> { _ ->
                        continuation.resume(true)
                    }

                val errorListener = Response.ErrorListener { error ->
                    MBLog.e(logTag, "Error confirming upload", error)
                    continuation.resumeWithException(
                        error
                    )
                }

                MbClientsAPIManager.postUploadConfirmation(
                    subscriberId,
                    contractId,
                    request,
                    successListener,
                    errorListener
                )
            }
        }
    }
}
