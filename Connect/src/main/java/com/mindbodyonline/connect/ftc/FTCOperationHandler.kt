package com.mindbodyonline.connect.ftc

import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.ftc.data.FTCImageUploadMetadata
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbAction
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbLogger
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbStatus

/**
 * Utility for handling FTC operation logging and error handling consistently
 */
object FTCOperationHandler {
    private const val TAG = "FTCAuditLog"

    suspend fun <T> executeOperation(
        action: FTCBreadCrumbAction,
        metadata: FTCImageUploadMetadata?,
        recordId: String? = null,
        operation: suspend () -> T
    ): T {
        logEvent(action, FTCBreadCrumbStatus.STARTED, metadata, recordId)
        try {
            val result = operation()
            logEvent(action, FTCBreadCrumbStatus.COMPLETED, metadata, recordId)
            return result
        } catch (e: Exception) {
            logEvent(action, FTCBreadCrumbStatus.FAILED, metadata, recordId, e)
            throw e
        }
    }

    private fun logEvent(
        action: FTCBreadCrumbAction,
        status: FTCBreadCrumbStatus,
        metadata: FTCImageUploadMetadata?,
        recordId: String? = null,
        error: Exception? = null
    ) {
        FTCBreadCrumbLogger.logFTCBreadCrumb(
            action = action,
            status = status,
            userId = metadata?.userId,
            cartID = metadata?.cartId,
            recordId = recordId,
            orderId = metadata?.orderId,
            contractID = metadata?.contractId,
            studioId = metadata?.subscriberId,
            errorMessage = error?.message
        )

        // Add debug logging
        val message = "FTC ${action.name} - ${status.name}" +
                (error?.let { " ERROR: ${it.message}" } ?: "")
        if (error != null) {
            MBLog.e(TAG, message, error)
        } else {
            MBLog.d(TAG, message)
        }
    }
}
