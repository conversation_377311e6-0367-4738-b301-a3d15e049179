package com.mindbodyonline.connect.ftc

import android.content.Context
import android.graphics.Bitmap
import com.mindbodyonline.android.api.clients.model.FTCImageUrlRequest
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.ftc.api.FTCConfirmationService
import com.mindbodyonline.connect.ftc.api.FTCImageUploadService
import com.mindbodyonline.connect.ftc.api.FTCRepositoryImpl
import com.mindbodyonline.connect.ftc.data.FTCImageUploadMetadata
import com.mindbodyonline.connect.ftc.data.FTCImageUploadState
import com.mindbodyonline.connect.ftc.data.FTCScreenShotSource
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbAction
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbLogger
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbStatus
import com.mindbodyonline.connect.utils.ScreenshotUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.io.File
import java.util.concurrent.ConcurrentHashMap

// Manager class to handle FTC audit log related operations
object FTCContractScreenshotManager {
    private const val TAG = "FTCAuditLog"
    private const val PLATFORM = "Mobile"
    private const val SOURCE = "Android Connect"
    private const val CONTENT_TYPE = "application/zip"

    private val managerScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private val mutex = Mutex()
    private val activeUploads = ConcurrentHashMap<String, FTCImageUploadState>()

    private val pendingImages = ConcurrentHashMap<String, MutableList<File>>()

    private val repository = FTCRepositoryImpl()
    private val uploadService = FTCImageUploadService(repository)
    private val confirmationService = FTCConfirmationService(repository)

    private fun getScreenshotFileName(ftcScreenShotSource: FTCScreenShotSource): String =
        when (ftcScreenShotSource) {
            FTCScreenShotSource.CONTRACT_DETAILS -> "image_1"
            FTCScreenShotSource.CONTRACT_TERMS -> "image_2"
            FTCScreenShotSource.QUICKBOOK_DIALOG -> "image_3"
        }

    fun addScreenshot(
        bitmap: Bitmap,
        context: Context,
        contractId: String,
        ftcScreenShotSource: FTCScreenShotSource,
        onComplete: (() -> Unit?)? = null
    ) {
        managerScope.launch {
            val imageFile = ScreenshotUtil.bitmapToFileNew(
                context,
                bitmap,
                "${getScreenshotFileName(ftcScreenShotSource)}${ScreenshotUtil.FILE_EXTENSION}"
            )
            synchronized(pendingImages) {
                val images = pendingImages.getOrPut(contractId) { mutableListOf() }
                val existingIndex = images.indexOfFirst { it.name == imageFile.name }
                if (existingIndex != -1) {
                    val oldFile = images[existingIndex]
                    if (oldFile.absolutePath != imageFile.absolutePath && oldFile.exists()) {
                        oldFile.delete()
                    }
                    // File with the same name exists, replace it
                    images[existingIndex] = imageFile
                    MBLog.d(
                        TAG,
                        "Replacing the screenshot ${imageFile.name} for contract: $contractId"
                    )
                } else {
                    images.add(imageFile)
                }
                MBLog.d(TAG, "Added screenshot ${images.size}/3 for contract: $contractId")
            }
            onComplete?.let { it() }
        }
    }

    fun addScreenshotAndUpload(
        bitmap: Bitmap,
        context: Context,
        subscriberId: String,
        contractId: String,
        cartId: String,
        userId: String,
        ftcScreenShotSource: FTCScreenShotSource
    ) {
        // Add the screenshot with a callback to upload when complete
        addScreenshot(bitmap, context, contractId, ftcScreenShotSource) {
            uploadZippedScreenshots(subscriberId, contractId, cartId, userId)
        }
    }

    private fun uploadZippedScreenshots(
        subscriberId: String,
        contractId: String,
        cartId: String,
        userId: String
    ) {
        managerScope.launch {
            runCatching {
                cancelUpload(cartId)

                val imagesToUpload = getImagesForContract(contractId)
                if (imagesToUpload.isEmpty()) {
                    MBLog.e(TAG, "No pending images to upload for contract: $contractId")
                    return@launch
                }

                val metadata = FTCImageUploadMetadata(subscriberId, contractId, userId, cartId)

                performUploadAndTrackState(imagesToUpload, metadata, userId, cartId)
            }.onFailure {
                MBLog.e(TAG, "Error in upload process for cart: $cartId")
            }
        }
    }

    private fun getImagesForContract(contractId: String): List<File> {
        return synchronized(pendingImages) {
            pendingImages[contractId]?.toList() ?: emptyList()
        }
    }

    private suspend fun performUploadAndTrackState(
        imagesToUpload: List<File>,
        metadata: FTCImageUploadMetadata,
        userId: String,
        cartId: String
    ) {
        MBLog.d(
            TAG,
            "Preparing to upload ${imagesToUpload.size} images for contract: ${metadata.contractId} (cartId: $cartId)"
        )

        mutex.withLock {
            val request = createUploadRequest(userId, cartId)
            val uploadJob = createUploadJob(imagesToUpload, metadata, request, cartId)
            activeUploads[cartId] = FTCImageUploadState(job = uploadJob, metadata = metadata)
        }
    }

    private fun createUploadRequest(userId: String, cartId: String): FTCImageUrlRequest {
        return FTCImageUrlRequest(
            mbUserId = userId,
            contentType = CONTENT_TYPE,
            platform = PLATFORM,
            source = SOURCE,
            metadata = mapOf("cartId" to cartId)
        )
    }

    private fun createUploadJob(
        imagesToUpload: List<File>,
        metadata: FTCImageUploadMetadata,
        request: FTCImageUrlRequest,
        cartId: String
    ) = managerScope.async {
        runCatching {
            val zipBytes = uploadService.createZipOutOfImages(imagesToUpload, metadata)

            val recordId = uploadService.uploadZipFileToS3(zipBytes, metadata, request)

            handleConfirmationIfNeeded(cartId, recordId)

            recordId
        }.onFailure {
            activeUploads.remove(cartId)
            MBLog.e(TAG, "Error in screenshot upload process for purchase: $cartId")
            throw it
        }.getOrThrow()
    }

    private suspend fun handleConfirmationIfNeeded(cartId: String, recordId: String) {
        val confirmationMetadata = synchronized(activeUploads) {
            activeUploads[cartId]?.let { state ->
                state.recordId = recordId
                if (state.confirmationRequested && state.metadata != null) {
                    state.metadata
                } else null
            }
        }

        confirmationMetadata?.let { confMetadata ->
            runCatching {
                val confirmResult = confirmationService.confirmUpload(confMetadata, recordId)
                if (confirmResult) {
                    activeUploads.remove(cartId)
                } else {
                    MBLog.e(TAG, "Failed to confirm upload for purchase: $cartId")
                }
            }.onFailure {
                MBLog.e(TAG, "Error confirming upload for purchase: $cartId")
            }
        }
    }

    private fun cancelUpload(cartId: String) {
        activeUploads[cartId]?.let { state ->
            state.job.cancel()
            activeUploads.remove(cartId)
            MBLog.d(TAG, "Cancelled upload for cart: $cartId")
        }
    }

    fun requestUploadConfirmation(
        subscriberId: String,
        contractId: String,
        userId: String,
        cartId: String,
        orderId: String? = null
    ): Boolean {
        val uploadExists = synchronized(activeUploads) { activeUploads.containsKey(cartId) }
        if (!uploadExists) {
            MBLog.e(TAG, "Cannot confirm upload: no active upload found for cart: $cartId")
            return false
        }

        managerScope.launch {
            runCatching {
                val (state, metadata, recordId) = synchronized(activeUploads) {
                    val currentState =
                        activeUploads[cartId] ?: return@synchronized Triple(null, null, null)
                    orderId?.let { currentState.metadata?.orderId = it }
                    clearPendingImages(contractId)
                    // Checking if the zip upload is still in progress
                    if (currentState.recordId == null || currentState.job.isActive) {
                        FTCBreadCrumbLogger.logFTCBreadCrumb(
                            action = FTCBreadCrumbAction.CONFIRM_CONTRACT_STATUS,
                            status = FTCBreadCrumbStatus.IMAGE_UPLOAD_IN_PROGRESS,
                            userId = userId, cartID = cartId, orderId = orderId,
                            contractID = contractId, studioId = subscriberId
                        )
                        currentState.confirmationRequested = true
                        MBLog.d(TAG, "Queued confirmation request for cart: $cartId")
                        return@synchronized Triple(currentState, null, null)
                    }
                    Triple(currentState, currentState.metadata, currentState.recordId)
                }

                if (state == null) {
                    MBLog.e(TAG, "Cannot confirm upload: state disappeared for cart: $cartId")
                    return@launch
                }

                if (metadata != null && recordId != null) {
                    runCatching {
                        val result = confirmationService.confirmUpload(metadata, recordId)
                        if (result) synchronized(activeUploads) { activeUploads.remove(cartId) }
                    }.onFailure { MBLog.e(TAG, "Error confirming upload for cart: $cartId") }
                }
            }.onFailure { MBLog.e(TAG, "Error processing confirmation for cart: $cartId") }
        }
        return true
    }

    private fun clearPendingImages(contractId: String) = synchronized(pendingImages) {
        pendingImages[contractId]?.let { imageFiles ->
            cleanupFiles(imageFiles, contractId)
        }
        pendingImages.remove(contractId)
        MBLog.d(TAG, "Cleared all pending images for contract: $contractId")
    }

    private fun cleanupFiles(imageFiles: List<File>, cartId: String) {
        imageFiles.forEach { imageFile ->
            if (imageFile.exists()) {
                imageFile.delete()
                MBLog.d(TAG, "Temporary file deleted for purchase: $cartId")
            }
        }
    }
}
