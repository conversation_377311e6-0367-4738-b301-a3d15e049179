package com.mindbodyonline.connect.ftc.logs

import com.mindbodyonline.connect.utils.BreadcrumbsUtils

object FTCBreadCrumbLogger {
    fun logFTCBreadCrumb(
        action: FTCBreadCrumbAction,
        status: FTCBreadCrumbStatus,
        userId: String? = null,
        cartID: String? = null,
        recordId: String? = null,
        orderId: String? = null,
        contractID: String? = null,
        studioId: String? = null,
        errorMessage: String? = null,
        location: String? = null
    ) {
        BreadcrumbsUtils.recordFTCAuditLogBreadCrumb(
            mutableMapOf(
                Pair(ParamConstants.ACTION.name, action.name),
                Pair(ParamConstants.STATUS.name, status.name)
            ).apply {
                // Adding only non null values
                userId?.let { this.put(ParamConstants.USER_ID.name, it) }
                cartID?.let { this.put(ParamConstants.CART_ID.name, it) }
                recordId?.let { this.put(ParamConstants.RECORD_ID.name, it) }
                orderId?.let { this.put(ParamConstants.ORDER_ID.name, it) }
                contractID?.let { this.put(ParamConstants.CONTRACT_ID.name, it) }
                studioId?.let { this.put(ParamConstants.STUDIO_ID.name, it) }
                errorMessage?.let { this.put(ParamConstants.ERROR_MESSAGE.name, it) }
                location?.let { this.put(ParamConstants.LOCATION.name, it) }
            }
        )
    }
}
