package com.mindbodyonline.connect.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.mindbodyonline.android.views.recycler.viewholder.SimpleViewHolder
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.activities.details.BusinessDetailsActivity
import com.mindbodyonline.connect.activities.list.services.RoutineServicesActivity
import com.mindbodyonline.connect.activities.schedule.BusinessScheduleActivity
import com.mindbodyonline.connect.analytics.OriginComponent
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.databinding.ViewSavedItemHomeV2Binding
import com.mindbodyonline.connect.home.savedbusiness.SavedBusinessLocation
import com.mindbodyonline.connect.utils.AnalyticsUtils
import com.mindbodyonline.connect.utils.api.toLocationReference
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.Companion.addUserFlowEvent
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeFavorites
import com.mindbodyonline.domain.HomeSeeAllPlacement
import com.mindbodyonline.domain.Location
import com.mindbodyonline.domain.dataModels.SubscriptionLevel
import com.mindbodyonline.framework.abvariant.FeatureFlag
import com.squareup.picasso.Picasso

class SavedBusinessRecyclerAdapter(
    var savedBusinessList: List<SavedBusinessLocation> = emptyList(),
    var longClickListener: (Location) -> Unit,
    var seeAllClickListener:() -> Unit,
    val onLocationClicked: (position: Int, Location) -> Unit = { _, _ -> }
): RecyclerView.Adapter<RecyclerView.ViewHolder>(){

    companion object{
        const val MAX_SAVED_BUSINESS_ALLOWED = 6
        const val MIN_BUSINESS_TO_SHOW_SEE_ALL = 2

        const val FAVORITE_LOCATION_VIEW_TYPE = 1
        const val SEE_ALL_FAVORITE_VIEW_TYPE = 2
    }

    var seeAllLabel: String?=null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        when (viewType) {
            FAVORITE_LOCATION_VIEW_TYPE -> {
                SavedBusinessViewHolder(
                        LayoutInflater.from(parent.context)
                            .inflate(R.layout.view_saved_item_home_v2, parent, false))
            }
            else->{
                SimpleViewHolder(LayoutInflater.from(parent.context)
                    .inflate(R.layout.view_saved_business_see_all_v2, parent, false)).also { holder->
                    holder.itemView.setOnClickListener { seeAllClickListener.invoke() }
                }
            }
        }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when(getItemViewType(position)) {
            FAVORITE_LOCATION_VIEW_TYPE ->
                (holder as SavedBusinessViewHolder).bind(position, savedBusinessList[position].business)
            else ->
                (holder as SimpleViewHolder).itemView.findViewById<TextView>(R.id.favorite_see_all).text = seeAllLabel
        }
    }

    override fun getItemCount(): Int {
        return savedBusinessList.size
    }

    override fun getItemViewType(position: Int): Int {
        return savedBusinessList[position].viewType
    }

    fun addExploreAndSeeAllOptions(favorites: List<Location>, homeSeeAllPlacement: HomeSeeAllPlacement) {
        val businessList = mutableListOf<SavedBusinessLocation>().apply {

            //Maximum 6 favorite studios allowed in list apart from "See all" options
            addAll(favorites.take(MAX_SAVED_BUSINESS_ALLOWED).map { location ->
                SavedBusinessLocation(FAVORITE_LOCATION_VIEW_TYPE, location)
            })

            //To show "See all" option in the end more than 2 favorite studios should be there
            if (favorites.size > MIN_BUSINESS_TO_SHOW_SEE_ALL && homeSeeAllPlacement.isCarouselItem) {
                seeAllLabel = homeSeeAllPlacement.titleLabel
                add(size, SavedBusinessLocation(SEE_ALL_FAVORITE_VIEW_TYPE, Location()))
            }
        }
        savedBusinessList = businessList
        notifyDataSetChanged()
    }

    inner class SavedBusinessViewHolder(itemView: View): RecyclerView.ViewHolder(itemView){

        var binding: ViewSavedItemHomeV2Binding = ViewSavedItemHomeV2Binding.bind(itemView)

        fun bind(position: Int, savedBusiness: Location){
            with(binding){
                favoriteLocationName.text = savedBusiness.studioName
                favoriteLocationAddress.text = savedBusiness.address
                Picasso.get()
                    .load(savedBusiness.studioImageUrl)
                    .placeholder(R.drawable.no_biz_logo)
                    .error(R.drawable.no_biz_logo)
                    .centerInside()
                    .fit()
                    .into(favoriteBusinessImage)

                itemView.setOnClickListener {
                    addUserFlowEvent(HomeFavorites)
                    onLocationClicked(position, savedBusiness)
                    it.context.apply {
                        if (savedBusiness.subscriptionLevel == SubscriptionLevel.ConnectListing) {
                            val businessDetailsIntent = BusinessDetailsActivity.newIntent(this, savedBusiness.toLocationReference())
                            startActivity(businessDetailsIntent)
                        } else {
                            if (FeatureFlag.CLASS_SCHEDULE_UI_REFACTOR.isFeatureEnabled()) {
                                startActivity(
                                    BusinessScheduleActivity.newIntent(
                                        this,
                                        savedBusiness.toLocationReference(),
                                        OriginView.HOME,
                                        OriginComponent.FAVORITE_BUSINESSES_STUDIO_CAROUSEL
                                    )
                                )
                            } else {
                                startActivity(
                                    RoutineServicesActivity.newIntent(
                                        this,
                                        savedBusiness.toLocationReference(),
                                        OriginView.HOME,
                                        OriginComponent.FAVORITE_BUSINESSES_STUDIO_CAROUSEL
                                    )
                                )
                            }
                        }
                        AnalyticsUtils.logEvent("(Home Screen) | Favorites Schedule Tapped")
                    }
                }

                itemView.setOnLongClickListener {
                    longClickListener.invoke(savedBusiness)
                    AnalyticsUtils.logBusinessEvent("(Home Screen) | Favorites business tapped", savedBusiness,
                            "Long press", true,
                            "View Schedule", false)
                    true
                }
            }
        }
    }
}