package com.mindbodyonline.connect.fragments

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Build.VERSION.SDK_INT
import android.os.Bundle
import android.provider.Settings
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout.LayoutParams
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.DrawableRes
import androidx.annotation.Size
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.google.android.gms.maps.model.LatLng
import com.mindbodyonline.android.api.sales.model.payments.PaymentMethod
import com.mindbodyonline.android.api.sales.model.pos.cart.Cart
import com.mindbodyonline.android.api.sales.model.pos.deals.Deal
import com.mindbodyonline.android.util.SafeGson
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.activities.custom.AbstractSearchActivity
import com.mindbodyonline.connect.activities.details.ClassTypeDetailsActivity
import com.mindbodyonline.connect.activities.details.DealDetailsActivity
import com.mindbodyonline.connect.activities.workflow.SearchLocationMapActivity
import com.mindbodyonline.connect.adapters.FitnessCategoryRecyclerAdapterV2
import com.mindbodyonline.connect.analytics.OriginComponent
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.analytics.home.HomeEventTracker
import com.mindbodyonline.connect.analytics.search.SearchFlowTracker
import com.mindbodyonline.connect.common.GlobalLocationIndicatorState
import com.mindbodyonline.connect.common.events.globallocationindicator.EVENT_ATTRIBUTE_VALUE_HOME
import com.mindbodyonline.connect.common.utilities.SearchUtil.clearAllFilters
import com.mindbodyonline.connect.common.utilities.getCalendarInstanceForSearch
import com.mindbodyonline.connect.common.utilities.isLocationAvailable
import com.mindbodyonline.connect.common.utilities.toCalendarInGlobalTimeZone
import com.mindbodyonline.connect.common.utilities.visible
import com.mindbodyonline.connect.databinding.FragmentHomeBinding
import com.mindbodyonline.connect.databinding.SpasCarouselBinding
import com.mindbodyonline.connect.databinding.ViewHomeBannerImageCtaBinding
import com.mindbodyonline.connect.databinding.ViewHomeCarouselClassesWithImageBinding
import com.mindbodyonline.connect.databinding.ViewHomeDiscoveryCarouselBinding
import com.mindbodyonline.connect.databinding.ViewHomeFitnessCategoriesBinding
import com.mindbodyonline.connect.databinding.ViewHomeFlexCarouselV2Binding
import com.mindbodyonline.connect.databinding.ViewHomeIntroOffersCarouselV2Binding
import com.mindbodyonline.connect.databinding.ViewHomeLmoCarouselV2Binding
import com.mindbodyonline.connect.databinding.ViewHomeNearYouItemBinding
import com.mindbodyonline.connect.databinding.ViewHomeSavedCarousalBinding
import com.mindbodyonline.connect.databinding.ViewHomeSimilarHistoryCarouselV2Binding
import com.mindbodyonline.connect.databinding.ViewHomeUpcomingCarousalV2Binding
import com.mindbodyonline.connect.explore.filters.CategoriesGroup
import com.mindbodyonline.connect.explore.search.SearchQueryActivity
import com.mindbodyonline.connect.fragments.custom.LocationAwareMainFragment
import com.mindbodyonline.connect.fragments.search.ExploreFitnessFragment
import com.mindbodyonline.connect.fragments.search.ExploreFitnessFragment.Companion.BEAUTY_VERTICAL
import com.mindbodyonline.connect.fragments.search.map.MbLatLng
import com.mindbodyonline.connect.fragments.search.map.MbMap
import com.mindbodyonline.connect.home.DiscoveryPagerAdapter
import com.mindbodyonline.connect.home.DiscoveryPagerAdapter.Companion.BEAUTY_ITEM_TYPE
import com.mindbodyonline.connect.home.DiscoveryPagerAdapter.Companion.DEALS_ITEM_TYPE
import com.mindbodyonline.connect.home.DiscoveryPagerAdapter.Companion.DISCOVERY_PAGE_MARGIN
import com.mindbodyonline.connect.home.DiscoveryPagerAdapter.Companion.FITNESS_ITEM_TYPE
import com.mindbodyonline.connect.home.DiscoveryPagerAdapter.Companion.WELLNESS_ITEM_TYPE
import com.mindbodyonline.connect.home.HomeCarouselRecyclerAdapter
import com.mindbodyonline.connect.home.HomeDomainModelFactory
import com.mindbodyonline.connect.home.HomeViewModelFactory
import com.mindbodyonline.connect.home.RecommendedHomeViewModel
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.EmptyState.EMPTY_FAVORITE_LOCATIONS
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.EmptyState.EMPTY_FLEX_EARLY_BIRDS
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.EmptyState.EMPTY_FLEX_NIGHT_OWLS
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.EmptyState.EMPTY_FLEX_STARTING_SOON
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.EmptyState.EMPTY_GENERAL_RECOMMENDER
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.EmptyState.EMPTY_INTRO_OFFERS
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.EmptyState.EMPTY_LMOS
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.EmptyState.EMPTY_SPAS
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.EmptyState.EMPTY_SURGE_LMOS
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.EmptyState.EMPTY_UPCOMINGS
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.OrderState.FLEX_USER_LOCATION_DISABLED
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.OrderState.FLEX_USER_LOCATION_ENABLED
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.OrderState.GUEST_USER_LOCATION_ENABLED
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.OrderState.NON_FLEX_LOCATION_DISABLED
import com.mindbodyonline.connect.home.RecommendedHomeViewModel.OrderState.VERIFIED_USER_LOCATION_ENABLED
import com.mindbodyonline.connect.home.SavedBusinessRecyclerAdapter
import com.mindbodyonline.connect.home.SpasCarouselAdapter
import com.mindbodyonline.connect.home.UpcomingPagerAdapterV2
import com.mindbodyonline.connect.home.UpcomingPagerAdapterV2.Companion.UPCOMING_V2_PAGER_PADDING
import com.mindbodyonline.connect.home.analytics.AlmostSoldOutItemClickEvent
import com.mindbodyonline.connect.home.analytics.BookAFavoriteItemClickEvent
import com.mindbodyonline.connect.home.analytics.DealsItemClickEvent
import com.mindbodyonline.connect.home.analytics.ExploreCategoryClickEvent
import com.mindbodyonline.connect.home.analytics.HomeSeeAllEvent
import com.mindbodyonline.connect.home.analytics.NewFavoriteClickEvent
import com.mindbodyonline.connect.home.analytics.PriceDropClickEvent
import com.mindbodyonline.connect.home.analytics.SomethingNewItemClickEvent
import com.mindbodyonline.connect.home.analytics.TreatYourselfItemClickEvent
import com.mindbodyonline.connect.home.analytics.UpcomingItemClickEvent
import com.mindbodyonline.connect.navigation.NavigationViewModel
import com.mindbodyonline.connect.utils.AnalyticsUtils
import com.mindbodyonline.connect.utils.AppLaunchTracker
import com.mindbodyonline.connect.utils.CATEGORY
import com.mindbodyonline.connect.utils.Constants
import com.mindbodyonline.connect.utils.Constants.BEAUTY_VERTICAL_ID
import com.mindbodyonline.connect.utils.Constants.EARLY_BIRD_END_HOUR
import com.mindbodyonline.connect.utils.Constants.EARLY_BIRD_START_HOUR
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_CLASS_INSTANCE_ID
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_DEAL
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_LATITUDE
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_LAT_LNG
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_LOCATION_SEARCHTERM
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_LONGITUDE
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_SEARCHTERM
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_SEARCH_STATE
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_SHOULD_SEARCH_WITH_LOCATION_ONLY
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_SITEID
import com.mindbodyonline.connect.utils.Constants.KEY_VERTICAL_ID
import com.mindbodyonline.connect.utils.Constants.MAX_MARKERS_NEAR_YOU
import com.mindbodyonline.connect.utils.Constants.NEAR_YOU_MAP_ZOOM_LEVEL
import com.mindbodyonline.connect.utils.Constants.NIGHT_OWL_END_HOUR
import com.mindbodyonline.connect.utils.Constants.NIGHT_OWL_START_HOUR
import com.mindbodyonline.connect.utils.Constants.SHOW_BUSINESS_DETAILS
import com.mindbodyonline.connect.utils.Constants.SIMILAR_SEE_ALL_END
import com.mindbodyonline.connect.utils.Constants.SIMILAR_SEE_ALL_START
import com.mindbodyonline.connect.utils.Constants.SORTING_OPTION_DISTANCE
import com.mindbodyonline.connect.utils.DISCOVERY
import com.mindbodyonline.connect.utils.DealDetailsUtils
import com.mindbodyonline.connect.utils.DialogUtils
import com.mindbodyonline.connect.utils.FLEX_EARLY_BIRD
import com.mindbodyonline.connect.utils.FLEX_LMO
import com.mindbodyonline.connect.utils.FLEX_NIGHT_OWLS
import com.mindbodyonline.connect.utils.FLEX_STARTING_SOON
import com.mindbodyonline.connect.utils.GeoLocationUtils
import com.mindbodyonline.connect.utils.HOME_ANY_SEE_ALL_CLICKED
import com.mindbodyonline.connect.utils.HOME_SCREEN_BEAUTY_DISCOVERY_TAPPED_PENDO
import com.mindbodyonline.connect.utils.HOME_SCREEN_DEALS_DISCOVERY_TAPPED_PENDO
import com.mindbodyonline.connect.utils.HOME_SCREEN_DISCOVERY_CAROUSEL_TAPPED_PENDO
import com.mindbodyonline.connect.utils.HOME_SCREEN_FITNESS_DISCOVERY_TAPPED_PENDO
import com.mindbodyonline.connect.utils.HOME_SCREEN_WELLNESS_DISCOVERY_TAPPED_PENDO
import com.mindbodyonline.connect.utils.INTRO_OFFER
import com.mindbodyonline.connect.utils.LMO_DROP
import com.mindbodyonline.connect.utils.LMO_SURGE
import com.mindbodyonline.connect.utils.NEAR_YOU
import com.mindbodyonline.connect.utils.SAVED
import com.mindbodyonline.connect.utils.SIMILAR_HISTORY
import com.mindbodyonline.connect.utils.SOURCE_APP_HOME_ACTIVITY
import com.mindbodyonline.connect.utils.SOURCE_APP_HOME_DISCOVERY
import com.mindbodyonline.connect.utils.SOURCE_APP_HOME_LMO
import com.mindbodyonline.connect.utils.SOURCE_APP_HOME_SIMILAR_HISTORY
import com.mindbodyonline.connect.utils.SOURCE_APP_HOME_SPA
import com.mindbodyonline.connect.utils.SPA
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.StringValue
import com.mindbodyonline.connect.utils.UPCOMING
import com.mindbodyonline.connect.utils.ViewUtils
import com.mindbodyonline.connect.utils.addGapItemDecoration
import com.mindbodyonline.connect.utils.addSeeAllEventData
import com.mindbodyonline.connect.utils.api.LmoFilterType
import com.mindbodyonline.connect.utils.api.RecommenderType
import com.mindbodyonline.connect.utils.api.toDomainModel
import com.mindbodyonline.connect.utils.convertToBusinessListingModel
import com.mindbodyonline.connect.utils.isFlexUser
import com.mindbodyonline.connect.utils.isGuestUser
import com.mindbodyonline.connect.utils.isVerifiedNonFlex
import com.mindbodyonline.connect.utils.sendTapEvent
import com.mindbodyonline.connect.utils.setDebounceClickListener
import com.mindbodyonline.connect.utils.trackHomeCarouselEvent
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.BeautyDiscovery
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.DealsDiscovery
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.FitnessDiscovery
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeCategories
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeFavorites
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeFlex
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeIntroOffers
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeIntroOffersSeeAllTapped
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeLMO
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeNearYou
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeSimilarHistory
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeSpa
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeTab
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.HomeUpcoming
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.WellnessDiscovery
import com.mindbodyonline.data.StaticInstance
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.Location
import com.mindbodyonline.domain.connv1.Visit
import com.mindbodyonline.framework.abvariant.ABHelperUtils
import com.mindbodyonline.framework.abvariant.DevelopmentFlag
import com.mindbodyonline.framework.abvariant.HOME_INTRO_OFFER_SEE_ALL_OFFER_TAPPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_ANY_SEE_ALL
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_BEAUTY_DISCOVERY_DISPLAYED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_BEAUTY_DISCOVERY_TAPPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_DEALS_DISCOVERY_DISPLAYED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_DEALS_DISCOVERY_TAPPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_DISCOVERY_CAROUSEL_SWIPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_DISCOVERY_CAROUSEL_TAPPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_FAVORITES_SWIPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_FITNESS_DISCOVERY_DISPLAYED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_FITNESS_DISCOVERY_TAPPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_INTRO_OFFERS_CARD_TAPPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_LMO_SWIPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_SEE_ALL_INTRO_OFFERS_TAPPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_SEE_ALL_LMO_TAPPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_SEE_ALL_SIMILAR_HISTORY_TAPPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_SIMILAR_HISTORY_SWIPED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_WELLNESS_DISCOVERY_DISPLAYED
import com.mindbodyonline.framework.abvariant.HOME_SCREEN_WELLNESS_DISCOVERY_TAPPED
import com.mindbodyonline.ui.common.views.BusinessListingCardActions
import com.mindbodyonline.ui.screen.home.DealsCarouselComposable
import com.mindbodyonline.views.dialog.BusinessOptionsDialog
import com.mindbodyonline.views.dialog.ConfirmationHUD
import com.mindbodyonline.views.dialog.MaterialOptionDialog
import com.tbuonomo.viewpagerdotsindicator.setPaddingHorizontal
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Collections
import java.util.concurrent.TimeUnit

class HomeFragment : LocationAwareMainFragment() {

    private val PUSH_CONSENT_DIALOG_TAG = "PUSH_CONSENT_DIALOG_TAG"
    private val MIN_TOP_OFFSET = 1

    private val viewModel: RecommendedHomeViewModel by viewModels {
        HomeViewModelFactory(
            ServiceLocator.recommendedRepository, ServiceLocator.userRepository,
            ServiceLocator.getFavoriteLocationRepository(), this, arguments,
            ServiceLocator.globalLocationIndicatorRepository,
            ServiceLocator.timeZoneApi
        )
    }

    private val navigationViewModel: NavigationViewModel by activityViewModels()

    //region ViewBinding Init
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!

    private var carouselImageBindings =
        HashMap<CarouselViewBinding, ViewHomeCarouselClassesWithImageBinding>()
    private var bannerCtaBindings = HashMap<BannerCtaBinding, ViewHomeBannerImageCtaBinding>()

    private var _savedBusinessBinding: ViewHomeSavedCarousalBinding? = null
    private val savedBusinessBinding get() = _savedBusinessBinding!!

    private var _upcomingCarousalBinding: ViewHomeUpcomingCarousalV2Binding? = null
    private val upcomingCarousalBinding get() = _upcomingCarousalBinding!!

    private var _introOffersCarouselBindingV2: ViewHomeIntroOffersCarouselV2Binding? = null
    private val introOffersCarouselBindingV2 get() = _introOffersCarouselBindingV2!!

    private var _similarHistoryCarouselBindingV2: ViewHomeSimilarHistoryCarouselV2Binding? = null
    private val similarHistoryCarouselBindingV2 get() = _similarHistoryCarouselBindingV2

    private var _lmoCarouselBindingV2: ViewHomeLmoCarouselV2Binding? = null
    private val lmoCarouselBindingV2 get() = _lmoCarouselBindingV2!!

    private var _discoveryCarouselBinding: ViewHomeDiscoveryCarouselBinding? = null
    private val discoveryCarouselBinding get() = _discoveryCarouselBinding!!

    private var _categoryBinding: ViewHomeFitnessCategoriesBinding? = null
    private val categoryBinding get() = _categoryBinding!!

    private var _spaCarouselBinding: SpasCarouselBinding? = null
    private val spaCarouselBinding get() = _spaCarouselBinding!!

    private var _nearYouMapBinding: ViewHomeNearYouItemBinding? = null
    private var mapFragment: MBMapFragment? = null

    private var _lmoSurgeCarouselBindingV2: ViewHomeLmoCarouselV2Binding? = null
    private val lmoSurgeCarouselBindingV2 get() = _lmoSurgeCarouselBindingV2!!

    lateinit var activityResultLauncher: ActivityResultLauncher<Intent>

    private var flexCarouselBindings =
        HashMap<CarouselViewBinding, ViewHomeFlexCarouselV2Binding>()
    //endregion

    // GCM Connected, if we haven't already ask if they want to receive push notifications
    override fun onConnected() {
        if (SharedPreferencesUtils.shouldShowConsentDialog()) requestPushNotificationPermission()
    }

    override fun applyTopOffset(topOffset: Int, root: View) {
        binding.statusBarScrim.apply {
            if (topOffset >= MIN_TOP_OFFSET) layoutParams.height = topOffset
            // Some devices report topOffset zero (no system bar), which is match_parent in ConstraintLayout
            // Therefore we must set a min offset so that the scrim bar view does not take up entire page
            else layoutParams.height = MIN_TOP_OFFSET
            setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.white))
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (DevelopmentFlag.DEVELOPMENT_APP_LAUNCH_STEPS.isFeatureEnabled()) {
            AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.HOME_FRAGMENT_ON_VIEW_CREATED)
            AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.END_LAUNCH)
        }

        activityResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val data: Intent? = result.data
                val location = data?.getStringExtra(Constants.KEY_BUNDLE_LOCATION)?.let { SafeGson.fromJson(it, Location::class.java) }
                val paymentMethod = data?.getStringExtra(Constants.KEY_BUNDLE_PAYMENT_METHOD)?.let { SafeGson.fromJson(it, PaymentMethod::class.java) }
                val cart = data?.getStringExtra(Constants.KEY_BUNDLE_CART)?.let { SafeGson.fromJson(it, Cart::class.java) }
                val showAttributionSurvey = data?.getBooleanExtra(Constants.KEY_BUNDLE_SHOW_ATTRIBUTION_SURVEY, false)
                val orderId = data?.getLongExtra(Constants.KEY_BUNDLE_ORDER_ID, 0L)
                DialogUtils.showItemPurchasedConfirmationDialog(
                        context,
                        activity?.supportFragmentManager,
                        ConfirmationHUD.DEAL_MODE,
                        location,
                        null,
                        null,
                        paymentMethod,
                        cart,
                        null,
                        showAttributionSurvey,
                        orderId,
                        null
                )
            }
        }
        initOrderStateObserver()
        initEmptyStateObserver()
        initDataObservers()
        initLocationAwareObservers()
        initLocationIndicatorObserver()
        viewModel.initialize()
        viewModel.getHomeSeeAllPlacement(getString(R.string.see_all))
        viewModel.setFirstTimeUserAnalytics()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        _savedBusinessBinding = null
        _upcomingCarousalBinding = null
        _introOffersCarouselBindingV2 = null
        _similarHistoryCarouselBindingV2 = null
        _lmoCarouselBindingV2 = null
        _discoveryCarouselBinding = null
        _spaCarouselBinding = null
        _nearYouMapBinding = null
        _lmoSurgeCarouselBindingV2 = null
        arrayOf(
            carouselImageBindings,
            bannerCtaBindings,
            flexCarouselBindings
        ).forEach { it.clear() }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        viewModel.onHomeScreenShown()
    }

    override fun onResume() {
        super.onResume()
        viewModel.updateCarousels()
    }

    private fun initLocationIndicatorObserver() {
        viewModel.globalLocationIndicatorState.observe(viewLifecycleOwner) { state ->
            viewModel.manageTimeZoneStoreForSelectedLocation(state)
            renderGlobalLocationIndicator(state)
            updateCarouselsAsPerGlobalState(state)
        }
    }

    // Location Dialog was shown and permission was accepted or denied
    override fun onLocationPermissionAction(accepted: Boolean) {
        super.onLocationPermissionAction(accepted)
        viewModel.checkLocationStateChanged()
        if (!accepted || !GeoLocationUtils.locationServicesEnabled(context)) {
            initNoLocationCard()
        }
    }

    // Android OS reported Location provider has changed due to user changing Location setting
    private fun initLocationAwareObservers() =
        super.observeLocationProviderChanges().observe(viewLifecycleOwner) {
            viewModel.checkLocationStateChanged()
        }

    /**
     * Initializes the view OrderState logic that determines which carousels or empty cards should
     * be inflated given a user type (guest, verified, flex) and location enabled state.  Based on
     * the OrderState, the visibility of the carousels and empty cards are set and adapters for
     * the carousels are initialized.
     */
    private fun initOrderStateObserver() {
        viewModel.orderState.observe(viewLifecycleOwner) { orderState ->
            binding.homeContent.removeAllViews()
            binding.homeNoLocation.removeAllViews()
            MBLog.d("HOME_PAGE", "ORDER STATE: " + orderState.name)
            when (orderState) {
                // Favorites (or empty favorites card), Intro offers, LMOs, Personalized Card, Categories
                VERIFIED_USER_LOCATION_ENABLED -> {
                    authUserLocationEnabledViewState()
                }
                // Empty favorites card, Intro offers, LMOs, Personalized Card (with no name), Categories
                GUEST_USER_LOCATION_ENABLED -> {
                    guestUserLocationEnabledViewState()
                }
                // Favorites (or empty favorites card), Intro offers, LMOs, Personalized Card,
                // Flex Starting Soon, Flex Early Birds, Flex Night Owls, Flex Categories
                FLEX_USER_LOCATION_ENABLED -> {
                    flexUserLocationEnabledViewState()
                }

                NON_FLEX_LOCATION_DISABLED, FLEX_USER_LOCATION_DISABLED -> {
                    locationDisabledViewState(orderState)
                }
            }
        }
    }

    private fun locationDisabledViewState(orderState: RecommendedHomeViewModel.OrderState?) {
        binding.homeContent.visible = false
        binding.homeNoLocation.visible = true
        introOffers(isLocationEnabled = false)
        discoveryCarousel(isLocationEnabled = false)
        nearYouMapView(isLocationEnabled = false)
        categories(isLocationEnabled = false, isFlex = orderState == FLEX_USER_LOCATION_DISABLED)
        initLmoSurgeCarouselAdapterV2(false)
        spasCarousel(isLocationEnabled = false)
    }

    private fun flexUserLocationEnabledViewState() {
        binding.homeContent.visible = true
        binding.homeNoLocation.visible = false
        viewModel.getCarouselOrder().forEach { carousel ->
            reorderCarousels(carousel, isLocationEnabled = true, isFlex = false)
        }
    }

    private fun guestUserLocationEnabledViewState() {
        binding.homeContent.visible = true
        binding.homeNoLocation.visible = false
        viewModel.getCarouselOrder().forEach { carousel ->
            reorderCarousels(carousel, isLocationEnabled = true, isFlex = false)
        }
    }

    private fun authUserLocationEnabledViewState() {
        binding.homeContent.visible = true
        binding.homeNoLocation.visible = false
        viewModel.getCarouselOrder().forEach { carousel ->
            reorderCarousels(carousel, isLocationEnabled = true, isFlex = false)
        }
    }

    private fun renderGlobalLocationIndicator(indicatorState: GlobalLocationIndicatorState) {
        binding.globalLocationIndicator.apply {
            root.visible = true
            currentLocation.text = indicatorState.locationText

            root.setDebounceClickListener {
                viewModel.trackEditLocationOpened()
                val intent = Intent(activity, SearchQueryActivity::class.java).apply {
                    putExtra(KEY_BUNDLE_SHOULD_SEARCH_WITH_LOCATION_ONLY, true)
                    if (viewModel.isLocationEnabledAndAllowed().not()
                        && indicatorState !is GlobalLocationIndicatorState.NoLocation
                    ) {
                        // We want to show last active location text from the indicator on the search bar,
                        // only when location services are disabled and the location text is not "No location"
                        putExtra(KEY_BUNDLE_LOCATION_SEARCHTERM, currentLocation.text.toString())
                    }
                }
                locationSearchResultLauncher.launch(intent)
            }
        }
    }

    /** This is to map "Use Current Location" as "Nearby"
     * else return the text as it is a actual location name
     */
    private fun transformGlobalLocationIndicatorText(text: String): String {
        return when (text) {
            getString(R.string.search_current_location_hint) -> {
                getString(R.string.global_location_indicator_nearby_text)
            }

            else -> {
                text
            }
        }
    }

    /**
    Initializes the appropriate no location card
     */
    private fun initNoLocationCard() {
        isGuestUser().let { isGuest ->
            with(binding) {
                noLocationCardWithoutCarousels.root.visible = isGuest
                contentScrollView.visible = !isGuest
                noLocationCardWithCarousels.root.visible = !isGuest
                val noLocationCard =
                    if (isGuest) noLocationCardWithoutCarousels else noLocationCardWithCarousels
                noLocationCard.tvOpenLocationSettings.setDebounceClickListener {
                    viewModel.trackOpenLocationSettings(EVENT_ATTRIBUTE_VALUE_HOME)
                    context?.startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
                }
            }
        }
    }

    /**
    Updates the carousels as per the global location indicator state
     */
    private fun updateCarouselsAsPerGlobalState(indicatorState: GlobalLocationIndicatorState) {
        when (indicatorState) {
            is GlobalLocationIndicatorState.CurrentLocation,
            is GlobalLocationIndicatorState.LastSearchedLocation,
            is GlobalLocationIndicatorState.LastKnownLocation,
            -> {
                with(binding) {
                    noLocationCardWithoutCarousels.root.visible = false
                    binding.contentScrollView.visible = true
                    noLocationCardWithCarousels.root.visible = false
                    homeNoLocation.visible = false
                    homeContent.visible = true
                    homeContent.removeAllViews()
                }

                //Inflate carousels in order
                if (isVerifiedNonFlex() || isFlexUser()) {
                    savedBusinessCarousal()
                    upcomingCarouselV2()
                }
                introOffers(isLocationEnabled = true)
                if (isFlexUser()) {
                    flexStartingSoonCarouselV2()
                }
                nearYouMapView(isLocationEnabled = true)
                initSimilarHistoryCarousel()
                lmoCarousel(isLocationEnabled = true)
                discoveryCarousel(isLocationEnabled = true)
                if (isFlexUser()) {
                    flexEarlyBirdsCarouselV2()
                    flexNightOwlsCarouselV2()
                }
                initLmoSurgeCarouselAdapterV2(isLocationEnabled = true)
                spasCarousel(isLocationEnabled = true)
                categories(isLocationEnabled = true, isFlexUser())
            }

            is GlobalLocationIndicatorState.NoLocation -> {
                // Display no location card, only after showing location permission dialog
                if (SharedPreferencesUtils.getLocationPermissionsDialogDisplayed() || !SharedPreferencesUtils.userFirstAskForLocationPermissions()) {
                    initNoLocationCard()
                }
                with(binding) {
                    if (!isGuestUser()) {
                        // Display favorites and upcoming
                        homeNoLocation.visible = false
                        homeContent.visible = true
                        homeContent.removeAllViews()
                        savedBusinessCarousal()
                        upcomingCarouselV2()
                    }
                }
            }

        }
    }

    /**
     * Initializes the view EmptyState which displays empty cards instead of a carousel for that
     * carousel type.  Use this state to show a card with an action instead of just hiding the
     * carousel for that data type.
     */
    private fun initEmptyStateObserver() {
        viewModel.emptyStateReason.observe(viewLifecycleOwner) { emptyStateReason ->
            viewModel.triggerAndSendAppLaunchMetrics()
            MBLog.d("HOME_PAGE", "EMPTY STATE: " + emptyStateReason.name)
            when (emptyStateReason) {
                // Guest users or verified users with no favorites yet are shown empty favorites card
                EMPTY_FAVORITE_LOCATIONS -> {
                    _savedBusinessBinding?.let {
                        it.root.visible = false
                        val savedIndex = binding.homeContent.indexOfChild(savedBusinessBinding.root)
                        _discoveryCarouselBinding?.let { discovery ->
                            binding.homeContent.removeView(discovery.root)
                            binding.homeContent.addView(discovery.root, savedIndex)
                        }
                    }
                }

                EMPTY_LMOS -> {
                    lmoCarouselBindingV2.root.visible = false
                }

                EMPTY_UPCOMINGS -> {
                    _upcomingCarousalBinding?.root?.visible = false
                }

                EMPTY_INTRO_OFFERS -> {
                    viewModel.trackNoIntroOffersDisplayed()
                    _introOffersCarouselBindingV2?.root?.visible = false
                }

                EMPTY_GENERAL_RECOMMENDER -> {
                    similarHistoryCarouselBindingV2?.root?.visible = false
                }

                EMPTY_FLEX_STARTING_SOON -> {
                    flexCarouselBindings[CarouselViewBinding.FLEX_SOON]?.root?.visible = false
                }

                EMPTY_FLEX_EARLY_BIRDS -> {
                    flexCarouselBindings[CarouselViewBinding.FLEX_EARLY_BIRD]?.root?.visible = false
                }

                EMPTY_FLEX_NIGHT_OWLS -> {
                    flexCarouselBindings[CarouselViewBinding.FLEX_NIGHTOWL]?.root?.visible = false
                }

                EMPTY_SPAS -> {
                    spaCarouselBinding.root.visible = false
                }

                EMPTY_SURGE_LMOS -> {
                    lmoSurgeCarouselBindingV2.root.visible = false
                }

                else -> {}
            }
        }
    }

    /**
     * Initialize the observers of the LiveData's in the RecommendedHomeViewModel.  Observes the
     * favorite locations, LMOs, intro offers, recommended classes.  The data being observed here
     * is non-null and non-empty and the adapter for this data type should already be initialized.
     * These observers set the data in the appropriate adapter and call notifyDataSetChanged.
     */
    private fun initDataObservers() {

        viewModel.favoriteLocations.observe(viewLifecycleOwner) { favorites ->

            viewModel.triggerAndSendAppLaunchMetrics()
            _savedBusinessBinding?.let {
                updateSavedBusinesses(favorites)
                _discoveryCarouselBinding?.let { discoveryBinding ->
                    binding.homeContent.removeView(discoveryBinding.root)
                    binding.homeContent.addView(
                        discoveryBinding.root,
                        viewModel.discoveryCarouselIndex - 1
                    )
                }
            }
        }

        viewModel.upcoming.observe(viewLifecycleOwner) { visits ->
            _upcomingCarousalBinding?.let { updateUpcomingCarouselV2(visits) }
        }

        viewModel.introOffers.observe(viewLifecycleOwner) { introOffers ->
            _introOffersCarouselBindingV2?.let { updateIntroOffersV2(introOffers) }
            //We want to hide the empty state UI in either case where we get any intro offers
            // or we don't get any intro offers for requested location
            bannerCtaBindings[BannerCtaBinding.INTRO]?.root?.visible = false
        }

        viewModel.lastMinuteOffers.observe(viewLifecycleOwner) { lastMinuteOffers ->
            _lmoCarouselBindingV2?.let { updateLmoCarouselV2(lastMinuteOffers) }
        }

        viewModel.recommendedClasses.observe(viewLifecycleOwner) { recommendedClasses ->
            _similarHistoryCarouselBindingV2?.let {
                updateSimilarHistoryCarouselV2(
                    recommendedClasses
                )
            }
        }

        viewModel.flexStartingSoon.observe(viewLifecycleOwner) { flexClasses ->
            flexClasses.takeIf { it.isNotEmpty() }?.let {
                updateFlexHomeCarouselAdapter(it, CarouselViewBinding.FLEX_SOON)
            }
        }

        viewModel.flexEarlyBirds.observe(viewLifecycleOwner) { flexClasses ->
            flexClasses.takeIf { it.isNotEmpty() }?.let {
                updateFlexHomeCarouselAdapter(it, CarouselViewBinding.FLEX_EARLY_BIRD)
            }
        }

        viewModel.flexNightOwls.observe(viewLifecycleOwner) { flexClasses ->
            flexClasses.takeIf { it.isNotEmpty() }?.let {
                updateFlexHomeCarouselAdapter(it, CarouselViewBinding.FLEX_NIGHTOWL)
            }
        }

        viewModel.spas.observe(viewLifecycleOwner) { spasList ->
            _spaCarouselBinding?.let { updateSpasCarousel(spasList) }
        }

        viewModel.nearYouLocations.observe(viewLifecycleOwner) { nearYouLocations ->
            _nearYouMapBinding?.let {
                if (requireContext().isLocationAvailable()) {
                    mapFragment?.let { mapFragment ->
                        val updatedLocation = SharedPreferencesUtils.getLastHomeScreenMapLocation()
                        mapFragment.clearMarkers()
                        mapFragment.moveMapToLocation(
                            MbLatLng(
                                updatedLocation.latitude,
                                updatedLocation.longitude
                            )
                        )
                    }
                    addMarkersForBusinessLocations(nearYouLocations)
                    it.toggleMapViewVisibility()
                }
            }
        }

        viewModel.surgeLastMinuteOffers.observe(viewLifecycleOwner) { surgeLmos ->
            _lmoSurgeCarouselBindingV2?.let {
                surgeLmos.takeIf { it.isNotEmpty() }?.let { surgeLmoList ->
                    updateLmoSurgeCarouselV2(surgeLmoList)
                }
            }
        }
    }

    private fun updateUpcomingCarouselV2(visits: List<Visit>) {
        with(upcomingCarousalBinding) {
            visits.takeIf { it.size == 1 }?.let {
                indicator.visibility = View.INVISIBLE
            } ?: kotlin.run { indicator.visible = true }

            if (visits.isNotEmpty()) {
                val upcomingTitle = viewModel.homeScreenHeadlineData.upcomingTitle.asString(requireActivity())
                homeUpcomingTitle.setNormalTextStyle()
                homeUpcomingTitle.setTitle(
                    (homeUpcomingViewPager.adapter as? UpcomingPagerAdapterV2)
                        ?.populateUpcomingTitle(
                            <EMAIL>(),
                            upcomingTitle,
                            visits.size
                        )
                )
                if (viewModel.homeSeeAllPlacement.isCarouselHeader) {
                    homeUpcomingTitle.also {
                        it.setSeeAllButtonText(viewModel.homeSeeAllPlacement.titleLabel)
                        it.setRightCaretVisibility(viewModel.homeSeeAllPlacement.isRightCaret)
                        it.setOnClickListener {
                            HomeEventTracker.trackHomeSeeAllEvent(
                                    originComponent = OriginComponent.UPCOMING_CLASSES
                            )
                            viewModel.onTrackedComponentClicked(HomeSeeAllEvent.Upcoming)
                            UserCheckoutFlowTracking.addUserFlowEvent(HomeUpcoming)
                            navigationViewModel.onMyScheduleButtonClicked()
                        }
                    }
                }
                val upcomingPagerAdapter = (homeUpcomingViewPager.adapter as? UpcomingPagerAdapterV2)
                upcomingPagerAdapter?.addSeeAllOption(
                    visits,
                    viewModel.homeSeeAllPlacement
                )
                upcomingPagerAdapter?.setOriginProperties(
                    originView = OriginView.HOME,
                    originComponent = OriginComponent.UPCOMING_CARD
                )
                homeUpcomingViewPager.visible = true
            }
            root.visible = visits.isNotEmpty()
        }
    }

    private fun refreshComposeIntroOfferAdapter(introOffers: List<Deal>) {
        val currentLocation = GeoLocationUtils.getBestLocation(this.requireContext())
        introOffersCarouselBindingV2.busListingsComposeView.visible = true
        with(introOffersCarouselBindingV2.busListingsComposeView) {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                DealsCarouselComposable(dealsModels = introOffers.mapNotNull { dealModel ->
                    ServiceLocator.locationRepository.introOfferLocations[dealModel.location.masterLocationId]?.toDomainModel()
                        ?.let {
                            dealModel.convertToBusinessListingModel(
                                dealLocation = it,
                                currentLocation = currentLocation,
                                context = <EMAIL>()
                            )
                        }
                }, BusinessListingCardActions(
                    onCardClick = { index ->
                        val location =
                            ServiceLocator.locationRepository.introOfferLocations[introOffers[index].location.masterLocationId]?.toDomainModel()
                        sendIntroOfferEvent(index, introOffers[index], location)
                        HomeEventTracker.trackHomeCardClickEvent(
                            originComponent = OriginComponent.INTRO_OFFERS,
                            rank = index + 1,
                            location = location,
                            deal = introOffers[index]
                        )
                        if (ABHelperUtils.showBusinessFocussedDealDetailsScreen()) {
                            context?.let {
                                DealDetailsUtils.getBusinessFocusedDealDetailsIntent(
                                        it,
                                        introOffers[index],
                                        OriginView.HOME,
                                        OriginComponent.INTRO_OFFERS
                                )
                            }?.apply {
                                activityResultLauncher.launch(this)
                            }
                        } else {
                            val dealsIntent = Intent(context, DealDetailsActivity::class.java)
                                    .putExtra(SHOW_BUSINESS_DETAILS, true)
                                    .putExtra(KEY_BUNDLE_DEAL, SafeGson.toJson(introOffers[index]))
                                    .putExtra(Constants.KEY_BUNDLE_ORIGIN_VIEW, OriginView.HOME)
                                    .putExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT, OriginComponent.INTRO_OFFERS)
                            <EMAIL>?.startActivity(dealsIntent)
                        }
                    }
                ))
            }
        }
    }

    private fun sendIntroOfferEvent(index: Int, introOffer: Deal, location: Location?) {
        with(viewModel) {
            viewModelScope.launch(Dispatchers.Default) {
                onTrackedComponentClicked(
                    SomethingNewItemClickEvent(
                        locationId = location?.siteLocationId,
                        studioId = introOffer.location?.masterLocationId,
                        itemIndex = index,
                        className = introOffer.name,
                        classId = introOffer.subscriberProductId,
                        classPrice = introOffer.price ?: 0,
                        inventorySource = location?.inventorySource
                    )
                )
            }

            ServiceLocator.abTestFramework.track(
                HOME_SCREEN_INTRO_OFFERS_CARD_TAPPED,
                ABHelperUtils.getUserAttributes()
            )
        }
    }

    private fun updateIntroOffersV2(introOffers: List<Deal>) {
        with(introOffersCarouselBindingV2) {
            viewModel.homeScreenHeadlineData.let {
                homeIntroOffersTitle.setTitle(it.introOffersTitle.asString(requireActivity()))
            }
            if (viewModel.homeSeeAllPlacement.isCarouselHeader) {
                homeIntroOffersTitle.also {
                    it.setSeeAllButtonText(viewModel.homeSeeAllPlacement.titleLabel)
                    it.setRightCaretVisibility(viewModel.homeSeeAllPlacement.isRightCaret)
                    it.setOnClickListener {
                        HomeEventTracker.trackHomeSeeAllEvent(
                                originComponent = OriginComponent.INTRO_OFFERS
                        )
                        viewModel.onTrackedComponentClicked(HomeSeeAllEvent.SomethingNew)
                        UserCheckoutFlowTracking.addUserFlowEvent(HomeIntroOffers)
                        ServiceLocator.abTestFramework.track(HOME_SCREEN_SEE_ALL_INTRO_OFFERS_TAPPED)
                        trackHomeCarouselEvent(
                            HOME_ANY_SEE_ALL_CLICKED,
                            HOME_SCREEN_ANY_SEE_ALL,
                            addSeeAllEventData(INTRO_OFFER)
                        )
                        UserCheckoutFlowTracking.addUserFlowEvent(HomeIntroOffersSeeAllTapped)
                        navigationViewModel.onIntroOfferButtonClicked(HOME_INTRO_OFFER_SEE_ALL_OFFER_TAPPED)
                    }
                }
            }
            refreshComposeIntroOfferAdapter(introOffers)
            root.visible = true
        }
    }

    private fun updateSimilarHistoryCarouselV2(similarHistoryClasses: List<ClassTypeObject>) {
        similarHistoryCarouselBindingV2?.apply {
            viewModel.homeScreenHeadlineData.let {
                homeSimilarHistoryTitle.setTitle(
                    viewModel.homeScreenHeadlineData.similarHistoryTitle.asString(requireActivity()))
            }
            if (viewModel.homeSeeAllPlacement.isCarouselHeader) {
                homeSimilarHistoryTitle.also {
                    it.setSeeAllButtonText(viewModel.homeSeeAllPlacement.titleLabel)
                    it.setRightCaretVisibility(viewModel.homeSeeAllPlacement.isRightCaret)
                    it.setOnClickListener {
                        SearchFlowTracker.searchOriginView = OriginView.HOME
                        SearchFlowTracker.searchOriginComponent = OriginComponent.SIMILAR_HISTORY
                        // Home carousels to search results navigation is not considered as a true search
                        SearchFlowTracker.trueSearchFlow = false
                        HomeEventTracker.trackHomeSeeAllEvent(
                                originComponent = OriginComponent.SIMILAR_HISTORY
                        )
                        viewModel.onTrackedComponentClicked(HomeSeeAllEvent.NewFavorite)
                        UserCheckoutFlowTracking.addUserFlowEvent(HomeSimilarHistory)
                        SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_SIMILAR_HISTORY)
                        val timeRange = HomeDomainModelFactory.create().getTodayTimeRange(
                            SIMILAR_SEE_ALL_START, SIMILAR_SEE_ALL_END
                        )
                        navigationViewModel.onSearchCategoryClicked(
                            classCategories = null,
                            startTime = timeRange.first,
                            endTime = timeRange.second,
                            isFlexOnly = false,
                            RecommenderType.SIMILAR_HISTORY
                        )
                        trackHomeCarouselEvent(
                            HOME_ANY_SEE_ALL_CLICKED,
                            HOME_SCREEN_ANY_SEE_ALL,
                            addSeeAllEventData(SIMILAR_HISTORY)
                        )
                        ServiceLocator.abTestFramework.track(
                            HOME_SCREEN_SEE_ALL_SIMILAR_HISTORY_TAPPED
                        )
                    }
                }
            }
            (homeSimilarHistoryRv.adapter as? HomeCarouselRecyclerAdapter)?.addSeeAllOptions(
                similarHistoryClasses, viewModel.homeSeeAllPlacement
            )
            homeSimilarHistoryRv.addOnScrollListener(
                logSwipeEventListener(
                    "(Home Screen) | Similar History Swiped",
                    getSimilarHistorySwipedData, HOME_SCREEN_SIMILAR_HISTORY_SWIPED
                )
            )
            root.visible = true
        }
    }

    private fun initLmoCarouselAdapter() {
        with(lmoCarouselBindingV2.homeLmoRv) {
            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
            adapter = HomeCarouselRecyclerAdapter(
                recommenderType = RecommenderType.LMO,
                seeAllClickListener = {
                    UserCheckoutFlowTracking.addUserFlowEvent(HomeLMO)
                    SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_LMO)
                    navigationViewModel.onDynamicPricingButtonClicked(RecommenderType.LMO)
                },
                itemClickListener = { classTypeObject, rank, recommenderType ->
                    UserCheckoutFlowTracking.addUserFlowEvent(HomeLMO)

                    viewModel.onTrackedComponentClicked(
                        with(classTypeObject) {
                            PriceDropClickEvent(
                                locationId = location.siteLocationId,
                                studioId = location.siteId,
                                itemIndex = rank,
                                classId = id,
                                className = name,
                                discountPercentage = lmoPercentDiscount,
                                discountPrice = dspoPrice,
                                inventorySource = classTypeObject.location.inventorySource
                            )
                        }
                    )

                    HomeEventTracker.trackHomeCardClickEvent(
                        originComponent = OriginComponent.LMO,
                        rank = rank + 1,
                        classTypeObject = classTypeObject,
                        location = classTypeObject.location
                    )

                    onHomeCarouselV2ItemClick(classTypeObject, rank, recommenderType, OriginComponent.LMO_CARD)
                }
            )
        }
    }

    private fun updateLmoCarouselV2(lastMinuteOffers: List<ClassTypeObject>) {
        with(lmoCarouselBindingV2) {
            viewModel.homeScreenHeadlineData.let {
                homeLmoTitle.setTitle(it.lmosTitle.asString(requireActivity()))
            }
            // need to sort the LMO list by Highest to Lowest discount
            val highToLowDiscountLmoList =
                lastMinuteOffers.sortedByDescending { it.lmoPercentDiscount }
            if (viewModel.homeSeeAllPlacement.isCarouselHeader) {
                homeLmoTitle.also {
                    it.setSeeAllButtonText(viewModel.homeSeeAllPlacement.titleLabel)
                    it.setRightCaretVisibility(viewModel.homeSeeAllPlacement.isRightCaret)
                    it.setOnClickListener {
                        SearchFlowTracker.searchOriginView = OriginView.HOME
                        SearchFlowTracker.searchOriginComponent = OriginComponent.LMO
                        // Home carousels to search results navigation is not considered as a true search
                        SearchFlowTracker.trueSearchFlow = false
                        HomeEventTracker.trackHomeSeeAllEvent(
                                originComponent = OriginComponent.LMO
                        )
                        viewModel.onTrackedComponentClicked(HomeSeeAllEvent.PriceDrop)
                        UserCheckoutFlowTracking.addUserFlowEvent(HomeLMO)
                        SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_LMO)
                        navigationViewModel.onDynamicPricingButtonClicked(RecommenderType.LMO)
                        trackHomeCarouselEvent(
                            HOME_ANY_SEE_ALL_CLICKED,
                            HOME_SCREEN_ANY_SEE_ALL,
                            addSeeAllEventData(LMO_DROP)
                        )
                        ServiceLocator.abTestFramework.track(HOME_SCREEN_SEE_ALL_LMO_TAPPED)
                    }
                }
            }
            (homeLmoRv.adapter as? HomeCarouselRecyclerAdapter)?.addSeeAllOptions(
                highToLowDiscountLmoList, viewModel.homeSeeAllPlacement
            )
            val lmoMetaData = mutableMapOf<String, Any>()
            lmoMetaData.putAll(getLmoCarouselViewData())
            homeLmoRv.addOnScrollListener(
                logSwipeEventListener(
                    "(Home Screen) | LMO Carousel Swiped",
                    { lmoMetaData }, HOME_SCREEN_LMO_SWIPED
                )
            )
            root.visible = true
        }
    }

    private fun initLmoSurgeCarouselAdapterV2(isLocationEnabled: Boolean) {
        when {
            isLocationEnabled -> {
                _lmoSurgeCarouselBindingV2 = ViewHomeLmoCarouselV2Binding
                    .inflate(LayoutInflater.from(context), binding.homeContent, true)
                with(lmoSurgeCarouselBindingV2.homeLmoRv) {
                    addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
                    adapter = HomeCarouselRecyclerAdapter(
                        recommenderType = RecommenderType.LMO,
                        lmoFilterType = LmoFilterType.SURGE,
                        seeAllClickListener = {
                            UserCheckoutFlowTracking.addUserFlowEvent(HomeLMO)
                            SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_LMO)
                            navigationViewModel.onDynamicPricingButtonClicked()
                        },
                        itemClickListener = { classTypeObject, rank, recommenderType ->
                            viewModel.onTrackedComponentClicked(
                                AlmostSoldOutItemClickEvent(
                                    locationId = classTypeObject.location.siteLocationId,
                                    studioId = classTypeObject.location.siteId,
                                    itemIndex = rank,
                                    className = classTypeObject.name,
                                    classId = classTypeObject.id,
                                    inventorySource = classTypeObject.location.inventorySource
                                )
                            )
                            HomeEventTracker.trackHomeCardClickEvent(
                                originComponent = OriginComponent.LMO_SURGE,
                                rank = rank + 1,
                                classTypeObject = classTypeObject,
                                location = classTypeObject.location
                            )
                            UserCheckoutFlowTracking.addUserFlowEvent(HomeLMO)
                            onHomeCarouselV2ItemClick(
                                classTypeObject,
                                rank,
                                recommenderType,
                                OriginComponent.LMO_SURGE_CARD,
                                LmoFilterType.SURGE
                            )
                        }
                    )
                }
            }
        }
    }

    private fun updateLmoSurgeCarouselV2(lastMinuteSurgeOffers: List<ClassTypeObject>) {
        with(lmoSurgeCarouselBindingV2) {
            homeLmoTitle.setTitle(getString(R.string.lmo_surge_title))
            if (viewModel.homeSeeAllPlacement.isCarouselHeader) {
                homeLmoTitle.also {
                    it.setSeeAllButtonText(viewModel.homeSeeAllPlacement.titleLabel)
                    it.setRightCaretVisibility(viewModel.homeSeeAllPlacement.isRightCaret)
                    it.setOnClickListener {
                        SearchFlowTracker.searchOriginView = OriginView.HOME
                        SearchFlowTracker.searchOriginComponent = OriginComponent.LMO_SURGE
                        // Home carousels to search results navigation is not considered as a true search
                        SearchFlowTracker.trueSearchFlow = false
                        HomeEventTracker.trackHomeSeeAllEvent(
                                originComponent = OriginComponent.LMO_SURGE
                        )
                        viewModel.onTrackedComponentClicked(HomeSeeAllEvent.AlmostSoldOut)
                        UserCheckoutFlowTracking.addUserFlowEvent(HomeLMO)
                        SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_LMO)
                        navigationViewModel.onDynamicPricingButtonClicked()
                        trackHomeCarouselEvent(
                            HOME_ANY_SEE_ALL_CLICKED,
                            HOME_SCREEN_ANY_SEE_ALL,
                            addSeeAllEventData(LMO_SURGE)
                        )
                    }
                }
            }
            (homeLmoRv.adapter as? HomeCarouselRecyclerAdapter)?.addSeeAllOptions(
                lastMinuteSurgeOffers, viewModel.homeSeeAllPlacement
            )
            homeLmoRv.addOnScrollListener(logSwipeEventListener("(Home Screen) | LMO Surge Carousel Swiped"))
            root.visible = true
        }
    }

    private fun onHomeCarouselV2ItemClick(
        classTypeObject: ClassTypeObject,
        rank: Int,
        recommenderType: RecommenderType?,
        originComponent: OriginComponent? = null,
        lmoFilterType: LmoFilterType = LmoFilterType.DROP_IN_PRICE
    ) {
        sendTapEvent(classTypeObject, rank, recommenderType, lmoFilterType)
        StaticInstance.selectedClassTypeObject = null
        val intent = Intent(context, ClassTypeDetailsActivity::class.java).apply {
            putExtra(KEY_BUNDLE_SITEID, classTypeObject.location?.siteId)
            putExtra(KEY_BUNDLE_CLASS_INSTANCE_ID, classTypeObject.id.toLong())
            putExtra(
                ClassTypeDetailsActivity.TYPE_EXTRA_STRING,
                ClassTypeDetailsActivity.Type.CLASS.ordinal
            )
            putExtra(Constants.KEY_BUNDLE_ORIGIN_VIEW, OriginView.HOME.viewName)
            originComponent?.let {
                putExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT, it.componentName)
            }
        }
        recommenderType?.let {
            SharedPreferencesUtils.setRecommenderTypeSelected(it.type)
            SharedPreferencesUtils.setRecommendedClassIdSelected(classTypeObject.id)
            SharedPreferencesUtils.setRecommendationSetIdSelected(classTypeObject.recommendationSetId)
        }

        startActivity(intent)
    }

    private fun savedBusinessCarousal() {
        _savedBusinessBinding = ViewHomeSavedCarousalBinding
            .inflate(LayoutInflater.from(context), binding.homeContent, true)
        initSavedBusinessAdapter()
    }

    private fun initSavedBusinessAdapter() {
        with(savedBusinessBinding.homeSavedBusinessRv) {
            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
            adapter = SavedBusinessRecyclerAdapter(
                longClickListener = { favoriteBiz ->
                    activity?.supportFragmentManager?.let { fragmentManager ->
                        BusinessOptionsDialog().apply {
                            setLocation(favoriteBiz)
                            setGoToFavoritesButtonVisibility(View.VISIBLE)
                            setRemoveFromFavoritesVisibility(View.GONE)
                            setShareButtonVisibility(View.GONE)
                            setGoToApptsButtonVisible(false)
                            setGoToClassesButtonVisible(false)
                            setGoToBusinessVisible(true)
                        }.show(fragmentManager, BusinessOptionsDialog.FRAGMENT_TAG)
                    }
                },
                seeAllClickListener = {
                    navigationViewModel.onFavoritesButtonClicked()
                    UserCheckoutFlowTracking.addUserFlowEvent(HomeFavorites)
                },
                onLocationClicked = { index, location ->
                    viewModel.onTrackedComponentClicked(
                        BookAFavoriteItemClickEvent(
                            studioId = location.siteId,
                            locationId = location.siteLocationId,
                            itemIndex = index,
                            inventorySource = location.inventorySource
                        )
                    )
                    HomeEventTracker.trackHomeCardClickEvent(
                        originComponent = OriginComponent.FAVORITE_BUSINESSES_STUDIO_CAROUSEL,
                        rank = index + 1,
                        location = location
                    )
                }
            )
        }
    }

    private fun updateSavedBusinesses(favorites: List<Location>) {
        with(savedBusinessBinding) {
            root.visible = true
            viewModel.homeScreenHeadlineData.let {
                homeSavedTitle.setTitle(it.favoritesTitle.asString(requireActivity()))
            }
            homeSavedBusinessRv.adapter?.let {
                if (viewModel.homeSeeAllPlacement.isCarouselHeader) {
                    homeSavedTitle.setSeeAllButtonText(viewModel.homeSeeAllPlacement.titleLabel)
                    homeSavedTitle.setRightCaretVisibility(viewModel.homeSeeAllPlacement.isRightCaret)
                    homeSavedTitle.setOnClickListener {
                        HomeEventTracker.trackHomeSeeAllEvent(
                                originComponent = OriginComponent.FAVORITE_BUSINESSES_STUDIO_CAROUSEL
                        )
                        UserCheckoutFlowTracking.addUserFlowEvent(HomeFavorites)
                        navigationViewModel.onFavoritesButtonClicked()
                        viewModel.onTrackedComponentClicked(HomeSeeAllEvent.BookFavorite)
                    }
                }
                (it as SavedBusinessRecyclerAdapter).addExploreAndSeeAllOptions(
                    favorites,
                    viewModel.homeSeeAllPlacement
                )
            }
            homeSavedBusinessRv.addOnScrollListener(
                logSwipeEventListener(
                    "(Home Screen) | Favorites Swiped",
                    optimizelyEventName = HOME_SCREEN_FAVORITES_SWIPED
                )
            )
        }
    }

    /**
     * Intro Offers carousel inflation and no location card inflation, also initializes adapter
     */
    private fun introOffers(isLocationEnabled: Boolean) {
        when {
            isLocationEnabled -> {
                _introOffersCarouselBindingV2 = ViewHomeIntroOffersCarouselV2Binding.inflate(
                    LayoutInflater.from(context),
                    binding.homeContent, true
                )
            }

            else -> {
                _introOffersCarouselBindingV2 = ViewHomeIntroOffersCarouselV2Binding.inflate(
                    LayoutInflater.from(context),
                    binding.homeNoLocation, true
                )

                bannerCtaBindings[BannerCtaBinding.INTRO] = ViewHomeBannerImageCtaBinding.inflate(
                    LayoutInflater.from(context),
                    binding.homeNoLocation,
                    true
                ).apply {
                    setBannerCtaTextAndImage(
                        this,
                        resources.getStringArray(R.array.home_intro_offer_banner_copy),
                        R.drawable.intro_offers_card
                    )
                    this.homeBannerCard.setOnClickListener {
                        UserCheckoutFlowTracking.addUserFlowEvent(HomeIntroOffers)
                        navigationViewModel.onIntroOfferButtonClicked(null)
                        ServiceLocator.abTestFramework.track(HOME_SCREEN_SEE_ALL_INTRO_OFFERS_TAPPED)
                        UserCheckoutFlowTracking.addUserFlowEvent(HomeIntroOffersSeeAllTapped)
                    }
                }
                viewModel.trackNoIntroOffersDisplayed()
            }
        }
    }

    /**
     * Upcoming Carousel V2 inflation, also initializes adapter
     */
    private fun upcomingCarouselV2() {
        _upcomingCarousalBinding = ViewHomeUpcomingCarousalV2Binding.inflate(
            LayoutInflater.from(context),
            binding.homeContent, true
        )
        initUpcomingCarousalV2Adapter()
    }

    /**
     * Recommended LMOs carousel and no location card inflation, also initializes adapter
     */
    private fun lmoCarousel(isLocationEnabled: Boolean) {
        when {
            isLocationEnabled -> {
                _lmoCarouselBindingV2 = ViewHomeLmoCarouselV2Binding.inflate(
                    LayoutInflater.from(context),
                    binding.homeContent, true
                )
                initLmoCarouselAdapter()
            }

            else -> {
                bannerCtaBindings[BannerCtaBinding.LMO] = ViewHomeBannerImageCtaBinding.inflate(
                    LayoutInflater.from(context),
                    binding.homeNoLocation,
                    true
                ).apply {
                    setBannerCtaTextAndImage(
                        this, resources.getStringArray(R.array.home_lmo_banner_copy),
                        R.drawable.last_minute_offer_card
                    )
                    this.homeBannerCard.setOnClickListener {
                        SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_LMO)
                        navigationViewModel.onDynamicPricingButtonClicked()
                    }
                }
            }
        }
    }

    private fun setBannerCtaTextAndImage(
        viewBinding: ViewHomeBannerImageCtaBinding,
        @Size(3) strings: Array<String>,
        @DrawableRes drawResource: Int
    ) {
        with(viewBinding) {
            bannerTopTitle.text = strings[0]
            bannerSubTitle.text = strings[1]
            imageForAda.contentDescription = strings[2]
            bannerMessage.text = strings[2]
            context?.let {
                homeBannerCard.radius = ViewUtils.dpToPx(8f, it)
                ContextCompat.getDrawable(it, drawResource)
            }?.let { drawable ->
                homeBannerImageViewgroup.background = drawable
            }
        }
    }

    /**
     * General recommender carousel and initializes adapter
     */
    private fun initSimilarHistoryCarousel() {
        _similarHistoryCarouselBindingV2 = ViewHomeSimilarHistoryCarouselV2Binding
            .inflate(LayoutInflater.from(context), binding.homeContent, true)
        similarHistoryCarouselBindingV2?.homeSimilarHistoryRv?.apply {
            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
            adapter = HomeCarouselRecyclerAdapter(
                recommenderType = RecommenderType.SIMILAR_HISTORY,
                seeAllClickListener = {
                    UserCheckoutFlowTracking.addUserFlowEvent(HomeSimilarHistory)
                    SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_SIMILAR_HISTORY)
                    val timeRange = HomeDomainModelFactory.create().getTodayTimeRange(
                        SIMILAR_SEE_ALL_START, SIMILAR_SEE_ALL_END
                    )
                    navigationViewModel.onSearchCategoryClicked(
                        classCategories = null,
                        startTime = timeRange.first,
                        endTime = timeRange.second,
                        isFlexOnly = false,
                        RecommenderType.SIMILAR_HISTORY
                    )
                    ServiceLocator.abTestFramework.track(
                        HOME_SCREEN_SEE_ALL_SIMILAR_HISTORY_TAPPED
                    )
                },
                itemClickListener = { classTypeObject, rank, recommenderType ->
                    UserCheckoutFlowTracking.addUserFlowEvent(HomeSimilarHistory)

                    viewModel.onTrackedComponentClicked(
                        with(classTypeObject) {
                            NewFavoriteClickEvent(
                                locationId = location.siteLocationId,
                                studioId = location.siteId,
                                itemIndex = rank,
                                className = name,
                                classId = id,
                                inventorySource = location.inventorySource
                            )
                        }
                    )

                    HomeEventTracker.trackHomeCardClickEvent(
                        originComponent = OriginComponent.SIMILAR_HISTORY,
                        rank = rank + 1,
                        classTypeObject = classTypeObject,
                        location = classTypeObject.location
                    )

                    onHomeCarouselV2ItemClick(classTypeObject, rank, recommenderType, OriginComponent.SIMILAR_HISTORY_CARD)
                }
            )
        }
    }

    /**
     * Flex Night Owl carousel inflation, also initializes adapters
     */
    private fun flexNightOwlsCarouselV2() {
        flexCarouselBindings[CarouselViewBinding.FLEX_NIGHTOWL] =
            ViewHomeFlexCarouselV2Binding.inflate(
                LayoutInflater.from(context),
                binding.homeContent, true
            ).apply {
                homeFlexTitle.text = context?.getString(R.string.title_flex_night_owls).orEmpty()
                initFlexNightOwlsCarouselAdapterV2(homeFlexRv)
            }
    }

    /**
     * Flex Early Birds carousel inflation, also initializes adapters
     */
    private fun flexEarlyBirdsCarouselV2() {
        flexCarouselBindings[CarouselViewBinding.FLEX_EARLY_BIRD] =
            ViewHomeFlexCarouselV2Binding.inflate(
                LayoutInflater.from(context),
                binding.homeContent, true
            ).apply {
                homeFlexTitle.text = context?.getString(R.string.title_flex_early_birds).orEmpty()
                initFlexEarlyBirdsCarouselAdapterV2(homeFlexRv)
            }
    }

    /**
     * Flex Starting Soon carousel inflation, also initializes adapters
     */
    private fun flexStartingSoonCarouselV2() {
        flexCarouselBindings[CarouselViewBinding.FLEX_SOON] =
            ViewHomeFlexCarouselV2Binding.inflate(
                LayoutInflater.from(context),
                binding.homeContent, true
            ).apply {
                homeFlexTitle.text = context?.getString(R.string.title_flex_starting_soon).orEmpty()
                initFlexStartingSoonCarouselAdapterV2(homeFlexRv)
            }
    }

    /**
     * Updates adapters shared by common HomeCarouselAdapter and sets the corresponding carousel visibility
     */
    private fun updateFlexHomeCarouselAdapter(
        updatedClasses: List<ClassTypeObject>,
        bindingType: CarouselViewBinding
    ) {
        with(fetchFlexCarouselBinding(bindingType)) {
            (homeFlexRv.adapter as? HomeCarouselRecyclerAdapter)?.addSeeAllOptions(updatedClasses)
            root.visible = true
        }
    }

    private fun initFlexStartingSoonCarouselAdapterV2(flexRecyclerView: RecyclerView) {
        with(flexRecyclerView) {
            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
            adapter = HomeCarouselRecyclerAdapter(seeAllClickListener = {
                UserCheckoutFlowTracking.addUserFlowEvent(HomeFlex)
                AnalyticsUtils.logEvent("(Home Screen) | Flex Starting Soon See All Tapped")
            }, itemClickListener = { classTypeObject, rank, recommenderType ->
                UserCheckoutFlowTracking.addUserFlowEvent(HomeFlex)
                onHomeCarouselV2ItemClick(classTypeObject, rank, recommenderType)
            })
        }
    }

    private fun initFlexEarlyBirdsCarouselAdapterV2(flexRecyclerView: RecyclerView) {
        with(flexRecyclerView) {
            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
            adapter = HomeCarouselRecyclerAdapter(seeAllClickListener = {
                UserCheckoutFlowTracking.addUserFlowEvent(HomeFlex)
                val flexClasses = (adapter as HomeCarouselRecyclerAdapter).classList
                AnalyticsUtils.logEvent("(Home Screen) | Flex Early Birds See All Tapped")
                HomeDomainModelFactory.create().getFlexSearchTimes(
                    // In case user is on home page awhile, ensures first start date is not in the past
                    startDate = flexClasses.firstOrNull {
                        it.classTypeObject?.startDate?.after(
                            getCalendarInstanceForSearch().time
                        ) == true
                    }?.classTypeObject?.startDate?.toCalendarInGlobalTimeZone(),
                    beginHour = EARLY_BIRD_START_HOUR,
                    endHour = EARLY_BIRD_END_HOUR
                )?.let {}
            }, itemClickListener = { classTypeObject, rank, recommenderType ->
                UserCheckoutFlowTracking.addUserFlowEvent(HomeFlex)
                onHomeCarouselV2ItemClick(classTypeObject, rank, recommenderType)
            })
        }
    }

    private fun initFlexNightOwlsCarouselAdapterV2(flexRecyclerView: RecyclerView) {
        with(flexRecyclerView) {
            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
            adapter = HomeCarouselRecyclerAdapter(seeAllClickListener = {
                UserCheckoutFlowTracking.addUserFlowEvent(HomeFlex)
                val flexClasses = (adapter as HomeCarouselRecyclerAdapter).classList
                AnalyticsUtils.logEvent("(Home Screen) | Flex Night Owls See All Tapped")
                HomeDomainModelFactory.create().getFlexSearchTimes(
                    // In case user is on home page awhile, ensures first start date is not in the past
                    startDate = flexClasses.firstOrNull {
                        it.classTypeObject?.startDate?.after(
                            getCalendarInstanceForSearch().time
                        ) == true
                    }?.classTypeObject?.startDate?.toCalendarInGlobalTimeZone(),
                    beginHour = NIGHT_OWL_START_HOUR,
                    endHour = NIGHT_OWL_END_HOUR
                )?.let {}
            }, itemClickListener = { classTypeObject, rank, recommenderType ->
                UserCheckoutFlowTracking.addUserFlowEvent(HomeFlex)
                onHomeCarouselV2ItemClick(classTypeObject, rank, recommenderType)
            })
        }
    }

    /**
     * Categories carousel inflation, also initializes adapters
     */
    private fun categories(isLocationEnabled: Boolean, isFlex: Boolean) {

        _categoryBinding = ViewHomeFitnessCategoriesBinding
            .inflate(
                LayoutInflater.from(context),
                if (isLocationEnabled) binding.homeContent else binding.homeNoLocation, true
            )
        categoryBinding.root.setBackgroundColor(
            ContextCompat.getColor(
                requireContext(),
                R.color.white
            )
        )
        categoryBinding.homeFitnessCategoryRv.setPaddingHorizontal(30)
        categoryBinding.fitnessCategoryTitle.setTextAppearance(R.style.HomeCarouselTitle)
        if (isFlex) categoryBinding.fitnessCategoryTitle.text =
            context?.getString(R.string.flex_category_title)
        else categoryBinding.fitnessCategoryTitle.text =
            viewModel.homeScreenHeadlineData.exploreCategoriesTitle.asString(requireActivity())
        initCategoriesAdapter(isFlex, categoryBinding)
    }

    /**
     * Intro Offers carousel inflation and no location card inflation, also initializes adapter
     */
    private fun spasCarousel(isLocationEnabled: Boolean) {
        when {
            isLocationEnabled -> {
                _spaCarouselBinding = SpasCarouselBinding.inflate(
                    LayoutInflater.from(context),
                    binding.homeContent, true
                )
                initSpasCarouselAdapter()
            }
        }
    }

    private fun initUpcomingCarousalV2Adapter() {
        val viewPager = upcomingCarousalBinding.homeUpcomingViewPager.apply {
            val width = resources.displayMetrics.widthPixels - 2 * UPCOMING_V2_PAGER_PADDING
            val params = LayoutParams(width, ViewGroup.LayoutParams.MATCH_PARENT)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            adapter = UpcomingPagerAdapterV2(
                seeYourScheduleClickListener = {
                    UserCheckoutFlowTracking.addUserFlowEvent(HomeUpcoming)
                    navigationViewModel.onMyScheduleButtonClicked()
                },
                onUpcomingVisitClicked = { position, visit ->
                    viewModel.onTrackedComponentClicked(
                        UpcomingItemClickEvent(
                            locationId = visit.Location?.SiteLocationId,
                            studioId = visit.Location?.MasterLocationId,
                            itemIndex = position,
                            className = visit.Name,
                            classId = visit.ClassVisitDetails?.ClassId,
                            inventorySource = visit.InventorySource
                        )
                    )
                    HomeEventTracker.trackHomeCardClickEvent(
                        originComponent = OriginComponent.UPCOMING_CLASSES,
                        rank = position + 1,
                        visit = visit,
                        locationSmall = visit.Location,
                    )
                }
            )
            clipChildren = false

            addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrollStateChanged(state: Int) {}
                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) {
                   // No-op
                }

                override fun onPageSelected(position: Int) {
                    AnalyticsUtils.logEvent("(Home Screen) | Upcoming Class Carousel Swiped")
                }
            })
        }
        upcomingCarousalBinding.indicator.setViewPager(viewPager)
    }

    /**
     * Discovery Carousel V2 inflation, also initializes adapter
     */
    private fun discoveryCarousel(isLocationEnabled: Boolean) {
        val homeLayout = if (isLocationEnabled) binding.homeContent else binding.homeNoLocation
        _discoveryCarouselBinding = ViewHomeDiscoveryCarouselBinding.inflate(
            LayoutInflater.from(context),
            homeLayout, true
        )
        initDiscoveryCarouselAdapter()
    }

    private fun initDiscoveryCarouselAdapter() {
        val discoveryViewPager = discoveryCarouselBinding.homeDiscoveryViewPager.apply {
            val params = LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            pageMargin = DISCOVERY_PAGE_MARGIN
            adapter = DiscoveryPagerAdapter(
                onItemSelectedListener = { discoveryItem -> onDiscoveryItemTap(discoveryItem) }
            )

            addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) =
                    Unit

                override fun onPageSelected(position: Int) {
                    //Pendo and Optimizely swipe event tracking
                    trackHomeCarouselEvent(
                        "(Home Screen) | Discovery Carousel Swiped",
                        HOME_SCREEN_DISCOVERY_CAROUSEL_SWIPED
                    )
                    //Optimizely display event tracking
                    var abEventName = ""
                    when (position) {
                        DEALS_ITEM_TYPE -> abEventName = HOME_SCREEN_DEALS_DISCOVERY_DISPLAYED
                        FITNESS_ITEM_TYPE -> abEventName = HOME_SCREEN_FITNESS_DISCOVERY_DISPLAYED
                        WELLNESS_ITEM_TYPE -> abEventName = HOME_SCREEN_WELLNESS_DISCOVERY_DISPLAYED
                        BEAUTY_ITEM_TYPE -> abEventName = HOME_SCREEN_BEAUTY_DISCOVERY_DISPLAYED
                    }
                    ServiceLocator.abTestFramework.track(abEventName)
                }

                override fun onPageScrollStateChanged(state: Int) = Unit
            })
        }
        context?.let {
            (discoveryViewPager.adapter as DiscoveryPagerAdapter).prepareDiscoveryCarouselList(
                it
            )
        }
        discoveryCarouselBinding.discoveryIndicator.setViewPager(discoveryViewPager)
        discoveryCarouselBinding.root.visible = true
    }

    private fun onDiscoveryItemTap(discoveryItem: Int) {
        SearchFlowTracker.searchOriginView = OriginView.HOME
        // Home carousels to search results navigation is not considered as a true search
        SearchFlowTracker.trueSearchFlow = false
        viewModel.onTrackedComponentClicked(DealsItemClickEvent(discoveryItem))
        SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_DISCOVERY)
        trackHomeCarouselEvent(
            HOME_SCREEN_DISCOVERY_CAROUSEL_TAPPED_PENDO,
            HOME_SCREEN_DISCOVERY_CAROUSEL_TAPPED
        )
        // Initializing to HomeTab bcs it is the previous user flow event, even though this will never be used
        var userFlowEvent: UserCheckoutFlowTracking.UserFlowEvent = HomeTab
        var pendoEventName = ""
        var abEventName = ""

        // Clearing previous filters on navigation
        clearAllFilters()

        // set default sorting option to be with Distance
        SharedPreferencesUtils.setLastSelectedSortingOption(SORTING_OPTION_DISTANCE)

        when (discoveryItem) {
            DEALS_ITEM_TYPE -> {
                HomeEventTracker.trackHomeCardClickEvent(
                    originComponent = OriginComponent.DISCOVERY_DEALS,
                    rank = discoveryItem + 1,
                )
                SearchFlowTracker.searchOriginComponent = OriginComponent.DISCOVERY_DEALS
                userFlowEvent = DealsDiscovery
                pendoEventName = HOME_SCREEN_DEALS_DISCOVERY_TAPPED_PENDO
                abEventName = HOME_SCREEN_DEALS_DISCOVERY_TAPPED
                navigationViewModel.onDealsNearYouClicked()
            }

            FITNESS_ITEM_TYPE -> {
                HomeEventTracker.trackHomeCardClickEvent(
                    originComponent = OriginComponent.DISCOVERY_FITNESS,
                    rank = discoveryItem + 1,
                )
                SearchFlowTracker.searchOriginComponent = OriginComponent.DISCOVERY_FITNESS
                userFlowEvent = FitnessDiscovery
                pendoEventName = HOME_SCREEN_FITNESS_DISCOVERY_TAPPED_PENDO
                abEventName = HOME_SCREEN_FITNESS_DISCOVERY_TAPPED
                viewModel.onFitnessButtonClicked()
                navigationViewModel.onFitnessButtonClicked()
            }

            WELLNESS_ITEM_TYPE -> {
                HomeEventTracker.trackHomeCardClickEvent(
                    originComponent = OriginComponent.DISCOVERY_WELLNESS,
                    rank = discoveryItem + 1,
                )
                SearchFlowTracker.searchOriginComponent = OriginComponent.DISCOVERY_WELLNESS
                userFlowEvent = WellnessDiscovery
                pendoEventName = HOME_SCREEN_WELLNESS_DISCOVERY_TAPPED_PENDO
                abEventName = HOME_SCREEN_WELLNESS_DISCOVERY_TAPPED
                navigationViewModel.onWellnessButtonClicked()
            }

            BEAUTY_ITEM_TYPE -> {
                HomeEventTracker.trackHomeCardClickEvent(
                    originComponent = OriginComponent.DISCOVERY_BEAUTY,
                    rank = discoveryItem + 1,
                )
                SearchFlowTracker.searchOriginComponent = OriginComponent.DISCOVERY_BEAUTY
                userFlowEvent = BeautyDiscovery
                pendoEventName = HOME_SCREEN_BEAUTY_DISCOVERY_TAPPED_PENDO
                abEventName = HOME_SCREEN_BEAUTY_DISCOVERY_TAPPED
                navigationViewModel.onBeautyButtonClicked()
            }
        }
        UserCheckoutFlowTracking.addUserFlowEvent(userFlowEvent)
        // TODO: Is this deprecated by the new eventing?
        trackHomeCarouselEvent(pendoEventName, abEventName)
    }

    private fun initSpasCarouselAdapter() {
        with(spaCarouselBinding.homeSpasViewPager) {
            val width = resources.displayMetrics.widthPixels - 2 * UPCOMING_V2_PAGER_PADDING
            val params = LayoutParams(width, ViewGroup.LayoutParams.MATCH_PARENT)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            adapter = SpasCarouselAdapter(
                seeAllClickListener = {
                    UserCheckoutFlowTracking.addUserFlowEvent(HomeSpa)
                    SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_SPA)
                    val intent = Intent().apply {
                        putExtra(ExploreFitnessFragment.KEY_BUNDLE_VERTICAL_NAME, BEAUTY_VERTICAL)
                        putExtra(KEY_VERTICAL_ID, BEAUTY_VERTICAL_ID)
                        putExtra(KEY_BUNDLE_SEARCHTERM, "Spa")
                    }
                    navigationViewModel.onSearchTriggered(intent, getGlobalLocationText())
                },
                onLocationClicked = { index, location ->
                    viewModel.onTrackedComponentClicked(
                        TreatYourselfItemClickEvent(
                            locationId = location.siteLocationId,
                            studioId = location.siteId,
                            itemIndex = index,
                            inventorySource = location.inventorySource
                        )
                    )
                    HomeEventTracker.trackHomeCardClickEvent(
                        originComponent = OriginComponent.SPAS,
                        rank = index + 1,
                        location = location
                    )
                }
            )
            clipChildren = false
        }
    }

    /**
     * Return actual global location text only when it is not a current location or a no location.
     * Otherwise return empty. When empty the search implementation is to use current location,  if enabled.
     */
    private fun getGlobalLocationText() =
        binding.globalLocationIndicator.currentLocation.text.toString().takeIf {
            (it != resources.getString(R.string.global_location_indicator_nearby_text)) &&
                    (it != resources.getString(R.string.global_location_indicator_no_location_text))
        } ?: run {
            StringValue.Empty.asString(requireActivity())
        }

    private fun updateSpasCarousel(spasList: List<Location>) {
        with(spaCarouselBinding) {
            if (viewModel.homeSeeAllPlacement.isCarouselHeader) {
                spasCarouselTitle.also {
                    it.setSeeAllButtonText(viewModel.homeSeeAllPlacement.titleLabel)
                    it.setRightCaretVisibility(viewModel.homeSeeAllPlacement.isRightCaret)
                    it.setOnClickListener {
                        SearchFlowTracker.searchOriginView = OriginView.HOME
                        SearchFlowTracker.searchOriginComponent = OriginComponent.SPAS
                        // Home carousels to search results navigation is not considered as a true search
                        SearchFlowTracker.trueSearchFlow = false
                        HomeEventTracker.trackHomeSeeAllEvent(
                                originComponent = OriginComponent.SPAS
                        )
                        viewModel.onTrackedComponentClicked(HomeSeeAllEvent.TreatYourself)
                        UserCheckoutFlowTracking.addUserFlowEvent(HomeSpa)
                        SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_SPA)
                        trackHomeCarouselEvent(
                            HOME_ANY_SEE_ALL_CLICKED,
                            HOME_SCREEN_ANY_SEE_ALL,
                            addSeeAllEventData(SPA)
                        )
                        val intent = Intent().apply {
                            putExtra(
                                ExploreFitnessFragment.KEY_BUNDLE_VERTICAL_NAME,
                                BEAUTY_VERTICAL
                            )
                            putExtra(KEY_VERTICAL_ID, BEAUTY_VERTICAL_ID)
                            putExtra(KEY_BUNDLE_SEARCHTERM, "Spa")
                        }
                        navigationViewModel.onSearchTriggered(intent, getGlobalLocationText())
                    }
                }
            }
            (homeSpasViewPager.adapter as? SpasCarouselAdapter)?.addSeeAllOptions(
                spasList,
                viewModel.homeSeeAllPlacement
            )
            root.visible = true
        }
    }

    private fun initCategoriesAdapter(
        isFlex: Boolean,
        categoryBinding: ViewHomeFitnessCategoriesBinding
    ) {
        categoryBinding.homeFitnessCategoryRv.adapter =
            FitnessCategoryRecyclerAdapterV2().apply {
                onCategoryClicked = { index, category ->
                    SearchFlowTracker.searchOriginView = OriginView.HOME
                    SearchFlowTracker.searchOriginComponent = OriginComponent.CATEGORY_GROUP
                    // Home carousels to search results navigation is not considered as a true search
                    SearchFlowTracker.trueSearchFlow = false
                    HomeEventTracker.trackHomeCardClickEvent(
                        originComponent = when (category) {
                            CategoriesGroup.STRENGTH -> OriginComponent.STRENGTH_CATEGORY_GROUP
                            CategoriesGroup.FLEXIBILITY -> OriginComponent.FLEXIBILITY_CATEGORY_GROUP
                            CategoriesGroup.RELAXATION -> OriginComponent.RELAXATION_CATEGORY_GROUP
                            CategoriesGroup.CARDIO -> OriginComponent.CARDIO_CATEGORY_GROUP
                            CategoriesGroup.OUTDOOR -> OriginComponent.OUTDOOR_CATEGORY_GROUP
                            CategoriesGroup.COMPETITION -> OriginComponent.COMPETITION_CATEGORY_GROUP
                            else -> null
                        },
                        rank = index + 1
                    )
                    UserCheckoutFlowTracking.addUserFlowEvent(HomeCategories)
                    SharedPreferencesUtils.setSearchSource(SOURCE_APP_HOME_ACTIVITY)
                    if (category == CategoriesGroup.STRENGTH) {
                        category.categories?.let {
                            Collections.replaceAll(
                                it,
                                "Crossfit",
                                getString(R.string.fitness_subvertical_crossfit)
                            )
                        }
                    }
                    navigationViewModel.onSearchCategoryClicked(
                        classCategories = category.concatenateCategories(),
                        isFlexOnly = isFlex
                    )
                    viewModel.onTrackedComponentClicked(
                        ExploreCategoryClickEvent(index, category.name)
                    )
                }
            }
        categoryBinding.homeFitnessCategoryRv.addOnScrollListener(logSwipeEventListener("(Home Screen) | Category Carousel Swiped"))
    }

    private val similarHistoryData: Map<String, Any> by lazy {
        val additionalData = mutableMapOf<String, Any>()
        additionalData["isGuest"] = MBAuth.isGuestUser()
        additionalData["pageDisplayed"] = "Home Screen"
        viewModel.recommendedClasses.value?.let {
            if (it.isNotEmpty()) additionalData["recommendation_set_id"] =
                it.first().recommendationSetId ?: ""
        }
        additionalData
    }

    private val getSimilarHistorySwipedData = {
        similarHistoryData
    }

    private fun getLmoCarouselViewData() = mutableMapOf<String, Any>().apply {
        this["isGuest"] = MBAuth.isGuestUser()
        viewModel.lastMinuteOffers.value?.let {
            if (it.isNotEmpty()) this["recommendation_set_id"] =
                it.first().recommendationSetId ?: ""
        }

        this["Page"] = "Home Screen"
        this["Platform"] = "connect-android"
        this["Source"] = "lmo"
        this["carousel_id"] = "Trending deals near you"
    }

    private fun logSwipeEventListener(
        eventName: String,
        fetchAdditionalData: (() -> Map<String, Any>)? = null,
        optimizelyEventName: String? = null
    ): RecyclerView.OnScrollListener =
        object : RecyclerView.OnScrollListener() {
            val SCROLL_THROTTLE_MS = TimeUnit.SECONDS.toMillis(2)
            var lastTimeScrolled = System.currentTimeMillis()
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (dy == 0 && dx != 0) {
                    lastTimeScrolled = System.currentTimeMillis()
                    recyclerView.postDelayed({
                        if (System.currentTimeMillis() - lastTimeScrolled >= SCROLL_THROTTLE_MS) {
                            //Home screen headline experiment Optimizely event tracking
                            ServiceLocator.abTestFramework.apply {
                                optimizelyEventName?.let {
                                    track(it, ABHelperUtils.getUserAttributes())
                                }
                            }
                        }
                    }, SCROLL_THROTTLE_MS)
                }
            }
        }

    /**
     * Request for notification permission if not granted
     * If permission is granted, then call the viewModel method to handle the permission
     * If permission is denied, then show the dialog to ask for permission
     */
    private fun requestPushNotificationPermission() {
        if (SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.POST_NOTIFICATIONS)
                    == PackageManager.PERMISSION_GRANTED) {
                viewModel.onPushNotificationPermissionAccepted()
            } else {
                showGcmConsentDialogs()
            }
        } else {
            // Below Android 13 You don't need to ask for notification permission.
            viewModel.onPushNotificationPermissionAccepted()
        }
    }

    /**
     * Request for notification permission if not granted
     * Register for the result of the permission request and handle the result
     */
    private val pushNotificationPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestPermission(),
    ) { isGranted: Boolean ->
        if (isGranted) {
            // Permission Granted
            viewModel.onPushNotificationPermissionAccepted()
        } else {
            // Permission Denied
            if (shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)) {
                showGcmConsentDialogs()
            } else {
                viewModel.onPushNotificationPermissionDenied()
            }
        }
    }
    private fun showGcmConsentDialogs() {
        val dialog = MaterialOptionDialog()
        dialog.setHorizontalButtonStyle(true)
            .setMessageText(resources.getString(R.string.gcm_consent_message))
            .setHeaderText(getString(R.string.gcm_consent_title))
            .setPositiveButton(getString(R.string.gcm_button_ok)) { result ->
                if (SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    pushNotificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                } else {
                    viewModel.onPushNotificationPermissionAccepted()
                }
                result?.dismiss()
            }
            .setNegativeButton(getString(R.string.gcm_button_not_now)) { result ->
                viewModel.onPushNotificationPermissionDenied()
                result?.dismiss()
            }
        dialog.isCancelable = false
        dialog.show(childFragmentManager, PUSH_CONSENT_DIALOG_TAG)
    }

    private fun fetchFlexCarouselBinding(bindingToRetrieve: CarouselViewBinding):
            ViewHomeFlexCarouselV2Binding = flexCarouselBindings[bindingToRetrieve]!!

    enum class CarouselViewBinding { FLEX_SOON, FLEX_EARLY_BIRD, FLEX_NIGHTOWL, LMO, FIND_SIMILAR }
    enum class BannerCtaBinding { LMO, VIRTUAL, INTRO }

    /**
     * Near You V2 inflation, also initializes map
     */
    private fun nearYouMapView(isLocationEnabled: Boolean) {
        val homeBinding = if (isLocationEnabled) binding.homeContent else binding.homeNoLocation
        _nearYouMapBinding = ViewHomeNearYouItemBinding.inflate(
            LayoutInflater.from(context),
            homeBinding, true
        ).apply {
            toggleMapViewVisibility()
            viewMapBtn.setOnClickListener {
                HomeEventTracker.trackHomeCardClickEvent(
                        originComponent = OriginComponent.NEAR_YOU_MAP
                )
                SearchFlowTracker.searchOriginView = OriginView.HOME
                SearchFlowTracker.searchOriginComponent = OriginComponent.NEAR_YOU_MAP
                viewModel.onTrackedComponentClicked(HomeSeeAllEvent.NearYou)
                when ((it as TextView).text.toString()) {
                    getString(R.string.view_map_text) -> {
                        launchSearchMapActivity()
                    }

                    getString(R.string.enable_location) -> {
                        viewModel.trackOpenLocationSettings(EVENT_ATTRIBUTE_VALUE_HOME)
                        context?.startActivity(Intent(Settings.ACTION_SETTINGS))
                    }
                }
                UserCheckoutFlowTracking.addUserFlowEvent(HomeNearYou)
            }
        }

        mapFragment = null
        initializeMap()
    }

    /**
     * This function toggles visibility of actual Map and Static Image of Map depending on
     * Location access OR Last search query availability
     */
    private fun ViewHomeNearYouItemBinding.toggleMapViewVisibility() {
        var nearYouEvent = ""
        when (requireContext().isLocationAvailable()) {
            true -> {
                noLocationImage.visible = false
                nearYouMapFragment.visible = true
                viewMapBtn.text = getString(R.string.view_map_text)
            }

            false -> {
                noLocationImage.visible = true
                nearYouMapFragment.visible = false
                viewMapBtn.text = getString(R.string.enable_location)
                nearYouEvent = "(Home Screen) | Near You Carousel Location Disabled"
            }
        }
        nearYouEvent.takeIf { it.isNotEmpty() }?.let { AnalyticsUtils.logEvent(it) }
    }

    /**
     * Initializes the MapFragment
     */
    private fun initializeMap() {
        mapFragment = MBMapFragment()
        mapFragment?.let {
            childFragmentManager.beginTransaction()
                .replace(R.id.near_you_map_fragment, it)
                .commit()
            val updatedLocation = SharedPreferencesUtils.getLastHomeScreenMapLocation()
            it.setInitialSearchArea(MbLatLng(updatedLocation.latitude, updatedLocation.longitude))
            it.getMbMapAsync { map: MbMap? ->
                it.clearMarkers()
                it.onMbMapReady(map)
                it.setMapDraggable(false)
                //Markers are not clickable
                it.setMbMapOnMarkerClickListener(false)
                //Map is not enabled for click or any UI gestures
                it.setMapZoomable(false)
            }
        }
    }

    /**
     * Adds markers for available businesses on the Map
     */
    private fun addMarkersForBusinessLocations(nearYouLocations: List<Location>) {
        nearYouLocations.take(MAX_MARKERS_NEAR_YOU).map {
            mapFragment?.addBusinessMarker(it)
        }
        //We want to zoom the map to 0.5 miles
        mapFragment?.zoomMapToRadius(NEAR_YOU_MAP_ZOOM_LEVEL)
    }

    /**
     * Launches Search Location Map activity
     */
    private fun launchSearchMapActivity() {
        //Launch the business map search
        context?.let {
            val mapLocation = SharedPreferencesUtils.getLastHomeScreenMapLocation()
            val searchIntent = Intent(it, SearchLocationMapActivity::class.java).apply {
                putExtra(KEY_BUNDLE_SEARCH_STATE, AbstractSearchActivity.SearchState.CURRENT_MAP)
                putExtra(KEY_BUNDLE_LAT_LNG, LatLng(mapLocation.latitude, mapLocation.longitude))
            }
            startActivity(searchIntent)
        }
    }

    /**
     * This function takes care of re-ordering of carousels as per User type and sequence provided
     */
    private fun reorderCarousels(
        carouselType: String,
        isLocationEnabled: Boolean,
        isFlex: Boolean
    ) {
        when (carouselType) {
            DISCOVERY -> discoveryCarousel(isLocationEnabled)
            INTRO_OFFER -> introOffers(isLocationEnabled)
            NEAR_YOU -> nearYouMapView(isLocationEnabled)
            LMO_DROP, FLEX_LMO -> lmoCarousel(isLocationEnabled)
            SPA -> spasCarousel(isLocationEnabled)
            SAVED -> savedBusinessCarousal()
            UPCOMING -> upcomingCarouselV2()
            SIMILAR_HISTORY -> initSimilarHistoryCarousel()
            LMO_SURGE -> initLmoSurgeCarouselAdapterV2(isLocationEnabled)
            CATEGORY -> categories(isLocationEnabled, isFlex)
            FLEX_STARTING_SOON -> flexStartingSoonCarouselV2()
            FLEX_EARLY_BIRD -> flexEarlyBirdsCarouselV2()
            FLEX_NIGHT_OWLS -> flexNightOwlsCarouselV2()
        }
    }

    private var locationSearchResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            result.takeIf {
                it.resultCode == Activity.RESULT_OK && it.data?.hasExtra(
                    KEY_BUNDLE_LOCATION_SEARCHTERM
                ) == true
            }?.let {
                it.data?.let { data ->
                    binding.globalLocationIndicator.currentLocation.text =
                        data.getStringExtra(KEY_BUNDLE_LOCATION_SEARCHTERM)
                            ?.let { name -> transformGlobalLocationIndicatorText(name) }

                    val lat = data.getDoubleExtra(KEY_BUNDLE_LATITUDE, 0.0)
                    val long = data.getDoubleExtra(KEY_BUNDLE_LONGITUDE, 0.0)

                    viewModel.saveLastSearchedLocation(lat, long)
                    viewModel.updateGlobalIndicatorState()
                    viewModel.saveTimeZoneIdForSelectedGlobalLocation(lat, long)
                }
            }
        }
}
