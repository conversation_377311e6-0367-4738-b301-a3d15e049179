package com.mindbodyonline.connect.fragments

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.app.ActivityOptionsCompat
import androidx.core.view.ViewCompat
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.PagerAdapter
import com.google.android.gms.common.ConnectionResult
import com.google.android.material.appbar.AppBarLayout
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.android.util.TaskCallback
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.activities.details.BusinessDetailsActivity
import com.mindbodyonline.connect.activities.details.ClassTypeDetailsActivity
import com.mindbodyonline.connect.activities.details.DealDetailsActivity
import com.mindbodyonline.connect.activities.list.services.RoutineServicesActivity
import com.mindbodyonline.connect.activities.schedule.BusinessScheduleActivity
import com.mindbodyonline.connect.activities.workflow.SearchQueryEntryActivity
import com.mindbodyonline.connect.adapters.FitnessCategoryRecyclerAdapter
import com.mindbodyonline.connect.analytics.OriginComponent
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.classes.ClassCategory
import com.mindbodyonline.connect.common.Result
import com.mindbodyonline.connect.common.utilities.SearchUtil
import com.mindbodyonline.connect.common.utilities.setOptionalPadding
import com.mindbodyonline.connect.common.utilities.toCalendar
import com.mindbodyonline.connect.common.utilities.visible
import com.mindbodyonline.connect.common.viewmodeladapters.IntroOfferCardViewModelAdapter
import com.mindbodyonline.connect.databinding.*
import com.mindbodyonline.connect.fragments.custom.LocationAwareMainFragment
import com.mindbodyonline.connect.home.HomeViewModel
import com.mindbodyonline.connect.home.HomeViewModel.IntroOfferErrorHideForThisUser
import com.mindbodyonline.connect.home.LmoClassHolder
import com.mindbodyonline.connect.utils.*
import com.mindbodyonline.connect.utils.api.dynamicpricing.DynamicPricingApi
import com.mindbodyonline.connect.utils.api.toLocationReference
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking
import com.mindbodyonline.data.StaticInstance
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.Location
import com.mindbodyonline.domain.dataModels.SubscriptionLevel
import com.mindbodyonline.framework.abvariant.FeatureFlag
import com.mindbodyonline.views.dialog.BusinessOptionsDialog
import com.mindbodyonline.views.dialog.MaterialOptionDialog
import com.mindbodyonline.views.dialog.sanitizeFilterClassCategory
import com.squareup.picasso.Picasso
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.math.absoluteValue

//TODO Make sure you go through strings in this class when we delete this class and remove any no longer needed.
class HomeFragmentV2 : LocationAwareMainFragment() {

    companion object {
        const val LAUNCH_SEARCH_ENTRY_REQUESTCODE = 983
        const val PUSH_CONSENT_DIALOG_TAG = "PUSH_CONSENT_DIALOG_TAG"

        // Designs have 12dp, but we also have 2dp padding for elevation
        val PAGER_PADDING = ViewUtils.dpToPx(10, ConnectApp.getInstance())
        const val MAX_FAV_LOCATIONS_SHOWN = 5

        const val EARLY_BIRD_START_HOUR = 5
        const val EARLY_BIRD_END_HOUR = 10
        const val NIGHT_OWL_START_HOUR = 19
        const val NIGHT_OWL_END_HOUR = 22
    }

    private val viewModel: HomeViewModel by viewModels()
    private var lastTopInset = 0
    private var dynamicPricingSupported: Boolean = false
        set(value) {
            field = value
            SharedPreferencesUtils.setDynamicPricingAvailable(value)
            if (!value) {
                SharedPreferencesUtils.setDynamicPricingFilterEnabled(false)
            }
        }

    private var _homeFragmentBinding: FragmentHomeV2Binding? = null
    private val homeFragmentBinding get() = _homeFragmentBinding!!
    private var _virtualClassSectionBinding: ViewHomeVirtualClassesSectionBinding? = null
    private val virtualClassesSectionBinding get() = _virtualClassSectionBinding!!
    private var _introOffersBinding: ViewHomeIntroOffersCarouselBinding? = null
    private val introOffersBinding get() = _introOffersBinding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewModel.getFavorites().observe(this, Observer { favorites ->
            with(homeFragmentBinding) {
                if (favorites?.isNotEmpty() == true) {
                    (favoritesPager.adapter as? FavoritesPagerAdapter)?.locations = favorites
                    favoritesPager.adapter?.notifyDataSetChanged()
                    homeFavoritesTitle.setOnClickListener {
                        // Clearing previous filters on opening favourites screen
                        SearchUtil.clearAllFilters()
                        UserCheckoutFlowTracking.addUserFlowEvent(UserCheckoutFlowTracking.UserFlowEvent.HomeFavorites)
                        AnalyticsUtils.logEvent("(Home Screen) | See all Favorites Tapped")
                    }
                    favoriteCard.visible = false
                    favoritesPager.visible = true
                } else if (favorites?.isEmpty() == true) {
                    favoriteCard.visible = true
                    favoritesPager.visible = false
                    homeFavoritesTitle.setOnClickListener(null)
                } else {
                    homeFavoritesTitle.visible = false
                    favoriteCard.visible = false
                    favoritesPager.visible = false
                    homeFavoritesTitle.setOnClickListener(null)
                }
            }
        })

        viewModel.getIntroOffers().observe(this, Observer {
            with(introOffersBinding) {
                when {
                    it is Result.Success -> {
                        homeIntroOffersTitle.setTitle(getString(R.string.intro_offers_header_alt))
                        homeIntroOffersTitle.setOnClickListener {
                            // Clearing previous filters on navigation
                            SearchUtil.clearAllFilters()
                            AnalyticsUtils.logEvent("(Home Screen) | See All Intro Offers Tapped")
                        }
                        homeIntroOffersPager.viewModels = IntroOfferCardViewModelAdapter.toViewModels(it.value)
                        val binding = IntroOfferViewPagerBinding.bind(this.homeIntroOffersPager)
                        binding.indicator.visible = true
//                        homeIntroOffersPagerContainer.visible = true
                        //noIntroOffers.visible = false
                        showIntroOfferCard(true)
                    }
                    it is Result.Error && it.error is IntroOfferErrorHideForThisUser -> {
                        showIntroOfferCard(false)
                    }
                    else -> {
                        homeIntroOffersTitle.setTitle(getString(R.string.intro_offer_card_title))
                        homeIntroOffersTitle.setOnClickListener(null)
//                        homeIntroOffersPagerContainer.visible = false
                        //noIntroOffers.visible = true
                        showIntroOfferCard(true)
                    }
                }
            }
        })

        viewModel.getShowVirtualCard().observe(this, Observer {
            homeFragmentBinding.homeVirtualPlacement.visible = it
        })

        viewModel.getCategoriesType().observe(this, Observer { categoriesType ->
            val viewHomeFitnessCategoryBinding = ViewHomeFitnessCategoriesBinding.bind(homeFragmentBinding.root)
            when (categoriesType) {
                HomeViewModel.CategoriesType.Flex -> {
                    viewHomeFitnessCategoryBinding.fitnessCategoryTitle.text = context?.getString(R.string.flex_category_title)
                    viewHomeFitnessCategoryBinding.homeFitnessCategoryRv?.adapter = FitnessCategoryRecyclerAdapter().apply {
                        onCategoryClicked = {}
                    }
                }
                HomeViewModel.CategoriesType.NonFlex -> {
                    viewHomeFitnessCategoryBinding.fitnessCategoryTitle.text = context?.getString(R.string.non_flex_category_title)
                    viewHomeFitnessCategoryBinding.homeFitnessCategoryRv?.adapter = FitnessCategoryRecyclerAdapter().apply {
                        onCategoryClicked = {}
                    }
                }

            }
        })
    }

    private fun getCategoryStrings(it: FitnessCategoryRecyclerAdapter.FitnessCategories) =
        getString(when (it) {
            FitnessCategoryRecyclerAdapter.FitnessCategories.YOGA -> ClassCategory.YOGA.displayNameRes
            FitnessCategoryRecyclerAdapter.FitnessCategories.MEDITATION -> ClassCategory.MEDITATION.displayNameRes
            FitnessCategoryRecyclerAdapter.FitnessCategories.PILATES -> ClassCategory.PILATES.displayNameRes
            FitnessCategoryRecyclerAdapter.FitnessCategories.BARRE -> ClassCategory.BARRE.displayNameRes
            FitnessCategoryRecyclerAdapter.FitnessCategories.STRENGTH -> ClassCategory.INTERVAL_TRAINING.displayNameRes
        }).sanitizeFilterClassCategory().toSet()

    private fun displayFlexTiles(response: List<ClassTypeObject>, adapter: LmoRecyclerAdapter?) {
        adapter?.run {
            lmos = response.take(HomeViewModel.MAX_LMOS)
            notifyDataSetChanged()
        }
    }

    private fun getFlexTimesNotElapsed(response: List<ClassTypeObject>, startHour: Int, endHour: Int): Pair<Date, Date>? =
        viewModel.getFlexSearchTimes(response.firstOrNull { it.startDate.after(Date()) }?.startDate?.toCalendar(), startHour, endHour)

    private fun showLmoCard(visible: Boolean) {
        //homeFragmentBinding.homeLmoPlacement.visible = visible
    }

    private fun showIntroOfferCard(visible: Boolean) {
        homeFragmentBinding.homeIntroOffersPlacement.visible = visible
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _homeFragmentBinding = FragmentHomeV2Binding.inflate(inflater, container, false)
        //_virtualClassSectionBinding = ViewHomeVirtualClassesSectionBinding.inflate(inflater, homeFragmentBinding.homeVirtualPlacement)
        //_lastMinuteOfferBinding = ViewHomeLastMinuteOfferSectionBinding.inflate(inflater, homeFragmentBinding.homeLmoPlacement)
        _introOffersBinding = ViewHomeIntroOffersCarouselBinding.inflate(inflater, homeFragmentBinding.homeIntroOffersPlacement, true)
        return (homeFragmentBinding.root)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        homeFragmentBinding.homeAppBarLayout.addOnOffsetChangedListener(
                AppBarLayout.OnOffsetChangedListener { appBar: AppBarLayout, offset: Int ->

                    // See if insets should be taken into consideration
                    val verticalInsets = if (ViewCompat.getFitsSystemWindows(appBar)) lastTopInset else 0

                    val fullyExpandedHeight = appBar.height - ViewCompat.getMinimumHeight(appBar) - verticalInsets

                    // How far has the appBar collapsed
                    val collapsePercentage = (offset.absoluteValue / fullyExpandedHeight.toFloat())
                    homeFragmentBinding.tileElevationShadow.visible = collapsePercentage >=
                            (homeFragmentBinding.tileContainer.root.height.toFloat() / fullyExpandedHeight.toFloat())
                })

        homeFragmentBinding.favoritesPager.apply {
            val width = resources.displayMetrics.widthPixels - (2 * PAGER_PADDING)
            val params = FrameLayout.LayoutParams(width, ViewGroup.LayoutParams.WRAP_CONTENT)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            offscreenPageLimit = 5
            adapter = FavoritesPagerAdapter()
            clipChildren = false

            addOnPageChangeListener(object : androidx.viewpager.widget.ViewPager.OnPageChangeListener {
                override fun onPageSelected(position: Int) {
                    AnalyticsUtils.logEvent("(Home Screen) | Favorites Swiped")
                }

                override fun onPageScrollStateChanged(ignored: Int) = Unit
                override fun onPageScrolled(ignored0: Int, ignored1: Float, ignored2: Int) = Unit
            })
        }

//        lastMinuteOfferBinding.homeLmoRecyclerview.apply {
//            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
//            adapter = LmoRecyclerAdapter(isLmoWorkflow = true, eventName = "(Home Screen) | LMO Tapped")
//            addOnScrollListener(getLmoScrollListener())
//        }

//        val similarBookingsBinding = ViewHomeFlexSimilarBookingsBinding.bind(homeFragmentBinding.root)
//        similarBookingsBinding.homeFlexSimilarBookingsRecyclerview.apply {
//            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
//            adapter = LmoRecyclerAdapter(isLmoWorkflow = false, eventName = "(Home Screen) | Flex Similar to Your Bookings Tapped",
//                    isFlexRecommenderWorkflow = true)
//            addOnScrollListener(getLmoScrollListener())
//        }
//
//        val flexStartingSoonSectionBinding = ViewHomeFlexStartingSoonSectionBinding.bind(homeFragmentBinding.root)
//        flexStartingSoonSectionBinding.homeFlexStartingSoonRecyclerview.apply {
//            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
//            adapter = LmoRecyclerAdapter(isLmoWorkflow = false, eventName = "(Home Screen) | Flex Starting Soon Class Tapped")
//            addOnScrollListener(getLmoScrollListener())
//        }
//        val earlyBirdsSectionBinding = ViewFlexEarlyBirdsSectionBinding.bind(homeFragmentBinding.root)
//        earlyBirdsSectionBinding.homeFlexEarlyBirdsRecyclerview.apply {
//            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
//            adapter = LmoRecyclerAdapter(isLmoWorkflow = false, eventName = "(Home Screen) | Flex Early Bird Class Tapped")
//            addOnScrollListener(getLmoScrollListener())
//        }
//
//        val nightOwlsSectionBinding = ViewFlexNightOwlsSectionBinding.bind(homeFragmentBinding.root)
//        nightOwlsSectionBinding.homeFlexNightOwlsRecyclerview.apply {
//            addGapItemDecoration(DividerItemDecoration.HORIZONTAL)
//            adapter = LmoRecyclerAdapter(isLmoWorkflow = false, eventName = "(Home Screen) | Flex Night Owl Class Tapped")
//            addOnScrollListener(getLmoScrollListener())
//        }

        homeFragmentBinding.exploreSearchTerm.setOnClickListener {
            launchSearchQueryActivity()
            AnalyticsUtils.logEvent("(Home Screen) | Search Bar Tapped")
        }

        homeFragmentBinding.tileContainer.homeFitnessButton.setOnClickListener {
            AnalyticsUtils.logEvent("(Home Screen) | Vertical tapped",
                    "Category", "Fitness")
        }

        homeFragmentBinding.tileContainer.homeWellnessButton.setOnClickListener {
            AnalyticsUtils.logEvent("(Home Screen) | Vertical tapped",
                    "Category", "Wellness")
        }

        homeFragmentBinding.tileContainer.homeBeautyButton.setOnClickListener {
            AnalyticsUtils.logEvent("(Home Screen) | Vertical tapped",
                    "Category", "Beauty")
        }

        introOffersBinding.homeIntroOffersPager.onItemClickListener = TaskCallback {
            if (it == null) {
                return@TaskCallback AnalyticsUtils.reportOrThrowException(NullPointerException("Intro offer cannot be null"))
            }

            val dealsIntent = Intent(context, DealDetailsActivity::class.java)
                .putExtra(Constants.SHOW_BUSINESS_DETAILS, true)
                .putExtra(Constants.KEY_BUNDLE_DEAL, it.jsonTag)
            activity?.startActivity(dealsIntent)

            //MBABTest.trackEvent("Home Screen - Intro Offers Card Tapped")
            AnalyticsUtils.logEvent("(Home Screen) | Intro Offers Card Tapped",
                    "Home Card Index", introOffersBinding.homeIntroOffersPager.viewModels.indexOf(it))
        }

        introOffersBinding.homeIntroOffersPager.addOnPageChangeListener(object : androidx.viewpager.widget.ViewPager.OnPageChangeListener {
            override fun onPageSelected(position: Int) {
                //MBABTest.trackEvent("Home Screen - Intro Offers Swiped")
                AnalyticsUtils.logEvent("(Home Screen) | Intro Offers Swiped")
            }

            override fun onPageScrollStateChanged(ignored: Int) = Unit
            override fun onPageScrolled(ignored0: Int, ignored1: Float, ignored2: Int) = Unit
        })

        // hide views that we don't want
        val binding = IntroOfferViewPagerBinding.bind(introOffersBinding.homeIntroOffersPager)
        binding.indicator.visible = false

//        lastMinuteOfferBinding.noLocationLastMinuteOffers.setOnClickListener {}

//        introOffersBinding.noIntroOffers.setOnClickListener {}

//        homeFragmentBinding.upcomingScheduleCard.upcomingMyScheduleButton.setOnClickListener {
//            AnalyticsUtils.logEvent("(Home Screen) | Upcoming Class Schedule Tapped")
//        }

//        virtualClassesSectionBinding.virtualClassesCard.setOnClickListener {
//            AnalyticsUtils.logEvent("(Home Screen) | Virtual Classes Tapped")
//        }

    }

    // There isn't really a swipe detection listener for RecyclerViews, so lets just check
    //  for a scroll event with a separation time of 2 seconds
    private fun getLmoScrollListener(): RecyclerView.OnScrollListener =
        object : RecyclerView.OnScrollListener() {
            val SCROLL_THROTTLE_MS = TimeUnit.SECONDS.toMillis(2)
            var lastTimeScrolled = System.currentTimeMillis()
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                // Only account for horizontal scroll
                if (dy == 0 && dx != 0) {
                    lastTimeScrolled = System.currentTimeMillis()
                    recyclerView.postDelayed({
                        if (System.currentTimeMillis() - lastTimeScrolled >= SCROLL_THROTTLE_MS) {
                            when (recyclerView.id) {
//                                R.id.home_lmo_recyclerview -> {
//                                    MBABTest.trackEvent("Home Screen - LMO Swiped")
//                                    AnalyticsUtils.logEvent("(Home Screen) | LMO Swiped",
//                                            "DSPO Available", SharedPreferencesUtils.getDynamicPricingAvailable())
//                                }
//                                R.id.home_flex_night_owls_recyclerview -> {
//                                    AnalyticsUtils.logEvent("(Home Screen) | Flex Night Owls Swiped")
//                                }
                            }
                        }
                    }, SCROLL_THROTTLE_MS)
                }
            }
        }

    private fun showGcmConsentDialogs() {
        if (SharedPreferencesUtils.shouldShowConsentDialog()) {
            val dialog = MaterialOptionDialog()
            dialog.setHorizontalButtonStyle(true)
                .setMessageText(resources.getString(R.string.gcm_consent_message))
                .setHeaderText(getString(R.string.gcm_consent_title))
                .setPositiveButton(getString(R.string.gcm_button_ok)) { result ->
                    val data = HashMap<String, Any>()
                    data["accept"] = true
                    AnalyticsUtils.logEvent("(Push Notification) | Consent Dialog", data)
                    SharedPreferencesUtils.setConsentDialogShown()
                    SharedPreferencesUtils.setPushNotificationsEnabled(true, true)
                    AnalyticsUtils.setBrazePushEnabled(true)
                    result?.dismiss()
                }
                .setNegativeButton(getString(R.string.gcm_button_not_now)) { result ->
                    val data = HashMap<String, Any>()
                    data["accept"] = false
                    AnalyticsUtils.logEvent("(Push Notification) | Consent Dialog", data)
                    SharedPreferencesUtils.setConsentDialogShown()
                    result?.dismiss()
                }
            dialog.isCancelable = false
            dialog.show(childFragmentManager, PUSH_CONSENT_DIALOG_TAG)
        }
    }

    private fun launchSearchQueryActivity() {
        val intent = Intent(activity, SearchQueryEntryActivity::class.java)
        val options = ActivityOptionsCompat
            .makeSceneTransitionAnimation(requireActivity(), homeFragmentBinding.exploreSearchTerm, "searchTerm")
        startActivityForResult(intent, LAUNCH_SEARCH_ENTRY_REQUESTCODE, options.toBundle())
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _homeFragmentBinding = null
        _virtualClassSectionBinding = null
//        _lastMinuteOfferBinding = null
        _introOffersBinding = null
    }

    inner class FavoritesPagerAdapter(var locations: Array<Location> = emptyArray()) : PagerAdapter() {
        // Inflate the view, attach the viewModel, add click listener, and add it to the ViewPager
        override fun instantiateItem(viewPager: ViewGroup, position: Int): View =
            LayoutInflater.from(viewPager.context).inflate(R.layout.home_favorite_card, viewPager, false).apply {
                findViewById<androidx.cardview.widget.CardView>(R.id.favorite_cardview_container).apply {
                    val binding = HomeFavoriteCardBinding.bind(this)
                    val favoriteBiz = locations[position]
                    binding.favoriteLocationName.text = favoriteBiz.studioName
                    if (favoriteBiz.subscriptionLevel == SubscriptionLevel.ConnectListing) {
                        binding.viewScheduleButtonText.setText(R.string.view_business)
                        setOnClickListener {
                            val businessDetailsIntent = BusinessDetailsActivity.newIntent(it.context, favoriteBiz.toLocationReference())
                            startActivity(businessDetailsIntent)
                            AnalyticsUtils.logEvent("(Home Screen) | Favorites Schedule Tapped")
                            AnalyticsUtils.logEvent("(Home Screen) | Favorites Tile Tapped",
                                    "Home Card Index", position)
                        }
                    } else {
                        setOnClickListener {
                            if (FeatureFlag.CLASS_SCHEDULE_UI_REFACTOR.isFeatureEnabled()) {
                                startActivity(
                                    BusinessScheduleActivity.newIntent(
                                        context, favoriteBiz.toLocationReference(),
                                        OriginView.HOME, OriginComponent.FAVORITE_BUSINESSES_STUDIO_CAROUSEL
                                    )
                                )
                            } else {
                                startActivity(
                                    RoutineServicesActivity.newIntent(
                                        context, favoriteBiz.toLocationReference(),
                                        OriginView.HOME, OriginComponent.FAVORITE_BUSINESSES_STUDIO_CAROUSEL
                                    )
                                )
                            }
                            AnalyticsUtils.logEvent("(Home Screen) | Favorites Schedule Tapped")
                            AnalyticsUtils.logEvent("(Home Screen) | Favorites Tile Tapped",
                                    "Home Card Index", position)
                        }
                    }

                    setOnLongClickListener {
                        activity?.supportFragmentManager?.let { fManager ->
                            BusinessOptionsDialog().apply {
                                setLocation(favoriteBiz)
                                setGoToFavoritesButtonVisibility(View.VISIBLE)
                                setRemoveFromFavoritesVisibility(View.GONE)
                                setShareButtonVisibility(View.GONE)
                                setGoToApptsButtonVisible(false)
                                setGoToClassesButtonVisible(false)
                                setGoToBusinessVisible(true)
                            }.show(fManager, BusinessOptionsDialog.FRAGMENT_TAG)
                        }
                        AnalyticsUtils.logBusinessEvent("(Home Screen) | Favorites business tapped", favoriteBiz,
                                "Long press", true,
                                "View Schedule", false)
                        true
                    }

                    Picasso.get()
                        .load(favoriteBiz.studioImageUrl)
                        .placeholder(R.drawable.no_biz_logo)
                        .error(R.drawable.no_biz_logo)
                        .centerInside()
                        .fit()
                        .into(binding.favoriteBusinessCardImage)

                    binding.viewScheduleButtonText.visible = true

                    // Fix the width for the current card
                    val paddingMultiplier = if (position != count - 1) 1 else 0
                    val width = viewPager.measuredWidth - (paddingMultiplier * PAGER_PADDING)
                    layoutParams = FrameLayout.LayoutParams(width, ViewGroup.LayoutParams.MATCH_PARENT)
                }

                viewPager.addView(this)
            }

        override fun destroyItem(viewPager: ViewGroup, position: Int, view: Any) {
            viewPager.removeView(view as View)
        }

        override fun isViewFromObject(view: View, objectIsView: Any) = view == objectIsView

        override fun getCount() = Math.min(locations.size, MAX_FAV_LOCATIONS_SHOWN)

        override fun getPageTitle(position: Int) = locations[position].title ?: ""

        // Force new item instantiation when data changed
        override fun getItemPosition(`object`: Any) = POSITION_NONE
    }

    inner class LmoRecyclerAdapter(var lmos: List<ClassTypeObject> = emptyList(), val isLmoWorkflow: Boolean = false,
                                   val eventName: String = "",
                                   val isFlexRecommenderWorkflow: Boolean = false) :
            RecyclerView.Adapter<LmoClassHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
            LmoClassHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_last_minute_offer, parent, false))
                .apply {
                    itemView.setOnClickListener {
                        val lastMinuteOffer = lmos[adapterPosition]
                        StaticInstance.selectedClassTypeObject = null
                        val intent = Intent(context, ClassTypeDetailsActivity::class.java)
                            .putExtra(Constants.KEY_BUNDLE_SITEID, lastMinuteOffer.location?.siteId)
                            //TODO [865063] Remove all references to class instance ID from the app
                            .putExtra(Constants.KEY_BUNDLE_CLASS_INSTANCE_ID, lastMinuteOffer.id.toLong())
                            .putExtra(Constants.KEY_BUNDLE_IS_LMO_WORKFLOW, isLmoWorkflow)
                            .putExtra(ClassTypeDetailsActivity.TYPE_EXTRA_STRING, ClassTypeDetailsActivity.Type.CLASS.ordinal)
                        startActivity(intent)
                        //if (isLmoWorkflow) MBABTest.trackEvent(eventName)
                        val eventMetadata: MutableMap<String, Any> = mutableMapOf()
                        eventMetadata["rank"] = adapterPosition + 1
                        eventMetadata["class_id"] = lastMinuteOffer.id
                        eventMetadata["class_name"] = lastMinuteOffer.name
                        eventMetadata["class_category"] = lastMinuteOffer.category
                        eventMetadata["business_id"] = lastMinuteOffer.location?.id ?: ""
                        eventMetadata["business_name"] = lastMinuteOffer.location?.name ?: ""
                        AnalyticsUtils.logEvent(eventName, eventMetadata)
                        //if (isFlexRecommenderWorkflow) SharedPreferencesUtils.setRecommendedLmoClassId(lastMinuteOffer.id)
                    }
                }

        override fun getItemCount() = lmos.size

        override fun onBindViewHolder(holder: LmoClassHolder, position: Int) {
            holder.lastMinuteDeal = lmos[position]
        }
    }

    override fun onLocationPermissionAction(accepted: Boolean) {
        super.onLocationPermissionAction(accepted)
        if (accepted) {
            checkAreaSupportsDspos()
        } else {
            dynamicPricingSupported = false
            viewModel.updateLastMinuteOffers(dynamicPricingSupported)
        }
        viewModel.updateIntroOffers()
        viewModel.updateFlexClasses()
    }

    override fun onConnected() {
        showGcmConsentDialogs()

        if (GeoLocationUtils.locationServicesAllowed(context)) {
            checkAreaSupportsDspos()
        } else {
            dynamicPricingSupported = false
            viewModel.updateLastMinuteOffers(dynamicPricingSupported)
        }
        viewModel.updateFlexClasses()
        viewModel.updateIntroOffers()
    }

    override fun onConnectionFailed(connectionResult: ConnectionResult) {
        super.onConnectionFailed(connectionResult)
        viewModel.updateIntroOffers()
        dynamicPricingSupported = false
        viewModel.updateLastMinuteOffers(dynamicPricingSupported)
        viewModel.updateFlexClasses()
    }

    private fun checkAreaSupportsDspos() {
        GeoLocationUtils.getBestLocation(requireContext()) { loc ->
            DynamicPricingApi.getLatLonSupported(loc.latitude, loc.longitude, {
                dynamicPricingSupported = it?.isSupported == true
                viewModel.updateLastMinuteOffers(dynamicPricingSupported)
            }, { dynamicPricingSupported = false })
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.updateUpcomings(StaticInstance.refreshVisits)
        AnalyticsUtils.logEvent("(Home Screen) | View Home Page")
    }

    override fun applyTopOffset(topOffset: Int, root: View) {
        lastTopInset = topOffset
        SharedPreferencesUtils.setLastKnownTopOffset(topOffset)
        homeFragmentBinding.homeFragmentSearchBarContainer.setOptionalPadding(top = topOffset)

        if (topOffset > 0) {
            // Since part of the collapsing toolbar has no gradient, we need our own shim
            homeFragmentBinding.statusBarScrim.apply {
                layoutParams = layoutParams.apply {
                    height = topOffset
                }
            }
        }
    }

}
