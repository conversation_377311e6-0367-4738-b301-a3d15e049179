package com.mindbodyonline.connect.fragments.search

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.lifecycleScope
import com.mindbodyonline.android.util.SafeGson
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.analytics.search.SearchFlowTracker
import com.mindbodyonline.connect.common.utilities.*
import com.mindbodyonline.connect.databinding.ExploreFitnessContainerBinding
import com.mindbodyonline.connect.explore.result.BusinessAndActivitiesListContainerFragment
import com.mindbodyonline.connect.fragments.search.NewFilterType.*
import com.mindbodyonline.connect.utils.AnalyticsUtils
import com.mindbodyonline.connect.utils.AnalyticsUtilsKt
import com.mindbodyonline.connect.utils.Constants
import com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_IS_FROM_NEW_SEARCH_HOMEPAGE
import com.mindbodyonline.connect.utils.Constants.SORTING_OPTION_RECOMMENDED
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.TimeUtils
import com.mindbodyonline.domain.dataModels.ConnectSearchModel
import com.mindbodyonline.domain.explore.ActivityType
import com.mindbodyonline.views.dialog.FilterType
import java.util.*


/**
 * This is the Fragment that is shown for fitness tab in Explore.
 * It is a container / switcher of 2 different fragments (the class search results
 * and the location search results)
 */
class ExploreFitnessFragment : ExploreFragmentV2.ExploreChildFragment(), View.OnClickListener,
        ExploreFragmentV2.TwoTabFragmentContract {

    private var selectedFilterType: FilterType = FilterType.NestedBusinessClass
    private var isFirstTimeLoad: Boolean = true

    companion object {
        const val FITNESS_VERTICAL = "Fitness"
        const val BEAUTY_VERTICAL = "Beauty"
        const val WELLNESS_VERTICAL = "Wellness"
        const val FITNESS_SHOWN_FRAGMENT_TAG = "FITNESS_SHOWN_FRAGMENT_TAG"
        const val KEY_BUNDLE_SEARCH_MODEL = "KEY_BUNDLE_SEARCH_MODEL"
        const val KEY_BUNDLE_SHOW_BUSINESSES = "KEY_BUNDLE_SHOW_BUSINESSES"
        const val KEY_BUNDLE_VERTICAL_NAME = "KEY_BUNDLE_VERTICAL_NAME"

        @JvmStatic
        fun newInstance(defaultSearch: ConnectSearchModel? = null) = ExploreFitnessFragment().apply {
            setDefaultSearchModel(defaultSearch)
        }
    }

    private var lastSearchModel: ConnectSearchModel? = null
    private var childLoading: Boolean = false
    var onTimeRangeChangedListener: ((Pair<Calendar, Calendar>) -> Unit)? = null
    var onExpandSearchClickListener: (() -> Unit)? = null
    var onNewSearchClickListener: (() -> Unit)? = null
    var onShareLocationCLickListener: (() -> Unit)? = null
    var onClearFilterClickListener: (() -> Unit)? = null
    var onClearSearchClickListener: (() -> Unit)? = null
    var onNoLocationChangeListener: ((isNoLocationState: Boolean) -> Unit)? = null
    var onDistanceFilterClickListener: (() -> Unit)? = null
    var onFilterTypeChangeListener: ((type: FilterType) -> Unit)? = null

    var onCategoryFilterClickListener: (() -> Unit)? = null

    var onTimeFilterClickListener: (() -> Unit)? = null

    var onSortOptionClickListener: (() -> Unit)? = null

    var onAllFilterClickListener: (() -> Unit)? = null

    var onDateSwipeListener: (() -> Unit)? = null

    var onDateSelectedListener: ((Calendar?) -> Unit)? = null

    var onSearchResultsReceived: (() -> Unit)? = null

    private var _binding: ExploreFitnessContainerBinding? = null
    private val binding get() = _binding!!

    private var recommenderType: String? = null

    private val filtersAdapter by lazy {
        NewFiltersAdapter {
           when(it) {
               FILTER_TIME -> { onTimeFilterClickListener?.invoke() }
               FILTER_DISTANCE-> { onDistanceFilterClickListener?.invoke() }
               FILTER_SORT_BY -> { onSortOptionClickListener?.invoke() }
               FILTER_CATEGORY -> { onCategoryFilterClickListener?.invoke()}

           }
        }
    }

    fun resetFiltersContainerViewScrollPosition(){
        binding.horizontalFilterContainer.scrollTo(0,0)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = ExploreFitnessContainerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Check saved instance state first, then arguments
        lastSearchModel = (savedInstanceState ?: arguments)?.getString(KEY_BUNDLE_SEARCH_MODEL)
                ?.fromJson<ConnectSearchModel>()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        if (lastSearchModel != null) {
            outState.putString(KEY_BUNDLE_SEARCH_MODEL, SafeGson.toJson(lastSearchModel))
        }
        outState.putBoolean(KEY_BUNDLE_SHOW_BUSINESSES, getCurrentlyShownFragment() is LocationResultsFragment)
        super.onSaveInstanceState(outState)
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        if (getCurrentlyShownFragment() != null) return

        handleNewSearchFilterExperienceView()

        val showBusinessTab = arguments?.getBoolean(KEY_BUNDLE_SHOW_BUSINESSES) == true
        if (arguments?.getBoolean(Constants.KEY_BUNDLE_SHOW_CLASSES) == true) {
            selectedFilterType = FilterType.Class
        }

        binding.showHeaderSectionForNestedSearchResults(showBusinessTab)

        with(binding) {
            classesButton.isActivated = !showBusinessTab
            businessesButton.isActivated = showBusinessTab
            businessesButton.setOnClickListener(this@ExploreFitnessFragment)
            classesButton.setOnClickListener(this@ExploreFitnessFragment)
            exploreBusinesses.setOnClickListener(this@ExploreFitnessFragment)
            exploreClasses.setOnClickListener(this@ExploreFitnessFragment)

            updateTabSelection(exploreClasses, selectedFilterType == FilterType.Class)
            updateTabSelection(exploreBusinesses, selectedFilterType != FilterType.Class)
        }

        val resultPage = getSearchResultPage(showBusinessTab)

        childFragmentManager.beginTransaction()
            .add(R.id.explore_fitness_content,
                    when (resultPage) {
                        SearchResultPage.CLASS -> getNewFitnessFragment()
                        SearchResultPage.BUSINESS -> getNewBusinessFragment()
                        SearchResultPage.NESTED_BUSINESS_AND_CLASS -> getNewNestedBusinessAndActivitiesFragment()
                    },
                    FITNESS_SHOWN_FRAGMENT_TAG).commit()
    }

    private fun getNewNestedBusinessAndActivitiesFragment() = BusinessAndActivitiesListContainerFragment.newInstance(lastSearchModel).apply {
        onFilterTypeFragmentChangeListener = {

            selectedFilterType = it

            with(binding) {
                classBusinessFilter.root.visible = true
                businessFilter.root.visible = false
                dateSelectionView.visible = true
                filtersAdapter.updateTimeFilterVisibility(FILTER_TIME, true)
            }
            onFilterTypeChangeListener?.invoke(it)
        }
        onBusinessSearchResultsReceived = {
            onSearchResultsReceived?.invoke()
        }
        onNewSearchButtonClicked = {
            handleNewSearchClick()
        }
        onNoLocationStateChangeListener = {
            onNoLocationChangeListener?.invoke(it)
        }
    }

    private fun handleNewSearchFilterExperienceView() {
        with(binding){
            exploreButtonContainer.visible = false
            enhancedFilterContainer.visible = true
            filtersAdapter.submitList(getFilterList())
            rcvFilterCategories.adapter = filtersAdapter

            classBusinessFilter.root.setSafeOnClickListener {
                onAllFilterClickListener?.invoke()
            }

            businessFilter.root.setSafeOnClickListener {
                onAllFilterClickListener?.invoke()
            }

            dateSelectionView.apply {
                visible = true
                setupView()
                onDaySelectedCallback = { date ->
                    lastSelectedDay = TimeUtils.toCalendar(date)
                    onDateSelectedListener?.invoke(lastSelectedDay)
                }
                onDateSwipeCallback = {
                    onDateSwipeListener?.invoke()
                }
            }
        }
    }

    fun resetDateFilter(){
        lifecycleScope.launchWhenStarted {
            lastSelectedDay = null
            binding.dateSelectionView.setupView()
        }
    }

    fun updateDateFilterByDate(startDate: Calendar) {
        lifecycleScope.launchWhenStarted {
            binding.dateSelectionView.setupView(startDate)
            lastSelectedDay = startDate
        }
    }

    // Cascade onResume down to the child fragment
    override fun onResume() {
        super.onResume()
        childFragmentManager.findFragmentByTag(FITNESS_SHOWN_FRAGMENT_TAG)?.onResume()
    }

    override fun showSecondaryTab(showSecondTab: Boolean) {
        if (isAdded) {
            showFragment(getSearchResultPage(showSecondTab))
        } else {
            arguments = (arguments ?: Bundle()).apply {
                putBoolean(KEY_BUNDLE_SHOW_BUSINESSES, showSecondTab)
            }
        }
    }

    private fun getSearchResultPage(showBusiness: Boolean): SearchResultPage {
        return when {
            showBusiness -> SearchResultPage.BUSINESS
            selectedFilterType == FilterType.Class -> SearchResultPage.CLASS
            else -> SearchResultPage.NESTED_BUSINESS_AND_CLASS
        }
    }

    fun setDefaultSearchModel(defaultSearch: ConnectSearchModel?) {
        arguments = (arguments ?: Bundle()).apply {
            putString(KEY_BUNDLE_SEARCH_MODEL, SafeGson.toJson(defaultSearch))
        }
    }

    private var lastSelectedDay: Calendar? = null

    // We need to restrict call to runSearch in fitness fragment when user navigates from new search homepage
    private fun getNewFitnessFragment() = FitnessSearchFragment.newInstance(lastSearchModel, lastSelectedDay,
            arguments?.getBoolean(KEY_BUNDLE_IS_FROM_NEW_SEARCH_HOMEPAGE) == true, recommenderType).apply {
        onSearchSelfTriggered = {
            onTimeRangeChangedListener?.invoke(it.timeRange)
            lastSelectedDay = it.timeRange.first
        }
        onExpandSearchButtonClicked = {
            onExpandSearchClickListener?.invoke()
        }
        onNewSearchButtonClicked = {
            handleNewSearchClick()
        }
        onClearFilterButtonClicked = {
            onClearFilterClickListener?.invoke()
        }
        onClassSearchResultsReceived = {
            onSearchResultsReceived?.invoke()
        }
        onFilterTypeFragmentChangeListener = {

            selectedFilterType = it

            with(binding) {
                showHeaderSectionForNestedSearchResults(false)
                dateSelectionView.visible = true
                filtersAdapter.updateTimeFilterVisibility(FILTER_TIME, true)
            }

            onFilterTypeChangeListener?.invoke(it)
        }
        onNoLocationStateChangeListener = {
            onNoLocationChangeListener?.invoke(it)
        }
        setLoading(childLoading)
    }

    private fun getNewBusinessFragment() = LocationResultsFragment.newInstance(lastSearchModel, ColorTheme.FITNESS).apply {
        onExpandSearchButtonClicked = {
            onExpandSearchClickListener?.invoke()
        }
        onNewSearchButtonClicked = {
            handleNewSearchClick()
        }
        onClearSearchButtonClicked = {
            onClearSearchClickListener?.invoke()
        }
        onBusinessSearchResultsReceived = {
            onSearchResultsReceived?.invoke()
        }
        onNoLocationStateChangeListener = {
            onNoLocationChangeListener?.invoke(it)
        }
        onFilterTypeFragmentChangeListener = {
            selectedFilterType = it
            with(binding){
                dateSelectionView.visible = false
            }
            filtersAdapter.updateTimeFilterVisibility(FILTER_TIME, false)
            onFilterTypeChangeListener?.invoke(it)
        }
    }

    private fun handleNewSearchClick() {
        if (context.shouldInvokeShareLocation()) {
            onShareLocationCLickListener?.invoke()
        } else {
            val additionalData = hashMapOf<String, Any>()
            // Adding city and state if valid city selected, for safer side added this to try catch
            try {
                val lastLocationQuery = SharedPreferencesUtils.getLastLocationQuery().orEmpty()
                lastLocationQuery.takeIf { it.isNotEmpty() && !it.lowercase().contains("current location") }?.let {
                    val locationParts = it.split(",")
                    locationParts.takeIf { parts -> parts.size > 1 }?.let { parts ->
                        additionalData["city"] = parts[0]
                        additionalData["state"] = parts[1]
                    }
                }
            } catch (ex: Exception) {
                MBLog.e(AnalyticsUtilsKt.TAG, ex.message.orEmpty())
            }
            AnalyticsUtils.logEvent("(Search) | New Search", additionalData)
            onClearSearchClickListener?.invoke()
            onNewSearchClickListener?.invoke()
        }
    }

//    private fun showFragment(classes: Boolean)
    private fun showFragment(resultPage: SearchResultPage) {
        getCurrentlyShownFragment()?.let {
            if (resultPage == SearchResultPage.CLASS && it is FitnessSearchFragment) return@showFragment
            if (resultPage == SearchResultPage.BUSINESS && it is LocationResultsFragment) return@showFragment
        }

        childFragmentManager.beginTransaction()
                .replace(R.id.explore_fitness_content,
                        when(resultPage){
                            SearchResultPage.CLASS -> getNewFitnessFragment()
                            SearchResultPage.BUSINESS -> getNewBusinessFragment()
                            SearchResultPage.NESTED_BUSINESS_AND_CLASS -> getNewNestedBusinessAndActivitiesFragment()
                        }, FITNESS_SHOWN_FRAGMENT_TAG)
                .commit()
    }

    private fun getCurrentlyShownFragment() =
            childFragmentManager.findFragmentByTag(FITNESS_SHOWN_FRAGMENT_TAG) as? ExploreFragmentV2.ChildFragmentContract

    override fun setLoading(loading: Boolean) {
        childLoading = loading
        if (isAdded) {
            getCurrentlyShownFragment()?.setLoading(loading)
        }
    }

    override fun runNewSearch(model: ConnectSearchModel) {
        lastSearchModel = model
        lifecycleScope.launchWhenStarted {
            if (!isFirstTimeLoad) {
                val searchTerm = lastSearchModel?.query.orEmpty()
                val activityType = SearchUtil.getActivityType(searchTerm)
                val showBusinessTab = arguments?.getBoolean(KEY_BUNDLE_SHOW_BUSINESSES) == true || activityType == ActivityType.Wellness || activityType == ActivityType.Beauty
                val showClassBusiness = (activityType == ActivityType.Fitness || (activityType == ActivityType.None && !showBusinessTab))

                // No need to call this show fragment again if search initiates from apply filter
                if (!SharedPreferencesUtils.isSearchFromFilter()) {

                    if (activityType != ActivityType.None) {
                        showFragment(getSearchResultPage(activityType != ActivityType.Fitness))
                    } else {
                        showFragment(getSearchResultPage(!showClassBusiness))
                    }
                    binding.showHeaderSectionForNestedSearchResults(!showClassBusiness)
                } else {
                    var containsClassTypeCategory = false
                    val selectedFilter = SharedPreferencesUtils.getSelectedFitnessClassTypes()?.toAcceptedValuesList()
                            ?: emptyList()
                    if (selectedFilter.isEmpty()) {
                        containsClassTypeCategory = showClassBusiness
                        if (activityType != ActivityType.None && shouldSwitchTab(activityType)) {
                            showFragment(getSearchResultPage(activityType != ActivityType.Fitness))
                        }
                    } else {
                        selectedFilter.forEach { selectedCategory ->
                            if (SearchUtil.getActivityType(selectedCategory) == ActivityType.Fitness) {
                                containsClassTypeCategory = true
                            }
                        }
                        showFragment(getSearchResultPage(!containsClassTypeCategory))
                    }
                    binding.showHeaderSectionForNestedSearchResults(!containsClassTypeCategory)
                    SharedPreferencesUtils.setSearchFromFilter(false)
                }
            } else {
                isFirstTimeLoad = false
            }

            if (isAdded) {
                getCurrentlyShownFragment()?.runNewSearch(model)
            } else {
                setDefaultSearchModel(lastSearchModel)
            }
        }
    }

    private fun shouldSwitchTab(activityType: ActivityType) =
        (activityType != ActivityType.Fitness && selectedFilterType == FilterType.Class)
                || (activityType == ActivityType.Fitness && selectedFilterType == FilterType.Business)

    private fun ExploreFitnessContainerBinding.showHeaderSectionForNestedSearchResults(showBusiness: Boolean) {
        exploreBusinessParentContainer.visible = !showBusiness
        classBusinessFilter.root.visible = !showBusiness
        businessFilter.root.visible = showBusiness
    }

    override fun getLastSearchModel() = lastSearchModel

    override fun returnScrollToTop() {
        if (isAdded) {
            getCurrentlyShownFragment()?.returnScrollToTop()
        }
    }

    override fun onClick(view: View) {
        view.isActivated = true
        when(view.id){
            R.id.explore_classes -> {
                updateClassAndBusinessSelection(isClassSelected = true)
            }
            R.id.explore_businesses -> {
                updateClassAndBusinessSelection(isClassSelected = false)
            }
        }
        // In case of new search home when user changes tab latest selected categories
        // was not being passed in api request So setting categories from shared preferences
        lastSearchModel?.selectedClassTypes = SharedPreferencesUtils.getSelectedFitnessClassTypes()
        showFragment(
                if (view.id == R.id.explore_classes) SearchResultPage.CLASS
                else SearchResultPage.NESTED_BUSINESS_AND_CLASS
        )
        // Tab switch is not considered as a true search
        SearchFlowTracker.trueSearchFlow = false
        returnScrollToTop()
    }

    private fun updateClassAndBusinessSelection(isClassSelected: Boolean) {
        with(binding) {
            classesButton.isActivated = isClassSelected
            businessesButton.isActivated = !isClassSelected
            if (isClassSelected) {
                updateTabSelection(exploreClasses, true)
                updateTabSelection(exploreBusinesses, false)
            } else {
                updateTabSelection(exploreBusinesses, true)
                updateTabSelection(exploreClasses, false)
            }
        }
    }

    private fun updateTabSelection(view: TextView, isSelected: Boolean) {
        if(isSelected){
            view.setBackgroundResource(R.drawable.bg_round_border_radius_16)
            view.setTypeface(ResourcesCompat.getFont(requireContext(), R.font.semibold))
            view.setTextColor(ContextCompat.getColor(requireContext(), R.color.brand_text_color))
        } else{
            view.setBackgroundResource(0)
            view.setTypeface(ResourcesCompat.getFont(requireContext(), R.font.regular))
            view.setTextColor(ContextCompat.getColor(requireContext(), R.color.black))
        }
    }

    fun updateCategoryFilterSelection(filterString: String, isSelected: Boolean) {
        lifecycleScope.launchWhenStarted {
            val updatedFilterString = if (isSelected) filterString else getString(R.string.filter_categories_title)
            filtersAdapter.updateItemState(FILTER_CATEGORY, updatedFilterString, isSelected)
        }
    }

    fun updateSortByOptionSelection(sortingOption: String) {
        lifecycleScope.launchWhenStarted {
            val isSelected = sortingOption != SORTING_OPTION_RECOMMENDED
            val filterString = if (isSelected) getString(R.string.filter_sort_place_holder, sortingOption) else getString(R.string.sorting_option_title)
            filtersAdapter.updateItemState(FILTER_SORT_BY, filterString, isSelected)
        }
    }

    fun updateDistanceFilterSelection(distanceString: String, isSelected: Boolean){
        lifecycleScope.launchWhenStarted {
            val filterString = if (isSelected) distanceString else getString(R.string.filter_distance_title)
            filtersAdapter.updateItemState(FILTER_DISTANCE, filterString, isSelected)
        }
    }

    fun getLastSelectedDate() = lastSelectedDay

    fun updateTimeFilterSelection(timeRangeString: String, isSelected: Boolean) {
        lifecycleScope.launchWhenStarted {
            val filterString = if (isSelected) timeRangeString else getString(R.string.filter_time_section_title)
            filtersAdapter.updateItemState(FILTER_TIME, filterString, isSelected)
        }
    }

    fun updateAllFilterSelection(filterSelected: Boolean, filterCount: Int) {
        lifecycleScope.launchWhenStarted {
            binding.classBusinessFilter.root.isActivated = filterSelected
            binding.classBusinessFilter.tvFilterCount.visible = filterSelected
            binding.businessFilter.root.isActivated = filterSelected
            binding.businessFilter.tvFilterCount.visible = filterSelected
            if (filterSelected) {
                binding.classBusinessFilter.tvFilterCount.text = filterCount.toString()
                if (filterCount > 0) {
                    binding.businessFilter.tvFilterCount.text = filterCount.toString()
                    updateAllFilterView(binding.businessFilter.ivFilterIcon, R.drawable.ic_new_filter_icon_active)
                }
                updateAllFilterView(binding.classBusinessFilter.ivFilterIcon, R.drawable.ic_new_filter_icon_active)

            } else {
                updateAllFilterView(binding.classBusinessFilter.ivFilterIcon, R.drawable.ic_new_filter_icon_default)
                updateAllFilterView(binding.businessFilter.ivFilterIcon, R.drawable.ic_new_filter_icon_default)
            }
        }
    }

    private fun updateAllFilterView(view: ImageView, imageResource: Int) {
        context?.let {
            view.setImageDrawable(ContextCompat.getDrawable(it,imageResource))
        }
    }

    override fun showResults() {
        // Do nothing. We are always showing results
    }

    override fun onDestroyView() {
        _binding = null
        super.onDestroyView()
    }

    fun setHomeCarouselRecommenderType(recommenderType: String?){
        this.recommenderType = recommenderType
        if (isAdded) {
            (getCurrentlyShownFragment() as? FitnessSearchFragment)?.setHomeCarouselRecommenderType(recommenderType)
        }
    }
}

enum class SearchResultPage {
   CLASS, BUSINESS, NESTED_BUSINESS_AND_CLASS
}
