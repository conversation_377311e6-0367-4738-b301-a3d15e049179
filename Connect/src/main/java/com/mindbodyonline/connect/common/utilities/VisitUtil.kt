package com.mindbodyonline.connect.common.utilities

import android.app.NotificationManager
import android.content.Context
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.connect.utils.CalendarUtils
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.data.StaticInstance
import com.mindbodyonline.data.services.MBStaticCache
import com.mindbodyonline.domain.ClassTypeVisit
import com.mindbodyonline.domain.connv1.Visit

/**
 * Removes a cancelled class or appointment. Call this helper method after a successful return
 * from a Gateway delete user bookings or refund request API call.
 */
fun Visit?.removeBookingFromCache() {
    if (this == null) return
    // Remove the visit from the user's synced Calendar (for example Google Calendar)
    if (SharedPreferencesUtils.isCalendarSyncEnabled()) {
        CalendarUtils.removeVisitFromCalendar(ConnectApp.getInstance(), this)
    }
    // Cancel any notification events that are scheduled to remind the user to start the visit
    (ConnectApp.getInstance()?.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager?)?.let {
        this.SiteVisitId?.toInt()?.let { visitId -> it.cancel(visitId) }
    }
    // Remove the visit from our MBStaticCache list of visits
    MBStaticCache.getInstance().removeCancelledVisit(this)
    setFlagsUpdateWidget()
}

/**
 * Removes a cancelled class. Call this helper method after a successful return
 * from a Gateway delete user bookings or refund request API call.
 */
fun ClassTypeVisit?.removeBookingFromCache() {
    if (this == null) return
    // Remove the visit from the user's synced Calendar (for example Google Calendar)
    if (SharedPreferencesUtils.isCalendarSyncEnabled()) {
        CalendarUtils.removeClassVisitFromCalendar(ConnectApp.getInstance(), this)
    }
    // Cancel any notification events that are scheduled to remind the user to start the visit
    (ConnectApp.getInstance()?.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager?)?.let {
        this.siteVisitId.toInt().let { visitId -> it.cancel(visitId) }
    }
    // Remove the visit from our MBStaticCache list of visits
    MBStaticCache.getInstance().removeCancelledVisit(this)
    setFlagsUpdateWidget()
}


/**
 * Helper method for the Visit and ClassTypeVisit remove from cache extension functions, common
 * for both types of visit objects.
 */
private fun setFlagsUpdateWidget() {
    // Update the Shared Pref that monitors how many notifications we show our users
    SharedPreferencesUtils.incrementOrDecrementTotalBookings(false)
    // Set the StaticInstance refresh data flag to force a new call to fetch the user's schedule
    StaticInstance.refreshDataAfterBooking()
    // Set the MBStaticCache favorites changed flag to check if the visit should be removed from favorites
    MBStaticCache.getInstance().setFavoritesChanged(true)
}