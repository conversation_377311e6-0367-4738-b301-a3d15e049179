package com.mindbodyonline.connect.navigation

import android.content.Intent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.mindbodyonline.connect.common.utilities.SearchUtil
import com.mindbodyonline.connect.common.utilities.resetTimePeriod
import com.mindbodyonline.connect.common.utilities.setDefaultTimeRangeInSharedPreference
import com.mindbodyonline.connect.fragments.search.ExploreFragmentV2.ExploreFilterValues
import com.mindbodyonline.connect.utils.Constants
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.api.RecommenderType
import com.mindbodyonline.framework.abvariant.ABHelperUtils
import java.util.Date

class NavigationViewModel : ViewModel() {
    private val _fragmentNavigationRequest = MutableLiveData<FragmentNavigationRequest>()
    val fragmentNavigationRequest: LiveData<FragmentNavigationRequest> = _fragmentNavigationRequest

    private val _activityNavigationRequest = MutableLiveData<ActivityNavigationRequest>()
    val activityNavigationRequest: LiveData<ActivityNavigationRequest> = _activityNavigationRequest

    private val _updateDistanceFilters = MutableLiveData<Boolean>()
    val updateDistanceFilters: LiveData<Boolean> = _updateDistanceFilters

    private val _checkForSignedInUser = MutableLiveData<Boolean>()
    val checkForSignedInUser: LiveData<Boolean> = _checkForSignedInUser

    var searchTerm: String? = null
    var globalLocationText: String? = null
    var homeIntroOfferContext: String? = null

    fun createFragmentNavigationRequest(
        fragmentTag: String,
        selectedTabIndex: Int? = null,
        showSecondaryTab: Boolean? = null,
        searchFilters: SearchFilters? = null,
    ) {
        _fragmentNavigationRequest.value =
            FragmentNavigationRequest(
                destinationFragmentTag = fragmentTag,
                selectedTabIndex = selectedTabIndex,
                showSecondaryTab = showSecondaryTab,
                searchFilters = searchFilters,
            )
    }

    private fun createActivityNavigationRequest(activityNavigationRequest: ActivityNavigationRequest) {
        _activityNavigationRequest.value = activityNavigationRequest
    }

    fun onFavoritesButtonClicked() {
        createFragmentNavigationRequest(
            fragmentTag = Constants.FAVORITES_FRAGMENT_TAG,
            selectedTabIndex = 0,
        )
    }

    fun onMyScheduleButtonClicked() {
        createFragmentNavigationRequest(
            fragmentTag = Constants.MY_PROFILE_FRAGMENT_TAG,
            selectedTabIndex = Constants.SCHEDULE_TAB,
        )
    }

    fun onSearchTriggered(
        searchIntent: Intent?,
        globalLocationText: String,
    ) {
        searchTerm = searchIntent?.getStringExtra(Constants.KEY_BUNDLE_SEARCHTERM)
        this.globalLocationText = globalLocationText
        createActivityNavigationRequest(ActivityNavigationRequest.SEARCH_RESULT)
    }

    fun onFitnessButtonClicked() {
        createActivityNavigationRequest(ActivityNavigationRequest.FITNESS_SEARCH_RESULT)
    }

    fun onWellnessButtonClicked() {
        createActivityNavigationRequest(ActivityNavigationRequest.WELLNESS_SEARCH_RESULT)
    }

    fun onBeautyButtonClicked() {
        createActivityNavigationRequest(ActivityNavigationRequest.BEAUTY_SEARCH_RESULT)
    }

    fun onIntroOfferButtonClicked(homeIntroOfferContext: String? = null) {
        this.homeIntroOfferContext = homeIntroOfferContext
        createActivityNavigationRequest(ActivityNavigationRequest.DEALS_SEARCH_RESULT)
    }

    fun onSearchCategoryClicked(
        classCategories: Set<String>? = null,
        startTime: Date? = null,
        endTime: Date? = null,
        isFlexOnly: Boolean = false,
        recommenderType: RecommenderType? = null,
    ) {
        SearchUtil.clearAllFilters()
        SharedPreferencesUtils.setLastSelectedDistanceValue(Constants.MAXIMUM_DISTANCE_VALUE_50_PLUS)
        val exploreFilterValues =
            ExploreFilterValues(
                dynamicPricingOnly = false,
                virtualOnly = false,
                flexOnly = isFlexOnly,
            )
        exploreFilterValues.classTypes = classCategories
        setExploreFilterValues(exploreFilterValues)
        val searchFilters =
            SearchFilters(
                startDate = startTime,
                endDate = endTime,
                exploreFilterValues = exploreFilterValues,
                recommenderType = recommenderType,
            )
        createFragmentNavigationRequest(
            fragmentTag = Constants.EXPLORE_FRAGMENT_TAG,
            selectedTabIndex = startTime?.let { Constants.FITNESS_VERTICAL_ID },
            searchFilters = searchFilters,
        )
    }

    private fun setExploreFilterValues(filterValues: ExploreFilterValues?) {
        SharedPreferencesUtils.setLastSelectedDistanceValue(Constants.DEFAULT_DISTANCE_VALUE)
        setDefaultTimeRangeInSharedPreference()
        resetTimePeriod()

        filterValues?.let {
            filterValues.flexOnly?.let {
                SharedPreferencesUtils.setFlexOnlyFilterEnabled(it)
                if (it) {
                    SharedPreferencesUtils.setLastSelectedDistanceValue(Constants.MAXIMUM_DISTANCE_VALUE_50_PLUS)
                }
            }
            filterValues.dynamicPricingOnly?.let {
                SharedPreferencesUtils.setDynamicPricingFilterEnabled(it)
            }
            filterValues.virtualOnly?.let {
                if (it) {
                    SharedPreferencesUtils.setLastSelectedDistanceValue(Constants.MAXIMUM_DISTANCE_VALUE_50_PLUS)
                }
                SharedPreferencesUtils.setVirtualOnlyFilterEnabled(it)
            }
            SharedPreferencesUtils.setFavoritesOnlyFilterEnabled(false)
            SharedPreferencesUtils.setSelectedFitnessClassTypes(filterValues.classTypes)
        }
    }

    fun onDynamicPricingButtonClicked(recommenderType: RecommenderType? = null) {
        SearchUtil.clearAllFilters()
        val searchFilters =
            SearchFilters(
                exploreFilterValues = ExploreFilterValues(dynamicPricingOnly = true),
                recommenderType = recommenderType,
            )
        createFragmentNavigationRequest(
            fragmentTag = Constants.EXPLORE_FRAGMENT_TAG,
            selectedTabIndex = Constants.FITNESS_VERTICAL_ID,
            searchFilters = searchFilters,
        )
    }

    fun onSearchVirtualClicked() {
        SearchUtil.clearAllFilters()
        val searchFilters =
            SearchFilters(exploreFilterValues = ExploreFilterValues(virtualOnly = true))
        createFragmentNavigationRequest(
            fragmentTag = Constants.EXPLORE_FRAGMENT_TAG,
            selectedTabIndex = Constants.FITNESS_VERTICAL_ID,
            searchFilters = searchFilters,
        )
    }

    fun onDealsNearYouClicked() {
        if (ABHelperUtils.getDealsNearYouDestination() == ABHelperUtils.DealsNearYouDestination.DEALS) {
            createFragmentNavigationRequest(fragmentTag = Constants.DEALS_FRAGMENT_TAG)
        } else {
            val searchFilters =
                SearchFilters(
                    exploreFilterValues = ExploreFilterValues(dynamicPricingOnly = true),
                    recommenderType = RecommenderType.UNKNOWN,
                )
            createFragmentNavigationRequest(
                fragmentTag = Constants.EXPLORE_FRAGMENT_TAG,
                selectedTabIndex = Constants.FITNESS_VERTICAL_ID,
                searchFilters = searchFilters,
            )
        }
    }

    fun onExploreClassesButtonClicked() {
        SearchUtil.clearAllFilters()
        _updateDistanceFilters.value = true
        createFragmentNavigationRequest(fragmentTag = Constants.EXPLORE_FRAGMENT_TAG)
    }

    fun checkForSignedInUser() {
        _checkForSignedInUser.value = true
    }
}
