package com.mindbodyonline.connect.activities.details;

import static com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel.BusinessDetailsClickEvent.DealDetailsDrawerExpand;
import static com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel.BusinessDetailsClickEvent.ViewPricing;
import static com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel.BusinessDetailsClickEvent.ViewSchedule;
import static com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_CART;
import static com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_DEAL;
import static com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_PAYMENT_METHOD;

import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.text.Html;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewGroup.LayoutParams;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextSwitcher;
import android.widget.TextView;

import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.compose.ui.platform.ComposeView;
import androidx.compose.ui.platform.ViewCompositionStrategy;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import androidx.lifecycle.ViewModelProvider;

import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.snackbar.Snackbar;
import com.mindbodyonline.android.api.sales.MBSalesApi;
import com.mindbodyonline.android.api.sales.model.payments.PaymentMethod;
import com.mindbodyonline.android.api.sales.model.pos.cart.Cart;
import com.mindbodyonline.android.api.sales.model.pos.cart.CartAbandonReason;
import com.mindbodyonline.android.api.sales.model.pos.catalog.CatalogItem;
import com.mindbodyonline.android.api.sales.model.pos.deals.Deal;
import com.mindbodyonline.android.util.SafeGson;
import com.mindbodyonline.android.util.TaskCallback;
import com.mindbodyonline.android.util.api.model.ProgramType;
import com.mindbodyonline.connect.BuildConfig;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.activities.custom.MBCompatActivity;
import com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel;
import com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel.BusinessDetailsClickEvent;
import com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel.BusinessDetailsEvents;
import com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel.BusinessDetailsEvents.FavoritedSelected;
import com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel.BusinessDetailsEvents.ShareSelected;
import com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel.BusinessDetailsEvents.ViewPricingSelected;
import com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel.BusinessDetailsEvents.ViewScheduleSelected;
import com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModel.BusinessDetailsEventsListener;
import com.mindbodyonline.connect.activities.details.viewmodels.BusinessViewModelFactory;
import com.mindbodyonline.connect.activities.details.viewmodels.DealDetailsViewModel;
import com.mindbodyonline.connect.activities.list.services.PricingListActivity;
import com.mindbodyonline.connect.activities.list.services.RoutineServicesActivity;
import com.mindbodyonline.connect.activities.schedule.BusinessScheduleActivity;
import com.mindbodyonline.connect.analytics.OriginComponent;
import com.mindbodyonline.connect.analytics.OriginView;
import com.mindbodyonline.connect.businessdetails.BusinessDetailsViewModel;
import com.mindbodyonline.connect.businessdetails.BusinessDetailsViewModelFactory;
import com.mindbodyonline.connect.common.components.ImageViewPager;
import com.mindbodyonline.connect.common.utilities.ContextUtilKt;
import com.mindbodyonline.connect.common.utilities.ViewUtilKt;
import com.mindbodyonline.connect.login.AccountWorkflow;
import com.mindbodyonline.connect.quickbook.QuickBookDialog;
import com.mindbodyonline.connect.quickbook.QuickBookDialogV2;
import com.mindbodyonline.connect.quickbook.QuickBookViewModelV2;
import com.mindbodyonline.connect.share.ShareBusinessImageGenerator;
import com.mindbodyonline.connect.tealium.InventoryType;
import com.mindbodyonline.connect.tealium.PageName;
import com.mindbodyonline.connect.tealium.TrackingHelperUtils;
import com.mindbodyonline.connect.utils.AnalyticsUtils;
import com.mindbodyonline.connect.utils.Constants;
import com.mindbodyonline.connect.utils.DealDetailsUtils;
import com.mindbodyonline.connect.utils.DeepLinkUtils;
import com.mindbodyonline.connect.utils.DialogUtils;
import com.mindbodyonline.connect.utils.DomainObjectUtils;
import com.mindbodyonline.connect.utils.ExtensionFunctionsKt;
import com.mindbodyonline.connect.utils.FamilyAccountsUtilsKt;
import com.mindbodyonline.connect.utils.IntentUtils;
import com.mindbodyonline.connect.utils.SharedPreferencesUtils;
import com.mindbodyonline.connect.utils.ToastUtils;
import com.mindbodyonline.connect.utils.Utils;
import com.mindbodyonline.connect.utils.api.ModelTranslationKt;
import com.mindbodyonline.connect.utils.api.dynamicpricing.HtmlAttribution;
import com.mindbodyonline.connect.utils.api.dynamicpricing.StudioGalleryImage;
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI;
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.LocationPricing;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.LocationSchedule;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.StudioDetails;
import com.mindbodyonline.data.StaticInstance;
import com.mindbodyonline.data.services.MBAuth;
import com.mindbodyonline.data.services.MBStaticCache;
import com.mindbodyonline.data.services.locator.ServiceLocator;
import com.mindbodyonline.domain.Location;
import com.mindbodyonline.domain.User;
import com.mindbodyonline.domain.dataModels.SubscriptionLevel;
import com.mindbodyonline.framework.abvariant.ABHelperUtils;
import com.mindbodyonline.framework.abvariant.DevelopmentFlag;
import com.mindbodyonline.framework.abvariant.FeatureFlag;
import com.mindbodyonline.ui.ComposeUtils;
import com.mindbodyonline.ui.screen.deals.model.CollapsibleDealsDrawerUiState;
import com.mindbodyonline.views.BusinessDetailsView;
import com.mindbodyonline.views.dialog.ConfirmationHUD;
import com.mindbodyonline.views.dialog.MBPayDialog;
import com.mindbodyonline.views.dialog.MaterialOptionDialog;
import com.mindbodyonline.views.dialog.UnboundedImageTextDialog;
import com.mindbodyonline.views.dialog.VerifyAccountDialog;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.util.Arrays;

import kotlin.Unit;

public class BusinessDetailsActivity extends MBCompatActivity implements BusinessDetailsEventsListener {

    public static final int GIFT_CARD_CHECKOUT = 8888;
    public static final int DELETE_REVIEW = 0xabcd;
    public static final int POS_CHECKOUT_FROM_CARDS = 999;
    private static final String SCREENSHOT_FILENAME = "MB_business_screenshot";
    private static final int FAVORITE_ADDED_DIALOG_TIME_MS = 6000;
    private static final long DIALOG_POST_DELAY_MS = 1000;

    private Location location;
    private Deal deal;
    private CatalogItem catalogItem;
    private BusinessDetailsView mDetailsView;
    private View mPricingButton;
    private View mScheduleButton;
    private View elevationShadow;
    private View favoriteHeart;
    private Uri screenshotUri;
    private ColorStateList lightColor = null;
    private ColorStateList darkColor = null;

    private long pageOpenTime;
    private ImageViewPager imagePager;
    private TextSwitcher imageAttributionText;
    private TextView getDealButton;
    private BusinessViewModel viewModel;
    private DealDetailsViewModel dealDetailsViewModel;
    private BusinessDetailsViewModel businessDetailsViewModel;

    BusinessDetailsViewModel.ScreenType screenType = BusinessDetailsViewModel.ScreenType.BUSINESS_DETAILS;

    public static Intent newIntent(@NotNull Context context, LocationReference locationReference) {
        return new Intent(context, BusinessDetailsActivity.class)
            .putExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE, locationReference);
    }

    public static Intent newIntent(
            @NotNull Context context,
            LocationReference locationReference,
            Deal deal,
            OriginView originView,
            OriginComponent originComponent
    ) {
        return new Intent(context, BusinessDetailsActivity.class)
                .putExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE, locationReference)
                .putExtra(KEY_BUNDLE_DEAL, SafeGson.toJson(deal))
                .putExtra(Constants.KEY_BUNDLE_ORIGIN_VIEW, originView)
                .putExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT, originComponent);
    }

    public static Intent newIntent(
            @NotNull Context context,
            LocationReference locationReference,
            Integer productId,
            OriginComponent originComponent
    ) {
        return new Intent(context, BusinessDetailsActivity.class)
                .putExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE, locationReference)
                .putExtra(Constants.KEY_BUNDLE_PRODUCT_ID, productId)
                .putExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT, originComponent);
    }

    public static Intent newIntent(
            @NotNull Context context,
            LocationReference locationReference,
            OriginView originView,
            OriginComponent originComponent
    ) {
        return new Intent(context, BusinessDetailsActivity.class)
                .putExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE, locationReference)
                .putExtra(Constants.KEY_BUNDLE_ORIGIN_VIEW, originView.getViewName())
                .putExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT, originComponent.getComponentName());
    }

    private int getLayoutResource() {
        if (ABHelperUtils.isBusinessDetailsRebrandActive()) {
            return R.layout.activity_business_details_v2;
        } else {
            return R.layout.activity_business_details;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        UserCheckoutFlowTracking.Companion.addUserFlowEvent(StudioDetails.INSTANCE);

        BusinessViewModelFactory viewModelFactory = new BusinessViewModelFactory(
            ServiceLocator.getLocationRepository(), ServiceLocator.getFavoriteLocationRepository(),
            this, null);
        viewModel = new ViewModelProvider(this, viewModelFactory).get(BusinessViewModel.class);

        dealDetailsViewModel = new ViewModelProvider(this).get(DealDetailsViewModel.class);

        BusinessDetailsViewModelFactory businessDetailsViewModelFactory = new BusinessDetailsViewModelFactory(SwamisAPI.getInstance(), this, null);
        businessDetailsViewModel = new ViewModelProvider(this, businessDetailsViewModelFactory).get(BusinessDetailsViewModel.class);

        pageOpenTime = SystemClock.elapsedRealtime();

        setContentView(getLayoutResource());
        initializeActionBar(findViewById(R.id.app_bar_layout));

        mDetailsView = findViewById(R.id.business_details_activity_main_view);
        mDetailsView.setOpenTime(pageOpenTime);
        mDetailsView.setEventsListener(this);

        favoriteHeart = findViewById(R.id.business_details_favorite_heart);

        elevationShadow = findViewById(R.id.elevation_shadow);

        mPricingButton = findViewById(R.id.business_details_pricing_button);

        mScheduleButton = findViewById(R.id.business_details_schedule_button);

        getDealButton = findViewById(R.id.tv_get_deal);

        imagePager = findViewById(R.id.business_details_image_pager);
        imagePager.initialize(getSupportFragmentManager());

        imageAttributionText = findViewById(R.id.studio_image_attribution);
        imageAttributionText.setFactory(() -> {
            TextView v = (TextView) LayoutInflater.from(BusinessDetailsActivity.this).inflate(R.layout.view_image_attribution, null);
            v.setLayoutParams(new FrameLayout.LayoutParams(LayoutParams.MATCH_PARENT,
                getResources().getDimensionPixelSize(R.dimen.business_image_attribution_view_height)));
            v.setMovementMethod(LinkMovementMethod.getInstance());
            return v;
        });
        imageAttributionText.setText("");

        imageAttributionText.setOutAnimation(AnimationUtils.loadAnimation(this, android.R.anim.fade_out));
        imageAttributionText.setInAnimation(AnimationUtils.loadAnimation(this, android.R.anim.fade_in));

        viewModel.getLocation().observe(this, this::handleLocationUpdate);

        LocationReference locationReference = getIntent().getParcelableExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE);
        viewModel.initialize(locationReference);

        // Deal details
        if (getIntent() != null) {
            deal = SafeGson.fromJson(getIntent().getStringExtra(KEY_BUNDLE_DEAL), Deal.class);
            mDetailsView.setDeal(deal);
        }
        mDetailsView.setActivityResultLauncher(registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    onActivityResult(Constants.DEAL_CHECKOUT_REQUESTCODE, result.getResultCode(), result.getData());
                }
        ));
    }

    private void handleLocationUpdate(Location fetchedLocation) {
        if (fetchedLocation != null) {
            this.location = fetchedLocation;

            if (ABHelperUtils.showBusinessFocussedDealDetailsScreen()) {
                // Could be business focussed deal details screen
                decideScreenTypeAndInitialize();
            } else {
                // Business details screen
                findViewById(R.id.schedule_pricing_button_area).setVisibility(View.VISIBLE);
            }

            setClickListeners();
            if (fetchedLocation.isOptedIntoConnect()) {
                checkAndAddUALFavorite();
                finishLoading();
            } else {
                showDeeplinkFailureAndFinish(0, R.string.deeplink_failure_business_message);
            }

            mDetailsView.setScreenType(screenType);
            if (screenType == BusinessDetailsViewModel.ScreenType.BUSINESS_DETAILS) {
                findViewById(R.id.average_ratings_container).setVisibility(View.VISIBLE);
                viewModel.trackBusinessDetailsView(
                        getIntent().getStringExtra(Constants.KEY_BUNDLE_ORIGIN_VIEW),
                        getIntent().getStringExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT),
                        location
                );
            }

        } else {
            showDeeplinkFailureAndFinish(0, R.string.deeplink_failure_business_message);
        }
    }

    private void decideScreenTypeAndInitialize() {
        LocationReference locationReference = getIntent().getParcelableExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE);
        if (deal == null && StaticInstance.selectedCatalogItem == null &&
                locationReference != null && getIntent().hasExtra(Constants.KEY_BUNDLE_PRODUCT_ID)) {
            // Coming from a deal deep link
            screenType = BusinessDetailsViewModel.ScreenType.BUSINESS_FOCUSSED_DEAL_DETAILS;
            initDealDetailsFromDeepLink(getIntent().getIntExtra(Constants.KEY_BUNDLE_PRODUCT_ID, 0));
        } else if (deal == null) {
            // Business details screen
            screenType = BusinessDetailsViewModel.ScreenType.BUSINESS_DETAILS;
            findViewById(R.id.schedule_pricing_button_area).setVisibility(View.VISIBLE);
        } else {
            // Business focussed deal details screen
            screenType = BusinessDetailsViewModel.ScreenType.BUSINESS_FOCUSSED_DEAL_DETAILS;
            if (!viewModel.getDealEligibilityStatusState().getValue()) {
                findViewById(R.id.schedule_pricing_button_area).setVisibility(View.VISIBLE);
            }
            refreshItemEligibility();
        }
    }

    private void initDealDetailsFromDeepLink(final int productID) {
        MBSalesApi.getCatalogItemById(location.getSiteId(), productID,
                response -> {
                    if (response != null) {
                        deal = DomainObjectUtils.convertToDeal(response, location);
                        catalogItem = response;
                        mDetailsView.setCatalogItem(catalogItem);
                        mDetailsView.setDeal(deal);
                        refreshItemEligibility();
                    } else {
                        showDeeplinkFailureAndFinish(R.string.deeplink_failure_deal_title, 0);
                    }
                }, error -> showDeeplinkFailureAndFinish(R.string.deeplink_failure_deal_title, 0));
    }

    private void refreshItemEligibility() {
        if (MBAuth.isGuestUser()) {
            getDealButton.setVisibility(View.VISIBLE);
            getDealButton.setEnabled(true);
        } else {
            CartAbandonReason reason = new CartAbandonReason();
            reason.setAsReset("Initializing deal details");

            // Display ineligible deal UI for enrollment specific deals if dev flag is OFF
            if (deal.getProgramType().equals(ProgramType.ENROLLMENT)
                    && !DevelopmentFlag.DEVELOPMENT_ENABLE_ENROLLMENT_DEAL_PURCHASE.isFeatureEnabled()) {
                initIneligibleDealUI();
                return;
            }

            MBSalesApi.deleteCart(
                    deal.getSubscriber().getId(),
                    reason,
                    response -> MBSalesApi.addItemToCart(
                            deal.getSubscriber().getId(),
                            deal.getSubscriberProductId(),
                            response1 -> {
                                catalogItem = response1.getItem();
                                mDetailsView.setCatalogItem(catalogItem);
                                initDealDetailsUi();
                                getDealButton.setVisibility(View.VISIBLE);
                                getDealButton.setEnabled(true);
                            },
                            error -> initIneligibleDealUI()
                    ),
                    error -> {
                        ToastUtils.showServerErrorToast();
                        finish();
                    });
        }
    }

    private void initIneligibleDealUI() {
        getDealButton.setVisibility(View.GONE);
        findViewById(R.id.schedule_pricing_button_area).setVisibility(View.VISIBLE);
        mDetailsView.setDealEligibilityStatus(false);
        viewModel.setDealEligibilityStatus(false);
        initDealDetailsUi();
    }

    private void initDealDetailsUi() {
        dealDetailsViewModel.trackDealDetailsViewed(
                (OriginView) getIntent().getSerializableExtra(Constants.KEY_BUNDLE_ORIGIN_VIEW),
                (OriginComponent) getIntent().getSerializableExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT),
                deal
        );
        switch (ABHelperUtils.getDealDetailsVariation()) {
            case TEST_2B: {
                adjustViewsForDealDetails(R.dimen.deal_details_buy_button_height);
                mDetailsView.initDealDetailsOfferLayout();
                break;
            }
            case TEST_2A: {
                adjustViewsForDealDetails(R.dimen.deal_details_drawer_and_buy_button_height);
                initDealDetailsDrawer();
                break;
            }
            case LEGACY: {
                // Unreachable, just a fail-safe
                findViewById(R.id.schedule_pricing_button_area).setVisibility(View.VISIBLE);
            }
        }
    }

    private void initDealDetailsDrawer() {
        if (deal != null) {
            String tagText = ExtensionFunctionsKt.getDealTag(
                    deal,
                    location,
                    getApplicationContext()
            );
            ComposeView dealDetailsDrawer = findViewById(R.id.cv_deal_details_drawer);
            dealDetailsDrawer.setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed.INSTANCE);
            ComposeUtils.setDealDetailsDrawerContent(
                    new CollapsibleDealsDrawerUiState(
                            tagText,
                            deal.getName(),
                            Arrays.asList(
                                    DealDetailsUtils.getSessionCountString(getApplicationContext(), deal),
                                    getResources().getString(deal.getIntroOfferType().getDescriptionStringRes()),
                                    getResources().getString(R.string.deal_expiry_date_formatter, deal.getExpirationString())
                            ),
                            viewModel.getExpandDealDetailsDrawerState(),
                            () -> {
                                onBusinessDetailsClickEvent(DealDetailsDrawerExpand.INSTANCE);
                                return Unit.INSTANCE;
                            },
                            viewModel.getDealEligibilityStatusState(),
                            getResources().getString(R.string.ineligible_deal_title),
                            getResources().getString(R.string.ineligible_deal_message, deal.getName()),
                            getResources().getString(DealDetailsUtils.getQualifiedServiceStringResource(deal)),
                            (Int) -> {
                                businessDetailsViewModel.viewServicesClicked(this, location, deal);
                                return Unit.INSTANCE;
                            }
                    ),
                    dealDetailsDrawer
            );
        }
    }

    private void adjustViewsForDealDetails(int dimenId) {
        View spaceBottom = findViewById(R.id.space_bottom);
        ViewGroup.LayoutParams layoutParams = spaceBottom.getLayoutParams();
        layoutParams.height = (int) getResources().getDimension(dimenId);
        spaceBottom.setLayoutParams(layoutParams);
    }

    private void initializeActionBar(AppBarLayout appBarLayout) {
        setActionBarTitle("");
        getSupportActionBar().setHomeAsUpIndicator(R.drawable.back_white);

        lightColor = ContextCompat.getColorStateList(this, R.color.white);
        darkColor = ContextUtilKt.getColorControlNormal(this);

        appBarLayout.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            boolean isShow = false;
            int scrollRange = -1;

            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                if (location == null) return;

                if (scrollRange == -1) {
                    scrollRange = appBarLayout.getTotalScrollRange();
                }
                boolean prev = isShow;
                isShow = scrollRange + verticalOffset == 0;
                if (isShow != prev) {
                    getToolbar().post(() -> {
                        setActionBarTheme(isShow);
                    });
                }
            }
        });
    }

    private void setActionBarTheme(boolean darkTheme) {
        if (getSupportActionBar() == null) return;

        if (darkTheme) {
            // The app bar layout is collapsed
            getSupportActionBar().setHomeAsUpIndicator(R.drawable.back_gray);
            tintMenu(getToolbar().getMenu(), darkColor);
            ContextUtilKt.showLightStatusBarOverlay(this, true);
            if (BuildConfig.DEBUG) {
                setActionBarTitle("(" + location.getSiteId() + ") " + location.getTitle());
            } else {
                setActionBarTitle(location.getTitle());
            }
            elevationShadow.setVisibility(View.VISIBLE);
        } else {
            // The app bar layout is expanded
            getSupportActionBar().setHomeAsUpIndicator(R.drawable.back_white);
            tintMenu(getToolbar().getMenu(), lightColor);
            ContextUtilKt.showLightStatusBarOverlay(this, false);
            setActionBarTitle("");
            elevationShadow.setVisibility(View.GONE);
        }
    }

    private void setClickListeners() {
        favoriteHeart.setOnClickListener(v -> {
            if (MBAuth.isGuestUser()) {
                AccountWorkflow.showLoginRequiredDialog(this, DeepLinkUtils.getLocationShareUri(location.getId(), location.getInventorySource()), "Studio detail");
            } else {
                toggleFavorited();
            }
        });

        mPricingButton.setOnClickListener(v -> {
            UserCheckoutFlowTracking.Companion.addUserFlowEvent(LocationPricing.INSTANCE);
            Intent servicesPricingIntent = PricingListActivity.newIntent(this, ModelTranslationKt.toLocationReference(location));
            startActivity(servicesPricingIntent);
            viewModel.sendBusinessDetailsEvent(ViewPricingSelected.INSTANCE);
            viewModel.trackBusinessDetailsClick(ViewPricing.INSTANCE, screenType);
        });

        mScheduleButton.setOnClickListener(v -> {
            UserCheckoutFlowTracking.Companion.addUserFlowEvent(LocationSchedule.INSTANCE);
            if (FeatureFlag.CLASS_SCHEDULE_UI_REFACTOR.isFeatureEnabled()) {
                Intent businessScheduleIntent = BusinessScheduleActivity.newIntent(this, ModelTranslationKt.toLocationReference(location), OriginView.BUSINESS_DETAILS, null);
                businessScheduleIntent.putExtra(Constants.KEY_BUNDLE_ANALYTICS_PREFIX, "Location Details");
                startActivity(businessScheduleIntent);
            } else {
                Intent routinesIntent = RoutineServicesActivity.newIntent(this, ModelTranslationKt.toLocationReference(location), OriginView.BUSINESS_DETAILS, null);
                routinesIntent.putExtra(Constants.KEY_BUNDLE_ANALYTICS_PREFIX, "Location Details");
                startActivity(routinesIntent);
            }
            viewModel.sendBusinessDetailsEvent(ViewScheduleSelected.INSTANCE);
            viewModel.trackBusinessDetailsClick(ViewSchedule.INSTANCE, screenType);
        });

        imagePager.setOnImageChangedListener(result -> {
            if (result != null) {
                if (location != null && location.getGalleryImages() != null
                        && location.getGalleryImages().length != 0 && result < location.getGalleryImages().length) {
                    setImageAttributionText(location.getGalleryImages()[result]);
                    viewModel.sendBusinessDetailsEvent(new BusinessDetailsEvents.ImageCarouselSelected(result));
                }
            }
        });

        if (getDealButton != null) {
            getDealButton.setOnClickListener(v -> {
                if (!deal.getProgramType().equals(ProgramType.ENROLLMENT)
                        || DevelopmentFlag.DEVELOPMENT_ENABLE_ENROLLMENT_DEAL_PURCHASE.isFeatureEnabled()) {
                    // Mixpanel
                    dealDetailsViewModel.trackDealDetailsBuyButtonInteracted(deal);
                    // Tealium
                    TrackingHelperUtils.INSTANCE.trackCheckoutBook(location, deal.getName(),
                            InventoryType.PACKAGE, PageName.DEALS, null, null);

                    // Guest user
                    if (MBAuth.isGuestUser()) {
                        AccountWorkflow.showLoginRequiredDialog(this,
                                DeepLinkUtils.getProductDetailsUri(location.getId(), deal.getSubscriberProductId()),
                                "Intro Offers");
                        return;
                    }

                    User user = MBAuth.getUser();

                    // Unverified user
                    if (user != null && !user.isVerified()) {
                        VerifyAccountDialog dialog = new VerifyAccountDialog();
                        dialog.show(getSupportFragmentManager(), VerifyAccountDialog.class.getSimpleName());
                        return;
                    }

                    // Verified logged in user
                    if (FamilyAccountsUtilsKt.isFamilyAccountType() || !DevelopmentFlag.Development_QBV2.isFeatureEnabled()) {
                        QuickBookDialog dialog = new QuickBookDialog()
                                .setBusinessDetailsSuccessCallback(result -> {
                                    if (result != null) {
                                        finishDealPurchase(
                                                result.getPaymentMethod(),
                                                result.getCart(),
                                                result.getShowAttributionSurvey(),
                                                result.getOrderId()
                                        );
                                    }
                                });
                        dialog.setCatalogItem(location, catalogItem);
                        dialog.setSuppressPurchaseConfirmationDialog(true);
                        dialog.setCatalogItemTrackingData(OriginView.DEAL_DETAILS, OriginComponent.DEAL_DETAILS_BUY_BUTTON);
                        dialog.show(getSupportFragmentManager(), QuickBookDialog.TAG);
                    } else {
                        String passRefJson = ServiceLocator.getRecommendedRepository()
                                .getPassRefJsonForDeal(deal.getSubscriberProductId());
                        QuickBookViewModelV2.QuickBookInitializer initializer = new QuickBookViewModelV2.QuickBookInitializer(QuickBookViewModelV2.QuickBookType.Alacarte,
                                passRefJson,
                                ModelTranslationKt.toLocationReference(location),
                                true, null, null, null, null,
                                deal.getName(), deal.getExpirationString());
                        QuickBookDialogV2.newInstance(initializer, () -> {
                            finishDealPurchase(null, null, false, 0L);
                            return Unit.INSTANCE;
                        }).show(getSupportFragmentManager(), QuickBookDialogV2.TAG);
                    }

                } else {
                    // Do nothing - Eligibility comes back as false for Enrollment/Event specific deals
                }
            });
        }

        if (ABHelperUtils.getDealDetailsVariation() == ABHelperUtils.DealDetailsVariation.TEST_2A) {
            NestedScrollView mainContentNestedScrollView = findViewById(R.id.main_content_scroll_container);
            mainContentNestedScrollView.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
                // Check if user is scrolling down and if the drawer is expanded
                if (scrollY > oldScrollY && viewModel.getExpandDealDetailsDrawerState().getValue()) {
                    viewModel.getExpandDealDetailsDrawerState().setValue(false);
                }
            });
        }
    }

    private void finishDealPurchase(PaymentMethod paymentMethod, Cart cart, boolean showAttributionSurvey, long orderId) {
        StaticInstance.refreshPasses = true;

        DialogUtils.showItemPurchasedConfirmationDialog(
                this,
                getSupportFragmentManager(),
                ConfirmationHUD.DEAL_MODE,
                location,
                catalogItem,
                null,
                paymentMethod,
                cart,
                null,
                showAttributionSurvey,
                orderId,
                deal
        );

        dealDetailsViewModel.logDealDetailsOptimizelyPurchasedEvent();
        TrackingHelperUtils.INSTANCE.trackCheckoutConfirmation(location, deal.getPrice(), null,
                false, null, deal.getName(), null,
                InventoryType.PACKAGE, "credit_card", null, null, null,
                null, null, null);
        finish();
    }

    public void showDeeplinkFailureAndFinish(Integer headerRes, Integer messageRes) {
        MaterialOptionDialog dialog = new MaterialOptionDialog();
        dialog.setText(headerRes,
                        messageRes,
                        R.string.ok_button_text,
                        0)
                .setButton1Callback(result -> {
                    if (result != null) {
                        result.dismiss();
                    }
                }).setCancelCallback(result -> finish())
                .show(getSupportFragmentManager(), MaterialOptionDialog.class.getSimpleName());
    }

    @Override
    protected void onResume() {
        super.onResume();

        StaticInstance.sourceType = AnalyticsUtils.SOURCE_TYPE_LOCATION_DEALS_CARD;
        StaticInstance.businessDetailsInBackstack = true;
    }

    private void checkAndAddUALFavorite() {
        if (getIntent().getBooleanExtra(Constants.KEY_BUNDLE_UA_LINK_AUTOFAVORITE, false)
            && !MBStaticCache.getInstance().isFavoriteBusiness(location)) {
            if (!location.isOptedIntoConnect()) {
                Snackbar.make(getWindow().getDecorView(), getString(R.string.business_optout_subtitle, location.getName()), Snackbar.LENGTH_SHORT).show();
                finish();
            } else {
                viewModel.addFavorite(location);
                showFavoriteAddedDialog();
            }
        }
    }

    private void showFavoriteAddedDialog() {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            String message = getString(R.string.favorite_added, location.getStudioName());
            UnboundedImageTextDialog dialog = new UnboundedImageTextDialog();
            dialog.setData(R.drawable.oval_7_copy_shape_copy_3, Utils.highlightInstanceString(
                location.getStudioName(),
                message, ActivityCompat.getColor(BusinessDetailsActivity.this, R.color.magenta)));
            dialog.setTimeInMsToDismiss(FAVORITE_ADDED_DIALOG_TIME_MS);  // default is 5000
            dialog.show(getSupportFragmentManager(), "UserActionLinkFavoriteDialog");
        }, DIALOG_POST_DELAY_MS);
    }

    private void finishLoading() {
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setHomeButtonEnabled(true);
        }

        setLocation(location);
        mDetailsView.stopLoading();
    }

    private void setLocation(Location location) {
        favoriteHeart.setActivated(MBStaticCache.getInstance().isFavoriteBusiness(location));
        mDetailsView.setBusiness(location);

        if (location.getSubscriptionLevel() == SubscriptionLevel.ConnectListing) {
            findViewById(R.id.schedule_pricing_button_area).setVisibility(View.GONE);
        }

        mPricingButton.setVisibility(location.getOnlineStoreActive() ? View.VISIBLE : View.GONE);

        setGalleryImages(location.getGalleryImages());
    }

    private void setGalleryImages(StudioGalleryImage[] galleryImages) {
        if (!Utils.isEmpty(galleryImages)) {
            // If any of the images have attributions, show the attribution bar
            boolean hasImageAttributions = false;
            String[] imageArray = new String[galleryImages.length];
            for (int i = 0; i < galleryImages.length; i++) {
                imageArray[i] = galleryImages[i].getUrl();

                hasImageAttributions |= !Utils.isEmpty(galleryImages[i].getHtmlAttributions());
            }
            imagePager.setImages(imageArray);
            imageAttributionText.setVisibility(hasImageAttributions ? View.VISIBLE : View.GONE);
            setImageAttributionText(galleryImages[0]);
        } else {
            imagePager.setImages(null);
            imageAttributionText.setVisibility(View.GONE);
        }
    }

    private void setImageAttributionText(StudioGalleryImage image) {
        if (image == null || Utils.isEmpty(image.getHtmlAttributions())) {
            imageAttributionText.setText("");
        } else {
            StringBuilder htmlLinks = new StringBuilder();

            // Constructing the HTML link is easier than spannables
            for (HtmlAttribution attribution : image.getHtmlAttributions()) {
                if (htmlLinks.length() > 0) htmlLinks.append(",");
                htmlLinks.append(" <a href='")
                    .append(attribution.getUrl())
                    .append("'>")
                    .append(attribution.getText())
                    .append("</a>");
            }

            imageAttributionText.setText(Html.fromHtml(getString(R.string.photo_by, htmlLinks.toString())));
        }
    }

    private void toggleFavorited() {
        boolean isFavorited = !favoriteHeart.isActivated();
        location.setUserFavoriteLocation(isFavorited);
        favoriteHeart.setActivated(isFavorited);
        viewModel.sendBusinessDetailsEvent(new FavoritedSelected(isFavorited));
        if (!isFavorited) {
            viewModel.removeFavorite(location);
        } else {
            viewModel.addFavorite(location);
        }

        //((ActionBarActivity) getContext()).setResult(Activity.RESULT_OK);
    }

    @NotNull
    private String getFilename() {
        return SCREENSHOT_FILENAME + location.getId() + ".jpg";
    }

    @Override
    protected void onDestroy() {
        StaticInstance.selectedCatalogItem = null;
        if (screenshotUri != null) {
            new File(screenshotUri.getPath()).delete();
        }
        //Also including this here (as well as onStop)in case the activity is removed from
        // the backstack via other means
        StaticInstance.businessDetailsInBackstack = false;
        super.onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case GIFT_CARD_CHECKOUT:
                break;
            case Constants.DEAL_CHECKOUT_REQUESTCODE:
                if (resultCode == RESULT_OK) {
                    PaymentMethod paymentMethod = SafeGson.fromJson(data.getStringExtra(KEY_BUNDLE_PAYMENT_METHOD), PaymentMethod.class);
                    Cart cart = SafeGson.fromJson(data.getStringExtra(KEY_BUNDLE_CART), Cart.class);
                    boolean showAttributionSurvey = data.getBooleanExtra(Constants.KEY_BUNDLE_SHOW_ATTRIBUTION_SURVEY, false);
                    long orderId = data.getLongExtra(Constants.KEY_BUNDLE_ORDER_ID, 0L);
                    DialogUtils.showItemPurchasedConfirmationDialog(
                            this,
                            getSupportFragmentManager(),
                            ConfirmationHUD.DEAL_MODE,
                            location,
                            catalogItem,
                            null,
                            paymentMethod,
                            cart,
                            null,
                            showAttributionSurvey,
                            orderId,
                            null
                    );
                }
                break;
            case DELETE_REVIEW:
                if (resultCode == RESULT_OK) {
                    mDetailsView.removeReview(data.getLongExtra(ReviewDetailsActivity.DELETED_REVIEW_KEY, -1));
                }
                break;
            case POS_CHECKOUT_FROM_CARDS:
                // FIXME should pass back relevant classTypeObject fields instead of storing it statically
                if (StaticInstance.selectedClassTypeObject != null) {
                    DialogUtils.showClassOrEventConfirmationDialog(
                            this,
                            StaticInstance.selectedClassTypeObject,
                            null,
                            null,
                            null,
                            location,
                            resultCode,
                            false,
                            0L,
                            null
                    );
                    mDetailsView.updateClass(StaticInstance.selectedClassTypeObject);
                    StaticInstance.selectedClassTypeObject = null;
                }
                break;
            case Constants.APPOINTMENT_BOOK_REQUEST_CODE:
                if (resultCode != RESULT_CANCELED) {
                    DialogUtils.showAppointmentBookingConfirmationDialog(getSupportFragmentManager(), null, data.getBooleanExtra(Constants.KEY_BUNDLE_IS_REQUEST, false),
                        new TaskCallback() {
                            @Override
                            public void onTaskComplete(Object result) {
                                if (!SharedPreferencesUtils.userSeenMBPayDialog()) {
                                    MBPayDialog dialog = new MBPayDialog();
                                    dialog.show(getSupportFragmentManager(), MBPayDialog.class.getSimpleName());
                                    SharedPreferencesUtils.setUserSeenMBPayDialog(true);
                                }
                            }
                        });
                }
                break;
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.business_details, menu);
        // Tint for expanded app bar
        tintMenu(menu, lightColor);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.menu_item_share:
                if (location != null) {
                    // hide menu items for screenshot
                   viewModel.sendBusinessDetailsEvent(ShareSelected.INSTANCE);
                    // ensure we don't have multiple share files for this class
                    if (screenshotUri != null) {
                        IntentUtils.shareBusinessIntent(this, location, screenshotUri);
                    } else if (!ABHelperUtils.isBusinessDetailsRebrandActive() && location.getTotalConnectRatings() > 0) {
                        // RatingBar wasn't setting its progress for the share image, so I just
                        //  draw the current one to a bitmap and use that
                        ViewUtilKt.afterMeasured(findViewById(R.id.business_details_rating_bar), v -> {
                            Bitmap b = Bitmap.createBitmap(v.getMeasuredWidth(), v.getMeasuredHeight(), Bitmap.Config.ARGB_8888);
                            Canvas c = new Canvas(b);
                            v.draw(c);

                            loadShareImage(b);

                            return Unit.INSTANCE;
                        });
                    } else {
                        loadShareImage(null);
                    }
                }
                return true;
            case android.R.id.home:
                onBackPressed();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    @Override protected void onStop() {
        super.onStop();
        if (isFinishing()) {
            StaticInstance.businessDetailsInBackstack = false;
        }
    }

    private void loadShareImage(@Nullable Bitmap ratingStarsBitmap) {
        new ShareBusinessImageGenerator(
            location,
            MBAuth.getUser(),
            this.<ImageView>findViewById(R.id.business_icon).getDrawable(),
            ratingStarsBitmap,
            findViewById(R.id.business_details_activity_main_content),
            getFilename())
            .generate(uri -> {
                screenshotUri = uri;
                IntentUtils.shareBusinessIntent(this, location, uri);
                return Unit.INSTANCE;
            });
    }

    @Override public void onBusinessDetailsEvent(@NonNull BusinessDetailsEvents event) {
        viewModel.sendBusinessDetailsEvent(event);
    }

    @Override
    public void onBusinessDetailsClickEvent(@NonNull BusinessDetailsClickEvent event) {
        viewModel.trackBusinessDetailsClick(event, screenType);
    }
}
