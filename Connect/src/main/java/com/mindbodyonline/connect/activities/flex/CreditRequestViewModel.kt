package com.mindbodyonline.connect.activities.flex

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.activities.flex.CreditRequestReasons.values
import com.mindbodyonline.connect.common.repository.SwamiApiDataSource
import com.mindbodyonline.connect.common.utilities.removeBookingFromCache
import com.mindbodyonline.connect.widgets.v3.refreshWidget
import com.mindbodyonline.domain.connv1.Visit

class CreditRequestViewModel(val swamiApiDataSource: SwamiApiDataSource) : ViewModel() {
    private val reasons = mutableSetOf<CreditRequestReasons>()
    private val _isReadyProceed = MutableLiveData<ReadyToProceed>()
    val isReadyProceed: LiveData<ReadyToProceed> = _isReadyProceed
    private val _visitCancelResult = MutableLiveData<CreditRequestResult>()
    val visitCancelResult: LiveData<CreditRequestResult> = _visitCancelResult
    var visit: Visit? = null
    private var previousReadyToProceed: ReadyToProceed? = null

    private var details: String? = null

    fun storeRequestReason(requestViewId: Int, selected: Boolean) {
        CreditRequestReasons.fromViewId(requestViewId)?.let { reason ->
            if (selected) reasons.add(reason) else reasons.remove(reason)
            if (reasons.contains(CreditRequestReasons.OTHER)) {
                postReadyToProceedIfNecessary(if (details.isNullOrBlank()) ReadyToProceed.NOT_READY_DETAILS else ReadyToProceed.READY)
            } else {
                postReadyToProceedIfNecessary(if (reasons.size > 0) ReadyToProceed.READY else ReadyToProceed.NOT_READY)
            }
        }
    }

    fun storeReasonDetails(newDetails: String?) {
        details = newDetails
        if (reasons.contains(CreditRequestReasons.OTHER)) {
            postReadyToProceedIfNecessary(if (newDetails?.isNotBlank() == true) ReadyToProceed.READY else ReadyToProceed.NOT_READY_DETAILS)
        }
    }

    fun continueSelected() =
        visit?.BookingRefJson?.let { bookingRefJson ->
            swamiApiDataSource.requestRefund(
                    bookingRefJson = bookingRefJson,
                    successListener = {
                        visit.removeBookingFromCache()
                        _visitCancelResult.postValue(CreditRequestResult.CreditRequestSuccess())
                        ConnectApp.getInstance()?.let {
                            refreshWidget(it)
                        }
                    },
                    errorListener = {
                        _visitCancelResult.postValue(
                                when (it.networkResponse.statusCode) {
                                    409 -> CreditRequestResult.CreditRequestAlreadyExists()
                                    422 -> CreditRequestResult.CreditRequestInvalid()
                                    else -> CreditRequestResult.CreditRequestGeneralResult()
                                }
                        )
                    }
            )
        } ?: _visitCancelResult.postValue(CreditRequestResult.CreditRequestNullRefJson())

    /**
     * To prevent livedata spamming, will post to ready to proceed live data only if the value has changed
     * from the last livedata post
     */
    private fun postReadyToProceedIfNecessary(newValue: ReadyToProceed) {
        newValue.takeIf { it != previousReadyToProceed }?.let {
            previousReadyToProceed = it
            _isReadyProceed.postValue(it)
        }
    }
}

sealed class CreditRequestResult {
    class CreditRequestSuccess : CreditRequestResult()
    class CreditRequestAlreadyExists : CreditRequestResult()
    class CreditRequestInvalid : CreditRequestResult()
    class CreditRequestNullRefJson : CreditRequestResult()
    class CreditRequestGeneralResult : CreditRequestResult()
}

enum class CreditRequestReasons(val id: Int, val analyticsString: String) {
    AUDIO(R.id.selector_reason_audio, "Poor audio quality"),
    VIDEO(R.id.selector_reason_video, "Poor video quality"),
    ACCESS(R.id.selector_reason_no_access, "Couldn't access class"),
    LINK(R.id.selector_reason_no_link, "No link provided"),
    OTHER(R.id.selector_reason_other, "Other");

    companion object {
        private val map = values().associateBy(CreditRequestReasons::id)
        fun fromViewId(viewId: Int) = map[viewId]
    }
}

enum class ReadyToProceed {
    READY,
    NOT_READY,
    NOT_READY_DETAILS
}