package com.mindbodyonline.connect.activities.details;

import static com.mindbodyonline.connect.common.utilities.EnhanceReviewUtilsKt.fetchSummaryAttributesByType;
import static com.mindbodyonline.connect.quickbook.QuickBookDialog.KEY_SELECTED_USER;
import static com.mindbodyonline.connect.utils.FamilyAccountsUtilsKt.isFamilyAccountType;
import static com.mindbodyonline.connect.utils.api.ModelTranslationKt.getAverageRatingModel;

import android.Manifest;
import android.app.Activity;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.text.Html;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.StringRes;
import androidx.compose.ui.platform.ComposeView;
import androidx.compose.ui.text.AnnotatedString;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;
import androidx.lifecycle.ViewModelProvider;

import com.android.volley.Response;
import com.android.volley.Response.ErrorListener;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.material.snackbar.Snackbar;
import com.mindbodyonline.android.api.sales.model.enums.CServiceCategoryType;
import com.mindbodyonline.android.api.sales.model.pos.catalog.CatalogItem;
import com.mindbodyonline.android.api.sales.util.CartItemUtil;
import com.mindbodyonline.android.util.TaskCallback;
import com.mindbodyonline.android.util.log.MBLog;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.activities.MainActivity;
import com.mindbodyonline.connect.activities.custom.MBCompatActivity;
import com.mindbodyonline.connect.activities.list.ReviewListActivity;
import com.mindbodyonline.connect.activities.list.services.classes.ClassStaffDetailsActivity;
import com.mindbodyonline.connect.activities.workflow.LiabilityReleaseActivity;
import com.mindbodyonline.connect.activities.workflow.RequiredFieldsActivity;
import com.mindbodyonline.connect.activities.workflow.booking.FullScreenConfirmationScreenActivity;
import com.mindbodyonline.connect.analytics.OriginComponent;
import com.mindbodyonline.connect.analytics.OriginView;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel.ClassDetailsEvents.AddressSelected;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel.ClassDetailsEvents.BusinessDetailsSelected;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel.ClassDetailsEvents.ClassDetailsShareSelected;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel.ClassDetailsEvents.ClassDetailsShown;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel.ClassDetailsEvents.ClassDetailsStaffBioSelected;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel.ClassDetailsEvents.MapSelected;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel.ClassDetailsEvents.NextSelected;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel.ClassDetailsEvents.ReadMoreSelected;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel.ClassDetailsEvents.ReviewsReadMoreSelected;
import com.mindbodyonline.connect.classes.ClassTypeDetailsViewModel.ClassDetailsEvents.ReviewsSeeAllSelected;
import com.mindbodyonline.connect.common.components.AmenitiesDetailCard;
import com.mindbodyonline.connect.common.components.AverageRatingView;
import com.mindbodyonline.connect.common.components.BusinessDetailCard;
import com.mindbodyonline.connect.common.components.ClassDetailCard;
import com.mindbodyonline.connect.common.components.ClassDetailViewModel;
import com.mindbodyonline.connect.common.components.ExperienceModel;
import com.mindbodyonline.connect.common.components.HighlightsDetailView;
import com.mindbodyonline.connect.common.components.RatingCard;
import com.mindbodyonline.connect.common.components.RatingCardViewModel;
import com.mindbodyonline.connect.common.components.SeeAllSubHeader;
import com.mindbodyonline.connect.common.components.TextCard;
import com.mindbodyonline.connect.common.components.TextCardViewModel;
import com.mindbodyonline.connect.common.components.TimeDatePriceViewModel;
import com.mindbodyonline.connect.common.repository.LocationRepository;
import com.mindbodyonline.connect.common.repository.PerformanceName;
import com.mindbodyonline.connect.common.utilities.StringUtilKt;
import com.mindbodyonline.connect.common.utilities.ViewUtilKt;
import com.mindbodyonline.connect.common.viewmodeladapters.BusinessDetailCardViewModelAdapter;
import com.mindbodyonline.connect.common.viewmodeladapters.TimeDatePriceViewModelAdapter;
import com.mindbodyonline.connect.quickbook.QuickBookDialog;
import com.mindbodyonline.connect.quickbook.QuickBookDialogV2;
import com.mindbodyonline.connect.quickbook.QuickBookViewModel;
import com.mindbodyonline.connect.quickbook.QuickBookViewModelV2.QuickBookInitializer;
import com.mindbodyonline.connect.quickbook.QuickBookViewModelV2.QuickBookType;
import com.mindbodyonline.connect.quickbook.QuickBookViewModelV2.QuickbookFailureReason;
import com.mindbodyonline.connect.reviews.StudioReviewsRepository;
import com.mindbodyonline.connect.share.ShareClassImageGenerator;
import com.mindbodyonline.connect.tealium.InventoryType;
import com.mindbodyonline.connect.tealium.PageName;
import com.mindbodyonline.connect.tealium.TrackingHelperUtils;
import com.mindbodyonline.connect.utils.AlarmUtils;
import com.mindbodyonline.connect.utils.AnalyticsUtils;
import com.mindbodyonline.connect.utils.AnimationUtils;
import com.mindbodyonline.connect.utils.BookabilitySourceScreen;
import com.mindbodyonline.connect.utils.BookingUtils;
import com.mindbodyonline.connect.utils.BreadcrumbsUtils;
import com.mindbodyonline.connect.utils.BusinessDetailsCardUtils;
import com.mindbodyonline.connect.utils.CalendarUtils;
import com.mindbodyonline.connect.utils.Constants;
import com.mindbodyonline.connect.utils.DeepLinkUtils;
import com.mindbodyonline.connect.utils.DialogUtils;
import com.mindbodyonline.connect.utils.DialogUtilsKtKt;
import com.mindbodyonline.connect.utils.DomainObjectUtils;
import com.mindbodyonline.connect.utils.FamilyAccountsUtilsKt;
import com.mindbodyonline.connect.utils.GeoLocationUtils;
import com.mindbodyonline.connect.utils.GeofenceUtils;
import com.mindbodyonline.connect.utils.IntentUtils;
import com.mindbodyonline.connect.utils.KeyboardUtils;
import com.mindbodyonline.connect.utils.MBPhoneUtils;
import com.mindbodyonline.connect.utils.PaymentUtils;
import com.mindbodyonline.connect.utils.SharedPreferencesUtils;
import com.mindbodyonline.connect.utils.Switches;
import com.mindbodyonline.connect.utils.ToastUtils;
import com.mindbodyonline.connect.utils.Utils;
import com.mindbodyonline.connect.utils.ViewUtils;
import com.mindbodyonline.connect.utils.api.APIWorkflowUtil;
import com.mindbodyonline.connect.utils.api.ModelTranslationKt;
import com.mindbodyonline.connect.utils.api.connv3.model.FavoriteStaff;
import com.mindbodyonline.connect.utils.api.dynamicpricing.cloudsearch.CloudSearchUtils;
import com.mindbodyonline.connect.utils.api.fitnessactivity.FitnessActivityViewModel;
import com.mindbodyonline.connect.utils.api.fitnessactivity.fitbit.FitBitAPI;
import com.mindbodyonline.connect.utils.api.fitnessactivity.fitbit.FitbitViewModel;
import com.mindbodyonline.connect.utils.api.fitnessactivity.googlefit.GoogleFitAPI;
import com.mindbodyonline.connect.utils.api.fitnessactivity.googlefit.GoogleFitViewModel;
import com.mindbodyonline.connect.utils.api.gateway.model.ResponseSummaryAttributes;
import com.mindbodyonline.connect.utils.api.gateway.model.ReviewSummaryData;
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference;
import com.mindbodyonline.connect.utils.api.gateway.serialization.GatewayResourceType;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.ClassDetails;
import com.mindbodyonline.connect.widgets.v3.WidgetUtils;
import com.mindbodyonline.data.StaticInstance;
import com.mindbodyonline.data.services.MBAuth;
import com.mindbodyonline.data.services.MBStaticCache;
import com.mindbodyonline.data.services.locator.ServiceLocator;
import com.mindbodyonline.domain.BaseVisit;
import com.mindbodyonline.domain.BookabilityStatus;
import com.mindbodyonline.domain.ClassPaymentStatus;
import com.mindbodyonline.domain.ClassStatusMessage;
import com.mindbodyonline.domain.ClassTypeObject;
import com.mindbodyonline.domain.ClassTypeVisit;
import com.mindbodyonline.domain.Enrollment;
import com.mindbodyonline.domain.FavoriteClass;
import com.mindbodyonline.domain.Location;
import com.mindbodyonline.domain.Rating;
import com.mindbodyonline.domain.Staff;
import com.mindbodyonline.domain.User;
import com.mindbodyonline.domain.Visit;
import com.mindbodyonline.domain.VisitCancelStatus;
import com.mindbodyonline.framework.abvariant.DevelopmentFlag;
import com.mindbodyonline.framework.abvariant.FeatureFlag;
import com.mindbodyonline.ui.ComposeUtils;
import com.mindbodyonline.ui.screen.waitlist.WaitlistConsentScreenEvents;
import com.mindbodyonline.ui.screen.waitlist.WaitlistConsentScreenUiState;
import com.mindbodyonline.ui.util.AnnotatedStringHelperKt;
import com.mindbodyonline.views.ExperienceRatingCard;
import com.mindbodyonline.views.FitnessActivityBadgeView;
import com.mindbodyonline.views.LoadingOverlay;
import com.mindbodyonline.views.LoadingOverlayWhite;
import com.mindbodyonline.views.MiniLocationView;
import com.mindbodyonline.views.RateClassView;
import com.mindbodyonline.views.dialog.EnhanceRateReviewDialog;
import com.mindbodyonline.views.dialog.InAppPurchasesBlockedDialog;
import com.mindbodyonline.views.dialog.LivestreamInfoDialog;
import com.mindbodyonline.views.dialog.MaterialOptionDialog;
import com.mindbodyonline.views.dialog.RateReviewDialog;
import com.mindbodyonline.views.dialog.SignIntoClassDialog;
import com.mindbodyonline.views.dialog.VerifyAccountDialog;
import com.mindbodyonline.views.dialog.familyaccounts.FamilyAccount;
import com.mindbodyonline.views.dialog.nonceredirect.NonceRedirectDialog;
import com.mindbodyonline.views.dialog.scheduleitempricing.GuestScheduleItemPricingDialog;
import com.mindbodyonline.views.staff.ClassTypeStaffView;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;

/**
 * Displays detailed information about a {@link ClassTypeObject}
 */
public class ClassTypeDetailsActivity extends MBCompatActivity implements View.OnClickListener,
    GoogleApiClient.ConnectionCallbacks, GoogleApiClient.OnConnectionFailedListener {
    public static final String TYPE_EXTRA_STRING = "type";
    public static final int CLASS_DETAILS_REQUEST_CODE = 1337;
    public static final String STARTED_FROM_STAFF_CLASSES = "Started from staff's classes";
    //    private final static String TAG = ClassTypeDetailsActivity.class.getCanonicalName();
    private static final String SCREENSHOT_FILENAME = "MB_screenshot";
    private static final int NUM_REVIEWS_TO_RETURN = 3;
    private static final int PAGE_NUMBER = 0;
    // Data Fields
    private ClassTypeObject classTypeObject;
    private BigDecimal dropInPrice;
    private String[] secondaryCategories;
    private Type classType;
    private ClassPaymentStatus paymentStatus;
    private VisitCancelStatus cancelStatus;
    private boolean isDynamicPricePurchased;
    private CatalogItem preselectedPricingOption;
    private Location location;
    private ViewGroup root;
    private TextView capacityCountHeader;
    private ClassDetailCard classDetailCard;
    //    private TextView eventRoomName;
    private TextCard descriptionCard;
    private TextCard cancellationPolicyCard;
    private TextCard prerequisitesCard;
    private MiniLocationView miniLocationView;
    private SeeAllSubHeader aboutStudioHeader;
    private BusinessDetailCard businessDetailCard;
    private AmenitiesDetailCard amenitiesDetailCard;
    private HighlightsDetailView highlightsDetailView;
    private TextView[] bookabilityStatusTextViews;
    private TextView[] cancelButtons;
    private TextView liveStreamInfo;
    private View liveStreamButton;
    private View[] loadingIndicators;
    private TextView[] bookButtons;
    private ClassTypeStaffView staffView;
    private SeeAllSubHeader studioReviewsHeader;
    private ViewGroup studioReviewsContainer;
    private LoadingOverlay studioReviewsLoadingIndicator;
    private FitnessActivityBadgeView fitnessActivityBadgeView;
    private RateClassView rateClassView;
    private GoogleApiClient mGoogleApiClient;
    private android.location.Location geofenceLocation;
    private ViewGroup bottomContainer;
    private View bottomShadow;
    private Rating classRating;
    private boolean showSignInButton;
    private APIWorkflowUtil.RequiredFieldsStatus requiredFieldsStatus;
    private boolean addToCalendarOnResume = false;
    private boolean showDealPurchasedDialog = false;
    private boolean signedUpSuccess;
    private Uri screenshotUri;
    private boolean gettingBitmap;
    private LoadingOverlayWhite fullscreenLoader;
    private boolean isLastMinuteOfferWorkflow;
    private boolean waitingForScreenshot = false;
    private boolean layoutInitialized = false;
    private WaitForImages waitForImages = new WaitForImages(this::storeBitmap);
    private ClassTypeDetailsViewModel viewModel;
    private ActivityResultLauncher<Intent> fullScreenConfirmationLauncher;

    private ViewGroup averageRatingsContainer;
    private Response.Listener<Void> signupSuccessCallback = new Response.Listener<Void>() {
        @Override
        public void onResponse(Void aVoid) {
            signupSuccess();
            MBStaticCache.getInstance().setFavoritesChanged(true);

            if (classTypeObject.getStatus().getId() == BookabilityStatus.WAITLISTABLE) {
                setResult(Constants.RESULT_BOOKED_WAITLIST);
            } else if (showDealPurchasedDialog) {
                setResult(Constants.RESULT_BOOKED_DEAL);
            } else {
                setResult(Constants.RESULT_BOOKED);
            }
        }
    };

    private static void unbindDrawables(View view) {
        if (view.getBackground() != null) {
            view.getBackground().setCallback(null);
        }
        if (view instanceof ViewGroup) {
            for (int i = 0; i < ((ViewGroup) view).getChildCount(); i++) {
                unbindDrawables(((ViewGroup) view).getChildAt(i));
            }
            if (view instanceof AdapterView) {
                // noinspection unchecked
                ((AdapterView) view).setAdapter(null);
            } else {
                ((ViewGroup) view).removeAllViews();
            }
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // This will check for program type and instance ID
        if (!initActivity()) {
            return;
        }

        UserCheckoutFlowTracking.Companion.addUserFlowEvent(ClassDetails.INSTANCE);

        setContentView(R.layout.activity_class_type_details);
        initViews();


        final Response.ErrorListener catastrophicFailure = error -> {
            // For when the business call fails
            // TODO: we can just hide location specific details in this instance (happens when a class is not tied to a location)
            ToastUtils.show(getString(R.string.class_unavailable_error_message));
            finish();
        };

        initializeViewModelAndRegisterObservers(catastrophicFailure);

        if (getIntent() != null) {
            extractOriginViewAndComponent();
            viewModel.isNavigatedFromProfile = getIntent().getBooleanExtra(Constants.KEY_BUNDLE_NAVIGATE_FROM_PROFILE_TAB, false);
            viewModel.selectedUserId = getIntent().hasExtra(Constants.KEY_BUNDLE_SELECTED_DEPENDENT_USER_ID) ? getIntent().getStringExtra(Constants.KEY_BUNDLE_SELECTED_DEPENDENT_USER_ID) : "";
        }

        businessDetailCard.setOnStudioImageLoaded(() -> {
            waitForImages.setStudioDrawableAvailable();
            return Unit.INSTANCE;
        });

        mGoogleApiClient = new GoogleApiClient.Builder(this)
            .addConnectionCallbacks(this)
            .addApi(LocationServices.API)
            .build();

        //If null, we are coming from a deeplink or has been created using a class ref json
        if (classTypeObject == null) {
            fullscreenLoader.show();
            String classRefJson = getIntent().getStringExtra(Constants.KEY_BUNDLE_CLASS_REF_JSON);
            if (classRefJson != null) {
                viewModel.runInitializeFromClassRefJson(classRefJson, getIntent().getStringExtra(Constants.KEY_BUNDLE_INVENTORY_SOURCE));
            } else {
                int siteId = getIntent().getIntExtra(Constants.KEY_BUNDLE_SITEID, 0);
                long classInstanceId = getIntent().getLongExtra(Constants.KEY_BUNDLE_CLASS_INSTANCE_ID, 0);
                int masterLocationId = getIntent().getIntExtra(Constants.KEY_BUNDLE_MASTER_LOCATION_ID,
                    LocationRepository.ILLEGAL_MASTER_LOCATION_ID);
                isLastMinuteOfferWorkflow = getIntent().getBooleanExtra(Constants.KEY_BUNDLE_IS_LMO_WORKFLOW, false);

                if (siteId != 0 && classInstanceId != 0) {
                    viewModel.runInitializeFromClass(siteId, classInstanceId);
                } else if (masterLocationId != 0 && classInstanceId != 0) {
                    viewModel.runInitializeFromLocation(masterLocationId, classInstanceId);
                } else {
                    catastrophicFailure.onErrorResponse(null);
                }
            }
        } else {
            continueLayoutInitialization();
        }
        fullScreenConfirmationLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == Activity.RESULT_OK) {
                        signupSuccessCallback.onResponse(null);
                    }
                });
    }

    private void extractOriginViewAndComponent() {
        String originComponent = getIntent().getStringExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT);
        if(originComponent != null){
            viewModel.originComponent = originComponent;
        }
        String originView = getIntent().getStringExtra(Constants.KEY_BUNDLE_ORIGIN_VIEW);
        if(originView != null){
            viewModel.originView = originView;
        }
    }

    private void initializeViewModelAndRegisterObservers(ErrorListener catastrophicFailure) {
        viewModel = new ViewModelProvider(this).get(ClassTypeDetailsViewModel.class);

        viewModel.getInitializeFromClass().observe(this, result -> {
            if (result != null) {
                classTypeObject = result.getClassTypeObject();
                initHeaderListCellView();
                classTypeObject.setDropInPrice(dropInPrice);
                location = result.getLocation();
                requiredFieldsStatus = result.getRequiredFieldsStatus();
                continueLayoutInitialization();
                finishRefresh();
            } else {
                catastrophicFailure.onErrorResponse(null);
            }
        });

        viewModel.getInitializeFromLocation().observe(this, result -> {
            if (result != null) {
                location = result.getLocation();
                requiredFieldsStatus = result.getRequiredFieldsStatus();
                refreshEvent(result.getClassInstanceId());
            } else {
                catastrophicFailure.onErrorResponse(null);
            }
        });

        viewModel.getLocation().observe(this, result -> {
            if (result != null) {
                location = result;
                classTypeObject.setLocation(location);
                checkLocationForGeofence();
                initLocationDependentViews();
            } else {
                waitForImages.setStudioDrawableAvailable();
                initDataFromModel();
                checkLocationForGeofence();
            }
        });

        viewModel.getRequiredFields().observe(this, result -> {
            requiredFieldsStatus = result;
            refreshEvent();
        });

        viewModel.getPricingToken().observe(this, result -> {
            if (result != null) {
                setUpPriceLayout(result.getDspoPrice(),
                    result.isSurge() ? null : result.getDropInPrice());
                CloudSearchUtils.assignPricingTokenToClassTypeObject(result, classTypeObject);
            } else if (dropInPrice != null) {
                setUpPriceLayout(dropInPrice, null);
            } else {
                setUpPriceLayout();
            }
            getPaymentStatus();
        });

        viewModel.getVisitCancelStatus().observe(this, result -> {
            if (result != null) {
                cancelStatus = VisitCancelStatus.fromOrdinal(result.isCancellable);
                isDynamicPricePurchased = result.purchasedWithDynamicPricing;
                setCancelState();
                showProgress(false);
            } else {
                showErrorSnackbar(getString(R.string.user_no_longer_scheduled));
                WidgetUtils.refreshWidget(this, this);
                finish();
                for (TextView view : cancelButtons) {
                    view.setVisibility(View.GONE);
                }
                cancellationPolicyCard.setVisibility(
                    !TextUtils.isEmpty(classTypeObject.getCancellationPolicy()) ? View.VISIBLE : View.GONE);
                showProgress(false);
            }
        });

        viewModel.getClassInformation().observe(this, result -> {
            if (result != null) {
                classTypeObject = result;
                viewModel.sendClassDetailsEvent(ClassDetailsShown.INSTANCE);
                initHeaderListCellView();
                classTypeObject.setDropInPrice(dropInPrice);
                if (!layoutInitialized) continueLayoutInitialization();
                finishRefresh();
            } else {
                if (classType == Type.ENROLLMENT) ToastUtils.showServerErrorToast();
                finishRefresh();
            }
        });

        viewModel.getWaitlistCanceled().observe(this, result -> {
            final ClassTypeVisit visitObjectReference = DomainObjectUtils.convertToClassTypeVisit(classTypeObject);
            if (result != null && result) {
                showErrorSnackbar(getString(R.string.removed_from_waitlist));
                classTypeObject.setFromVisit(false);
                cancelEventNotifications();
                if (SharedPreferencesUtils.isCalendarSyncEnabled()) {
                    CalendarUtils.removeClassVisitFromCalendar(ClassTypeDetailsActivity.this, visitObjectReference);
                }
                refreshEvent();
                MBStaticCache.getInstance().setFavoritesChanged(true);
                setResult(Constants.RESULT_UNBOOKED);
                StaticInstance.refreshDataAfterBooking();

                //TODO [865063] Remove all references to class instance ID from the app
                if (classTypeObject.getId() == 0) {
                    finish();
                }
            } else {
                showErrorSnackbar(getString(R.string.remove_from_waitlist_failed));
                classTypeObject.setFromVisit(false);
                refreshEvent();
            }
        });

        viewModel.getRosterCanceled().observe(this, result -> {
            final ClassTypeVisit visitObjectReference = DomainObjectUtils.convertToClassTypeVisit(classTypeObject);
            if (result != null && result) {
                classTypeObject.setFromVisit(false);
                refreshEvent();
                setResult(Constants.RESULT_UNBOOKED);
            } else {
                classTypeObject.setFromVisit(false);
                refreshEvent();
            }
        });

        viewModel.getPaymentStatus().observe(this, result -> {
            if (result != null) {
                paymentStatus = result;

                if (paymentStatus.getCode() == ClassPaymentStatus.REQUIRES_PAYMENT) {
                    classTypeObject.setStatus(new BookabilityStatus(
                        classTypeObject.isBookable() ? BookabilityStatus.PAYMENT_REQUIRED : BookabilityStatus.PAY_WAITLIST, "Payment Required"));
                } else if (paymentStatus.isBookable()) {
                    classTypeObject.setStatus(new BookabilityStatus(
                        classTypeObject.isBookable() ? BookabilityStatus.BOOKABLE : BookabilityStatus.WAITLISTABLE, "Bookable"));
                }

                setButtonStatus();
            } else {
                ToastUtils.showServerErrorToast();
            }
        });


        viewModel.getStudioReviews().observe(this, response -> {
            if (response != null) {
                studioReviewsLoadingIndicator.setVisibility(View.GONE);
                if (response.length == 0) {
                    studioReviewsHeader.setVisibility(View.GONE);
                    studioReviewsContainer.setVisibility(View.GONE);
                    return;
                }

                studioReviewsHeader.setVisibility(View.VISIBLE);
                studioReviewsContainer.setVisibility(View.VISIBLE);

                for (RatingCardViewModel vm : BusinessDetailsCardUtils.getRatingCardModels(response)) {
                    RatingCard card = new RatingCard(ClassTypeDetailsActivity.this);
                    card.setClassDetailsScreen(true);
                    card.setViewModel(vm);
                    card.setOnReportAbuseClickListener(() -> {
                        MaterialOptionDialog dialog = new MaterialOptionDialog();
                        dialog.setText(R.string.flag_as_inappropriate, 0, R.string.report_abuse, R.string.close);
                        dialog.setButton1Callback(result -> {
                            viewModel.reportAbuse(vm.getId());
                            dialog.dismiss();
                        });
                        dialog.setButton2Callback(result -> dialog.dismiss());

                        dialog.show(getSupportFragmentManager(), null);
                    });
                    studioReviewsContainer.addView(card);

                    card.setOnClickListener(v -> {
                        ClassTypeDetailsActivity.this.startActivityForResult(
                            ReviewDetailsActivity.newIntent(v.getContext(), ((Rating) vm.getTag()), null)
                                .putExtra(Constants.KEY_LOCATION_STUDIO_NAME, location.getStudioName()),
                            BusinessDetailsActivity.DELETE_REVIEW);
                    });
                }

                if (response.length == StudioReviewsRepository.TOP_REVIEWS_COUNT) {
                    studioReviewsHeader.setOnClickListener(v -> {
                        Intent listActivityIntent = ReviewListActivity.newIntent(v.getContext(), location.getId(), new int[]{classTypeObject.getClassDescriptionId()}, classTypeObject.getTotalReviews());
                        v.getContext().startActivity(listActivityIntent);
                    });
                }
            } else {
                MBLog.e("ClassTypeDetails", "Network error getting location reviews");
                studioReviewsLoadingIndicator.setVisibility(View.GONE);
                studioReviewsContainer.setVisibility(View.GONE);
                studioReviewsHeader.setVisibility(View.GONE);
            }
        });


        viewModel.getLiveStreamViewstate().observe(this, result -> {
            liveStreamInfo.setVisibility(
                (result.getShowLiveStreamState() && !result.getEnabled()) ? View.VISIBLE : View.GONE
            );

            boolean liveStreamReady = result.getShowLiveStreamState() && result.getEnabled();
            liveStreamButton.setVisibility(liveStreamReady ? View.VISIBLE : View.GONE);

            for (TextView view : bookButtons) {
                view.setTextColor(ContextCompat.getColor(getApplicationContext(),
                    liveStreamReady ? R.color.dark_gray : R.color.white_1));
                view.setBackgroundResource(liveStreamReady ? R.drawable.transparent_rounded_corner_selector
                    : R.drawable.primary_rounded_corner_selector);
                view.setPadding(ViewUtils.dpToPx(16, this), ViewUtils.dpToPx(20, this),
                    ViewUtils.dpToPx(16, this), ViewUtils.dpToPx(20, this));
            }

        });

        viewModel.getLivestreamUrl().observe(this, livestreamLink -> {
            if (livestreamLink == null) {
                ToastUtils.show(getString(R.string.livestream_link_failure_message));
            } else {
                NonceRedirectDialog.newInstance(livestreamLink).show(getSupportFragmentManager(), null);
            }
        });

        viewModel.getReviewSummaryData().observe(this, this::addEnhancedReviewAverageDataView);
        viewModel.getReviewsList().observe(this, this::handleReviewsListData);
    }

    public void showDeeplinkFailureAndFinish() {
        // Business isn't listed anymore?
        final MaterialOptionDialog dialog = new MaterialOptionDialog();
        dialog.setText(R.string.deeplink_failure_class_title,
                R.string.deeplink_failure_class_message,
                R.string.ok_button_text,
                0)
            .setButton1Callback(result -> dialog.dismiss())
            .setCancelCallback(result -> finish())
            .show(getSupportFragmentManager(), MaterialOptionDialog.class.getSimpleName());
    }

    private void continueLayoutInitialization() {
        AnimationUtils.animateFade(fullscreenLoader, false);
        initActionBar();
        initDataFromModel();
        getReviews();

        showProgress(true);

        if (location == null) {
            location = classTypeObject.getLocation();
            if (location != null) {
                initLocationDependentViews();
            }

            // ummm, obvious NPE ...
            viewModel.fetchLocation(location.getId());
        } else {
            classTypeObject.setLocation(location);
            initDataFromModel();
            checkLocationForGeofence();
            initLocationDependentViews();
        }

        layoutInitialized = true;
    }

    private String getFilename() {
        //TODO [865063] Remove all references to class instance ID from the app
        return SCREENSHOT_FILENAME + (location == null ? "" : location.getId()) + "_" + classTypeObject.getId() + ".jpg";
    }

    @Override
    protected void onResume() {
        super.onResume();

        if (staffView != null) {
            staffView.setEnabled(Switches.VIEW_CLASSES_BY_STAFF);
            if (classTypeObject != null) {
                initStaff(); //TODO CK - Heavy handed quick fix, needs to be redone to pass in the favorited staff or most likely add a livedata to viewmodel as is the pattern, but not in initStaff for some reason
            }
        }

        if (addToCalendarOnResume) {
            addToCalendar();
            addToCalendarOnResume = false;
            return;
        }

        User user = MBAuth.getUser();
        if (user != null && !user.isVerified()) {
            viewModel.updateUser();
        }

        if (location != null && classTypeObject != null) {
            if (requiredFieldsStatus == null) {
                viewModel.updateRequiredFields(location.getSiteId());
            } else {
                refreshEvent();
            }
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.share_menu, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (screenshotUri != null) {
            new File(screenshotUri.getPath()).delete();
        }
        unbindDrawables(getWindow().getDecorView().getRootView());
    }

    private void showProgress(boolean show) {
        for (View view : loadingIndicators) {
            view.setVisibility(show ? View.VISIBLE : View.GONE);
        }
        if (show) {
            cancellationPolicyCard.setVisibility(classTypeObject != null &&
                !TextUtils.isEmpty(classTypeObject.getCancellationPolicy()) ? View.VISIBLE : View.GONE);
            for (TextView view : cancelButtons) {
                view.setVisibility(View.GONE);
            }
            for (TextView view : bookButtons) {
                view.setVisibility(View.GONE);
            }
            for (TextView view : bookabilityStatusTextViews) {
                view.setVisibility(View.GONE);
            }
        }
    }

    private boolean initActivity() {
        classTypeObject = StaticInstance.selectedClassTypeObject;

        if (classTypeObject == null && !getIntent().hasExtra(Constants.KEY_BUNDLE_CLASS_INSTANCE_ID)
            && !getIntent().hasExtra(TYPE_EXTRA_STRING)) {
            ToastUtils.showServerErrorToast();
            finish();
            return false;
        }

        preselectedPricingOption = StaticInstance.selectedCatalogItem;
        StaticInstance.selectedCatalogItem = null;

        dropInPrice = (BigDecimal) getIntent().getSerializableExtra(Constants.KEY_BUNDLE_DROP_IN_PRICE);
        if (dropInPrice == null && classTypeObject != null) {
            dropInPrice = classTypeObject.getDropInPrice();
        }

        secondaryCategories = (String[]) getIntent().getSerializableExtra(Constants.KEY_BUNDLE_SECONDARY_CATEGORIES);

        initClassType(getIntent().getExtras());
        return true;
    }

    private void initClassType(Bundle bundle) {
        if (bundle != null && bundle.containsKey(TYPE_EXTRA_STRING)) {
            classType = Type.values()[bundle.getInt(TYPE_EXTRA_STRING, Type.ENROLLMENT.ordinal())];
        } else if (classTypeObject != null) {
            if (classTypeObject instanceof Enrollment) {
                classType = Type.ENROLLMENT;
            } else if (classTypeObject instanceof FavoriteClass) {
                classType = Type.CLASS;
            } else {
                ToastUtils.showServerErrorToast();
                finish();
            }
        }
    }

    private void initActionBar() {
        setActionBarTitle(classTypeObject.hasVisits() ? classTypeObject.getName() :
            getString(R.string.class_detail_title));

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    //TODO [865063] Remove all references to class instance ID from the app
    private void getLastMinuteOffer() {
        showProgress(true);
        viewModel.fetchPricingToken(classTypeObject.getLocation().getSiteId(), classTypeObject.getId());
    }

    //region Network methods
    private void getPaymentStatus() {
        showProgress(true);

        viewModel.fetchClassPaymentStatus(classTypeObject);
    }

    private void refreshEvent() {
        //TODO [865063] Remove all references to class instance ID from the app
        refreshEvent(classTypeObject.getId());
    }

    private void refreshEvent(long classEventId) {
        showProgress(true);
        paymentStatus = null;

        switch (classType) {
            case CLASS:
                refreshClassEvent(classEventId);
                break;
            case ENROLLMENT:
                refreshEnrollmentEvent(classEventId);
                break;
        }
    }

    private void fetchVisitCancelStatus() {
        if (classTypeObject.hasVisits() && classTypeObject.getVisits()[0] != null && !classTypeObject.hasClassPassed() && !classTypeObject.onTheWaitlist()) {
            viewModel.fetchVisitCancelStatus(classTypeObject.getVisits()[0], classTypeObject.getLocation());
        } else if (classTypeObject.onTheWaitlist()) {
            cancelStatus = VisitCancelStatus.CANCELLABLE;
            setCancelState();
        } else {
            setCancelState();
        }
    }

    private void refreshClassEvent(long classInstanceId) {
        if (classInstanceId != 0) {
            viewModel.refreshClassInformation(classInstanceId, location);
        } else {
            finishRefresh();
        }
    }

    private void finishRefresh() {
        if (classTypeObject != null) {
            if (signedUpSuccess) {
                StaticInstance.selectedClassTypeObject = classTypeObject;
                if (classTypeObject.hasVisits()) {
                    AlarmUtils.scheduleNotificationsForClass(classTypeObject);

                    MBStaticCache.getInstance().addUpcomingVisit(
                        DomainObjectUtils.convertToClassTypeVisit(classTypeObject));
                }
            }
            boolean waitingOnPrice = false;
            if (!processSignInActionItems()) {
                if (classTypeObject.hasClassPassed()) {
                    refreshPassedViews();
                } else if (classTypeObject.hasVisits() || classTypeObject.isFromVisit() || classTypeObject.onTheWaitlist()) {
                    refreshBookedViews();
                    initCapacityCountHeader();
                } else if (classTypeObject.isBookable() || classTypeObject.isCrossRegional() ||
                   (classTypeObject.getStatus() != null && classTypeObject.getStatus().getId() == BookabilityStatus.WAITLISTABLE)) {
                    if (preselectedPricingOption != null) {
                        getPaymentStatus();
                    } else {
                        waitingOnPrice = true;
                        getLastMinuteOffer();
                    }
                    initCapacityCountHeader();
                } else {
                    setButtonStatus();
                    initCapacityCountHeader();
                }
            } else {
                fetchVisitCancelStatus();
            }
            if (!waitingOnPrice) setUpPriceLayout();
        } else {
            //Can't do anything without a class
            showDeeplinkFailureAndFinish();
        }
        if (!viewModel.selectedUserId.isEmpty()) {
            bottomContainer.setVisibility(View.GONE);
        }
    }

    private void refreshPassedViews() {
        //Account for the case where they wait and then the class doesn't exist anymore
        if (classTypeObject.hasClassPassed() && classTypeObject.hasVisits()) {
            final ClassTypeVisit visit = DomainObjectUtils.convertToClassTypeVisit(classTypeObject);

            // TODO: Why are these hidden? Inconsistent with iOS app.
            descriptionCard.setVisibility(View.GONE);
            cancellationPolicyCard.setVisibility(View.GONE);
            prerequisitesCard.setVisibility(View.GONE);

            classRating = visit.getVisitDataId() <= 0 ? null :
                MBStaticCache.getInstance().getRating(visit.getVisitDataId());
            if (classRating != null) {
                rateClassView.setRating(classRating);
            }

            rateClassView.setBookingTypeString(classType == Type.CLASS
                ? getString(R.string.visit_class_type_string)
                : getString(R.string.visit_enrollment_type_string));

            if (viewModel.selectedUserId.isEmpty()) {
                rateClassView.setVisibility(View.VISIBLE);
            }


            rateClassView.setOnClickListener(v -> {
                ClassTypeVisit visit1 = DomainObjectUtils.convertToClassTypeVisit(classTypeObject);
                if (visit1.getProgramType().getName().equals(BaseVisit.PROGRAM_TYPE_CLASS)) {
                    EnhanceRateReviewDialog dialog = EnhanceRateReviewDialog.Companion.newInstance(false);
                    dialog.setVisit(visit1);
                    dialog.setSuccessCallback(result -> {
                        KeyboardUtils.hideKeyboard(ClassTypeDetailsActivity.this, rateClassView);
                        if (classRating == null) classRating = new Rating();
                        classRating.setRating(result != null ? result.getRating() : 0);
                        classRating.setReviewText(result != null ? result.getReviewText() : "");
                        if (result == null) classRating.setId(0);
                        rateClassView.setRating(classRating);
                        averageRatingsContainer.removeAllViews();
                        studioReviewsContainer.removeAllViews();
                        getReviews();
                    });
                    dialog.show(getSupportFragmentManager(), EnhanceRateReviewDialog.ENHANCE_REVIEW_DIALOG_TAG);
                } else {
                    RateReviewDialog dialog = new RateReviewDialog();
                    dialog.setVisit(visit1);
                    dialog.setSuccessCallback(result -> {
                        KeyboardUtils.hideKeyboard(ClassTypeDetailsActivity.this, rateClassView);
                        if (classRating == null) classRating = new Rating();
                        classRating.setRating(result != null ? result.getRating() : 0);
                        classRating.setReviewText(result != null ? result.getReviewText() : "");
                        if (result == null) classRating.setId(0);
                        rateClassView.setRating(classRating);
                        studioReviewsContainer.removeAllViews();
                        getReviews();
                    });
                    dialog.show(getSupportFragmentManager(), RateReviewDialog.REVIEW_DIALOG_TAG);
                }
            });


            if (viewModel.selectedUserId.isEmpty() && FitBitAPI.DATABASE_SOURCE_NAME.equals(SharedPreferencesUtils.getCurrentFitnessTracker())) {
                configureViewModel(visit, FitbitViewModel.class);
            } else if (viewModel.selectedUserId.isEmpty() && GoogleFitAPI.DATABASE_SOURCE_NAME.equals(SharedPreferencesUtils.getCurrentFitnessTracker())) {
                configureViewModel(visit, GoogleFitViewModel.class);
            }
        } else if (classTypeObject.hasClassPassed()) {
            capacityCountHeader.setText(R.string.class_has_passed);
            capacityCountHeader.setVisibility(View.VISIBLE);
        }
        bottomContainer.setVisibility(View.VISIBLE);
        bottomShadow.setVisibility(View.VISIBLE);
        showProgress(false);
    }

    private void refreshBookedViews() {
        if (classTypeObject.onTheWaitlist() || classTypeObject.isSignedIn()) {
            if (classTypeObject.onTheWaitlist()) {
                int num = classTypeObject.getWaitlistPosition();
                capacityCountHeader.setText(getString(R.string.position_in_line_text, num, Utils.getNumberSuffix(num)));
            } else {
                capacityCountHeader.setText(getString(R.string.signed_in_text));
            }
            capacityCountHeader.setVisibility(View.VISIBLE);
        } else if (classTypeObject.isBooked()) {
            for (TextView view : bookabilityStatusTextViews) {
                if (isFamilyAccountType() && !viewModel.isNavigatedFromProfile) {
                    view.setText(String.format(getString(R.string.user_name_booked_header_text), viewModel.getUserName()));
                } else {
                    view.setText(getString(R.string.event_booked_header_text));
                }
            }
            capacityCountHeader.setVisibility(View.GONE);
        }

        for (TextView view : bookabilityStatusTextViews) {
            view.setVisibility(View.VISIBLE);
        }

        if ((classTypeObject.getVisits() != null && classTypeObject.getVisits().length > 0) || classTypeObject.onTheWaitlist()) {
            AnimationUtils.expand(bottomContainer);
            fetchVisitCancelStatus();
        } else {
            showProgress(false);
        }
    }

    private void configureViewModel(BaseVisit visit, Class<? extends FitnessActivityViewModel> viewModelClass) {
        FitnessActivityViewModel viewModel = new ViewModelProvider(this)
            .get(viewModelClass);

        fitnessActivityBadgeView.setVisibility(View.VISIBLE);
        fitnessActivityBadgeView.setVisit(visit);

        fitnessActivityBadgeView.setOnLaunchFitnessTracker(() -> {
            IntentUtils.launchAppIntent(this, viewModel.getTrackerAppPackageName());
            return Unit.INSTANCE;
        });

        fitnessActivityBadgeView.setOnOpenFitnessTrackerSettings(() -> {
            Intent intent = new Intent(this, viewModel.getTrackerSettingsActivity());
            intent.putExtra(Constants.KEY_BUNDLE_SKIP_PROMPT, true);
            startActivity(intent);
            return Unit.INSTANCE;
        });

        viewModel.getAllFitnessData().observe(this, fitnessActivityBadgeView::setData);

        fitnessActivityBadgeView.setOnRefreshListener(() -> {
            viewModel.refreshData(visit);
            return Unit.INSTANCE;
        });

        fitnessActivityBadgeView.setShareCallback(result ->
            IntentUtils.shareFitbitData(ClassTypeDetailsActivity.this, visit, result));

        viewModel.refreshData(visit);
    }

    private void setCancelState() {
        if (cancelStatus == null) return;

        showProgress(false);
        if (shouldShowNextForCancelState()) {
            ViewUtilKt.setVisibilityAndText(bookButtons, R.string.next, true);
        } else {
            switch (cancelStatus) {
                case NON_CANCELLABLE:
                    adjustBookButtonAfterCancelStatus();
                    ViewUtilKt.setVisibilityAndText(cancelButtons, R.string.action_call_to_cancel_long, true);
                    ViewUtilKt.setVisibilityAndText(bookabilityStatusTextViews, null, false);
                    break;
                case CANCELLABLE:
                    adjustBookButtonAfterCancelStatus();
                    ViewUtilKt.setVisibilityAndText(cancelButtons, R.string.cancel_booking_button, true);
                    ViewUtilKt.setVisibilityAndText(bookabilityStatusTextViews, null, false);
                    break;
                case CANCELLABLE_LATE:
                    adjustBookButtonAfterCancelStatus();
                    ViewUtilKt.setVisibilityAndText(cancelButtons, R.string.cancel_booking_button, true);
                    ViewUtilKt.setVisibilityAndText(bookabilityStatusTextViews, R.string.late_cancel_message, true);
                    break;
                case CANCELLED:
                    for (TextView view : cancelButtons) {
                        view.setOnClickListener(null);
                    }
                    ViewUtilKt.setVisibilityAndText(cancelButtons, R.string.call_business_message, true);
                    ViewUtilKt.setVisibilityAndText(bookabilityStatusTextViews, null, false);
                    ViewUtilKt.setVisibilityAndText(bookButtons, !showSignInButton ? R.string.action_book_call : null, true);
                    break;
            }
        }
    }

    //We want to show next cta for cancel state when class is not waitlist, not signedin and not flex
    private boolean shouldShowNextForCancelState() {
        return isFamilyAccountType() && !viewModel.isNavigatedFromProfile && !classTypeObject.onTheWaitlist() && !classTypeObject.isSignedIn() && !classTypeObject.isFlexOptionEnabled();
    }

    private void adjustBookButtonAfterCancelStatus() {
        boolean hideAddToCalButton = !(
                (classTypeObject != null && classTypeObject.hasVisits())
                        || (StaticInstance.selectedClassTypeObject != null && StaticInstance.selectedClassTypeObject.hasVisits())
        ) || SharedPreferencesUtils.isCalendarSyncEnabled();
        if (showSignInButton) {
            for (TextView view : bookButtons) {
                view.setVisibility(View.VISIBLE);
            }
        } else if (!hideAddToCalButton) {
            for (TextView view : bookButtons) {
                view.setVisibility(View.VISIBLE);
            }
            for (TextView view : bookButtons) {
                view.setText(R.string.booking_confirmation_add_to_calendar_text);
            }
        } else {
            for (TextView view : bookButtons) {
                view.setVisibility(View.GONE);
            }
        }
    }

    private void refreshEnrollmentEvent(long eventInstanceId) {
        if (eventInstanceId != 0) {
            viewModel.refreshEventInformation(location.getSiteId(), location.getSiteLocationId(), eventInstanceId);
        } else {
            finishRefresh();
        }
    }

    //endregion

    private void initViews() {
        root = findViewById(R.id.class_type_details_root);
        capacityCountHeader = findViewById(R.id.current_capacity_text_view);
        classDetailCard = findViewById(R.id.class_detail_card);
//        eventRoomName = findViewById(R.id.event_room_text_view);
//        prerequisiteTextView = findViewById(R.id.class_type_details_prerequisite);
        miniLocationView = findViewById(R.id.class_type_details_map_view);
        aboutStudioHeader = findViewById(R.id.about_this_studio_header);
        businessDetailCard = findViewById(R.id.business_detail_card);
        amenitiesDetailCard = findViewById(R.id.class_details_amenities);
        highlightsDetailView = findViewById(R.id.class_details_highlights);
        bottomContainer = findViewById(R.id.bottom_button_container);
        bottomShadow = findViewById(R.id.button_divider);

        liveStreamInfo = findViewById(R.id.tv_livestream_info);
        liveStreamButton = findViewById(R.id.join_livestream_button);

        bookabilityStatusTextViews = new TextView[]{
            bottomContainer.findViewById(R.id.class_type_status_message)
        };
        bookButtons = new TextView[]{
            bottomContainer.findViewById(R.id.class_event_type_book_button)
        };
        cancelButtons = new TextView[]{
            bottomContainer.findViewById(R.id.class_event_type_cancel_button)
        };
        loadingIndicators = new View[]{
            bottomContainer.findViewById(R.id.class_type_details_loading)
        };

        bottomContainer.setVisibility(View.VISIBLE);

        staffView = findViewById(R.id.class_type_staff_view);
        studioReviewsHeader = findViewById(R.id.rating_card_title);
        studioReviewsContainer = findViewById(R.id.ratings_container);
        studioReviewsLoadingIndicator = findViewById(R.id.ratings_loader);
        rateClassView = findViewById(R.id.rate_class_view);
        fitnessActivityBadgeView = findViewById(R.id.activity_class_details_fitbit_badge_view);
        descriptionCard = findViewById(R.id.class_description_card);
        cancellationPolicyCard = findViewById(R.id.class_cancellation_card);
        prerequisitesCard = findViewById(R.id.class_prerequisites_card);
//        prerequisiteView = findViewById(R.id.class_type_details_prerequisite_layout);
//        upcomingClassList = findViewById(R.id.upcoming_classes_list_view);

        fullscreenLoader = findViewById(R.id.class_type_details_full_page_loader);
        averageRatingsContainer = findViewById(R.id.average_ratings_container);

        for (TextView view : bookButtons) {
            view.setOnClickListener(this);
        }
        for (TextView view : cancelButtons) {
            view.setOnClickListener(this);
        }
        liveStreamInfo.setOnClickListener(this);
        liveStreamButton.setOnClickListener(this);

        if (!StaticInstance.businessDetailsInBackstack) {
            businessDetailCard.setOnClickListener(this);
        }

        studioReviewsLoadingIndicator.setLoadingMessageText(null);
    }

    //region View Initialization methods
    private void initDataFromModel() {
        initCapacityCountHeader();
        initHeaderListCellView();
        initStaff();

        if (!TextUtils.isEmpty(classTypeObject.getDescription())) {
            descriptionCard.setViewModel(new TextCardViewModel(getString(R.string.business_details_description_text),
                Html.fromHtml(StringUtilKt.trimEmptyLines(classTypeObject.getDescription())).toString()));
            descriptionCard.setOnExpandedListener(this::onDescriptionExpanded);
            descriptionCard.setVisibility(View.VISIBLE);
        } else {
            descriptionCard.setVisibility(View.GONE);
        }

        String cancelPolicy = classTypeObject.getCancellationPolicy();
        cancellationPolicyCard.setViewModel(
            new TextCardViewModel(
                getString(R.string.business_details_cancellation_policy_header),
                StringUtilKt.trimEmptyLines(cancelPolicy == null ? "" : cancelPolicy)));
        cancellationPolicyCard.setVisibility(
            !TextUtils.isEmpty(classTypeObject.getCancellationPolicy()) ? View.VISIBLE : View.GONE);

        if (!TextUtils.isEmpty(classTypeObject.getPrerequisiteNotes())) {
            prerequisitesCard.setViewModel(new TextCardViewModel(getString(R.string.prereq_info_header),
                Html.fromHtml(StringUtilKt.trimEmptyLines(classTypeObject.getPrerequisiteNotes())).toString()));
            prerequisitesCard.setVisibility(View.VISIBLE);
        } else {
            prerequisitesCard.setVisibility(View.GONE);
        }
    }

    private void setUpPriceLayout() {
        BigDecimal price = null;
        BigDecimal oldPrice = null;
        if (preselectedPricingOption != null) {
            price = preselectedPricingOption.getSalePrice();
            if (preselectedPricingOption.isDiscounted()) {
                oldPrice = CartItemUtil.getItemOnlinePrice(preselectedPricingOption);
            }
        }
        setUpPriceLayout(price, oldPrice);
    }

    private void onDescriptionExpanded(boolean isNowExpanded) {
        if (isNowExpanded) {
            viewModel.sendClassDetailsEvent(ReadMoreSelected.INSTANCE);
        }
    }

    private void setUpPriceLayout(BigDecimal price, BigDecimal oldPrice) {
        classDetailCard.setPriceViewModel(TimeDatePriceViewModelAdapter.adapt(classTypeObject, price, oldPrice));
    }

    private void initCapacityCountHeader() {
        if (classType == Type.CLASS) {
            initCapacityHeaderForClass();
        }
        // UPDATE:  We are hiding the capacity header for all enrollments
        /* else if(preselectedPricingOption == null) {
            initCapacityHeaderForEnrollment();
        }*/
        else if (!classTypeObject.isSignedIn()) {
            capacityCountHeader.setVisibility(View.GONE);
        }
    }

    private void initCapacityHeaderForClass() {
        if ((classTypeObject.getCapacity() == 0 && !classTypeObject.onTheWaitlist() && !classTypeObject.isSignedIn()) ||
            (classTypeObject.hasClassPassed() && classTypeObject.hasVisits())) {
            capacityCountHeader.setVisibility(View.GONE);
            return;
        } else if (classTypeObject.onTheWaitlist()) {
            return;
        }

        if (classTypeObject.isSignedIn()) {
            capacityCountHeader.setText(getString(R.string.signed_in_text));
        } else {
            int spacesAvailable = classTypeObject.getCapacity() - classTypeObject.getNumberRegistered();
            int primaryColorResId = R.color.text_header;
            int secondaryColorResId = R.color.text_body;

            if (spacesAvailable < 0) spacesAvailable = 0;

            int spacesBooked = classTypeObject.getNumberRegistered();

            SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();

            SpannableString spannableSpacesAvailable = new SpannableString(getResources().getQuantityString(R.plurals.spaces_available_text, spacesAvailable, spacesAvailable));
            spannableSpacesAvailable.setSpan(new StyleSpan(Typeface.BOLD), 0, String.valueOf(spacesAvailable).length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableSpacesAvailable.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, primaryColorResId)), 0, String.valueOf(spacesAvailable).length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableSpacesAvailable.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, secondaryColorResId)), String.valueOf(spacesAvailable).length() + 1, spannableSpacesAvailable.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableStringBuilder.append(spannableSpacesAvailable);
            spannableStringBuilder.append(", ");

            SpannableString spannableSpacesBooked = new SpannableString(getResources().getQuantityString(R.plurals.spaces_booked_text, spacesBooked, spacesBooked, spacesBooked));
            spannableSpacesBooked.setSpan(new StyleSpan(Typeface.BOLD), 0, String.valueOf(spacesBooked).length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableSpacesBooked.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, primaryColorResId)), 0, String.valueOf(spacesBooked).length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableSpacesBooked.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, secondaryColorResId)), String.valueOf(spacesBooked).length() + 1, spannableSpacesBooked.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

            spannableStringBuilder.append(spannableSpacesBooked);
            capacityCountHeader.setText(spannableStringBuilder);
        }
        capacityCountHeader.setVisibility(View.VISIBLE);
    }

    private void initHeaderListCellView() {
        if (classTypeObject.getDistanceInKm() != null) {
            initHeaderListCellView(GeoLocationUtils.kilometersToMiles(classTypeObject.getDistanceInKm().doubleValue()));
        } else {
            // Fill in the card details beforehand in case the location takes a while to fetch
            initHeaderListCellView(null);

            GeoLocationUtils.getCurrentLocation(this, userLocation -> {
                Double distanceInMiles = null;
                if (userLocation != null && classTypeObject.getLocation() != null &&
                    !(classTypeObject.getLocation().getLatitude() == 0 && classTypeObject.getLocation().getLongitude() == 0)) {
                    LatLng classLocation = new LatLng(classTypeObject.getLocation().getLatitude(),
                        classTypeObject.getLocation().getLongitude());

                    distanceInMiles = GeoLocationUtils.distanceBetweenLatLngsInMiles(GeoLocationUtils.toLatLng(userLocation), classLocation);
                }

                initHeaderListCellView(distanceInMiles);
                return Unit.INSTANCE;
            });
        }
    }

    /**
     * A helper method for {@link #initHeaderListCellView}
     */
    private void initHeaderListCellView(@Nullable Double distanceInMiles) {
        String category = null;
        //Creating new list as classTypes is immutable and we need allCategories to be mutable
        List<String> allCategories = new ArrayList<>();
        if (!Utils.isEmpty(classTypeObject.getClassTypes())) {
            allCategories.addAll(classTypeObject.getClassTypes());
        }
        if (classTypeObject instanceof Enrollment) {
            category = getString(R.string.event_tag);
        } else if (!Utils.isEmpty(allCategories)) {
            //Display secondary categories along with the primary
            if (!Utils.isEmpty(secondaryCategories)) {
                allCategories.addAll(Arrays.asList(secondaryCategories));
            }
            category = TextUtils.join(" | ", allCategories);
        }

        classDetailCard.setViewModel(new ClassDetailViewModel(
            classTypeObject.getName(),
            classTypeObject.getLocation().getName(),
            distanceInMiles,
            classTypeObject.getRoom() != null ? classTypeObject.getRoom().getName() : null,
            category,
            classTypeObject.getAverageReviewRating(),
            classTypeObject.getTotalReviews(),
            classTypeObject.isVirtual(),
            classTypeObject.isFlexClass(),
            classTypeObject.isFreeToEnroll()));

        classDetailCard.setRatingContainerClickListener(view -> {
            Intent listActivityIntent = ReviewListActivity.newIntent(view.getContext(), location.getId(), new int[]{classTypeObject.getClassDescriptionId()}, classTypeObject.getTotalReviews());
            view.getContext().startActivity(listActivityIntent);
            return Unit.INSTANCE;
        });
    }

    private void initStaff() {
        // Check for masked staff
        if (classTypeObject.getStaff() == null) {
//            findViewById(R.id.staff_list_row_divider).setVisibility(View.GONE);
            staffView.setVisibility(View.GONE);
            waitForImages.setStaffDrawableAvailable();
            return;
        }

        ServiceLocator.getFavoriteStaffRepository().getFavoriteStaff(favoriteStaffs -> {
            Staff visitStaff = classTypeObject.getStaff();
            for (FavoriteStaff staff : favoriteStaffs) {
                if (visitStaff.getId() == staff.getStaffId() &&
                    classTypeObject.getLocation().getId() == staff.getMasterLocationId()) {
                    classTypeObject.getStaff().setFavorite(true);
                    break;
                }
            }
            finishInitializingStaff();
            return Unit.INSTANCE;
        }, throwable -> finishInitializingStaff(), false);
    }

    private Unit finishInitializingStaff() {
        staffView.setStaffData(classTypeObject.getStaff(), classTypeObject.getAssistants(), false, result ->
            waitForImages.setStaffDrawableAvailable());

        staffView.setOnClickListener(v -> {
            viewModel.sendClassDetailsEvent(ClassDetailsStaffBioSelected.INSTANCE);
            if (getIntent() != null && getIntent().getBooleanExtra(STARTED_FROM_STAFF_CLASSES, false)) {
                finish();
            } else {
                Intent staffBioIntent = ClassStaffDetailsActivity.newIntent(
                    ClassTypeDetailsActivity.this, classTypeObject.getStaff().getId(), location.getId(),
                    OriginView.CLASS_DETAILS, OriginComponent.STAFF_CARD);
                staffBioIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(staffBioIntent);
            }
        });
        return Unit.INSTANCE;
    }

    private void initLocationDependentViews() {
        miniLocationView.setLocation(location);
        miniLocationView.setAnalyticsPrefix("Schedule Item Details", "Program type", classType.toString());
        miniLocationView.setAddressPassThroughClickListener(v -> {
            viewModel.sendClassDetailsEvent(AddressSelected.INSTANCE);
        });
        miniLocationView.setMapPassThroughClickListener(v -> {
            viewModel.sendClassDetailsEvent(MapSelected.INSTANCE);
        });
        businessDetailCard.setViewModel(BusinessDetailCardViewModelAdapter.adapt(location));

        if (!Utils.isEmpty(location.getAmenities())) {
            amenitiesDetailCard.setVisibility(View.VISIBLE);
            amenitiesDetailCard.setViewData(Arrays.asList(location.getAmenities()));
        }
        if (location != null) {
            highlightsDetailView.setupView(location);
        }
    }
    //endregion

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
            case R.id.action_share:
                if (screenshotUri == null) {
                    waitingForScreenshot = true;
                    storeBitmap();
                } else {
                    shareScreenshot();
                }
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.class_event_type_book_button:
                if (showSignInButton) {
                    SignIntoClassDialog dialog = SignIntoClassDialog.newInstance(location.getSiteId(), classTypeObject.getName());
                    dialog.setCancelCallback(new TaskCallback() {
                        @Override
                        public void onTaskComplete(Object result) {
                            signedUpSuccess = true;
                            Intent mainIntent = new Intent(ClassTypeDetailsActivity.this, MainActivity.class);
                            StaticInstance.selectedFragment = Constants.MY_SCHEDULE_FRAGMENT_TAG;
                            mainIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                            startActivity(mainIntent);
                        }
                    });
                    dialog.show(getSupportFragmentManager(), SignIntoClassDialog.class.getSimpleName());
                } else {
                    bookButtonClicked();
                }
                break;
            case R.id.class_event_type_cancel_button:
                cancelButtonClicked();
                break;
            case R.id.business_detail_card:
                viewModel.sendClassDetailsEvent(BusinessDetailsSelected.INSTANCE);
                startActivity(BusinessDetailsActivity.newIntent(
                        this,
                        ModelTranslationKt.toLocationReference(location),
                        OriginView.CLASS_DETAILS,
                        OriginComponent.ABOUT_BUSINESS_CARD
                ));
                break;
            case R.id.tv_livestream_info:
                new LivestreamInfoDialog().show(getSupportFragmentManager(), null);
                break;
            case R.id.join_livestream_button:
                viewModel.joinLiveStreamSelected(classTypeObject);
                break;
        }
    }

    private void setButtonStatus() {
        if (classTypeObject.isAvailable() && !classTypeObject.requiresPayment() && requiredFieldsStatus.requiresSomething() && !FamilyAccountsUtilsKt.isFamilyAccountType()) {
            if (requiredFieldsStatus.requiresPhone) {
                for (TextView view : bookabilityStatusTextViews) {
                    view.setText(R.string.order_summary_required_fields_legal);
                }
                for (TextView view : bookButtons) {
                    view.setText(R.string.action_review_required_fields);
                }
            } else {
                for (TextView view : bookabilityStatusTextViews) {
                    view.setText(R.string.order_summary_liability_legal);
                }
                for (TextView view : bookButtons) {
                    view.setText(R.string.action_review_liability);
                }
            }

            for (TextView view : bookabilityStatusTextViews) {
                view.setVisibility(View.VISIBLE);
            }
            for (TextView view : bookButtons) {
                view.setVisibility(View.VISIBLE);
            }
            for (TextView view : bookButtons) {
                view.setEnabled(true);
            }
        } else {

            ClassStatusMessage classStatusMessage;
            if (paymentStatus != null) {
                classStatusMessage = paymentStatus.getClassStatusMessage();
                if (classTypeObject.getStatus().getId() == BookabilityStatus.WAITLISTABLE) {
                    if (classStatusMessage == ClassStatusMessage.PASSES_AVAILABLE) {
                        classStatusMessage = ClassStatusMessage.WAITLIST_PASSES_AVAILABLE;
                    } else if (classStatusMessage == ClassStatusMessage.BOOKABLE_UNPAID) {
                        classStatusMessage = ClassStatusMessage.WAITLISTABLE;
                    }
                } else if (classTypeObject.getStatus().getId() == BookabilityStatus.CLASS_FULL) {
                    classStatusMessage = ClassStatusMessage.CLASS_FULL;
                }
            } else {
                classStatusMessage = ClassStatusMessage.getStatusMessageFromStatusCode(classTypeObject.getStatus().getId());
            }

            // We are forcing book and buy when going through LMO workflow for an unpaid status
            if (classStatusMessage == ClassStatusMessage.BOOKABLE_UNPAID && isLastMinuteOfferWorkflow) {
                classStatusMessage = ClassStatusMessage.PAYMENT_REQUIRED;
                classTypeObject.setStatus(new BookabilityStatus(BookabilityStatus.PAYMENT_REQUIRED, null));
            }

            if (classStatusMessage != null && !classTypeObject.hasClassPassed() && !classTypeObject.hasVisits()) {
                if (MBAuth.isGuestUser() && classTypeObject.isAvailable()) {
                    for (TextView view : bookabilityStatusTextViews) {
                        view.setText(R.string.guest_mode_class_book_button);
                    }
                    for (TextView view : bookabilityStatusTextViews) {
                        view.setVisibility(View.VISIBLE);
                    }
                } else {
                    for (TextView view : bookabilityStatusTextViews) {
                        if (isFamilyAccountType() && classTypeObject.isBookedAtThisTime() && !classTypeObject.isFlexOptionEnabled()) {
                            view.setText(getString(R.string.user_name_booked_at_same_time_header_text, viewModel.getUserName()));
                            view.setVisibility(View.VISIBLE);
                        } else {
                            String stringParameterToPass = null;
                            if (classTypeObject != null && classTypeObject.getLocation() != null) {
                                stringParameterToPass = classTypeObject.getLocation().getName();
                            }
                            classStatusMessage.configureStatusTextView(view, classType == Type.ENROLLMENT, stringParameterToPass);
                        }
                    }
                }

                if (preselectedPricingOption != null && classStatusMessage == ClassStatusMessage.PAYMENT_REQUIRED) {
                    for (TextView view : bookButtons) {
                        view.setText(R.string.next);
                    }
                } else {
                    for (TextView view : bookButtons) {
                        // When payment is not required but class is bookable then we need to show next for FA.
                        // For eg. class is free or passes are available or Book now pay later.
                        if (stillBookableForOtherFamilyAccountMembers()) {
                            view.setText(R.string.next);
                        } else {
                            classStatusMessage.configureBookButton(view);
                        }
                    }
                }

                if (classStatusMessage.getBookStatus() == R.string.call_business) {
                    if (location != null && !TextUtils.isEmpty(location.getPhone())) {
                        for (TextView view : bookButtons) {
                            if (stillBookableForOtherFamilyAccountMembers()) {
                                view.setText(getString(R.string.next));
                            } else {
                                view.setText(getString(R.string.action_book_call));
                            }
                        }
                        for (TextView view : bookButtons) {
                            view.setEnabled(IntentUtils.canCallNumber(this, location.getPhone()));
                        }
                    } else {
                        for (TextView view : bookButtons) {
                            view.setEnabled(false);
                        }
                    }
                }

                if (classTypeObject.getStatus().getId() == BookabilityStatus.NO_ONLINE_BOOKING) {
                    capacityCountHeader.setText(R.string.booking_not_available_text);
                    capacityCountHeader.setVisibility(View.VISIBLE);
                }

                for (TextView view : bookButtons) {
                    view.setVisibility(View.VISIBLE);
                }
            } else {
                for (TextView view : bookabilityStatusTextViews) {
                    view.setVisibility(View.GONE);
                }
                for (TextView view : bookButtons) {
                    view.setVisibility(View.GONE);
                }
            }
        }

        showProgress(false);
    }

    // We need to show next cta when class is bookable or booked or class is booked at this time status when
    // Family account is enabled and user has not navigated from profile screen
    private boolean stillBookableForOtherFamilyAccountMembers() {
        return isFamilyAccountType() && (classTypeObject.getStatus().getId() == BookabilityStatus.BOOKED || classTypeObject.getStatus().getId() == BookabilityStatus.BOOKED_AT_THIS_TIME || classTypeObject.getStatus().getId() == BookabilityStatus.BOOKABLE) && !classTypeObject.isFlexOptionEnabled();
    }

    //We need user to navigate to calender screen if it has visits or it is not family account or
    // It is family account and navigated from profile screen or it has signedIn status or is a flex class
    private boolean shouldNavigateToCalendar() {
        return ((classTypeObject != null && classTypeObject.hasVisits())
                || (StaticInstance.selectedClassTypeObject != null && StaticInstance.selectedClassTypeObject.hasVisits()))
                && (!isFamilyAccountType() || isFamilyAccountType()
                && (viewModel.isNavigatedFromProfile || classTypeObject.isSignedIn() || classTypeObject.isFlexOptionEnabled()));
    }

    //region Click Handler methods
    private void bookButtonClicked() {
        viewModel.sendClassDetailsEvent(NextSelected.INSTANCE);
        if (shouldNavigateToCalendar()) {
            //Add to calendar button?
            addToCalendar();
            return;
        }
        BookabilityStatus bookabilityStatus = classTypeObject.getStatus();
        boolean bookabilityBlockedMessageNeeded = true;
        switch (bookabilityStatus.getId()) {
            case BookabilityStatus.BOOKABLE:
            case BookabilityStatus.WAITLISTABLE:
            case BookabilityStatus.PAYMENT_REQUIRED:
            case BookabilityStatus.PAY_WAITLIST:
            case BookabilityStatus.CROSS_REGIONAL_BOOKABLE:
            case BookabilityStatus.CROSS_REGIONAL_WAITLISTABLE:
                bookabilityBlockedMessageNeeded = false;
                signUpOnClick();
                break;
            case BookabilityStatus.BOOKED_AT_THIS_TIME:
            case BookabilityStatus.PREREQUISITES_NOT_MET:
            case BookabilityStatus.NO_ONLINE_BOOKING:
            case BookabilityStatus.ONLINE_CAPACITY_FULL:
            case BookabilityStatus.CLASS_FULL:
            case BookabilityStatus.OVERLAPPING_WAITLIST_RESTRICTION:
            case BookabilityStatus.OUTSIDE_BOOKING_WINDOW:
                finish();
                break;
            case BookabilityStatus.ERROR:
                bookabilityBlockedMessageNeeded = false;
                ToastUtils.showServerErrorToast();
                break;
            case BookabilityStatus.BOOKED:
            case BookabilityStatus.CANNOT_CANCEL:
                BreadcrumbsUtils.INSTANCE.breadcrumbBookabilityBlocker(classTypeObject,
                    BookabilitySourceScreen.ClassDetails);
                bookabilityBlockedMessageNeeded = false;
                if (isFamilyAccountType()) {
                    signUpOnClick();
                } else {
                    finish();
                }
                break;

            case BookabilityStatus.UNAVAILABLE:
                bookabilityBlockedMessageNeeded = false;
                finish();
                break;
            case BookabilityStatus.LATE_CANCELLABLE:
            case BookabilityStatus.LATE_CANCELLED:
            default:
                bookabilityBlockedMessageNeeded = false;
                callBusiness();
                break;
        }
        if (bookabilityBlockedMessageNeeded) {
            logBlockerAndFinish();
        }
    }

    private void logBlockerAndFinish() {
        BreadcrumbsUtils.INSTANCE.breadcrumbBookabilityBlocker(classTypeObject, BookabilitySourceScreen.ClassDetails);
        finish();
    }


    private void callBusiness() {
        startActivity(MBPhoneUtils.getInternationalCallIntent(classTypeObject.getLocation(),
            ServiceLocator.getDeviceRepository().getNetworkCountryCode(false)));
    }

    private void cancelButtonClicked() {
        switch (cancelStatus) {
            case NON_CANCELLABLE:
                MaterialOptionDialog dialog = new MaterialOptionDialog();
                dialog.setText(
                    0,
                    R.string.call_to_cancel_dialog_message,
                    R.string.call_to_cancel_dialog_call_action,
                    R.string.call_to_cancel_copy_number);
                dialog.setHorizontalButtonStyle(true);
                dialog.setButton1Callback(new TaskCallback<DialogFragment>() {
                    @Override
                    public void onTaskComplete(DialogFragment result) {
                        executeCallIntent();
                    }
                });
                dialog.setButton2Callback(new TaskCallback<DialogFragment>() {
                    @Override
                    public void onTaskComplete(DialogFragment result) {
                        IntentUtils.copyToClipboard(ClassTypeDetailsActivity.this, classTypeObject.getLocation().getPhone());
                        ToastUtils.show(getString(R.string.phone_copied_to_clipboard));
                    }
                });
                dialog.show(getSupportFragmentManager(), "CallToCancelDialog");
                break;
            case CANCELLABLE:
                showEarlyCancelConfirmationDialog();
                break;
            case CANCELLABLE_LATE:
                showLateCancelConfirmationDialog();
                break;
            case CANCELLED:
                break;
        }
    }
    //endregion

    private void showEarlyCancelConfirmationDialog() {
        showDialogForText(0, (isDynamicPricePurchased ? R.string.dspo_cancel_dialog_message : R.string.cancel_dialog_message),
            R.string.go_back_text, R.string.confirm, result -> cancelEvent(), null);
    }

    private void showLateCancelConfirmationDialog() {
        showDialogForText(0, R.string.late_cancel_dialog_message, R.string.go_back_text, R.string.confirm,
            result -> cancelEvent(), null);
    }

    private void showDialogForText(@StringRes int headerRes, @StringRes int messageRes, @StringRes int negativeButtonRes, @StringRes int actionRes, final TaskCallback callback, @Nullable final TaskCallback cancelCallback) {
        final MaterialOptionDialog dialog = new MaterialOptionDialog();
        dialog.setText(headerRes,
            messageRes,
            actionRes,
            negativeButtonRes != 0 ? negativeButtonRes : R.string.cancel);

        dialog.setButton2Callback(result -> {
            showProgress(false);
            dialog.dismiss();

            if (cancelCallback != null) cancelCallback.onTaskComplete();
        });

        dialog.setButton1Callback(result -> {
            dialog.dismiss();
            if (callback != null) {
                callback.onTaskComplete();
            }
        });
        dialog.setHorizontalButtonStyle(true);
        dialog.show(getSupportFragmentManager(), MaterialOptionDialog.class.getCanonicalName());
    }

    private void cancelEvent() {
        showProgress(true);

        int waitlistID = classTypeObject.getWaitlistID();
        if (waitlistID < 0 && classTypeObject.hasVisits()) {
            waitlistID = classTypeObject.getVisits()[0].getWaitlistID();
        }

        if (waitlistID > 0) {
            cancelFromWaitlist();
        } else if (classTypeObject.getVisits() != null) {
            cancelFromRoster();
        } else {
            MBStaticCache.getInstance().removeCancelledVisit(DomainObjectUtils.convertToClassTypeVisit(classTypeObject));
            showProgress(false);
        }
    }

    private void cancelFromWaitlist() {
        MBStaticCache.getInstance().removeCancelledVisit(DomainObjectUtils.convertToClassTypeVisit(classTypeObject));
        StaticInstance.refreshDataAfterBooking();
        viewModel.cancelFromWaitlist(classTypeObject.getWaitlistID(), classTypeObject.getLocation());
    }

    private void cancelFromRoster() {
        final ClassTypeVisit visitObjectReference = DomainObjectUtils.convertToClassTypeVisit(classTypeObject);
        viewModel.cancelFromRoster(visitObjectReference.SiteVisitID, classTypeObject.getLocation(), visitObjectReference);
    }

    private void signupSuccess() {
        signedUpSuccess = true;
        refreshEvent();
        askForFeedback();
    }

    private void getReviews() {
        studioReviewsLoadingIndicator.setVisibility(View.VISIBLE);
        viewModel.fetchEnhanceReviewSummary(classTypeObject.getClassDescriptionId(), classTypeObject.getLocation());
        viewModel.fetchEnhanceReviewsByCourse(NUM_REVIEWS_TO_RETURN, PAGE_NUMBER, classTypeObject.getClassDescriptionId(), classTypeObject.getLocation());
    }

    //region Callbacks

    //region Utility methods
    private void cancelEventNotifications() {
        //cancels the notifications based on event visit ID
        NotificationManager notificationManager =
            (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        if (notificationManager != null && classTypeObject.hasVisits()) {
            // warning: truncation might cause conflicts
            notificationManager.cancel((int) classTypeObject.getVisits()[0].getSiteVisitId());
        }
    }
    //endregion

    public void signUpOnClick() {
        if (MBAuth.isGuestUser()) {
            // Waiting on API work for the following to be used instead
            //TODO [865063] Remove all references to class instance ID from the app
            GuestScheduleItemPricingDialog.newInstance(
                location.getSiteId(),
                classTypeObject.getId(),
                classType == Type.ENROLLMENT ? CServiceCategoryType.Enrollment : CServiceCategoryType.Class,
                location.getLocale(),
                DeepLinkUtils.getClassUri(location.getId(), classTypeObject.getId())
            ).show(getSupportFragmentManager(), null);
            return;
        }

        User user = MBAuth.getUser();
        if (user != null && !user.isVerified()) {
            VerifyAccountDialog dialog = new VerifyAccountDialog();
            dialog.show(getSupportFragmentManager(), VerifyAccountDialog.class.getSimpleName());
            return;
        }

        // Launch appropriate required fields activity
        if (!classTypeObject.requiresPayment() && requiredFieldsStatus.requiresPhone && !FamilyAccountsUtilsKt.isFamilyAccountType()) {
            StaticInstance.requiredFieldsStatus = requiredFieldsStatus;

            if (FeatureFlag.WAITLIST_CONFIRMATION_REBRAND.isFeatureEnabled()) {
                showSmsConsent();
            } else {
                showRequiredFields();
            }
        } else if (!classTypeObject.requiresPayment() && requiredFieldsStatus.requiresLiability && !FamilyAccountsUtilsKt.isFamilyAccountType()) {
            showLiabilityRelease();
        } else {
            // If payment required, have them sign the liability after checkout
            finishSignUpOnClick();
        }
    }

    private void showSmsConsent() {
        ViewGroup root = findViewById(android.R.id.content);
        ComposeView composeView = new ComposeView(root.getContext());
        root.addView(composeView);

        String fullText = getString(com.mindbodyonline.ui.R.string.waitlistConsent_fine_print);
        Map<String, String> labelsToUrls = new HashMap<>();
        labelsToUrls.put(
                getString(com.mindbodyonline.ui.R.string.waitlistConsent_fine_print_hyperlink_text),
                getString(com.mindbodyonline.ui.R.string.waitlistConsent_fine_print_hyperlink_url)
        );
        String title = getString(com.mindbodyonline.ui.R.string.waitlistConsent_title);
        AnnotatedString finePrint = AnnotatedStringHelperKt.addHyperlinksTo(
                fullText,
                labelsToUrls,
                AnnotatedStringHelperKt.defaultSpanStyle()
        );
        String ctaLabel = getString(com.mindbodyonline.ui.R.string.waitlistConsent_cta_label);
        String backButton = getString(com.mindbodyonline.ui.R.string.waitlistConsent_cancel);

        WaitlistConsentScreenUiState uiState = new WaitlistConsentScreenUiState(
                title,
                finePrint,
                ctaLabel,
                backButton
        );

        WaitlistConsentScreenEvents events = new WaitlistConsentScreenEvents(
                () -> { // onCtaClick
                    root.removeView(composeView);
                    showRequiredFields();
                    return null;
                },
                () -> { // onCloseClick
                    root.removeView(composeView);
                    return null;
                }
        );

        ComposeUtils.setSmsConsentContent(uiState, events, composeView);
    }

    private void showRequiredFields() {
        Intent liabilityIntent = RequiredFieldsActivity.newIntent(
                getApplicationContext(),
                ModelTranslationKt.toLocationReference(location)
        );
        startActivityForResult(liabilityIntent, Constants.REQUEST_CODE_REQUIRED_FIELDS);
        showProgress(false);
    }

    private void showLiabilityRelease() {
        StaticInstance.liabilityRelease = requiredFieldsStatus.liabilityRelease;
        Intent liabilityIntent = LiabilityReleaseActivity.newIntent(getApplicationContext(), location);
        startActivityForResult(liabilityIntent, Constants.REQUEST_CODE_REQUIRED_FIELDS);
        SharedPreferencesUtils.setIgnoreLiability(true);
        showProgress(false);
    }

    private void finishSignUpOnClick() {
        String staffName = null;
        if (classTypeObject.getStaff() != null) {
            staffName = classTypeObject.getStaff().getDisplayName();
        }

        TrackingHelperUtils.INSTANCE.trackCheckoutBook(location, classTypeObject.getName(),
            classType == Type.CLASS ? InventoryType.CLASS : InventoryType.ENROLLMENT,
            PageName.CLASS_DETAILS, staffName, classTypeObject.getGatewayId());
        if (classTypeObject.isAvailable()) {
            launchCheckoutActivity(classTypeObject, null);
        } else {
            executeCallIntent();
        }
        supportInvalidateOptionsMenu();
    }

    private void launchCheckoutActivity(ClassTypeObject cto, FamilyAccount selectedFamilyAccount) {
        TimeDatePriceViewModel pricingViewModel = classDetailCard.getPricingViewModel();
        if (pricingViewModel != null) {
            Map<String, Object> data = new HashMap<>();
            data.put(AnalyticsUtils.KEY_QB_OPENED_FROM, "Class Detail");
            data.put(AnalyticsUtils.KEY_STUDIO_ID, classTypeObject.getLocation().getSiteId());
            data.put(AnalyticsUtils.KEY_QB_IS_FREE, classTypeObject.isFreeToEnroll());
            String price = PaymentUtils.getFormattedCurrency(classDetailCard.getPricingViewModel().isFreeEnrollment() ? BigDecimal.ZERO :
                pricingViewModel.getPrice(), pricingViewModel.getLocale(), true);
            data.put(AnalyticsUtils.KEY_QB_TRIGGER_PRICE, price);
            QuickBookViewModel.sendQuickBookOpenedEvent(data);
        } else {
            AnalyticsUtils.logEvent("(Quickbook) | Quickbook opened", "Area tapped from", "Class Detail");
        }

        if (!DevelopmentFlag.Development_QBV2.isFeatureEnabled() ||
            cto.getInventoryRefJson() == null || FamilyAccountsUtilsKt.isFamilyAccountType()) {
            QuickBookDialog quickBookV1 = QuickBookDialog.newInstance(
                    cto,
                    selectedFamilyAccount,
                    classType == Type.ENROLLMENT ? OriginView.EVENT_DETAILS : OriginView.CLASS_DETAILS,
                    OriginComponent.NEXT_BUTTON
            );
            ServiceLocator.getPerfMeasureRepo().startPerformanceMeasure(System.currentTimeMillis(), PerformanceName.BOOK_TO_COMPLETE_OR_ABANDON);
            ServiceLocator.getPerfMeasureRepo().startPerformanceMeasure(System.currentTimeMillis(), PerformanceName.BOOK_TO_QB_READY);
            quickBookV1.setSuccessCallback(classTypeObjectResult -> {
                if (preselectedPricingOption != null) {
                    showDealPurchasedDialog = true;
                    preselectedPricingOption = null;
                }
                fullScreenConfirmationLauncher.launch(FullScreenConfirmationScreenActivity.newCTOIntent(
                                this,
                                classTypeObjectResult != null ? classTypeObjectResult.getClassTypeObject() : null,
                                classTypeObjectResult != null ? classTypeObjectResult.getCart() : null,
                                classTypeObjectResult != null ? classTypeObjectResult.getPaymentMethod() : null,
                                classTypeObjectResult != null ? classTypeObjectResult.getPricingReference() : null,
                                location,
                                classTypeObjectResult != null && classTypeObjectResult.getShowAttributionSurvey(),
                                classTypeObjectResult != null ? classTypeObjectResult.getOrderId() : 0L,
                                classTypeObjectResult != null ? classTypeObjectResult.getQbViewState() : null
                        )
                );
            });
            quickBookV1.setFailureCallback(
                failureResult -> {
                    if (failureResult == QuickbookFailureReason.IAP) {
                        InAppPurchasesBlockedDialog dialog = InAppPurchasesBlockedDialog.newInstance(
                            MBPhoneUtils.getInternationalPhoneNumber(classTypeObject.getLocation(),
                                ServiceLocator.getDeviceRepository().getNetworkCountryCode(false)));
                        dialog.show(this.getSupportFragmentManager(), null);
                    }
                }
            );
            quickBookV1.setSelectedFamilyMemberCallback(result -> onFamilyAccountSelected(quickBookV1, result));
            // We need to provide new tag everytime so that it reopens a new dialog for selected family account.
            quickBookV1.show(getSupportFragmentManager(), QuickBookDialog.SELECTED_USER_TAG != null ? QuickBookDialog.SELECTED_USER_TAG : QuickBookDialog.TAG);

        } else {
            if (BookingUtils.INSTANCE.checkAndInformClassStartedOrDone(classTypeObject, this)) {
                return;
            }
            QuickBookDialogV2.newInstance(
                new QuickBookInitializer(QuickBookType.Class, classTypeObject.getInventoryRefJson(),
                    ModelTranslationKt.toLocationReference(classTypeObject.getLocation()),
                    classTypeObject.getDspoPrice() != null),
                null,
                failureReason -> {
                    if (failureReason == QuickbookFailureReason.IAP) {
                        InAppPurchasesBlockedDialog dialog = InAppPurchasesBlockedDialog.newInstance(
                            MBPhoneUtils.getInternationalPhoneNumber(classTypeObject.getLocation(),
                                ServiceLocator.getDeviceRepository().getNetworkCountryCode(false)));
                        dialog.show(getSupportFragmentManager(), null);
                    } else {
                        BookingUtils.INSTANCE.checkAndInformClassStartedOrDone(classTypeObject, this);
                    }
                    return Unit.INSTANCE;
                }
            ).show(getSupportFragmentManager(), QuickBookDialogV2.TAG);
        }
    }

    private void askForFeedback() {
        if (SharedPreferencesUtils.userCanBeAskedForAppFeedback()) {
            LocationReference locationReference = null;
            if (location != null) {
                locationReference = ModelTranslationKt.toLocationReference(location);
            }
            DialogUtilsKtKt.createApplicationFeedbackDialog(this, locationReference, dialog -> {
                runOnUiThread(() -> dialog.show(getSupportFragmentManager(), null));
                return Unit.INSTANCE;
            });
        }
    }

    //endregion

    //region Navigation methods

    /***
     * This method is to dismiss existing dialog and open new one with new tag. It calls
     * launchCheckouActivity() with classTypeObject and also provides familyAccount.
     * @param quickBook existing object of dialog which needs to dismiss
     * @param result call back result of type map having class type object and family account object
     */
    private void onFamilyAccountSelected(QuickBookDialog quickBook, Map<String, Object> result) {
        if (result != null) {
            quickBook.dismiss();
            FamilyAccount familyAccount = (FamilyAccount) result.get(KEY_SELECTED_USER);
            if (familyAccount != null && classTypeObject != null) {
                QuickBookDialog.SELECTED_USER_TAG = QuickBookDialog.TAG + familyAccount.getId();
                SharedPreferencesUtils.setSelectedUserId((familyAccount.isPrimaryUser()) ? "" : familyAccount.getId());
                launchCheckoutActivity(classTypeObject, familyAccount);
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case Constants.REQUEST_CODE_REQUIRED_FIELDS:
                if (requiredFieldsStatus.requiresLiability) {
                    SharedPreferencesUtils.setIgnoreLiability(false);
                }
                if (resultCode == RESULT_OK || resultCode == LiabilityReleaseActivity.RESULT_UPLOAD_FAILED) {
                    requiredFieldsStatus = new APIWorkflowUtil.RequiredFieldsStatus();
                    setButtonStatus();
                } else {
                    Snackbar.make(classDetailCard, getString(R.string.required_fields_error, classTypeObject.getLocation().getStudioName()), Snackbar.LENGTH_LONG).show();
                }
                break;
            case CLASS_DETAILS_REQUEST_CODE:
                if (resultCode != Activity.RESULT_CANCELED) {
                    DialogUtils.showClassOrEventConfirmationDialog(
                            this,
                            StaticInstance.selectedClassTypeObject,
                            null,
                            null,
                            null,
                            location,
                            resultCode,
                            false,
                            0L,
                            null
                    );
                    StaticInstance.selectedClassTypeObject = null;
//                    fetchUpcomingClasses();
                }
                break;
            default:
        }
    }

    private void executeCallIntent() {
        startActivity(MBPhoneUtils.getInternationalCallIntent(classTypeObject.getLocation(),
            ServiceLocator.getDeviceRepository().getNetworkCountryCode(false)));
    }

    private void addToCalendar() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CALENDAR)
            != PackageManager.PERMISSION_GRANTED) {

            if (ActivityCompat.shouldShowRequestPermissionRationale(this,
                android.Manifest.permission.WRITE_CALENDAR)) {
                checkCalendarSync();
            } else {
                // No explanation needed, we request the permission every time
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.READ_CALENDAR, Manifest.permission.WRITE_CALENDAR},
                    Constants.READ_CALENDAR_PERMISSIONS_CODE);
            }

        } else {
            checkCalendarSync();
        }
    }

    private void checkCalendarSync() {
        CalendarUtils.addClassToCalendarCheckSyncFirst(this,
            getSupportFragmentManager(), classTypeObject, new TaskCallback<ClassTypeObject>() {
                @Override
                public void onTaskComplete(ClassTypeObject result) {
                    if (classTypeObject.hasVisits()) {
                        Visit visit = classTypeObject.getVisits()[0];
                        CalendarUtils.copyClassDetailsIntoVisit(classTypeObject, visit);
                        CalendarUtils.addClassVisitToCalendar(ClassTypeDetailsActivity.this, DomainObjectUtils.convertToClassTypeVisit(visit), location);
                        adjustBookButtonAfterCancelStatus();
                    }
                }
            }
        );
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
        @NotNull String permissions[], @NotNull int[] grantResults) {
        // Added this super call to remove warning
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case Constants.READ_CALENDAR_PERMISSIONS_CODE: {
                addToCalendarOnResume = true;
            }
        }
    }

    private boolean isInRadius() {
        boolean isInRadius = false;
        if (geofenceLocation != null) {
            double distance = GeoLocationUtils.distanceBetweenLatLngsInMeters(
                new LatLng(classTypeObject.getLocation().getLatitude(), classTypeObject.getLocation().getLongitude()),
                new LatLng(geofenceLocation.getLatitude(), geofenceLocation.getLongitude()));

            isInRadius = distance < GeofenceUtils.DEFAULT_RADIUS;
        }

        return isInRadius;
    }

    private void checkLocationForGeofence() {
        geofenceLocation = GeoLocationUtils.getBestLocation(this);
        if (!isInRadius()) {
            if (mGoogleApiClient != null) {
                mGoogleApiClient.connect();
            }
        } else {
            processSignInActionItems();
        }
    }

    /**
     * Interface implementations for geofenced check ins
     */

    @Override
    public void onConnected(Bundle bundle) {
        geofenceLocation = GeoLocationUtils.getBestLocation(this);
        if (isInRadius()) {
            processSignInActionItems();
        }
    }

    //endregion

    private boolean processSignInActionItems() {
        if (classTypeObject == null || bookButtons == null) return false;

        showSignInButton = false;

        final Date window = new Date();
        window.setTime((new Date().getTime() + Constants.GEOFENCE_START_WINDOW_IN_MILLS));

        boolean onTheWaitlist = classTypeObject.onTheWaitlist() ||
            (classTypeObject.hasVisits() &&
                classTypeObject.getVisits()[0].getWaitlistPosition() != null &&
                classTypeObject.getVisits()[0].getWaitlistPosition() > 0);

        boolean isSignedIn = !classTypeObject.hasClassPassed() && !onTheWaitlist &&
            (classTypeObject.isSignedIn() ||
                (classTypeObject.hasVisits() && classTypeObject.getVisits()[0].isSignedIn()));

        if (classTypeObject.hasVisits() &&
            !classTypeObject.hasClassPassed() &&
            !onTheWaitlist &&
            classTypeObject.getStartDate().before(window) &&
            classTypeObject.getLocation().getStudioAllowsGeofenceCheckins() &&
            isInRadius() &&
            !isSignedIn) {
            for (TextView view : bookButtons) {
                view.setText(getString(R.string.action_signin_class));
            }
            for (TextView view : bookButtons) {
                view.setVisibility(View.VISIBLE);
            }
            for (TextView view : bookButtons) {
                view.setEnabled(true);
            }
            showSignInButton = true;
        } else if (isSignedIn) {
            showSignInButton = false;
            initCapacityCountHeader();
        }
        return showSignInButton;
    }

    @Override
    public void onConnectionSuspended(int i) {
    }

    @Override
    public void onConnectionFailed(@NotNull ConnectionResult connectionResult) {
    }

    private void showErrorSnackbar(String message) {
        Snackbar.make(root, message, Snackbar.LENGTH_LONG).show();
    }

    // keep a local copy to prevent GC
    private void storeBitmap() {
        if (!gettingBitmap) {
            gettingBitmap = true;
            try {
                new ShareClassImageGenerator(
                    classTypeObject,
                    classTypeObject.getLocation(),
                    ((ImageView) staffView.findViewById(R.id.staff_list_row_image_view)).getDrawable(),
                    ((ImageView) businessDetailCard.findViewById(R.id.studio_logo)).getDrawable(),
                    root,
                    getFilename()).generate(uri -> {

                    screenshotUri = uri;
                    gettingBitmap = false;
                    if (waitingForScreenshot) shareScreenshot();
                    return Unit.INSTANCE;
                });
            } catch (Exception ignored) {
                // This failed due to PS-1117.  Cause unknown, catching for now
            }
        }
    }

    private void shareScreenshot() {
        waitingForScreenshot = false;
        viewModel.sendClassDetailsEvent(ClassDetailsShareSelected.INSTANCE);
        IntentUtils.shareClassBookingIntent(this, classTypeObject, screenshotUri);
    }

    private void addEnhancedReviewAverageDataView(ReviewSummaryData reviewSummaryData) {
        if (reviewSummaryData == null) return;
        List<ResponseSummaryAttributes> responseSummaryList = reviewSummaryData.getReviewSummaryAttributes();
        if (responseSummaryList.size() > 0) {
            List<ResponseSummaryAttributes> avgSummaryList = fetchSummaryAttributesByType(responseSummaryList, GatewayResourceType.TYPE_RANGE_STARS);
            List<ResponseSummaryAttributes> experienceRatingList = fetchSummaryAttributesByType(responseSummaryList, GatewayResourceType.TYPE_LABELED_RATING);
            if (avgSummaryList.size() > 0) {
                //Adding Average Review rating View
                averageRatingsContainer.addView(getAverageRatingView(avgSummaryList.get(0)));
            }

            if (experienceRatingList.size() > 0) {
                ExperienceModel experienceModel = new ExperienceModel();
                experienceModel.setRatingHeadingVisible(true);
                experienceModel.setExperienceList(experienceRatingList);
                //Adding Experience rating view
                averageRatingsContainer.addView(getExperienceRatingView(experienceModel));
            }
        }
    }

    private void handleReviewsListData(List<RatingCardViewModel> reviewsList) {
        if (reviewsList != null) {
            studioReviewsLoadingIndicator.setVisibility(View.GONE);
            if (reviewsList.size() == 0) {
                studioReviewsHeader.setVisibility(View.GONE);
                studioReviewsContainer.setVisibility(View.GONE);
                return;
            }

            studioReviewsHeader.setVisibility(View.VISIBLE);
            studioReviewsContainer.setVisibility(View.VISIBLE);

            int ratingCardIndex = 0;
            for (RatingCardViewModel vm : reviewsList) {
                RatingCard card = new RatingCard(ClassTypeDetailsActivity.this);
                card.setClassDetailsScreen(true);
                card.setViewModel(vm);
                card.setOnReportAbuseClickListener(() -> {
                    MaterialOptionDialog dialog = new MaterialOptionDialog();
                    dialog.setText(R.string.flag_as_inappropriate, 0, R.string.report_abuse, R.string.close);
                    dialog.setButton1Callback(result -> {
                        viewModel.reportAbuse(vm.getId());
                        dialog.dismiss();
                    });
                    dialog.setButton2Callback(result -> dialog.dismiss());

                    dialog.show(getSupportFragmentManager(), null);
                });
                studioReviewsContainer.addView(card);
                int finalRatingCardIndex = ratingCardIndex;
                card.setOnClickListener(v -> {
                    viewModel.sendClassDetailsEvent(new ReviewsReadMoreSelected(finalRatingCardIndex));
                    ClassTypeDetailsActivity.this.startActivityForResult(
                        ReviewDetailsActivity.newIntent(v.getContext(), null, vm)
                            .putExtra(Constants.KEY_LOCATION_STUDIO_NAME, location.getStudioName()),
                        BusinessDetailsActivity.DELETE_REVIEW);
                });
                ratingCardIndex++;
            }

            if (reviewsList.size() == StudioReviewsRepository.TOP_REVIEWS_COUNT) {
                studioReviewsHeader.setOnClickListener(v -> {
                    Intent listActivityIntent = ReviewListActivity.newIntent(v.getContext(), location.getId(), new int[]{classTypeObject.getClassDescriptionId()}, classTypeObject.getTotalReviews());
                    v.getContext().startActivity(listActivityIntent);
                    viewModel.sendClassDetailsEvent(ReviewsSeeAllSelected.INSTANCE);
                });
            }
        } else {
            MBLog.e("ClassTypeDetails", "Network error getting location reviews");
            studioReviewsLoadingIndicator.setVisibility(View.GONE);
            studioReviewsContainer.setVisibility(View.GONE);
            studioReviewsHeader.setVisibility(View.GONE);
        }
    }

    private View getAverageRatingView(ResponseSummaryAttributes attributes) {
        AverageRatingView averageRatingView = new AverageRatingView(this, null);
        averageRatingView.setAverageRatingData(getAverageRatingModel(attributes), false);
        return averageRatingView;
    }

    private View getExperienceRatingView(ExperienceModel experienceModel) {
        ExperienceRatingCard experienceRatingCard = new ExperienceRatingCard(this);
        experienceRatingCard.setExperienceRatingData(experienceModel);
        return experienceRatingCard;
    }

    public enum Type {
        CLASS("class"),
        ENROLLMENT("event");

        private String type;

        Type(String t) {
            type = t;
        }

        @NotNull @Override
        public String toString() {
            return type;
        }
    }

    /**
     * Waits until all the required {@link Drawable}s are available before performing actions that
     * rely on them (eg, creating and storing the share screen image)
     */
    private class WaitForImages {
        private final @NotNull Runnable onFinishedWaiting;
        private boolean staffDrawableAvailable = false;
        private boolean studioDrawableAvailable = false;

        public WaitForImages(@NotNull Runnable onFinishedWaiting) {
            this.onFinishedWaiting = onFinishedWaiting;
        }

        public void setStaffDrawableAvailable() {
            staffDrawableAvailable = true;
            fieldSet();
        }

        public void setStudioDrawableAvailable() {
            studioDrawableAvailable = true;
            fieldSet();
        }

        /**
         * Reacts to a field being set. Essentially an internal observer
         */
        private void fieldSet() {
            if (staffDrawableAvailable && studioDrawableAvailable) {
                onFinishedWaiting.run();
            }
        }
    }

}
