package com.mindbodyonline.connect.activities.details;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;

import com.google.android.gms.maps.model.LatLng;
import com.mindbodyonline.android.util.TaskCallback;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.activities.custom.MBCompatActivity;
import com.mindbodyonline.connect.activities.details.viewmodels.AppointmentDetailsViewModel;
import com.mindbodyonline.connect.activities.details.viewmodels.AppointmentDetailsViewModel.ToastViewState;
import com.mindbodyonline.connect.activities.details.viewmodels.AppointmentDetailsViewModelFactory;
import com.mindbodyonline.connect.activities.list.services.classes.ClassStaffDetailsActivity;
import com.mindbodyonline.connect.analytics.OriginComponent;
import com.mindbodyonline.connect.analytics.OriginView;
import com.mindbodyonline.connect.analytics.UtilsKt;
import com.mindbodyonline.connect.analytics.appointment.AppointmentEventTracker;
import com.mindbodyonline.connect.analytics.cancel.CancelEventTracker;
import com.mindbodyonline.connect.common.components.BusinessDetailCard;
import com.mindbodyonline.connect.common.components.ClassDetailCard;
import com.mindbodyonline.connect.common.components.ClassDetailViewModel;
import com.mindbodyonline.connect.common.components.SeeAllSubHeader;
import com.mindbodyonline.connect.common.components.TextCard;
import com.mindbodyonline.connect.common.components.TextCardViewModel;
import com.mindbodyonline.connect.common.components.TimeDatePriceRow;
import com.mindbodyonline.connect.common.components.TimeDatePriceViewModel;
import com.mindbodyonline.connect.common.repository.LocationRepository;
import com.mindbodyonline.connect.common.viewmodeladapters.BusinessDetailCardViewModelAdapter;
import com.mindbodyonline.connect.utils.AnalyticsUtils;
import com.mindbodyonline.connect.utils.CalendarUtils;
import com.mindbodyonline.connect.utils.Constants;
import com.mindbodyonline.connect.utils.DomainObjectUtils;
import com.mindbodyonline.connect.utils.GeoLocationUtils;
import com.mindbodyonline.connect.utils.IntentUtils;
import com.mindbodyonline.connect.utils.KeyboardUtils;
import com.mindbodyonline.connect.utils.SharedPreferencesUtils;
import com.mindbodyonline.connect.utils.ToastUtils;
import com.mindbodyonline.connect.utils.Utils;
import com.mindbodyonline.connect.utils.api.ModelTranslationKt;
import com.mindbodyonline.connect.utils.api.fitnessactivity.FitnessActivityViewModel;
import com.mindbodyonline.connect.utils.api.fitnessactivity.fitbit.FitBitAPI;
import com.mindbodyonline.connect.utils.api.fitnessactivity.fitbit.FitbitViewModel;
import com.mindbodyonline.connect.utils.api.fitnessactivity.googlefit.GoogleFitAPI;
import com.mindbodyonline.connect.utils.api.fitnessactivity.googlefit.GoogleFitViewModel;
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI;
import com.mindbodyonline.connect.widgets.v3.WidgetUtils;
import com.mindbodyonline.data.StaticInstance;
import com.mindbodyonline.data.services.MBStaticCache;
import com.mindbodyonline.data.services.locator.ServiceLocator;
import com.mindbodyonline.domain.AppointmentStatus;
import com.mindbodyonline.domain.AppointmentTypeVisit;
import com.mindbodyonline.domain.BaseVisit;
import com.mindbodyonline.domain.Location;
import com.mindbodyonline.domain.Rating;
import com.mindbodyonline.framework.abvariant.DevelopmentFlag;
import com.mindbodyonline.views.FitnessActivityBadgeView;
import com.mindbodyonline.views.LoadingOverlay;
import com.mindbodyonline.views.MiniLocationView;
import com.mindbodyonline.views.RateClassView;
import com.mindbodyonline.views.dialog.MaterialOptionDialog;
import com.mindbodyonline.views.dialog.RateReviewDialog;
import com.mindbodyonline.views.staff.ClassTypeStaffView;

import org.jetbrains.annotations.NotNull;

import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;

import kotlin.Unit;
import kotlin.collections.ArraysKt;


/**
 * Shows information about a booked appointment
 * <p>
 * NOTE: this is not {@link AppointmentTypeDetailsActivity}, which shows details about an unbooked
 * appointment
 */
public class AppointmentDetailsActivity extends MBCompatActivity {
    private static final String ANALYTICS_PREFIX = "Appointment Details";
    public static final String CANCEL_APPOINTMENT_DIALOG_TAG = "CancelAppointmentDialog";
    private LoadingOverlay mLoadingOverlay;
    private RateClassView mRateAppointmentView;
    private Rating appointmentRating;
    private FitnessActivityBadgeView fitnessActivityBadgeView;

    private ClassTypeStaffView staffView;
    private TextCard mApptDescription;
    private SeeAllSubHeader mApptDescriptionHeader;
    private MiniLocationView mApptLocationView;
    private TextView bottomContainerMessage;
    private TextView bottomContainerClearButton;
    private TextView bottomContainerOrangeButton;
    private TimeDatePriceRow timeDatePriceRow;
    private ClassDetailCard classDetailCard;
    private View studioDetailHeader;
    private BusinessDetailCard businessDetailCard;

    private View.OnClickListener addToCalendarListener, bookSimilarAppointmentListener;
    private Rating rating;

    private AppointmentDetailsViewModel viewModel;

    private Location location;

    public static Intent newInstance(Context context, String bookingRefJson) {
        Intent intent = new Intent(context, AppointmentDetailsActivity.class);
        intent.putExtra(Constants.KEY_BUNDLE_VISITID, bookingRefJson);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_appointment_details);

        AppointmentDetailsViewModelFactory appointmentDetailsViewModelFactory = new AppointmentDetailsViewModelFactory(SwamisAPI.getInstance(), ServiceLocator.getUserRepository(), ServiceLocator.getLocationRepository(), ServiceLocator.getFavoriteStaffRepository(), this, null);
        viewModel = new ViewModelProvider(this, appointmentDetailsViewModelFactory).get(AppointmentDetailsViewModel.class);

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setHomeButtonEnabled(true);
            actionBar.setDisplayHomeAsUpEnabled(true);
        }
        setActionBarTitle(getString(R.string.title_activity_appointment_details));

        mLoadingOverlay = findViewById(R.id.appointment_details_loading_overlay);
        classDetailCard = findViewById(R.id.class_detail_card);
        studioDetailHeader = findViewById(R.id.about_this_studio_header);
        businessDetailCard = findViewById(R.id.business_detail_card);
        mRateAppointmentView = findViewById(R.id.rate_appointment_view);
        fitnessActivityBadgeView = findViewById(R.id.activity_appointment_details_fitbit_badge_view);
        staffView = findViewById(R.id.appt_type_staff_view);
        mApptDescription = findViewById(R.id.appt_type_details_description);
        mApptDescriptionHeader = findViewById(R.id.appt_type_details_description_header);
        mApptLocationView = findViewById(R.id.appt_type_details_map_view);
        mApptLocationView.setAnalyticsPrefix(ANALYTICS_PREFIX);
        bottomContainerMessage = findViewById(R.id.appt_type_status_message);
        bottomContainerClearButton = findViewById(R.id.appt_type_cancel_button);
        bottomContainerOrangeButton = findViewById(R.id.appt_type_book_button);
        mRateAppointmentView.setBookingTypeString(getString(R.string.appointment));
        mRateAppointmentView.findViewById(R.id.class_rating).setEnabled(false);
        mLoadingOverlay.show();

        initObservers();
        mLoadingOverlay.show();
        viewModel.retrieveAppointmentVisit(getIntent().getStringExtra(Constants.KEY_BUNDLE_VISITID));
    }

    private void initObservers() {
        viewModel.getAppointmentVisit().observe(this, this::handleAppointmentVisitChange);
        viewModel.getLocation().observe(this, this::handleLocationUpdate);
        viewModel.getToastMessage().observe(this, this::handleToastMessage);
    }

    private void handleToastMessage(ToastViewState toastViewState) {
        mLoadingOverlay.hide();
        ToastUtils.show(getString(toastViewState.getToastMessageId()));
        if (toastViewState.getUpdateWidget()) WidgetUtils.refreshWidget(this, this);
        if (toastViewState.getFinished()) finish();
    }

    private void handleAppointmentVisitChange(AppointmentTypeVisit appointmentTypeVisit) {
        if (appointmentTypeVisit == null) {
            finish();
            return;
        }

        AppointmentStatus status = ArraysKt.firstOrNull(appointmentTypeVisit.getStatus());
        if (status != null && status.getStatusCodeId() == AppointmentStatus.CANCELLED) {
            StaticInstance.refreshDataAfterBooking();
            finish();
        }
        finishLoadingSetVisit(appointmentTypeVisit);
    }

    private void finishLoadingSetVisit(AppointmentTypeVisit visit) {
        setAppointmentTypeVisit(visit);
        initListeners(visit);
        updateView(visit);
    }

    public void setAppointmentTypeVisit(AppointmentTypeVisit appointmentTypeVisit) {
        setActionBarTitle(appointmentTypeVisit.getAppointmentName());
        Double distanceToAppointment = getDistanceToAppointment();
        classDetailCard.setViewModel(new ClassDetailViewModel(
            appointmentTypeVisit.getAppointmentName(),
            appointmentTypeVisit.getLocationName(),
            distanceToAppointment,
            null,
            appointmentTypeVisit.getCategoryType())); // hmm, I feel like room is more important for appointments

        Intent intent = getIntent();
        OriginView originView = (OriginView) intent.getSerializableExtra(Constants.KEY_BUNDLE_ORIGIN_VIEW);
        OriginComponent originComponent = (OriginComponent) intent.getSerializableExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT);

        AppointmentEventTracker.trackAppointmentDetailsViewedEvent(
                originView,
                originComponent,
                appointmentTypeVisit,
                viewModel.getLocation().getValue()
        );

        Locale locale = Locale.getDefault();
        TimeZone timeZone = TimeZone.getDefault();
        if (viewModel.getLocation().getValue() != null) {
            if (viewModel.getLocation().getValue().getLocale() != null) {
                locale = viewModel.getLocation().getValue().getLocale();
            }
            if (viewModel.getLocation().getValue().getTimezoneId() != null) {
                timeZone = TimeZone.getTimeZone(viewModel.getLocation().getValue().getTimezoneId());
            }
        }
        classDetailCard.setPriceViewModel(new TimeDatePriceViewModel(
            appointmentTypeVisit.getStartCal().getTime(),
            appointmentTypeVisit.getEndCal().getTime(),
            null,
            null,
            locale,
            false,
            timeZone
        ));

        setAppointmentRatingViewVisibility(appointmentTypeVisit);
        staffView.setStaffData(appointmentTypeVisit.getStaffMember(), null, false, null);
        staffView.setOnClickListener(v -> {
            long staffId = appointmentTypeVisit.getStaffMember() != null ? appointmentTypeVisit.getStaffMember().getId() : 0;
            int masterLocId = viewModel.getLocation().getValue() != null ? viewModel.getLocation().getValue().getId() : LocationRepository.ILLEGAL_MASTER_LOCATION_ID;
            Intent staffBioIntent = ClassStaffDetailsActivity.newIntent(
                    AppointmentDetailsActivity.this,
                    staffId,
                    masterLocId,
                    OriginView.APPOINTMENT_STAFF_SELECTION,
                    OriginComponent.STAFF_CARD
            );
            staffBioIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(staffBioIntent);
        });

        String description = appointmentTypeVisit.getAppointmentTypeDescription();
        if (description != null && !description.trim().isEmpty()) {
            description = Utils.unescapeAndStripHtml(description);
            mApptDescription.setViewModel(new TextCardViewModel(getString(R.string.business_details_description_text), description));
        }

        if (appointmentTypeVisit.isCompleted()) {
            long visitDataId = appointmentTypeVisit.getVisitDataId();
            appointmentRating = visitDataId <= 0 ? null :
                MBStaticCache.getInstance().getRating(visitDataId);
            if (appointmentRating != null) {
                mRateAppointmentView.setRating(appointmentRating);
            }
        }

        supportInvalidateOptionsMenu();
    }

    private void setAppointmentRatingViewVisibility(AppointmentTypeVisit appointmentTypeVisit) {
        boolean showRating = appointmentTypeVisit.isCompleted() && appointmentTypeVisit.hasPassed();
        if (DevelopmentFlag.DEVELOPMENT_HIDE_BOOKER_RATING.isFeatureEnabled()){
            showRating = showRating && Objects.equals(appointmentTypeVisit.getBookingReferenceJsonModel().getInventory_source(), SwamisAPI.MB_INVENTORY_SOURCE);
        }

        if (showRating) {
            mRateAppointmentView.setVisibility(View.VISIBLE);

            rating = appointmentTypeVisit.getVisitDataId() > 0 ?
                MBStaticCache.getInstance().getRating(appointmentTypeVisit.getVisitDataId()) :
                null;
            if (rating != null) {
                mRateAppointmentView.setRating(rating);
            }

            mRateAppointmentView.setOnClickListener(v -> {
                    RateReviewDialog dialog = new RateReviewDialog();
                    dialog.setVisit(appointmentTypeVisit);
                    dialog.setSuccessCallback(result -> {
                        KeyboardUtils.hideKeyboard(AppointmentDetailsActivity.this, mRateAppointmentView);
                        if (rating == null) rating = new Rating();
                        rating.setRating(result != null ? result.getRating() : 0);
                        rating.setReviewText(result != null ? result.getReviewText() : "");
                        if (result == null) rating.setId(0);
                        mRateAppointmentView.setRating(rating);
                    });
                    dialog.show(getSupportFragmentManager(), RateReviewDialog.REVIEW_DIALOG_TAG);
                AnalyticsUtils.logVisitEvent("(Visit details) | Rate review tapped", appointmentTypeVisit);
            });
        } else {
            mRateAppointmentView.setVisibility(View.GONE);
        }
    }

    @Nullable private Double getDistanceToAppointment() {
        LatLng appointmentLatLng = null;
        if (viewModel.getLocation().getValue() != null) {
            appointmentLatLng = new LatLng(viewModel.getLocation().getValue().getLatitude(),
                viewModel.getLocation().getValue().getLongitude());
        }
        Double distanceToAppointment = null;
        if (appointmentLatLng != null) {
            distanceToAppointment =
                GeoLocationUtils.distanceBetweenLatLngsInMiles(GeoLocationUtils.toLatLng(GeoLocationUtils.getBestLocation(this)), appointmentLatLng);
        }
        return distanceToAppointment;
    }

    private void handleLocationUpdate(Location location) {
        this.location = location;
        if (location != null) {
            mApptLocationView.setLocation(location);
            businessDetailCard.setViewModel(BusinessDetailCardViewModelAdapter.adapt(location));
        }
        businessDetailCard.setOnClickListener(v -> {
                    if (location != null) {
                        startActivity(BusinessDetailsActivity.newIntent(
                                this,
                                ModelTranslationKt.toLocationReference(location),
                                OriginView.APPOINTMENT_DETAILS,
                                OriginComponent.ABOUT_BUSINESS_CARD
                        ));
                    }
                }
        );
        mLoadingOverlay.hide();
    }

    private void updateBottomContainer(AppointmentTypeVisit appointmentTypeVisit) {
        if (appointmentTypeVisit.hasPassed()) {
            bottomContainerMessage.setVisibility(View.GONE);
            bottomContainerClearButton.setVisibility(View.GONE);
            bottomContainerOrangeButton.setVisibility(View.VISIBLE);
            bottomContainerOrangeButton.setText(R.string.book_similar_appointment_button_text);
            bottomContainerOrangeButton.setOnClickListener(bookSimilarAppointmentListener);
            mLoadingOverlay.hide();
        } else {
            bottomContainerMessage.setVisibility(View.VISIBLE);
            bottomContainerMessage.setText(appointmentTypeVisit.isRequested() ? R.string.appointment_requested_message : R.string.appointment_booked_message);

            if (!SharedPreferencesUtils.isCalendarSyncEnabled()) {
                bottomContainerOrangeButton.setText(R.string.add_to_calendar);
                bottomContainerOrangeButton.setVisibility(View.VISIBLE);
                bottomContainerOrangeButton.setOnClickListener(addToCalendarListener);
            } else {
                bottomContainerOrangeButton.setVisibility(View.GONE);
            }

            bottomContainerClearButton.setText(getResources().getString(appointmentTypeVisit.isRequested() ? R.string.cancel_request_button : R.string.cancel_booking_button));
            bottomContainerClearButton.setVisibility(appointmentTypeVisit.isCancellable() ? View.VISIBLE : View.GONE);

            mLoadingOverlay.hide();
        }
    }

    private void updateView(AppointmentTypeVisit appointmentTypeVisit) {
        if (appointmentTypeVisit == null) return;
        mLoadingOverlay.show();

        setAppointmentDescriptionVisibility(appointmentTypeVisit);

        setLocationVisibility(appointmentTypeVisit);
        studioDetailHeader.setVisibility(mApptLocationView.getVisibility());
        businessDetailCard.setVisibility(mApptLocationView.getVisibility());

        setAppointmentRatingVisibility(appointmentTypeVisit);

        setFitnessTrackerVisibility(appointmentTypeVisit);
        updateBottomContainer(appointmentTypeVisit);
    }

    private void setFitnessTrackerVisibility(AppointmentTypeVisit appointmentTypeVisit) {
        if (!DevelopmentFlag.DEVELOPMENT_APPOINTMENT_DETAILS_REORDERING.isFeatureEnabled()) {
            if (appointmentTypeVisit.hasPassed()) {
                if (FitBitAPI.DATABASE_SOURCE_NAME.equals(SharedPreferencesUtils.getCurrentFitnessTracker())) {
                    configureTrackerViewModel(appointmentTypeVisit, FitbitViewModel.class);
                } else if (GoogleFitAPI.DATABASE_SOURCE_NAME.equals(SharedPreferencesUtils.getCurrentFitnessTracker())) {
                    configureTrackerViewModel(appointmentTypeVisit, GoogleFitViewModel.class);
                }
            }
        }
    }

    private void setAppointmentRatingVisibility(AppointmentTypeVisit appointmentTypeVisit) {
        if (DevelopmentFlag.DEVELOPMENT_HIDE_BOOKER_RATING.isFeatureEnabled())
        {
            mRateAppointmentView.setVisibility(appointmentTypeVisit.isCompleted() && appointmentTypeVisit.hasPassed()
                    && Objects.equals(appointmentTypeVisit.getBookingReferenceJsonModel().getInventory_source(), SwamisAPI.MB_INVENTORY_SOURCE)
                    ? View.VISIBLE : View.GONE);
        } else {
            mRateAppointmentView.setVisibility(appointmentTypeVisit.isCompleted() && appointmentTypeVisit.hasPassed() ? View.VISIBLE : View.GONE);
        }
    }

    private void setLocationVisibility(AppointmentTypeVisit appointmentTypeVisit) {
        if (DevelopmentFlag.DEVELOPMENT_APPOINTMENT_DETAILS_REORDERING.isFeatureEnabled()) {
            mApptLocationView.setVisibility(View.VISIBLE);
        } else if (!DevelopmentFlag.DEVELOPMENT_HIDE_BOOKER_RATING.isFeatureEnabled()) {
            mApptLocationView.setVisibility(appointmentTypeVisit.isCompleted() ? View.GONE : View.VISIBLE);
        } else {
            mApptLocationView.setVisibility(appointmentTypeVisit.isCompleted()
                    && Objects.equals(appointmentTypeVisit.getBookingReferenceJsonModel().getInventory_source(), SwamisAPI.MB_INVENTORY_SOURCE) ? View.GONE : View.VISIBLE);
        }
    }

    private void setAppointmentDescriptionVisibility(AppointmentTypeVisit appointmentTypeVisit) {
        mApptDescriptionHeader.setVisibility(shouldHideAppointmentDescription(appointmentTypeVisit) ? View.GONE : View.VISIBLE);
        mApptDescription.setVisibility(mApptDescriptionHeader.getVisibility());
    }

    private boolean shouldHideAppointmentDescription(AppointmentTypeVisit appointmentTypeVisit) {
        boolean hideAppointmentDescription = appointmentTypeVisit.getAppointmentTypeDescription() == null
                || appointmentTypeVisit.getAppointmentTypeDescription().trim().isEmpty();

        if(!DevelopmentFlag.DEVELOPMENT_APPOINTMENT_DETAILS_REORDERING.isFeatureEnabled())
        {
            hideAppointmentDescription = hideAppointmentDescription || appointmentTypeVisit.isCompleted();
        }
        return hideAppointmentDescription;
    }

    private void configureTrackerViewModel(BaseVisit visit, Class<? extends FitnessActivityViewModel> viewModelClass) {
        FitnessActivityViewModel viewModel = new ViewModelProvider(this).get(viewModelClass);

        fitnessActivityBadgeView.setVisibility(View.VISIBLE);
        fitnessActivityBadgeView.setVisit(visit);
        fitnessActivityBadgeView.setOnLaunchFitnessTracker(() -> {
            IntentUtils.launchAppIntent(this, viewModel.getTrackerAppPackageName());
            return Unit.INSTANCE;
        });

        fitnessActivityBadgeView.setOnOpenFitnessTrackerSettings(() -> {
            Intent intent = new Intent(this, viewModel.getTrackerSettingsActivity());
            intent.putExtra(Constants.KEY_BUNDLE_SKIP_PROMPT, true);
            startActivity(intent);
            return Unit.INSTANCE;
        });

        viewModel.getAllFitnessData().observe(this, fitnessActivityBadgeView::setData);

        fitnessActivityBadgeView.setOnRefreshListener(() -> {
            viewModel.refreshData(visit);
            return Unit.INSTANCE;
        });

        fitnessActivityBadgeView.setShareCallback(result ->
            IntentUtils.shareFitbitData(AppointmentDetailsActivity.this, visit, result));

        viewModel.refreshData(visit);
    }


    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.appointment_details, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
            case R.id.action_share_appointment:
                viewModel.launchShareAppointmentBookingIntent(AppointmentDetailsActivity.this);
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void cancelAppointment() {
        String cancelTitle = getString(R.string.cancel_appointment_dialog_title);
        String cancelText = "";
        String positiveText = "";

        if (viewModel.getAppointmentVisit().getValue() == null) {
            return;
        }

        final int statusId = viewModel.getAppointmentVisit().getValue() != null ?
            DomainObjectUtils.getCancellableIdFromStatus(
                viewModel.getAppointmentVisit().getValue().getStatus()) : AppointmentStatus.NOT_CANCELLABLE;

        if (statusId == AppointmentStatus.LATE_CANCELLABLE) {
            cancelText = getString(R.string.late_cancel_appointment_message);
            positiveText = getString(R.string.yes_late_cancel);
        }

        if (statusId == AppointmentStatus.UNPAID_LATE_CANCELLABLE) {
            cancelText = getString(R.string.unpaid_late_cancellation_message);
            positiveText = getString(R.string.yes_late_cancel);
        }

        if (statusId == AppointmentStatus.EARLY_CANCELLABLE ||
            statusId == AppointmentStatus.REQUESTED) {
            cancelTitle = null;
            cancelText = getString(viewModel.getAppointmentVisit().getValue().isRequested() ? R.string.early_cancel_request_message : R.string.early_cancel_appointment_message);
            positiveText = getString(R.string.confirm);
        }

        final MaterialOptionDialog materialOptionDialog = new MaterialOptionDialog();

        if (cancelTitle != null) materialOptionDialog.setHeaderText(cancelTitle);
        materialOptionDialog.setMessageText(cancelText);
        materialOptionDialog.setNegativeButton(getString(R.string.go_back_text), ignore -> materialOptionDialog.dismiss());

        materialOptionDialog.setPositiveButton(positiveText, ignore -> {

            CancelEventTracker.trackCancelAppointmentTap(
                    OriginView.APPOINTMENT_DETAILS,
                    UtilsKt.getAnalyticsTextForAppointmentStatusId(statusId),
                    viewModel.getAppointmentVisit().getValue(),
                    viewModel.getLocation().getValue()
            );

            runOnUiThread(() -> mLoadingOverlay.show(R.string.cancelling_appointment_text));
            viewModel.cancelAppointment();
            mLoadingOverlay.hide();
        });

        materialOptionDialog.setHorizontalButtonStyle(true);
        materialOptionDialog.show(getSupportFragmentManager(), CANCEL_APPOINTMENT_DIALOG_TAG);
    }

    private TaskCallback<Void> calendarSyncEnabledCallback = new TaskCallback<Void>() {
        @Override
        public void onTaskComplete(Void ignore) {
            bottomContainerOrangeButton.setVisibility(SharedPreferencesUtils.isCalendarSyncEnabled() ? View.GONE : View.VISIBLE);
            bottomContainerOrangeButton.setOnClickListener(addToCalendarListener);
        }
    };

    private void initListeners(AppointmentTypeVisit appointmentTypeVisit) {

        bottomContainerClearButton.setOnClickListener(v -> cancelAppointment());

        addToCalendarListener = v -> {
            if (ContextCompat.checkSelfPermission(AppointmentDetailsActivity.this, Manifest.permission.READ_CALENDAR)
                != PackageManager.PERMISSION_GRANTED) {

                if (ActivityCompat.shouldShowRequestPermissionRationale(AppointmentDetailsActivity.this,
                    Manifest.permission.WRITE_CALENDAR)) {
                    CalendarUtils.addAppointmentToCalendarCheckSyncFirst(AppointmentDetailsActivity.this,
                        getSupportFragmentManager(), viewModel.getAppointmentVisit().getValue(), calendarSyncEnabledCallback, viewModel.getLocation().getValue());
                } else {
                    // No explanation needed, we request the permission every time
                    ActivityCompat.requestPermissions(AppointmentDetailsActivity.this, new String[]{Manifest.permission.READ_CALENDAR, Manifest.permission.WRITE_CALENDAR},
                        Constants.READ_CALENDAR_PERMISSIONS_CODE);
                }

            } else {
                CalendarUtils.addAppointmentToCalendarCheckSyncFirst(
                    AppointmentDetailsActivity.this, getSupportFragmentManager(),
                    viewModel.getAppointmentVisit().getValue(), calendarSyncEnabledCallback, viewModel.getLocation().getValue());
            }
        };

        bookSimilarAppointmentListener = v -> {
            Intent appointmentIntent = AppointmentTypeDetailsActivity.newIntent(
                AppointmentDetailsActivity.this, appointmentTypeVisit.getLocationReference(),
                appointmentTypeVisit.getAppointmentServiceRefJson()
            );
            startActivity(appointmentIntent);
        };

        mRateAppointmentView.setOnClickListener(v -> {
            if (viewModel.getAppointmentVisit().getValue() == null) return;
            RateReviewDialog dialog = new RateReviewDialog();
            dialog.setVisit(viewModel.getAppointmentVisit().getValue());
            dialog.setSuccessCallback(result -> {
                KeyboardUtils.hideKeyboard(AppointmentDetailsActivity.this, mRateAppointmentView);
                if (appointmentRating == null) appointmentRating = new Rating();
                appointmentRating.setRating(result != null ? result.getRating() : 0);
                appointmentRating.setReviewText(result != null ? result.getReviewText() : "");
                if (result == null) appointmentRating.setId(0);
                mRateAppointmentView.setRating(appointmentRating);
            });
            dialog.show(getSupportFragmentManager(), RateReviewDialog.REVIEW_DIALOG_TAG);
            AnalyticsUtils.logVisitEvent("(Visit details) | Rate review tapped", viewModel.getAppointmentVisit().getValue());
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
        @NotNull String[] permissions, @NotNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == Constants.READ_CALENDAR_PERMISSIONS_CODE) {
            CalendarUtils.addAppointmentToCalendarCheckSyncFirst(
                AppointmentDetailsActivity.this, getSupportFragmentManager(),
                viewModel.getAppointmentVisit().getValue(), calendarSyncEnabledCallback, viewModel.getLocation().getValue());
        }
    }
}
