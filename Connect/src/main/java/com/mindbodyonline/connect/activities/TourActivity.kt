package com.mindbodyonline.connect.activities

import android.content.*
import android.location.Location
import android.net.Uri
import android.os.*
import android.provider.Settings
import android.util.Log
import android.view.View
import android.view.Window
import androidx.browser.customtabs.CustomTabsIntent
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.savedstate.SavedStateRegistryOwner
import com.android.volley.VolleyError
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.android.util.TaskCallback
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.activities.custom.MBCompatActivity
import com.mindbodyonline.connect.activities.viewmodels.AbstractTourViewModel
import com.mindbodyonline.connect.activities.viewmodels.AbstractTourViewModel.ThirdPartyLoginData
import com.mindbodyonline.connect.activities.viewmodels.AbstractTourViewModel.UpdateRequirement.*
import com.mindbodyonline.connect.activities.viewmodels.TourViewModel
import com.mindbodyonline.connect.activities.viewmodels.TourViewModelFactory
import com.mindbodyonline.connect.analytics.shared_login.SharedLoginEvenTracker
import com.mindbodyonline.connect.common.repository.PerformanceName
import com.mindbodyonline.connect.common.utilities.SearchUtil.clearAllFilters
import com.mindbodyonline.connect.common.utilities.showLightStatusBarOverlay
import com.mindbodyonline.connect.common.utilities.visible
import com.mindbodyonline.connect.fragments.*
import com.mindbodyonline.connect.fragments.viewmodel.*
import com.mindbodyonline.connect.ftc.DelegateTokenRefreshService
import com.mindbodyonline.connect.login.*
import com.mindbodyonline.connect.onboarding.OnboardingDialogFragment.OnboardingContract
import com.mindbodyonline.connect.profile.dashboard.ActivityNotification.setupRepeatingNotification
import com.mindbodyonline.connect.tealium.LoginTrackingUtils
import com.mindbodyonline.connect.tealium.TrackingHelperUtils
import com.mindbodyonline.connect.tealium.UserLoginType
import com.mindbodyonline.connect.utils.*
import com.mindbodyonline.connect.utils.BreadcrumbsUtils.breadcrumbIsGuestuser
import com.mindbodyonline.connect.utils.FlavorInitialization.Companion.initializeFromOpeningActivity
import com.mindbodyonline.connect.utils.analytics.AnalyticsLocator
import com.mindbodyonline.connect.utils.api.fitnessactivity.fitbit.FitBitAPI
import com.mindbodyonline.connect.utils.api.fitnessactivity.fitbit.model.FitbitUserResponse
import com.mindbodyonline.connect.utils.api.gateway.model.GeolocationAttributes
import com.mindbodyonline.connect.utils.api.identity.IdentityAPI
import com.mindbodyonline.connect.utils.api.identity.LoginProviders
import com.mindbodyonline.connect.utils.api.identity.model.IdentityErrorCodes
import com.mindbodyonline.connect.utils.api.identity.model.ProblemDetails
import com.mindbodyonline.connect.widgets.v3.refreshWidget
import com.mindbodyonline.data.StaticInstance
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.data.services.MBNotificationService
import com.mindbodyonline.data.services.MBStaticCache
import com.mindbodyonline.data.services.MbCacheService
import com.mindbodyonline.data.services.http.MbDataService
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.data.services.locator.ServiceLocator.geolocationRepository
import com.mindbodyonline.data.services.locator.ServiceLocator.getCountriesRepository
import com.mindbodyonline.data.services.locator.ServiceLocator.getFavoriteLocationRepository
import com.mindbodyonline.data.services.locator.ServiceLocator.userRepository
import com.mindbodyonline.domain.*
import com.mindbodyonline.domain.MobileAppVersion.UpdateStatus
import com.mindbodyonline.domain.apiModels.UserAgreementState
import com.mindbodyonline.framework.abvariant.ABHelperUtils
import com.mindbodyonline.framework.abvariant.DevelopmentFlag
import com.mindbodyonline.framework.abvariant.FeatureFlag
import com.mindbodyonline.framework.helpcenter.MbHelpCenter
import com.mindbodyonline.views.LoadingOverlay
import com.mindbodyonline.views.LoginView
import com.mindbodyonline.views.dialog.MaterialOptionDialog
import com.mindbodyonline.views.dialog.TermsOfUseDialog
import com.mindbodyonline.views.dialog.TermsOfUseDialog.Companion.newInstance
import java.util.*
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

/**
 * The opening Activity to the application.  This activity takes care
 * of either logging the user in, or refreshing their token upon app
 * reinitialization.
 */
class TourActivity : MBCompatActivity(), OnboardingContract {
	// When this flag is set to true, the user will be taken to Shared Login UI flow
	// instead of the legacy registration flow.
	private var launchedThirdPartyMindbodyLogin = false
	private var hideLoginContainer = false
	private var isConnected = true
	private var forceShowLogin = false
	private var dialog: MaterialOptionDialog? = null
	private lateinit var receiver: BroadcastReceiver
	private var fromUserNoData = false
	private var sourceName: String? = null

	private var loadingOverlay: LoadingOverlay? = null
	private lateinit var registrationFlowContainer: View
	private lateinit var root: ConstraintLayout

	private var pageOpenTime: Long = 0
	private val inVerificationFlow = false

	lateinit var viewModel: AbstractTourViewModel
	private var loginProviderAnalyticsType: UserLoginType? = null
	private val marketingSessionInitialized = AtomicBoolean(false)
	private val userAlreadyTakenToMain = AtomicBoolean(false)
	lateinit var sharedViewModel: RegistrationFlowSharedViewModel
	private val eventThrottler = EventThrottler()
	private var blockAppLaunchForForceUpdate = false
	private var trackAppLaunchSteps =
		DevelopmentFlag.DEVELOPMENT_APP_LAUNCH_STEPS.isFeatureEnabled()

	override fun onCreate(savedInstanceState: Bundle?) {
		pageOpenTime = if (MBAuth.isLoggedIn()) SystemClock.elapsedRealtime() else 0

		//IMPORTANT: This is necessary before super.onCreate() for the app to maintain state.
		StaticInstance.MEMORY_DESTROYED_IF_NULL = Any()
		preOnCreateInitializations()
		super.onCreate(savedInstanceState)

		if (trackAppLaunchSteps) {
			AppLaunchTracker.trackAndClearPreviousSessionAppLaunchSteps()
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.APP_LAUNCH)
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.TOUR_ACTIVITY_ON_CREATE)
		}

		super.setContentView(R.layout.activity_tour)

		initializeFromOpeningActivity(this)
		sharedViewModel = ViewModelProvider(this)[RegistrationFlowSharedViewModel::class.java]
		val viewModelFactory = TourViewModelFactory(
				getCountriesRepository(), getFavoriteLocationRepository(),
				userRepository,
				(this as SavedStateRegistryOwner), null
		)
		viewModel = ViewModelProvider(this, viewModelFactory)[TourViewModel::class.java]

		// Token refresh with delegate grant is required here to support the newly added scopes
		// if we don't do this then we have to logout the user to get new token with latest scopes
		if (FeatureFlag.ANDROID_TOKEN_DELEGATION_FOR_SCOPES.isFeatureEnabled(ABHelperUtils.getAllUserAttributes())) {
			DelegateTokenRefreshService.refreshTokenWithDelegate()
		}

		if ((MBAuth.isLoggedIn() || MBAuth.isGuestUser())) {
			ServiceLocator.perfMeasureRepo.startPerformanceMeasure(
					System.currentTimeMillis(),
					PerformanceName.APP_LAUNCH
			);
		}
		if (SharedPreferencesUtils.getLaunchedAfterInstall()) {
			breadcrumbIsGuestuser(true)
			LoginTrackingUtils.trackLaunchedAfterInstall()
			SharedPreferencesUtils.clearLaunchedAfterInstall()
		}
		initViews()
		viewModel.marketSetInitialized.observe(this) { initialized: Boolean ->
			marketingSessionInitialized.set(initialized)
			if (!DevelopmentFlag.DEVELOPMENT_REMOVE_NAVIGATE_CALL_FROM_MARKET_OBSERVER.isFeatureEnabled()) {
				findLandingFragmentAndNavigate()
			}
		}
		viewModel.updateRequirement.observe(this) {
			blockAppLaunchForForceUpdate = it == AbstractTourViewModel.UpdateRequirement.REQUIRED
			handleUpdateRequirementReturned(it)
		}
		viewModel.checkBranchAndUserActionLinks(this, intent.data)

		// If it is a valid deep link, it will navigate.  Otherwise ignored by DeepLinkUtils
		StaticInstance.deepLinkedData = DeepLinkUtils.getDeeplinkFromIntent(intent)
		if (intent.extras != null && (intent.extras?.getBoolean(
						Constants.KEY_BUNDLE_HAS_JUST_LOGGED_OUT,
						false
				) == true)
		) {
			beginLoginWorkflow()
		} else {
			if (trackAppLaunchSteps) {
				AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.FORCE_UPDATE_VERSIONS_API_CALL)
			}
			blockAppLaunchForForceUpdate = true
			viewModel.fetchUpdateRequirement(
					applicationContext.packageManager.getPackageInfoCompat(
							applicationContext.packageName
					).versionName?.replace(Constants.DEV_BUILD_SUFFIX, "") ?: ""
			)
		}

		// If user is guest or country was not saved during account creation, set country code with user's GPS location
		if (MBAuth.getUser() == null || MBAuth.getUser() != null && MBAuth.getUser()?.country == null) {
			checkIfUserIsOutsideTheUS()
		}
		viewModel.favoriteBusinesses.observe(this) {
			if (trackAppLaunchSteps) {
				AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.FAV_BUSINESSES_OBSERVER)
			}
			StaticInstance.selectedFragment = Constants.HOME_FRAGMENT_TAG
			if (!blockAppLaunchForForceUpdate) {
				launchMainActivity(mainActivityIntent)
			}
		}
		viewModel.postAuthcodeProcessing.observe(this) { accountCreated: ThirdPartyLoginData? ->
			handleAuthcodeResponse(
					accountCreated
			)
		}
		setupRegistrationFlowObserver()
	}

	private fun setupRegistrationFlowObserver() {
		sharedViewModel.thirdPartyLoginLiveData.observe(this) { loginSource ->
			try {
				handleThirdPartyLoginClick(loginSource)
			} catch (e: Exception) {
				BreadcrumbsUtils.recordHandledException(
					e,
					"Failed to handle ${loginSource.name} third party login"
				)
			}
		}

		sharedViewModel.guestLoginInitiated.observe(this) { isGuestLoginInitiated ->
			if (isGuestLoginInitiated) {
				continueAsGuest()
			}
		}

		sharedViewModel.navigationInitiated.observe(this) { navigation ->
			when (navigation) {
				RegistrationNavigation.EmailVerification -> onFragmentNavigate(
						EmailVerificationFragment()
				)

				is RegistrationNavigation.PreLoginSignup -> onFragmentNavigate(PreLoginFragment())
				is RegistrationNavigation.Login -> onFragmentNavigate(
						LoginFragment.newInstance(
								navigation.emailId
						)
				)

				is RegistrationNavigation.Signup -> onFragmentNavigate(
						SignupFragment.newInstance(
								navigation.emailId
						)
				)
			}
		}

		sharedViewModel.checkTermsOfUseAndFinishLogin.observe(this) { guestUser ->
			checkTermsOfUseAndFinishLogin(guestUser)
		}

		sharedViewModel.loginResponseLiveData.observe(this) {
			if (it is LoginResponse.Failure) {
				onFetchUserError(it.error)
			}
		}

		sharedViewModel.userTokenResponseLiveData.observe(this) { response ->
			if (response is TokenResponse.Failure) {
				onFetchTokenError(
						response.actOnFailure,
						sharedViewModel.onVerificationScreen,
						response.problemDetails
				)
			}
		}

		sharedViewModel.createAccountLiveData.observe(this) { dataModel ->
			onCreateAccount(dataModel)
		}

		sharedViewModel.showProgress.observe(this) {
			showProgress(it)
		}
	}

	private fun onFetchUserError(error: Throwable) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.ON_FETCH_USER_ERROR)
		}
		MBLog.e(TAG, "Network error getting user data:" + error.message)
		eventThrottler.executeOrThrottle {
			LoginTrackingUtils.trackLoginWithEmailFailed(null, error.message)
			TrackingHelperUtils.trackUserLoginFailEvent(UserLoginType.EMAIL, null, error.message)
		}

		// check if we still have a connection to finish logging in
		if (DeviceUtils.dataConnectionAvailable(this)) {
			triggerNavigationCallBack(false)
		} else {
			BreadcrumbsUtils.recordHandledException(
					Exception(error), "onFetchUserError",
					LoginTrackingUtils.getLoggedInState()
			)
			ToastUtils.show("Network error. Please try again later.")
			sharedViewModel.showProgress(false) //present in LoginView for old flow
		}
	}

	private fun onFetchTokenError(
			actOnFailure: Boolean,
			onVerificationScreen: Boolean,
			error: ProblemDetails?
	) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.ON_FETCH_TOKEN_ERROR)
		}
		MBLog.e(TAG, "Network error logging in user:" + (error?.Title ?: "Unknown error"))
		eventThrottler.executeOrThrottle {
			if (error != null && actOnFailure) {
				when (error.Code) {
					IdentityErrorCodes.ACCOUNT_NOT_VERIFIED -> if (onVerificationScreen) {
						sharedViewModel.showProgress(false) //present in LoginView for old flow
						triggerNavigationCallBack(true)
						if (sharedViewModel.fromConfirmButtonClick) {
							LoginTrackingUtils.trackConfirmNowTapped()
							sharedViewModel.fromConfirmButtonClick = false
						}
					} else {
						LoginTrackingUtils.trackLoginWithEmailFailed(error.Code, error.Title)
						TrackingHelperUtils.trackUserLoginFailEvent(
								UserLoginType.EMAIL,
								error.Code,
								error.Title
						)
						sharedViewModel.initiateNavigation(RegistrationNavigation.EmailVerification)
					}

					IdentityErrorCodes.INVALID_PASSWORD -> {
						LoginTrackingUtils.trackLoginWithEmailFailed(error.Code, error.Title)
						TrackingHelperUtils.trackUserLoginFailEvent(
								UserLoginType.EMAIL,
								error.Code,
								error.Title
						)
						presentDialogForReason(LoginView.LoginWorkflowAlertReason.INCORRECT_EMAIL_PASSWORD)
					}

					IdentityErrorCodes.ACCOUNT_LOCKED_OUT -> {
						LoginTrackingUtils.trackLoginWithEmailFailed(error.Code, error.Title)
						TrackingHelperUtils.trackUserLoginFailEvent(
								UserLoginType.EMAIL,
								error.Code,
								error.Title
						)
						presentDialogForReason(LoginView.LoginWorkflowAlertReason.ACCOUNT_LOCKED_OUT)
					}

					else -> {
						BreadcrumbsUtils.recordHandledException(
								Exception("onFetchTokenError"),
								"Error fetching token: $error",
								LoginTrackingUtils.getLoggedInState()
						)
						LoginTrackingUtils.trackLoginWithEmailFailed(
								error.Code, error.Title
								?: "Network error"
						)
						TrackingHelperUtils.trackUserLoginFailEvent(
								UserLoginType.EMAIL, error.Code, error.Title
								?: "Network error"
						)
						presentDialogForReason(LoginView.LoginWorkflowAlertReason.LOGIN_FAILED)
					}
				}
			} else if (actOnFailure) {
				LoginTrackingUtils.trackLoginWithEmailFailed(null, "Network error")
				TrackingHelperUtils.trackUserLoginFailEvent(
						UserLoginType.EMAIL,
						null,
						"Network error"
				)
				BreadcrumbsUtils.recordHandledException(
						Exception("onFetchTokenError"),
						"null error",
						LoginTrackingUtils.getLoggedInState()
				)
				presentDialogForReason(LoginView.LoginWorkflowAlertReason.LOGIN_FAILED)
			}
		}
		sharedViewModel.fromConfirmButtonClick = false
		sharedViewModel.showProgress(false) //present in LoginView for old flow
	}

	private fun triggerNavigationCallBack(launchEmail: Boolean) {
		if (launchEmail) {
			IntentUtils.emailIntent(this)
			/* This is where a callback needs to be hooked up or something to the email activity
			 * so when the activity exits it starts the main activity */
		} else {
			sharedViewModel.checkTermsOfUseAndFinishLogin(false)
		}
	}

	private fun presentDialogForReason(reason: LoginView.LoginWorkflowAlertReason) {
		MaterialOptionDialog().apply {
			setText(0, reason.errorMessageResId, R.string.ok_button_text, 0)
			setHorizontalButtonStyle(true)
		}.show(supportFragmentManager, ERROR_DIALOG_TAG)
	}

	private fun handleThirdPartyLoginClick(thirdPartyLoginSource: ThirdPartyLoginSource) {
		pageOpenTime = 0
		var loginProviderUri: Uri? = null
		LoginTrackingUtils.trackIntroScreensContinueTapped(
				thirdPartyLoginSource.toString(),
				sourceName
		)
		TrackingHelperUtils.trackInitiateSocialLoginEvent(
				thirdPartyLoginSource.toString(),
				sourceName
		)
		when (thirdPartyLoginSource) {
			ThirdPartyLoginSource.Facebook -> {
				loginProviderAnalyticsType = UserLoginType.FACEBOOK
				loginProviderUri = IdentityAPI.INSTANCE.getAuthorizationUrl(LoginProviders.FACEBOOK)
			}

			ThirdPartyLoginSource.Apple -> {
				loginProviderAnalyticsType = UserLoginType.APPLE
				loginProviderUri = IdentityAPI.INSTANCE.getAuthorizationUrl(LoginProviders.APPLE)
			}

			ThirdPartyLoginSource.Google -> {
				loginProviderAnalyticsType = UserLoginType.GOOGLE
				loginProviderUri = IdentityAPI.INSTANCE.getAuthorizationUrl(LoginProviders.GOOGLE)
			}

			ThirdPartyLoginSource.MINDBODY -> {
				loginProviderAnalyticsType = UserLoginType.MINDBODY
				loginProviderUri = IdentityAPI.INSTANCE.getAuthorizationUrl(LoginProviders.MINDBODY)
			}

		}
		if (!DeviceUtils.dataConnectionAvailable(ConnectApp.getInstance())) {
			showDataConnectionErrorDialog()
			return
		}
		if (thirdPartyLoginSource == ThirdPartyLoginSource.MINDBODY) {
			launchedThirdPartyMindbodyLogin = true
			val customTabs = CustomTabsIntent.Builder().build()
			customTabs.intent.flags = Intent.FLAG_ACTIVITY_NO_HISTORY
			customTabs.launchUrl(this, loginProviderUri)
		} else {
			val browserIntent = Intent(Intent.ACTION_VIEW, loginProviderUri)
			startActivity(browserIntent)
		}
	}

	/**
	 * Handles the auth code response from an Identity login or Identity account creation
	 * @param accountCreated contains the user object and a boolean flag that indicates whether
	 * or not this was an existing user login or a new user creation
	 */
	private fun handleAuthcodeResponse(thirdPartyLoginData: ThirdPartyLoginData?) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.HANDLE_AUTH_CODE_RESPONSE)
		}
		if (thirdPartyLoginData == null) { //can be either account creation failure or login failure
			loginProviderAnalyticsType?.let {
				LoginTrackingUtils.trackLoginWithSocialFailed(it, null, "Unknown error")
				TrackingHelperUtils.trackUserLoginFailEvent(it, null, "Unknown error")
			}

			loginProviderAnalyticsType = null
			showErrorDialog(false)
		} else if (!thirdPartyLoginData.accountCreated || loginProviderAnalyticsType == UserLoginType.MINDBODY) {
			// Native social login or Shared UI login/registration

			loginProviderAnalyticsType?.let {
				MBAuth.setUserLoginType(it)
				LoginTrackingUtils.trackUserLogin(thirdPartyLoginData, it, sourceName)
				loginProviderAnalyticsType = null
			}

			// Initialize the analytics api session and stitch universal id as an alias
			if (MBAuth.getUser() != null) {
				AnalyticsLocator.analyticsTracker.trackSession(MBAuth.getUser()!!.id.toString())
				AnalyticsLocator.analyticsTracker.stitchAlias(
					alias = thirdPartyLoginData.user.identityUserId
				)
			}
			// Successful Login
			sharedViewModel.showProgress(true)
			checkTermsOfUseAndFinishLogin(false)
		} else {
			// Native social registration

			//Registration event forwarded for Branch.io
			LoginTrackingUtils.trackBranchRegistration()

			// Account was created, we need to ask the user for their country and marketing preference
			loginProviderAnalyticsType?.let {
				MBAuth.setUserLoginType(it)
				LoginTrackingUtils.trackUserCreated(it.loginType)
			}

			// Registration event for Tealium
			TrackingHelperUtils.trackAccountCreationEvent(thirdPartyLoginData.user, sourceName)
			sharedViewModel.showProgress(false)
			onFragmentNavigate(SocialSignupFragment())
		}
	}

	@Deprecated("")
	private fun checkIfUserIsOutsideTheUS() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.CHECK_IF_USER_OUTSIDE_US)
		}
		if (SharedPreferencesUtils.getUsersCountryCode().isNullOrEmpty()) {
			geolocationRepository.getGeolocationInformation(
					null,
					null,
					{ geolocationData: GeolocationAttributes ->
						// Set that this is geoLocated location
						SharedPreferencesUtils.setLastKnownLocationIsGeoLocated(true)
						saveUserLocation(geolocationData)
					}
			) { }
		}
	}

	private fun saveUserLocation(geolocationData: GeolocationAttributes) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.SAVE_USER_LOCATION)
		}
		geolocationData.latitude?.let { latitude ->
			geolocationData.longitude?.let { longitude ->
				if (RESERVED_COUNTRY_CODE != geolocationData.countryCode) {
					val location = Location("geolocation data")
					location.latitude = latitude
					location.longitude = longitude
					location.accuracy = Constants.DEFAULT_ACCURACY
					location.time = System.currentTimeMillis()
					GeoLocationUtils.updateLocation(location)
				}
			}
		}
		SharedPreferencesUtils.setUsersCountryCode(geolocationData.countryCode)
	}

	private fun handleUpdateRequirementReturned(updateRequirement: AbstractTourViewModel.UpdateRequirement?) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.HANDLE_UPDATE_REQUIREMENT)
		}
		when (updateRequirement) {
			REQUIRED -> showUpdateRequired()
			OPTIONAL -> {
				if (SharedPreferencesUtils.lastUpdateCheckLessThanAWeekOld()) {
					beginLoginWorkflow()
					return
				}
				showUpgradeAvailable()
			}

			else -> {
				checkAppBoyUserValid()
				beginLoginWorkflow()
			}
		}
	}

	private fun checkForUpdatesAndContinue() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.CHECK_FOR_UPDATES_AND_CONTINUE)
		}
		val hasFinished = AtomicBoolean(false)
		//Check for app updates
		val req =
			DeviceUtils.queryMobileAppVersions({ mobileAppVersions: Array<MobileAppVersion?>? ->
				if (hasFinished.get()) return@queryMobileAppVersions
				hasFinished.set(true)
				val status = MobileAppVersion.getUpdateStatus(this@TourActivity, mobileAppVersions)
				MBLog.d(TAG, "Version update status returned = " + status.name)
				when (status) {
					UpdateStatus.Retired -> showUpdateRequired()
					UpdateStatus.UpgradeAvailable -> {
						if (SharedPreferencesUtils.lastUpdateCheckLessThanAWeekOld()) {
							beginLoginWorkflow()
							return@queryMobileAppVersions
						}
						showUpgradeAvailable()
					}

					else -> {
						checkAppBoyUserValid()
						beginLoginWorkflow()
					}
				}
			}) {
				if (hasFinished.get()) return@queryMobileAppVersions
				hasFinished.set(true)
				beginLoginWorkflow()
			}

		// Don't let this run for longer than 4 seconds
		Timer().schedule(object : TimerTask() {
			override fun run() {
				if (!hasFinished.get() && !req.isDone) {
					hasFinished.set(true)
					req.cancel()
					runOnUiThread { beginLoginWorkflow() }
				}
			}
		}, 4000)
	}

	private fun showUpgradeAvailable() {
		safeShowDialogFragment(MaterialOptionDialog().apply {
			setText(
					R.string.update_available_header,
					R.string.update_available_message,
					R.string.action_update, R.string.skip_button_text
			)
			setButton1Callback { IntentUtils.launchAppInstallIntent(this@TourActivity) }
			setButton2Callback { dialog -> dialog?.dismiss() }
			setCancelCallback { beginLoginWorkflow() }
			setHorizontalButtonStyle(true)
		}, "MBUpdateDialog")
		SharedPreferencesUtils.setLastUpdateCheck(Calendar.getInstance())
	}

	private fun showUpdateRequired() {
		safeShowDialogFragment(MaterialOptionDialog().apply {
			setText(
					R.string.update_required_header,
					R.string.update_required_message,
					R.string.action_update, -1
			)
			setButton1Callback { IntentUtils.launchAppInstallIntent(this@TourActivity) }
			setCancelCallback { finish() }
			setHorizontalButtonStyle(false)
		}, "MBUpdateDialog")
	}

	override fun onOnboardingDialogDismissed() {
		SharedPreferencesUtils.setHasSeenOnboarding()
		beginLoginWorkflow()
	}

	private fun beginLoginWorkflow() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.BEGIN_LOGIN_WORKFLOW)
		}
		sharedViewModel.showProgress(false)
		if (MBAuth.isLoggedIn() && isConnected && !forceShowLogin) {
			sharedViewModel.showProgress(true)
			continueLogin(null)
		} else if (StaticInstance.deepLinkedData != null && isConnected && !forceShowLogin) {
			// We are coming from a deep link, auto-continue the user as a guest
			continueAsGuest()
		} else {
			root.visibility = View.VISIBLE
			if (FeatureFlag.SHARED_LOGIN_UI_FLOW.isFeatureEnabled()) {
				SharedLoginEvenTracker.trackSharedLoginInitiated()
				sharedViewModel.initiateLogin(LoginSource.ThirdPartyLogin(ThirdPartyLoginSource.MINDBODY))
			} else {
				navigateToOptimizedRegistrationFlow()
			}
		}
	}

	private fun navigateToOptimizedRegistrationFlow() {
		showLightStatusBarOverlay(true)
		registrationFlowContainer.visible = true
		onFragmentNavigate(PreLoginFragment())
	}

	private fun onFragmentNavigate(fragment: Fragment) {
		if (isFinishing || isDestroyed) return
		lifecycleScope.launchWhenResumed {
			val transaction = supportFragmentManager.beginTransaction()
			transaction.replace(R.id.registration_flow_container, fragment)
			transaction.commitAllowingStateLoss()
		}
	}

	override fun onStart() {
		super.onStart()

		if (DevelopmentFlag.DEVELOPMENT_FREQUENT_LOGOUT_FIX.isFeatureEnabled()) return
		else viewModel.runCountriesSearch()
	}

	public override fun onNewIntent(intent: Intent) {
		super.onNewIntent(intent)
		// We launched a third party Mindbody login, using the custom tabs earlier and hence this boolean is true
		// We need to set it to false here, so that we can handle the intent data,
		// the redirection here is only when logged in with Mindbody and then a call to onResume() or by any other legacy flows.
		// If the custom tabs are closed we are not redirected in here. Instead we reach the onResume() method directly.
		// This handling is because of a known issue in the custom tabs library that doesn't call onNewIntent()
		// or give any callback when the custom tabs are closed.
		var trackThirdPartyLoginMindbodyLogin = false
		if (launchedThirdPartyMindbodyLogin) {
			trackThirdPartyLoginMindbodyLogin = true
			launchedThirdPartyMindbodyLogin = false
		}

		//Keep the existing bundle extras, but still override the intent
		intent.putExtras(getIntent())
		this.intent = intent
		val redirectUri = Uri.parse(IdentityAPI.INSTANCE.oauthParams.redirectUri)
		val uri = intent.data
		MBLog.d("MBDOauthRedirect", "Back into app with intent URI: $uri")
		if (uri != null && uri.scheme != null && uri.scheme == redirectUri.scheme) {
			val authError = uri.getQueryParameter("error")
			val authCode = uri.getQueryParameter("code")
			if (authError.isNullOrEmpty() && authCode?.isNotEmpty() == true) {
				if (trackThirdPartyLoginMindbodyLogin) {
					SharedLoginEvenTracker.trackSharedLoginCompleted(authCode)
				}
				sharedViewModel.showProgress(true)
				viewModel.processAuthorizationCode(authCode)
			} else if (authError?.isNotEmpty() == true) {
				val identityErrorMap = HashMap<String, String>()
				identityErrorMap["authError"] = authError
				authCode?.let {
					identityErrorMap["authCode"] = it
				}
				uri.path?.let { path ->
					identityErrorMap["redirectUri"] = path
				}

				BreadcrumbsUtils.recordHandledException(
						Exception("TourActivity onNewIntent"),
						"Identity redirect error", identityErrorMap
				)
				if (trackThirdPartyLoginMindbodyLogin) {
					SharedLoginEvenTracker.trackSharedLoginFailed(
						authCode = identityErrorMap["authCode"],
						authError = identityErrorMap["authError"]
					)
				}
			}
		}
		viewModel.checkBranchAndUserActionLinks(this, getIntent().data)
	}

	private fun preOnCreateInitializations() {
		userAlreadyTakenToMain.set(false)
		//Turn off the Actionbar for the splash screen
		requestWindowFeature(Window.FEATURE_NO_TITLE)
		setupRepeatingNotification(this)
		// Clearing all filters on app relaunch
		clearAllFilters()
		if (intent.getBooleanExtra("EXIT", false)) {
			finish()
		}
		forceShowLogin = intent.getBooleanExtra(Constants.KEY_BUNDLE_FORCE_LOGIN, false)
		if (forceShowLogin) {
			window.setBackgroundDrawableResource(R.color.white) //hide mb enso
		}
		window.decorView.systemUiVisibility = (
				View.SYSTEM_UI_FLAG_LAYOUT_STABLE
						or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)
		hideLoginContainer = MBAuth.isLoggedIn() && !forceShowLogin
		if (!DeviceUtils.dataConnectionAvailable(this@TourActivity)) {
			isConnected = false
		}
		if (StaticInstance.applicationFirstLaunch) {
			LoginTrackingUtils.trackAppLaunched()
			SharedPreferencesUtils.setSelectedFitnessClassTypes(HashSet())
			StaticInstance.applicationFirstLaunch = false
		}
		MBStaticCache.getInstance().setFavoritesChanged(true)
	}

	override fun onPause() {
		super.onPause()
		try {
			unregisterReceiver(receiver)
		} catch (e: IllegalArgumentException) {
			if (e.message?.contains("Receiver not registered") == true) {
				// Ignore this exception. This is exactly what is desired
				MBLog.w(TAG, "Tried to unregister the receiver when it's not registered")
			} else {
				// unexpected, re-throw
				throw e
			}
		}
	}

	private fun initViews() {
		root = findViewById(R.id.tour_activity_view_root)
		registrationFlowContainer = findViewById(R.id.registration_flow_container)
		loadingOverlay = findViewById(R.id.loading_overlay)
		sourceName =
			if (forceShowLogin) intent.getStringExtra(Constants.KEY_BUNDLE_ANALYTICS_PREFIX) else null
		sharedViewModel.sourceName = sourceName
		if (!MBAuth.isLoggedIn()) {
			LoginTrackingUtils.trackOnboardingLandingScreen(sourceName)
		}
		if (!DeviceUtils.dataConnectionAvailable(ConnectApp.getInstance())) {
			showDataConnectionErrorDialog()
		}
		sharedViewModel.showProgress(true)

		//Pre-populate the window offset for screens that have to use the cache
		ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { v: View?, insets: WindowInsetsCompat ->
			var topOffset = insets.systemWindowInsetTop
			val cutout = insets.displayCutout
			if (cutout != null && cutout.safeInsetTop > topOffset) {
				topOffset = cutout.safeInsetTop
			}
			SharedPreferencesUtils.setLastKnownTopOffset(topOffset)
			insets
		}
	}

	private fun onCreateAccount(dataModel: CreateAccountDataModel?) {
		if (loginProviderAnalyticsType === UserLoginType.EMAIL) {
			return   //Analytics will already be sent in the LoginView
		}
		if (viewModel.postAuthcodeProcessing.value == null) return
		val processedUser = viewModel.postAuthcodeProcessing.value?.user
		processedUser?.let { user ->
			user.country = dataModel?.countryOfOrigin ?: ""
			user.isMarketingOptIn = dataModel?.isOptIntoNewsletters ?: false
			loginProviderAnalyticsType?.let {
				LoginTrackingUtils.trackThirdPartyLogin(user, it, sourceName)
			}
		}
		continueLogin(dataModel)
	}

	private fun continueLogin(createAccountData: CreateAccountDataModel?) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.CONTINUE_LOGIN)
		}
		var user = MBAuth.getUser()
		if (createAccountData != null && user != null) {
			user.country = createAccountData.countryOfOrigin
			user.isMarketingOptIn = createAccountData.isOptIntoNewsletters
			userRepository.saveUser(user, null) { error: VolleyError ->
				MBLog.e(
						TAG,
						"Network error saving user data: " + error.message
				)
			}
			StaticInstance.firstTimeUse = true
		}
		MbHelpCenter.getInstance().setUser(this@TourActivity, MBAuth.getUser())
		SharedPreferencesUtils.postLoginPreferenceCheck()
		MBNotificationService.sync(null)

		// The user should have push notifications re-enabled or disabled if they are returning after logging out
		if (!SharedPreferencesUtils.shouldShowConsentDialog()) {
			AnalyticsUtils.setBrazePushEnabled(SharedPreferencesUtils.getPushNotificationsEnabled())
		}
		if (createAccountData != null && user != null) {
			AnalyticsUtils.setBaseUserInformation(user, false)
			checkTermsOfUseAndFinishLogin(false)
		} else if (StaticInstance.firstTimeUse && user != null) {
			// User signed up with email and password
			LoginTrackingUtils.trackUserCreated(UserLoginType.EMAIL.loginType)
			LoginTrackingUtils.logEmailRegistrationCompleted()
			AnalyticsUtils.setBaseUserInformation(user, false)
			checkTermsOfUseAndFinishLogin(false)
		} else {
			if (MBAuth.isGuestUser()) {
				findLandingFragmentAndNavigate()
			} else {
				// User is just logging back in, refresh their user object
				userRepository.getUser(
						{ currentUser: User ->
							onLoggedBackInSuccess(currentUser)
						}, { error: Throwable ->
					onLoggedBackInError(error)
				}, forceRefresh = true, cacheOnly = false
				)
			}
		}
		refreshFitbitUser()
	}

	@Deprecated("")
	private fun refreshFitbitUser() {
		//Refresh the fitbit user if they have a token with no creation date
		if (FitBitAPI.hasToken() && SharedPreferencesUtils.getFitbitUserCreationDate() == null) {
			FitBitAPI.getUserInfo({ response: FitbitUserResponse ->
				if (response.user != null) {
					val timeZone = response.user.timezone
					SharedPreferencesUtils.setFitbitUserTimezone(timeZone)
					SharedPreferencesUtils.setFitbitUserCreationDate(response.user.memberSince)
					Log.d("MBFitbit", "Updated Fitbit creation date for user")
				}
			}) { FitBitAPI.deleteToken() }
		}
	}

	private fun onLoggedBackInError(error: Throwable) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.ON_LOGGED_BACK_IN_ERROR)
		}
		MBLog.e(TAG, "Network error loading user data: " + error.message)
		root.visibility = View.VISIBLE
		val resp = if (error is VolleyError) error.networkResponse else null
		if (resp != null && resp.statusCode / 100 == 4) {
			// We have a refresh token failure, kick the user out
			SessionUtils.showSessionErrorDialog(
					this,
					"TourActivity onLoggedBackInError error code " + resp.statusCode
			)
		} else {
			showErrorDialog(true)
		}
	}

	private fun onLoggedBackInSuccess(user1: User) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.ON_LOGGED_BACK_IN_SUCCESS)
		}
		AnalyticsUtils.setBaseUserInformation(user1, false)

		// There might be a case in which the user signed up but we didn't
		//  have their ID yet.  Log analytics here
		if (StaticInstance.firstTimeUse) {
			LoginTrackingUtils.trackUserCreated(UserLoginType.EMAIL.loginType)
			LoginTrackingUtils.logEmailRegistrationCompleted()
		}
		checkTermsOfUseAndFinishLogin(false)
	}

	private fun checkTermsOfUseAndFinishLogin(guestUser: Boolean) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.CHECK_TERMS_OF_USE_AND_FINISH_LOGIN)
		}
		// Need to check that they agreed to the terms of service
		val user = MBAuth.getUser()
		val agreementState = user?.userAgreementState
		when {
			(guestUser) -> retrieveGuestTokenAndGoToMain()
			((Switches.FORCE_SHOW_AGREEMENT_DIALOG || user != null) && agreementState != UserAgreementState.Ok) -> {
				MbDataService.getServiceInstance().loadUserService()
					.getConsumerAgreement({ response: ConsumerAgreement? ->
						showTermsOfUseDialog(
								response
						) {}
					}) { findLandingFragmentAndNavigate() }
			}

			else -> findLandingFragmentAndNavigate()
		}
	}

	private fun retrieveGuestTokenAndGoToMain() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.RETRIEVE_GUEST_TOKEN_AND_GO_TO_MAIN)
		}
		IdentityAPI.INSTANCE.getGuestToken({ token: Token? ->
			MBAuth.setToken(token)
			findLandingFragmentAndNavigate()

			// The user should have push notifications re-enabled or disabled if they are returning after logging out
			if (!SharedPreferencesUtils.shouldShowConsentDialog()) {
				AnalyticsUtils.setBrazePushEnabled(SharedPreferencesUtils.getPushNotificationsEnabled())
			}
			Unit
		}
		) {
			ToastUtils.showServerErrorToast()
			sharedViewModel.showProgress(false)
		}
	}

	private fun showTermsOfUseDialog(
			response: ConsumerAgreement?,
			acceptedCallback: Function0<*>?
	) {
		newInstance(response, true).apply {
			isCancelable = false
			onTermActionCallback = TaskCallback { result: ConsumerUserAgreementState? ->
				if (result == null) return@TaskCallback
				LoginTrackingUtils.trackUserAgreement(response, result)
				when (result) {
					ConsumerUserAgreementState.Declined -> {
						// We need to log this before calling logout, for the user ID
						onUserDeclinedTerms()
					}

					ConsumerUserAgreementState.Shown, ConsumerUserAgreementState.Accepted, ConsumerUserAgreementState.Skipped -> onUserAcceptedTerms(
							acceptedCallback
					)
				}
			}
		}.show(supportFragmentManager, TermsOfUseDialog::class.java.name)
	}

	private fun onUserAcceptedTerms(acceptedCallback: Function0<*>?) {
		SharedPreferencesUtils.setTermsOfUseAccepted(true)
		if (acceptedCallback != null) {
			acceptedCallback.invoke()
		} else {
			findLandingFragmentAndNavigate()
		}
	}

	private fun onUserDeclinedTerms() {
		AnalyticsUtils.logEvent(
				"(Settings) | User signed out",
				"Reason", "Declined terms of use"
		)
		MBAuth.logout()
		root.visibility = View.VISIBLE
		sharedViewModel.showProgress(false)
	}

	private fun runBackgroundTasks() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.RUN_BACKGROUND_TASKS)
		}
		//Refresh the user site cache
		updateUserSites()
		updateMarketingOptIn()
	}

	@Deprecated("")
	private fun updateMarketingOptIn() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.UPDATE_MARKETING_OPT_IN)
		}
		val user = MBAuth.getUser()
		if (user != null && user.username != null) {
			val username = user.username
			val marketingOptin = SharedPreferencesUtils.getCachedMarketingValue(user.username)
			if (marketingOptin != null) {
				user.isMarketingOptIn = marketingOptin
				userRepository.saveUser(user,
						{ SharedPreferencesUtils.setCachedMarketingValue(username, null) }
				) { }
			}
		}
	}

	@Deprecated("")
	private fun updateUserSites() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.UPDATE_USER_SITES)
		}
		MbDataService.getServiceInstance()
			.setDontCancel()
			.loadUserService()
			.getUserSites({ userSites: Array<UserSite?>? ->
				MbCacheService.get().setUserSites(userSites)
			}, null)
		MbDataService.getServiceInstance()
			.loadUserService()
			.syncUserData(null, null)
	}

	override fun onBackPressed() {
		sourceName?.let {
			LoginTrackingUtils.trackGuestLoginDismissed(it)
			window.setBackgroundDrawableResource(R.drawable.splash_screen)
			continueAsGuest()
		} ?: run {
			super.onBackPressed()
		}
	}

	fun receive() {
		if (DeviceUtils.dataConnectionAvailable(this)) {
			lifecycleScope.launchWhenResumed {
				dialog?.dismiss()
			}

			if (MBAuth.isLoggedIn() && !inVerificationFlow && !forceShowLogin) {
				sharedViewModel.showProgress(true)
				continueLogin(null)
			}
		}
	}

	private fun continueAsGuest() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.CONTINUE_AS_GUEST)
		}
		MBAuth.setUser(null)
		checkTermsOfUseAndFinishLogin(true)
		breadcrumbIsGuestuser(true)
	}

	private fun findLandingFragmentAndNavigate() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.FIND_LANDING_FRAGMENT_AND_NAVIGATE)
		}
		if (intent.hasExtra(Constants.KEY_BUNDLE_DEEP_LINK_AFTER_LOGIN)) {
			StaticInstance.deepLinkedData =
				intent.getParcelableExtra(Constants.KEY_BUNDLE_DEEP_LINK_AFTER_LOGIN)
		}
		ConnectApp.getInstance().refreshAnonymousClientAccess()
		if (MBAuth.getUser() == null && !MBAuth.isGuestUser()) {
			userRepository.getUser(
					{
						onFetchUserBeforeNavSuccess()
						landOnDefaultFragment(mainActivityIntent)
					}, {
				onFetchUserBeforeNavError()
			}, forceRefresh = true, cacheOnly = false
			)
		} else {
			landOnDefaultFragment(mainActivityIntent)
		}
	}

	private fun onFetchUserBeforeNavError() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.ON_FETCH_USER_BEFORE_NAV_ERROR)
		}
		root.visibility = View.VISIBLE
		sharedViewModel.showProgress(false)
		userAlreadyTakenToMain.set(false)
	}

	private fun onFetchUserBeforeNavSuccess() {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.ON_FETCH_USER_BEFORE_NAV_SUCCESS)
		}
		refreshWidget(this)
		MbHelpCenter.getInstance().setUser(this@TourActivity, MBAuth.getUser())
	}

	@get:Deprecated("")
	private val mainActivityIntent: Intent
		get() {
			val mainActivityIntent = Intent(this@TourActivity, MainActivity::class.java)
			mainActivityIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
			mainActivityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
			mainActivityIntent.putExtra(
					Constants.KEY_BUNDLE_SHOW_ACTIVITY_DASHBOARD,
					intent.getBooleanExtra(Constants.KEY_BUNDLE_SHOW_ACTIVITY_DASHBOARD, false)
			)
			return mainActivityIntent
		}

	private fun landOnDefaultFragment(intent: Intent) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.LAND_ON_DEFAULT_FRAGMENT)
		}
		if (DevelopmentFlag.DEVELOPMENT_REMOVE_FAV_BUSINESSES_CALL_DURING_APP_LAUNCH.isFeatureEnabled()) {
			if (!MBAuth.isGuestUser()) {
				runBackgroundTasks()
			}
			if (!blockAppLaunchForForceUpdate) {
				goToHomeScreen(intent)
			}
		} else {
			if (!MBAuth.isGuestUser()) {
				runBackgroundTasks()
				viewModel.fetchFavoriteBusinesses()
			} else {
				goToHomeScreen(intent)
			}
		}
	}

	private fun goToHomeScreen(intent: Intent) {
		StaticInstance.selectedFragment = Constants.HOME_FRAGMENT_TAG
		SharedPreferencesUtils.storeCurrentFragmentTag(Constants.HOME_FRAGMENT_TAG)
		launchMainActivity(intent)
	}

	override fun onResume() {
		super.onResume()
		// We launched a third party Mindbody login, using the custom tabs earlier and hence this boolean is true
		// In this case, we need to continue the login process as a guest because the user closed the custom tabs.
		// This handling is because of a known issue in the custom tabs library that doesn't call onNewIntent()
		// or give any callback when the custom tabs are closed.
		if (launchedThirdPartyMindbodyLogin) {
			launchedThirdPartyMindbodyLogin = false
			sharedViewModel.showProgress(true)
			// guest user login when shared ui login is cancelled
			SharedLoginEvenTracker.trackSharedLoginCancelled()
			continueAsGuest()
			return
		}
		receiver = object : BroadcastReceiver() {
			override fun onReceive(context: Context, intent: Intent) {
				receive()
			}
		}
		if (!DeviceUtils.dataConnectionAvailable(this)) {
			fromUserNoData = true
			val filter = IntentFilter()
			filter.addAction("android.net.conn.CONNECTIVITY_CHANGE")
			filter.addAction("android.net.wifi.WIFI_STATE_CHANGED")
			registerReceiver(receiver, filter)
		} else if (fromUserNoData) {
			receive()
		}
		if (!MBAuth.isLoggedIn() || !DeviceUtils.dataConnectionAvailable(this)) {
			pageOpenTime = 0
		}
		showPendingDialogFragment()
		StaticInstance.refreshDataAfterBooking()
	}

	private fun launchMainActivity(mainActivityIntent: Intent) {
		if (trackAppLaunchSteps) {
			AppLaunchTracker.addLaunchStep(AppLaunchTracker.AppLaunchStep.LAUNCH_MAIN_ACTIVITY)
		}
		Log.e(
				"MBDBranch",
				"userATTM: " + userAlreadyTakenToMain.get() + " :: marketingSI: " + marketingSessionInitialized.get()
		)
		if (userAlreadyTakenToMain.get()) return
		// We need to handle the situation that the marketing initialization (branch) is taking
		// longer than the initial app splash screen initialization.  When that happens, we will
		// wait for a maximum of 5 more seconds for the marketing initialization to finish
		if (!marketingSessionInitialized.getAndSet(true)) {
			Handler(Looper.getMainLooper()).postDelayed({

				// If the marketing session hasn't been initialized, then ignore the marketing
				// initialization, it's taking too long
				marketingSessionInitialized.set(true)
				launchMainActivity(mainActivityIntent)
			}, TimeUnit.SECONDS.toMillis(MARKETING_INIT_WAIT_TIME_SECONDS))
			return
		}
		userAlreadyTakenToMain.set(true)
		if (!StaticInstance.firstTimeUse && pageOpenTime > 0) {
			AnalyticsUtils.logTimingEvent(
					"App open", AnalyticsUtils.SCREEN_API_LOAD_CATEGORY,
					"User already logged in", SystemClock.elapsedRealtime() - pageOpenTime
			)
			pageOpenTime = 0
		}
		startActivity(mainActivityIntent)
		finish()
	}

	private fun showErrorDialog(finishOnComplete: Boolean) {
		dialog = MaterialOptionDialog().apply {
			setHorizontalButtonStyle(true)
			setHeaderText(<EMAIL>(R.string.server_error_dialog_title))
			setMessageText(<EMAIL>(R.string.server_error_dialog_message))
			setPositiveButton(<EMAIL>(R.string.ok_button_text)) { result: DialogFragment? ->
				sharedViewModel.showProgress(false)
				result?.dismiss()
				if (finishOnComplete) {
					finish()
				}
			}
			setOnDismissListener { dialog: DialogInterface ->
				sharedViewModel.showProgress(false)
				dialog.dismiss()
				if (finishOnComplete) {
					finish()
				}
			}
		}
		safeShowDialogFragment(dialog, ERROR_DIALOG_TAG)
		sharedViewModel.showProgress(false)
		BreadcrumbsUtils.recordHandledException(
				Exception("TourActivityServerErrorDialog"), "TourActivity whoops something went wrong",
				LoginTrackingUtils.getLoggedInState()
		)
	}

	private fun showDataConnectionErrorDialog() {
		dialog = MaterialOptionDialog().apply {
			setText(
					R.string.no_connection_title,
					R.string.no_connection_message,
					R.string.goto_connection_settings_button_text,
					R.string.cancel
			)
			setButton1Callback { startActivity(Intent(Settings.ACTION_SETTINGS)) }
			setButton2Callback { dialog -> dialog?.dismiss() }
			setHorizontalButtonStyle(true)
		}

		dialog?.let {
			supportFragmentManager.beginTransaction().remove(it).commitAllowingStateLoss()
		}

		supportFragmentManager.executePendingTransactions()
		dialog?.show(supportFragmentManager, null)
		sharedViewModel.showProgress(false)
	}

	private fun showProgress(show: Boolean) {
		if (show) {
			loadingOverlay?.show("")
		} else {
			loadingOverlay?.hide()
		}
	}

	override fun onStop() {
		super.onStop()
		sharedViewModel.showProgress(false)
	}

	override fun onRequestPermissionsResult(
			requestCode: Int,
			permissions: Array<String>,
			grantResults: IntArray
	) {
		super.onRequestPermissionsResult(requestCode, permissions, grantResults)
		Thread { checkForUpdatesAndContinue() }.start()
	}

	companion object {
		private const val TAG = "TourActivity"
		private const val ERROR_DIALOG_TAG = TAG + "ErrorDialog"
		private const val RESERVED_COUNTRY_CODE = "RD"
		const val MARKETING_INIT_WAIT_TIME_SECONDS: Long = 5
	}
}
