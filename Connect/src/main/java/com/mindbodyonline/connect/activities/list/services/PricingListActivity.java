package com.mindbodyonline.connect.activities.list.services;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;

import com.mindbodyonline.android.api.sales.MBSalesApi;
import com.mindbodyonline.android.api.sales.model.enums.CCatalogFeed;
import com.mindbodyonline.android.api.sales.model.pos.ODataFilters;
import com.mindbodyonline.android.api.sales.model.pos.odata.FilterGrouping;
import com.mindbodyonline.android.api.sales.model.pos.odata.FilterPair;
import com.mindbodyonline.android.util.TaskCallback;
import com.mindbodyonline.android.util.api.model.ProgramType;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.activities.custom.MBLightCompatActivity;
import com.mindbodyonline.connect.activities.list.services.viewmodels.PricingListViewModel;
import com.mindbodyonline.connect.activities.list.services.viewmodels.PricingListViewModelFactory;
import com.mindbodyonline.connect.analytics.OriginView;
import com.mindbodyonline.connect.analytics.EventsPathIdentifier;
import com.mindbodyonline.connect.appointments.AppointmentCategoryListFragment;
import com.mindbodyonline.connect.contracts.ContractsCategoryListFragment;
import com.mindbodyonline.connect.fragments.ServiceCategoryListFragment;
import com.mindbodyonline.connect.utils.AnalyticsUtils;
import com.mindbodyonline.connect.utils.Constants;
import com.mindbodyonline.connect.utils.MBPhoneUtils;
import com.mindbodyonline.connect.utils.ToastUtils;
import com.mindbodyonline.connect.utils.Utils;
import com.mindbodyonline.connect.utils.ViewUtils;
import com.mindbodyonline.connect.utils.api.ModelTranslationKt;
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI;
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.AppointmentsPricings;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.ClassesPricings;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking.UserFlowEvent.ContractPricings;
import com.mindbodyonline.data.StaticInstance;
import com.mindbodyonline.data.services.http.MbDataService;
import com.mindbodyonline.data.services.locator.ServiceLocator;
import com.mindbodyonline.domain.Location;
import com.mindbodyonline.domain.ServiceCategory;
import com.mindbodyonline.framework.abvariant.DevelopmentFlag;
import com.mindbodyonline.views.LoadingOverlayWhite;
import com.mindbodyonline.views.NoResultsView;

import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.google.android.material.tabs.TabLayout;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.ViewPager;
import kotlin.Unit;

public class PricingListActivity extends MBLightCompatActivity {

    private TabLayout tabLayout;
    private ViewPager viewPager;
    private LoadingOverlayWhite vw_loading;
    private NoResultsView noResultsView;

    private List<ServiceCategory> classCategories = new ArrayList<>();

    private ServiceCategoryListFragment classesFragment;
    private List<Fragment> categoryFragments = new ArrayList<>();

    private AtomicInteger apiCallCount;

    private Location _location;

    private PricingListViewModel viewModel;

    private Response.ErrorListener apiErrorListener = new Response.ErrorListener() {
        @Override
        public void onErrorResponse(VolleyError volleyError) {
            if (volleyError.networkResponse != null && volleyError.networkResponse.statusCode == 404) {
                ToastUtils.show(getString(R.string.no_service_categories_message));
                _location.setHasAppointments(false);
                _location.setHasClasses(false);
                logAnalytics(_location);
                finish();
            } else {
                ToastUtils.showServerErrorToast();
                finish();
            }
        }
    };
    private TaskCallback apiFinishCallback = new TaskCallback() {
        @Override
        public void onTaskComplete(Object result) {
            if (_location.hasClasses() && classesFragment != null) {
                classesFragment.setCategoryData(classCategories.toArray(
                    new ServiceCategory[0]), null);
            }
            logAnalytics(_location);
        }
    };

    public static @NotNull Intent newIntent(@NotNull Context context, @NotNull LocationReference locationReference) {
        return new Intent(context, PricingListActivity.class)
            .putExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE, locationReference);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_services_pricing);

        PricingListViewModelFactory viewModelFactory = new PricingListViewModelFactory(ServiceLocator.getLocationRepository(), this, null);
        viewModel = new ViewModelProvider(this, viewModelFactory).get(PricingListViewModel.class);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setHomeButtonEnabled(true);
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        moveToolbarBelowStatusBar();

        if (!getIntent().hasExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE)) {
            ToastUtils.showServerErrorToast();
            finish();
            return;
        }

        tabLayout = findViewById(R.id.activity_services_pricing_tab_layout);
        viewPager = findViewById(R.id.activity_services_pricing_view_pager);
        vw_loading = findViewById(R.id.activity_services_pricing_overlay);
        noResultsView = findViewById(R.id.no_results_view);

        noResultsView.setButtonAction(getResources().getString(R.string.call_menu_title), (TaskCallback) result -> {
            if (_location != null) {
                startActivity(MBPhoneUtils.getInternationalCallIntent(_location,
                    ServiceLocator.getDeviceRepository().getNetworkCountryCode(false)));
            }
        });

        vw_loading.show();

        viewModel.getLocation().observe(this, location -> {
            if (location != null) {
                setLocation(location);
            } else {
                ToastUtils.showServerErrorToast();
                finish();
            }
        });

        viewModel.initialize(getIntent().getParcelableExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE));
    }

    private void setLocation(Location location) {
        _location = location;
        noResultsView.setText(0, getResources().getString(R.string.coming_soon), getResources()
            .getString(R.string.call_business_coming_soon_message, _location.getStudioName()));
        checkLocationHasClassesAppointments();
    }

    private void checkLocationHasClassesAppointments() {
        vw_loading.show();
        int numberOFApiCalls = 2;
        if (!SwamisAPI.BOOKER_INVENTORY_SOURCE.equalsIgnoreCase(_location.getInventorySource())) {
            numberOFApiCalls = 3;
        }
        apiCallCount = new AtomicInteger(numberOFApiCalls);
        _location.setHasClasses(false);
        _location.setHasAppointments(false);
        _location.setHasContracts(false);
        Response.ErrorListener failureCallback = error -> loadFragmentsAndQueryData();

        if (!SwamisAPI.BOOKER_INVENTORY_SOURCE.equalsIgnoreCase(_location.getInventorySource())) {
            MbDataService.getServiceInstance().loadLocationService().getClassesForSpecificLocation(
                _location.getSiteId(), _location.getSiteLocationId(), 1, Calendar.getInstance(), null, null,
                response -> {
                    if (!Utils.isEmpty(response)) {
                        _location.setHasClasses(true);
                    }
                    loadFragmentsAndQueryData();
                },
                failureCallback);
            ODataFilters filters = new ODataFilters();
            //We're looking for contracts
            filters.addOrSet(new FilterGrouping(ODataFilters.Operator.AND)
                .add(new FilterPair(ODataFilters.ITEM_TYPE, ODataFilters.FilterType.EQ, CCatalogFeed.PACKAGE))
                .add(new FilterPair(ODataFilters.HAS_CONTRACT, ODataFilters.FilterType.EQ, true)));
            MBSalesApi.searchCatalogFeed(_location.getSiteId(), 0, 1, filters, null,
                catalogFeedResponse -> {
                    if (catalogFeedResponse.getItems().length > 0) {
                        _location.setHasContracts(true);
                    }
                    loadFragmentsAndQueryData();
                }, failureCallback
            );

        } else {
            _location.setHasClasses(false);
            loadFragmentsAndQueryData();
        }

        ServiceLocator.getAppointmentRepository().getAppointmentServices(
            ModelTranslationKt.toLocationReference(_location),
            appointmentServiceAttributes -> {
                _location.setHasAppointments(!Utils.isEmpty(appointmentServiceAttributes));
                loadFragmentsAndQueryData();
                return Unit.INSTANCE;
            }, false
        );


    }

    @Override
    protected void onResume() {
        super.onResume();

        StaticInstance.sourceType = AnalyticsUtils.SOURCE_TYPE_PRICING;
    }

    private void loadFragmentsAndQueryData() {
        if (apiCallCount.decrementAndGet() > 0) return;

        vw_loading.hide();
        if (_location.hasClasses()) {
            classesFragment = new ServiceCategoryListFragment();
            classesFragment.setProgramType(ProgramType.CLASS);
            classesFragment.setLocationData(_location);
            categoryFragments.add(classesFragment);
            UserCheckoutFlowTracking.Companion.addUserFlowEvent(ClassesPricings.INSTANCE);
        }

        if (_location.hasAppointments()) {
            if (categoryFragments.isEmpty()) {
                UserCheckoutFlowTracking.Companion.addUserFlowEvent(AppointmentsPricings.INSTANCE);
            }
            categoryFragments.add(AppointmentCategoryListFragment.newInstance(
                ModelTranslationKt.toLocationReference(_location),
                OriginView.BUSINESS_PRICING, null,
                EventsPathIdentifier.BUSINESS_PRICING_APPOINTMENTS));
        }

        if (_location.hasContracts()) {
            if (categoryFragments.isEmpty()) {
                UserCheckoutFlowTracking.Companion.addUserFlowEvent(ContractPricings.INSTANCE);
            }
            categoryFragments.add(ContractsCategoryListFragment.newInstance(_location));
        }

        if (categoryFragments.size() > 1) {
            tabLayout.setVisibility(View.VISIBLE);

            if (_location.hasClasses()) {
                tabLayout.addTab(tabLayout.newTab().setText(R.string.banner_classes));
            }
            if (_location.hasAppointments()) {
                tabLayout.addTab(tabLayout.newTab().setText(R.string.banner_appointments_abbreviated));
            }
            if (_location.hasContracts()) {
                tabLayout.addTab(tabLayout.newTab().setText(R.string.banner_contracts));
            }
            tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
                @Override
                public void onTabSelected(TabLayout.Tab tab) {
                    if (!categoryFragments.isEmpty()) {
                        Fragment selectedFragment = categoryFragments.get(tab.getPosition());
                        UserCheckoutFlowTracking.UserFlowEvent userFlowEvent = null;
                        if (selectedFragment instanceof ServiceCategoryListFragment) {
                            userFlowEvent = ClassesPricings.INSTANCE;
                        } else if (selectedFragment instanceof AppointmentCategoryListFragment) {
                            userFlowEvent = AppointmentsPricings.INSTANCE;
                        } else if (selectedFragment instanceof ContractsCategoryListFragment) {
                            userFlowEvent = ContractPricings.INSTANCE;
                        }
                        UserCheckoutFlowTracking.Companion.addUserFlowEvent(userFlowEvent);
                        viewModel.trackTabSelectedEvent(userFlowEvent);
                    }
                    viewPager.setCurrentItem(tab.getPosition(), true);
                }

                @Override
                public void onTabUnselected(TabLayout.Tab tab) {
                }

                @Override
                public void onTabReselected(TabLayout.Tab tab) {
                }
            });
            if (getSupportActionBar() != null) {
                getSupportActionBar().setElevation(0f);
            }
        } else {
            tabLayout.setVisibility(View.GONE);
            if (getSupportActionBar() != null) {
                getSupportActionBar().setElevation(ViewUtils.dpToPx(Constants.TOOLBAR_ELEVATION, this));
            }
        }

        noResultsView.setVisibility(categoryFragments.size() > 0 ? View.GONE : View.VISIBLE);

        viewPager.addOnPageChangeListener(new TabLayout.TabLayoutOnPageChangeListener(tabLayout));
        viewPager.setOffscreenPageLimit(categoryFragments.size());
        viewPager.setAdapter(new ServicesPricingPagerAdapter(getSupportFragmentManager()));

        queryClassServiceCategories();
    }

    private void logAnalytics(Location location) {
        Map<String, String> additionalData = new HashMap<>();

        String available = "None";
        if (location.hasAppointments() && location.hasClasses()) {
            available = "Both classes and appointments";
        } else if (location.hasAppointments()) {
            available = "Appointments only";
        } else if (location.hasClasses()) {
            available = "Classes only";
        }

        additionalData.put("Available Categories", available);
        AnalyticsUtils.logBusinessEvent("(Service Pricing List) | User entered pricing screen", location, additionalData);
    }

    private void queryClassServiceCategories() {
        if (_location.hasClasses()) {
            classesFragment.setLoading(true);
            MbDataService.getServiceInstance().loadAppointmentService()
                .getServiceCategories(_location.getSiteId(),
                    serviceCategories -> {
                        buildCategoryLists(serviceCategories);
                        apiFinishCallback.onTaskComplete();
                    }, apiErrorListener);
        } else {
            apiFinishCallback.onTaskComplete();
        }
    }

    private void buildCategoryLists(ServiceCategory[] categories) {
        classCategories.clear();
        boolean isEligibleCategoryType;
        for (ServiceCategory cat : categories) {
            isEligibleCategoryType = (cat.getType() == ProgramType.CLASS);
            if (DevelopmentFlag.DEVELOPMENT_SHOW_ENROLLMENT_PRICING.isFeatureEnabled()) {
                isEligibleCategoryType = (cat.getType() == ProgramType.CLASS) || (cat.getType() == ProgramType.ENROLLMENT);
            }
            if (isEligibleCategoryType && cat.isBookableOnline()) {
                classCategories.add(cat);
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                super.onBackPressed();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    private class ServicesPricingPagerAdapter extends FragmentPagerAdapter {

        ServicesPricingPagerAdapter(FragmentManager fm) {
            super(fm);
        }

        @NotNull @Override
        public Fragment getItem(int position) {return categoryFragments.get(position);}

        @Override
        public int getCount() {return categoryFragments.size();}
    }
}
