package com.mindbodyonline.connect.activities.schedule

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.activities.custom.MBCompatActivity
import com.mindbodyonline.connect.activities.details.ClassTypeDetailsActivity
import com.mindbodyonline.connect.activities.viewmodels.ScheduledClassListViewModel
import com.mindbodyonline.connect.adapters.filters.IFilter
import com.mindbodyonline.connect.analytics.APPOINTMENTS_TAB
import com.mindbodyonline.connect.analytics.CLASSES_TAB
import com.mindbodyonline.connect.analytics.EventsPathIdentifier
import com.mindbodyonline.connect.analytics.OriginComponent
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.analytics.appointment.AppointmentEventTracker
import com.mindbodyonline.connect.analytics.viewschedule.trackCalendarDialogOpened
import com.mindbodyonline.connect.analytics.viewschedule.trackFilterDialogOpened
import com.mindbodyonline.connect.analytics.viewschedule.trackTabSelected
import com.mindbodyonline.connect.analytics.viewschedule.trackViewScheduleOpened
import com.mindbodyonline.connect.appointments.AppointmentCategoryListFragment.Companion.KEY_LOCATION_REFERENCE
import com.mindbodyonline.connect.appointments.AppointmentCategoryViewModel
import com.mindbodyonline.connect.appointments.AppointmentCategoryViewModelFactory
import com.mindbodyonline.connect.quickbook.QuickBookDialog
import com.mindbodyonline.connect.quickbook.QuickBookViewModelV2.QuickbookFailureReason
import com.mindbodyonline.connect.utils.AlarmUtils
import com.mindbodyonline.connect.utils.BookabilitySourceScreen
import com.mindbodyonline.connect.utils.BookingUtils
import com.mindbodyonline.connect.utils.BreadcrumbsUtils
import com.mindbodyonline.connect.utils.Constants
import com.mindbodyonline.connect.utils.DialogUtils
import com.mindbodyonline.connect.utils.MBPhoneUtils.getInternationalPhoneNumber
import com.mindbodyonline.connect.utils.ToastUtils
import com.mindbodyonline.connect.utils.analytics.key
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference
import com.mindbodyonline.connect.utils.extractParcelableFromArguments
import com.mindbodyonline.connect.utils.extractSerializable
import com.mindbodyonline.connect.utils.launchBookabilityBlockedInformation
import com.mindbodyonline.data.StaticInstance
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.data.services.locator.ServiceLocator.deviceRepository
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.Location
import com.mindbodyonline.domain.dataModels.schedule.ClassAttributes
import com.mindbodyonline.domain.dataModels.schedule.toClassScheduleState
import com.mindbodyonline.domain.datamapper.toCto
import com.mindbodyonline.domain.quickbook.CTOQuickBookResponse
import com.mindbodyonline.ui.screen.schedule.BusinessScheduleScreen
import com.mindbodyonline.views.dialog.CalendarDayPickerDialog
import com.mindbodyonline.views.dialog.FilterDialog
import com.mindbodyonline.views.dialog.InAppPurchasesBlockedDialog
import java.util.Calendar

class BusinessScheduleActivity : MBCompatActivity() {

    private lateinit var appointmentCategoryViewModel: AppointmentCategoryViewModel

    private val scheduledClassListViewModel: ScheduledClassListViewModel by viewModels()

    private val categories = mutableStateOf<List<Pair<String?, String?>>>(listOf())

    companion object {
        @JvmStatic
        fun newIntent(
            context: Context, locationReference: LocationReference,
            originView: OriginView?, originComponent: OriginComponent?
        ): Intent {
            val intent = Intent(context, BusinessScheduleActivity::class.java)
                .putExtra(Constants.KEY_BUNDLE_LOCATION_REFERENCE, locationReference)
            originView?.let {
                intent.putExtra(Constants.KEY_BUNDLE_ORIGIN_VIEW, it)
            }
            originComponent?.let {
                intent.putExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT, it)
            }
            return intent
        }

        const val BUSINESS_SCHEDULE_ACTIVITY_FILTERS_DIALOG_TAG: String =
            "BUSINESS_SCHEDULE_ACTIVITY_FILTERS_DIALOG_TAG"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        scheduledClassListViewModel.originView = intent.extractSerializable(
            Constants.KEY_BUNDLE_ORIGIN_VIEW,
            OriginView::class.java
        )

        scheduledClassListViewModel.originComponent = intent.extractSerializable(
            Constants.KEY_BUNDLE_ORIGIN_COMPONENT,
            OriginComponent::class.java
        )

        val locationReference = intent.extras?.extractParcelableFromArguments(
            Constants.KEY_BUNDLE_LOCATION_REFERENCE,
            LocationReference::class.java
        )

        scheduledClassListViewModel.location.let { location ->
            scheduledClassListViewModel.originView?.let {
                trackViewScheduleOpened(
                    viewOriginView = it,
                    originComponent = scheduledClassListViewModel.originComponent,
                    location = location.value,
                )
            }
        }

        appointmentCategoryViewModel = AppointmentCategoryViewModelFactory(
            this,
            ServiceLocator.locationRepository,
            ServiceLocator.appointmentRepository,
            this,
            Bundle().apply {
                putParcelable(KEY_LOCATION_REFERENCE, locationReference)
                putSerializable(
                    Constants.KEY_BUNDLE_ORIGIN_VIEW,
                    scheduledClassListViewModel.originView,
                )
                putSerializable(
                    Constants.KEY_BUNDLE_ORIGIN_COMPONENT,
                    scheduledClassListViewModel.originComponent,
                )
            }
        ).create(AppointmentCategoryViewModel::class.java)

        scheduledClassListViewModel.initialize(
            locationReference = locationReference
        )

        appointmentCategoryViewModel.categories.observe(this) { newCategories ->
            categories.value = newCategories ?: listOf()
            AppointmentEventTracker.trackAppointmentCategoriesViewedEvent(
                scheduledClassListViewModel.originView,
                scheduledClassListViewModel.originComponent,
                EventsPathIdentifier.BUSINESS_SCHEDULE_APPOINTMENTS.key,
                appointmentCategoryViewModel.location,
                newCategories?.size ?: 0
            )

        }

        appointmentCategoryViewModel.activityToLaunch.observe(this) {
            it?.let(::startActivity)
        }

        setContent {
            if (scheduledClassListViewModel.showDatePickerDialog.collectAsState().value) {
                showDatePickerDialog()
            }

            if (scheduledClassListViewModel.showFilterDialog.collectAsState().value) {
                showFilterDialog()
            }

            val scheduleState = scheduledClassListViewModel.scheduleState.collectAsState().value
            val locationWithDetails =
                scheduledClassListViewModel.locationWithDetails.collectAsState(null).value

            if (scheduleState.error != null) {
                if (scheduleState.error.isEmpty()) {
                    ToastUtils.show(getString(R.string.server_error_dialog_message))
                } else {
                    ToastUtils.show(scheduleState.error)
                }
            } else {
                locationWithDetails.let {
                    BusinessScheduleScreen(
                        onBackPressed = { this.onBackPressedDispatcher.onBackPressed() },
                        timezoneId = it?.location?.timezoneId,
                        menuItems = if (it?.details?.hasClasses == true) scheduledClassListViewModel.menuItems else listOf(),
                        emptyListText = getString(R.string.no_classes_results),
                        emptyListSubText = getString(R.string.tap_different_day_text),
                        appointmentCategories = categories.value,
                        classScheduleState = scheduleState.toClassScheduleState(),
                        goToDate = scheduledClassListViewModel.goToDate.value,
                        hasClasses = it?.details?.hasClasses == true,
                        hasAppointments = it?.details?.hasAppointments == true,
                        onDateSelected = { startDate, endDate ->
                            scheduledClassListViewModel.onDateRangeSelected(startDate, endDate)
                        },
                        onActionButtonClick = { position ->
                            val classAttributes = scheduledClassListViewModel.scheduleState.value.classScheduleItems[position]
                            onActionButtonClick(classAttributes)
                        },
                        onClassClick = { pos ->
                            val classAttributes = scheduledClassListViewModel.scheduleState.value.classScheduleItems[pos]
                            onClassItemClick(classAttributes)
                        },
                        onAppointmentCategoryClick = {
                            appointmentCategoryViewModel.handleItemClick(
                                this,
                                it
                            )
                        },
                        onTabSelected = { tabId ->
                            trackTabSelections(tabId)
                        }
                    )
                }
            }
        }
    }

    private fun trackTabSelections(tabId: String) {
        when (tabId) {
            "classes_tab" -> {
                trackTabSelected(
                    OriginView.BUSINESS_SCHEDULE,
                    null,
                    scheduledClassListViewModel.location.value,
                    CLASSES_TAB
                )
            }

            "appointments_tab" -> {
                trackTabSelected(
                    OriginView.BUSINESS_SCHEDULE,
                    null,
                    appointmentCategoryViewModel.location,
                    APPOINTMENTS_TAB
                )
            }

            else -> {}
        }
    }

    private fun onActionButtonClick(classAttributes: ClassAttributes) {
        val cto = classAttributes.toCto()
        if (cto?.bookabilityIsBlocked() == true) {
            BreadcrumbsUtils.breadcrumbBookabilityBlocker(cto, BookabilitySourceScreen.FavoriteClassList)
            launchBookabilityBlockedInformation(supportFragmentManager, cto, this)
        } else {
            scheduledClassListViewModel.quickBookClass(
                classAttributes = classAttributes,
                showDialogCallback = { quickbookDialog ->
                    quickbookDialog.show(
                        supportFragmentManager,
                        QuickBookDialog.SELECTED_USER_TAG ?: QuickBookDialog.TAG
                    )
                },
                onFailureCallback = { failureResult: QuickbookFailureReason? ->
                    val phoneString = getInternationalPhoneNumber(
                        classAttributes.location,
                        deviceRepository.getNetworkCountryCode(false)
                    )
                    if (failureResult == QuickbookFailureReason.IAP) {
                        val dialog = InAppPurchasesBlockedDialog.newInstance(
                            phoneString
                        )
                        dialog.show(supportFragmentManager, null)
                    }
                },
                onSuccessCallback = { res ->
                    onSuccessfulClassBooking(res)
                }
            )
        }
    }

    private fun showDatePickerDialog() {
        val dialog = CalendarDayPickerDialog()
        dialog.manualSelect(scheduledClassListViewModel.goToDate.value)
        dialog.setDateSelectedListener { result: Calendar? ->
            result?.let {
                scheduledClassListViewModel.goToDate.value = result
            }
            scheduledClassListViewModel.showDatePickerDialog(false)
        }
        dialog.setDismissListener { result: Calendar? ->
            scheduledClassListViewModel.showDatePickerDialog(false)
        }
        dialog.show(supportFragmentManager, Constants.CALENDAR_DAY_PICKER_TAG)
        trackCalendarDialogOpened(OriginView.BUSINESS_SCHEDULE, null, scheduledClassListViewModel.location.value)
    }

    private fun showFilterDialog() {
        trackFilterDialogOpened(
            originView = OriginView.BUSINESS_SCHEDULE,
            originComponent = null,
            location = scheduledClassListViewModel.location.value
        )
        val filterDialog = FilterDialog<ClassTypeObject>()
        filterDialog.setFilters(
            scheduledClassListViewModel.allFilters,
            scheduledClassListViewModel.currentFilters,
        )
        filterDialog.setSaveFiltersCallback { filters: Set<IFilter<ClassTypeObject>?>? ->
            filters?.let { scheduledClassListViewModel.setCurrentFilters(filters) }
            scheduledClassListViewModel.showFilterDialog(false)
        }
        filterDialog.setDismissListener {
            scheduledClassListViewModel.showFilterDialog(false)
        }
        filterDialog.show(supportFragmentManager, BUSINESS_SCHEDULE_ACTIVITY_FILTERS_DIALOG_TAG)
    }

    private fun onClassItemClick(classAttributes: ClassAttributes) {
        val classDetailsIntent = Intent(this, ClassTypeDetailsActivity::class.java)
        StaticInstance.selectedClassTypeObject = classAttributes.toCto()
        if (classAttributes.isEnrolment) {
            classDetailsIntent.putExtra(ClassTypeDetailsActivity.TYPE_EXTRA_STRING, ClassTypeDetailsActivity.Type.ENROLLMENT.ordinal)
        } else {
            classDetailsIntent.putExtra(ClassTypeDetailsActivity.TYPE_EXTRA_STRING, ClassTypeDetailsActivity.Type.CLASS.ordinal)
        }
        classDetailsIntent.putExtra(Constants.KEY_BUNDLE_ORIGIN_VIEW, OriginView.BUSINESS_SCHEDULE_CLASSES.viewName)
        classDetailsIntent.putExtra(Constants.KEY_BUNDLE_ORIGIN_COMPONENT, OriginComponent.CLASS_TIME_CARD.componentName)

        startActivityForResult(classDetailsIntent, ClassTypeDetailsActivity.CLASS_DETAILS_REQUEST_CODE)
    }

    private fun onSuccessfulClassBooking(result: CTOQuickBookResponse?) {
        var location: Location? = null
        result?.classTypeObject?.let {
            location = it.location
            if (it.hasVisits()) {
                AlarmUtils.scheduleNotificationsForClass(result.classTypeObject)
            }
        }

        DialogUtils.showSuccessfulClassBookingDialog(
            this,
            result?.classTypeObject,
            result?.cart,
            result?.paymentMethod,
            result?.pricingReference,
            location,
            result != null && result.showAttributionSurvey,
            result?.orderId ?: 0L,
            result?.qbViewState
        )
        // Full screen confirmation screen is fire and forget, so no need to wait for it to close
        BookingUtils.onBookingConfirmationDialogClosed(this, location)
    }
}
