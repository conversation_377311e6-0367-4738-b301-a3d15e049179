package com.mindbodyonline.connect.activities;

import static android.os.Build.VERSION.SDK_INT;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.preference.CheckBoxPreference;
import android.preference.Preference;
import android.preference.PreferenceCategory;
import android.preference.PreferenceFragment;
import android.preference.PreferenceScreen;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import com.mindbodyonline.android.util.log.MBLog;
import com.mindbodyonline.connect.BuildConfig;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.activities.custom.MBCompatActivity;
import com.mindbodyonline.connect.activities.details.EditMyProfileActivity;
import com.mindbodyonline.connect.activities.workflow.FitbitAccessTokenActivity;
import com.mindbodyonline.connect.activities.workflow.GoogleFitAccessTokenActivity;
import com.mindbodyonline.connect.activities.workflow.StravaAccessTokenActivity;
import com.mindbodyonline.connect.activities.workflow.WorkflowUtil;
import com.mindbodyonline.connect.adapters.RadioButtonListAdapter;
import com.mindbodyonline.connect.analytics.OriginView;
import com.mindbodyonline.connect.analytics.rokt.AppFeedbackDialogEventTracker;
import com.mindbodyonline.connect.common.repository.privacy.PrivacyPolicyDataSource.Callback;
import com.mindbodyonline.connect.login.AccountWorkflow;
import com.mindbodyonline.connect.profile.ScheduleProfileContainerFragment;
import com.mindbodyonline.connect.tealium.TrackingHelperUtils;
import com.mindbodyonline.connect.utils.AnalyticsUtils;
import com.mindbodyonline.connect.utils.CalendarUtils;
import com.mindbodyonline.connect.utils.Constants;
import com.mindbodyonline.connect.utils.DeepLinkUtils;
import com.mindbodyonline.connect.utils.DialogUtilsKtKt;
import com.mindbodyonline.connect.utils.IntentUtils;
import com.mindbodyonline.connect.utils.RequestPushNotificationPermissionDialogClickListener;
import com.mindbodyonline.connect.utils.SharedPreferencesUtils;
import com.mindbodyonline.connect.utils.ToastUtils;
import com.mindbodyonline.connect.utils.api.fitnessactivity.fitbit.FitBitAPI;
import com.mindbodyonline.connect.utils.api.fitnessactivity.googlefit.GoogleFitAPI;
import com.mindbodyonline.connect.utils.api.strava.StravaAPI;
import com.mindbodyonline.data.services.MBAuth;
import com.mindbodyonline.data.services.locator.ServiceLocator;
import com.mindbodyonline.domain.User;
import com.mindbodyonline.domain.dataModels.UserCalendar;
import com.mindbodyonline.framework.abvariant.FeatureFlag;
import com.mindbodyonline.framework.helpcenter.MbHelpCenter;
import com.mindbodyonline.views.LoadingOverlay;
import com.mindbodyonline.views.dialog.CTAButtonData;
import com.mindbodyonline.views.dialog.GenericListDialog;
import com.mindbodyonline.views.dialog.GenericVerticalCtaDialog;

import com.google.android.material.snackbar.Snackbar;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.SwitchCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;
import kotlin.Pair;
import kotlin.Unit;

import static com.mindbodyonline.connect.analytics.AnalyticsConstantsKt.CANCEL_BUTTON;
import static com.mindbodyonline.connect.analytics.AnalyticsConstantsKt.LIKE_IT_BUTTON;
import static com.mindbodyonline.connect.analytics.AnalyticsConstantsKt.NEEDS_IMPROVEMENT_BUTTON;
import static com.mindbodyonline.connect.utils.Constants.MANAGE_MEMBER_ACCOUNTS_URL;
import static com.mindbodyonline.connect.utils.IntentUtils.feedbackIntent;

public class SettingsActivity extends MBCompatActivity {

    private static final String TAG = "MBSettings";

    public static final String PREF_ACTION = "pref_action";

    private static final String[] CHANGE_LISTENER_PREFS = new String[]{
        SharedPreferencesUtils.FITBIT_LINK_PREFERENCE,
        SharedPreferencesUtils.STRAVA_LINK_PREFERENCE,
        SharedPreferencesUtils.NOTIFICATIONS_PREFERENCE,
        SharedPreferencesUtils.GOOGLE_FIT_LINK_PREFERENCE
    };

    /**
     * Pairs are of top level preference/category key (ID) to lower level preference key (ID)
     */
    private static final List<Pair<Integer, Integer>> USER_ONLY_PREFERENCES = Arrays.asList(
        new Pair<>(R.string.pref_key_account, null),
        new Pair<>(R.string.pref_key_wallet, null),
        new Pair<>(R.string.pref_apps_devices_header, null),
        new Pair<>(R.string.pref_calendar_setting_header, null),
        new Pair<>(R.string.pref_key_marketing_opt_in, null),
        new Pair<>(R.string.pref_remote_privacy_preferences, null),
        new Pair<>(R.string.pref_key_manage_family_accounts, null),
        new Pair<>(R.string.pref_key_manage_cancel_contracts, null)
    );

    private static final int REQUEST_CODE_OAUTH = 80081;
    private static final int REQUEST_CODE_STRAVA_OAUTH = 80082;
    private static final int REQUEST_CODE_GOOGLE_FIT = 80083;
    private static List<String> calendarNamesList;
    private static List<String> calendarIdList;
    private SettingsFragment settingsFragment;
    private String currentAction;

    private static void addCalendarsToListPreference(Activity context) {
        calendarNamesList = new ArrayList<>();
        calendarIdList = new ArrayList<>();
        if (CalendarUtils.readCalendarPermissionAllowed(context, false)) {
            SparseArray<UserCalendar> calendars = CalendarUtils.getMapOfCalendars(context);

            for (int i = 0; i < calendars.size(); i++) {
                calendarNamesList.add(calendars.valueAt(i).getName());
                calendarIdList.add("" + calendars.valueAt(i).getId());
            }
        }

        if (calendarNamesList.size() == 0) {
            calendarNamesList.add(context.getString(R.string.default_calendar));
            calendarIdList.add("0");
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (currentAction != null && currentAction.equals(SharedPreferencesUtils.NOTIFICATIONS_PREFERENCE)) {
            getMenuInflater().inflate(R.menu.notifications_menu, menu);
            MenuItem menuItem = menu.findItem(R.id.menu_item);

            SwitchCompat menuSwitch = (SwitchCompat) menuItem.getActionView();

            if (SharedPreferencesUtils.getUserSpecificSharedPreferences().getBoolean(SharedPreferencesUtils.NOTIFICATIONS_PREFERENCE, true)) {
                menuSwitch.setChecked(true);
                settingsFragment.enableNotifications(true);
            } else {
                settingsFragment.enableNotifications(false);
            }

            menuSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                settingsFragment.enableNotifications(isChecked);
                AnalyticsUtils.logEvent("(Settings) | Updated notification setting", "All notifications", isChecked);
                AnalyticsUtils.setBrazePushEnabled(!Boolean.FALSE.equals(isChecked) &&
                    SharedPreferencesUtils.getPushNotificationsEnabled());
            });
        }

        return super.onCreateOptionsMenu(menu);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        currentAction = getIntent().getAction();
        Bundle prefBundle = new Bundle();
        prefBundle.putString(PREF_ACTION, currentAction);

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setHomeButtonEnabled(true);
            actionBar.setDisplayHomeAsUpEnabled(true);
            if (SharedPreferencesUtils.NOTIFICATIONS_PREFERENCE.equals(currentAction)) {
                setActionBarTitle(getString(R.string.pref_title_manage_notifications));
            } else if (SharedPreferencesUtils.INTEGRATIONS_PREFERENCE.equals(currentAction)) {
                setActionBarTitle(getString(R.string.pref_apps_devices_header));
            } else if (SharedPreferencesUtils.CALENDAR_CATEGORY_PREFERENCE.equals(currentAction)) {
                setActionBarTitle(getString(R.string.pref_calendar_setting_header));
            } else if (SharedPreferencesUtils.MORE_CATEGORY_PREFERENCE.equals(currentAction)) {
                setActionBarTitle(getString(R.string.more_cap));
            } else {
                setActionBarTitle(getString(R.string.title_activity_settings));
            }
        }

        settingsFragment = new SettingsFragment();
        settingsFragment.setArguments(prefBundle);

        getFragmentManager().beginTransaction()
            .replace(R.id.activity_settings_content, settingsFragment)
            .commit();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NotNull String[] permissions, @NotNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case Constants.READ_CALENDAR_PERMISSIONS_CODE: {
                addCalendarsToListPreference(this);

                // make sure we don't allow option to sync calendar be active if we were denied permissions
                if (grantResults.length == 2 && grantResults[0] == grantResults[1] && grantResults[1] == PackageManager.PERMISSION_DENIED) {
                    settingsFragment.calendarSyncPreference.setChecked(false);
                }
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            // Respond to the action bar's Up/Home button
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    //TODO If we EVER handle configuration changes, we need to make this a static inner class.  We
    //     do that by switching over to the androidx preference packages, which will be a ton of work
    //     due to how this preference screen is laid out.  We need the support packages because we
    //     call our own material dialog from this fragment, for logout.  As-is, any configuration
    //     change would leak the Activity, so this is OK as long as we still don't support configchanges.
    @SuppressLint("ValidFragment")
    public static class SettingsFragment extends PreferenceFragment implements SharedPreferences.OnSharedPreferenceChangeListener,
        Preference.OnPreferenceChangeListener, Preference.OnPreferenceClickListener {
        private View rootView;
        private User user;
        private Preference fitbitPreference;
        private Preference stravaPreference;
        private Preference googleFitPreference;
        private CheckBoxPreference calendarSyncPreference;
        private LoadingOverlay loadingOverlay;
        private int currentDistanceUnitPreferencePos;
        private int currentCalendarPreferencePos;
        private SettingsActivity activity;
        private SettingsViewModel viewModel;
        private ActivityResultLauncher<String> pushNotificationPermissionLauncher;
        private boolean didNavigateToNotificationSettings = false;

        @Override
        public void onCreate(Bundle savedInstanceState) {
            super.onCreate(savedInstanceState);
            String action = getArguments().getString(PREF_ACTION);
            getPreferenceManager().setSharedPreferencesName(SharedPreferencesUtils.getUserIdPrefsName(MBAuth.getUser()));
            user = MBAuth.getUser();
            SettingsViewModelFactory viewModelFactory = new SettingsViewModelFactory(ServiceLocator.getUserRepository(), ServiceLocator.getAbTestFramework(), activity, null);
            viewModel = new ViewModelProvider(activity, viewModelFactory).get(SettingsViewModel.class);
            viewModel.getUserData().observe(activity, user -> {
                if (user != null) {
                    CheckBoxPreference newsletters = (CheckBoxPreference) findPreference(SharedPreferencesUtils.MINDBODY_NEWSLETTERS_PREFERENCE);
                    if (newsletters != null) newsletters.setChecked(user.isMarketingOptIn());
                }
                loadingOverlay.hide();
            });
            // Always override user pref with global pref
            getPreferenceManager().getSharedPreferences()
                .edit()
                .putBoolean(SharedPreferencesUtils.PUSH_PREFERENCE, SharedPreferencesUtils.getPushNotificationsEnabled())
                .apply();

            if (action == null) {
                addPreferencesFromResource(R.xml.pref_general);
                setUpClickListeners();
            } else if (action.equals(SharedPreferencesUtils.NOTIFICATIONS_PREFERENCE)) {
                addPreferencesFromResource(R.xml.pref_notification);
                findPreference(SharedPreferencesUtils.PUSH_PREFERENCE).setOnPreferenceChangeListener(this);
                CheckBoxPreference newsletters = (CheckBoxPreference) findPreference(SharedPreferencesUtils.MINDBODY_NEWSLETTERS_PREFERENCE);
                if (newsletters != null && user != null) {
                    newsletters.setChecked(user.isMarketingOptIn());
                }
            } else if (action.equals(SharedPreferencesUtils.INTEGRATIONS_PREFERENCE)) {
                addPreferencesFromResource(R.xml.pref_integrations);
                refreshLinkedAccountsStatus();
                fitbitPreference = findPreference(SharedPreferencesUtils.FITBIT_LINK_PREFERENCE);
                stravaPreference = findPreference(SharedPreferencesUtils.STRAVA_LINK_PREFERENCE);
                googleFitPreference = findPreference(SharedPreferencesUtils.GOOGLE_FIT_LINK_PREFERENCE);
                setUpIntegrationClickListeners();
            } else if (action.equals(SharedPreferencesUtils.CALENDAR_CATEGORY_PREFERENCE)) {
                addPreferencesFromResource(R.xml.pref_calendar_sync);
                calendarSyncPreference = (CheckBoxPreference) findPreference(SharedPreferencesUtils.SYNC_CALENDAR_PREFERENCE);
                addCalendarsToListPreference(getActivity());
                setOnPreferenceClickListener(getString(R.string.pref_key_user_calendar));
                findPreference(SharedPreferencesUtils.SYNC_CALENDAR_PREFERENCE).setOnPreferenceChangeListener(this);
            } else if (action.equals(SharedPreferencesUtils.MORE_CATEGORY_PREFERENCE)) {
                addPreferencesFromResource(R.xml.pref_more);
                setUpMoreCategoryClickListeners();
                Preference version = findPreference(getString(R.string.version_title));
                version.setSummary(BuildConfig.VERSION_NAME + " (" + BuildConfig.VERSION_CODE + ")");
            }

            pushNotificationPermissionLauncher = activity.registerForActivityResult(new ActivityResultContracts.RequestPermission(),
                    this::onPushNotificationPermissionResult);

            disableUserPreferencesForGuestUser();
            addPreferenceChangeListeners();
        }

        @Override
        public View onCreateView(@NotNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
            rootView = inflater.inflate(R.layout.fragment_settings, container, false);

            loadingOverlay = rootView.findViewById(R.id.loading_overlay);
            loadingOverlay.hide();
            return rootView;
        }

        @Override
        public void onResume() {
            super.onResume();

            if (!MBAuth.isGuestUser()) {
                loadingOverlay.show();
                viewModel.getUsers();
            }

            /*
              If the user navigated to the notification settings screen and then navigated back,
              we need to check if the user has granted the notification permission and update the
              push notification preference accordingly.
              This is necessary because the user may have granted the permission while on the
              notification settings screen.
             */
            if (didNavigateToNotificationSettings) {
                viewModel.handlePushNotificationPreferenceChanged(hasPushNotificationPermission());
            } else {
                // The user did not enable the push notification preference, so we need to update the
                // push notification preference to false.
                CheckBoxPreference pushNotificationPref = (CheckBoxPreference) findPreference(getString(R.string.pref_key_push_notifications));
                if (pushNotificationPref != null && !pushNotificationPref.isChecked()) {
                    pushNotificationPref.setChecked(false);
                }
            }

            getPreferenceScreen().getSharedPreferences()
                .registerOnSharedPreferenceChangeListener(this);
            refreshLinkedAccountsStatus();
        }

        @Override
        public void onPause() {
            super.onPause();
            getPreferenceScreen().getSharedPreferences()
                .unregisterOnSharedPreferenceChangeListener(this);
        }

        private void addPreferenceChangeListeners() {
            for (String pref : CHANGE_LISTENER_PREFS) {
                Preference preference = findPreference(pref);
                if (preference != null) {
                    preference.setOnPreferenceChangeListener(this);
                }
            }

            Preference activityNotif = findPreference(getString(R.string.pref_key_activity_dashboard_notification));
            if (activityNotif != null) {
                activityNotif.setOnPreferenceChangeListener(this);
            }
        }

        private void refreshLinkedAccountsStatus() {
            if (fitbitPreference != null) {
                fitbitPreference.setSummary(FitBitAPI.hasToken() ? R.string.connected : R.string.not_connected);
            }

            if (stravaPreference != null) {
                stravaPreference.setSummary(StravaAPI.getInstance().hasToken() ? R.string.connected : R.string.not_connected);
            }

            if (googleFitPreference != null) {
                googleFitPreference.setSummary(GoogleFitAPI.getInstance().isGoogleFitConnected() ? R.string.connected : R.string.not_connected);
            }
        }

        @Override
        public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
            switch (key) {
                case SharedPreferencesUtils.MINDBODY_NEWSLETTERS_PREFERENCE:
                    boolean marketingOptIn = sharedPreferences.getBoolean(key, false);
                    user.setMarketingOptIn(marketingOptIn);
                    TrackingHelperUtils.INSTANCE.trackChangeNotificationsEvent(marketingOptIn);
                    ServiceLocator.getUserRepository().saveUser(user, null, error -> MBLog.e(TAG, "Could not save marketing opt in setting"));
                    break;
                case SharedPreferencesUtils.SYNC_CALENDAR_PREFERENCE:
                    CalendarUtils.readCalendarPermissionAllowed(getActivity(), true);
                    break;
                case SharedPreferencesUtils.SIGNIN_NOTIFICATIONS_PREFERENCE:
                    boolean signInEnabled = sharedPreferences.getBoolean(key, true);
                    AnalyticsUtils.logEvent("(Settings) | Updated Sign-in prompt setting", "Enabled", signInEnabled);
                    break;
                case SharedPreferencesUtils.RATINGS_REVIEWS_PREFERENCE:
                    break;
            }
        }

        @Override
        public boolean onPreferenceChange(Preference preference, final Object newValue) {
            switch (preference.getKey()) {
                case SharedPreferencesUtils.SYNC_CALENDAR_PREFERENCE:
                    AnalyticsUtils.logEvent("(Settings) | Updated Calendar sync settings", "Enabled syncing", newValue.toString());
                    break;
                case SharedPreferencesUtils.RATINGS_REVIEWS_PREFERENCE:
                    AnalyticsUtils.logEvent("(Settings) | Updated review notification setting", "Enabled review notifications", newValue.toString());
                    break;
                case SharedPreferencesUtils.SIGNIN_NOTIFICATIONS_PREFERENCE:
                    break;
                case SharedPreferencesUtils.PUSH_PREFERENCE:
                    if (newValue instanceof Boolean) {
                        if (newValue.equals(Boolean.TRUE)) {
                            if (hasPushNotificationPermission()) {
                                viewModel.handlePushNotificationPreferenceChanged((Boolean) newValue);
                            } else {
                                if (SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                                    pushNotificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS);
                                } else {
                                    // Below Android 13 You don't need to ask for notification permission.
                                    viewModel.handlePushNotificationPreferenceChanged((Boolean) newValue);
                                }
                            }
                        } else {
                            viewModel.handlePushNotificationPreferenceChanged((Boolean) newValue);
                        }
                    }
                    break;
            }

            // For preferences with keys that are defined in strings.xml
            switch (preference.getTitleRes()) {
                case R.string.pref_title_activity_dashboard_notification:
                    AnalyticsUtils.logEvent("(Settings) | Updated notification setting", "Activity notifications", newValue.toString());
            }
            return true;
        }

        /**
         * Checks if the app has the notification permission.
         * This method is only available on Android 13 and above.
         * On Android 12 and below, the app does not need to ask for notification permission.
         * @return true if the app has the notification permission, false otherwise.
         */
        private boolean hasPushNotificationPermission() {
            if (SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                return ContextCompat.checkSelfPermission(activity, Manifest.permission.POST_NOTIFICATIONS)
                        == PackageManager.PERMISSION_GRANTED;
            } else {
                // Below Android 13 You don't need to ask for notification permission.
                return true;
            }
        }

        @Override
        public boolean onPreferenceClick(Preference preference) {
            String key = preference.getKey();

            if (key.equalsIgnoreCase(getString(R.string.pref_key_fitbit))) {
                onFitbitLink();
            } else if (key.equalsIgnoreCase(getString(R.string.pref_key_strava))) {
                onStravaLink();
            } else if (key.equalsIgnoreCase(getString(R.string.pref_key_google_fit))) {
                onGoogleFitLink();
            } else if (key.equalsIgnoreCase(getString(R.string.pref_key_user_calendar))) {
                onSelectCalendarClicked();
            } else if (key.equalsIgnoreCase(getString(R.string.pref_key_distance_units))) {
                onSelectUnitsClicked();
            } else if (key.equalsIgnoreCase(getString(R.string.pref_key_account))) {
                AnalyticsUtils.logEvent("(My Info) | Account button tapped");
                startActivityForResult(
                    new Intent(getActivity(), EditMyProfileActivity.class),
                    ScheduleProfileContainerFragment.EDIT_PROFILE_REQUEST
                );
                return true;
            } else if (key.equalsIgnoreCase(getString(R.string.pref_key_wallet))) {
                AnalyticsUtils.logEvent("(My Info) | Payments button tapped");
            } else if (key.equalsIgnoreCase(getString(R.string.action_live_chat))) {
                AnalyticsUtils.logEvent("(Settings) | Contact us tapped", "location", "Profile");
                IntentUtils.launchWebsite(getActivity(), Constants.CONTACT_SUPPORT_URL);
            } else if (key.equalsIgnoreCase(getString(R.string.log_in_or_create_an_account))) {
                AccountWorkflow.launchLoginIntent(getActivity(),
                    DeepLinkUtils.buildProfileDeepLink(),
                    "User Profile");
            } else if (key.equalsIgnoreCase(getString(R.string.action_logout))) {
                WorkflowUtil.launchLogoutDialogNonStyled(getActivity());
            } else if (key.equalsIgnoreCase(getString(R.string.pref_share_app))) {
                AnalyticsUtils.startLogShare("(Settings) | Shared application");
                IntentUtils.shareConnectIntent((SettingsActivity) getActivity(), true);
            } else if (key.equalsIgnoreCase(getString(R.string.nav_feedback_text))) {
                onClickFeedback();
            } else if (preference.getTitleRes() == R.string.pref_help_center) {
                AnalyticsUtils.logEvent("(Settings) | Opened Help Center");
            } else if (preference.getTitleRes() == R.string.about_terms) {
                AnalyticsUtils.logEvent("(Settings) | Opened Terms of Service");
                IntentUtils.launchWebsite(getActivity(), getString(R.string.tos_address));
                //UPDATE:  we aren't using the dialog anymore
                /*if (getActivity() instanceof FragmentActivity) {
                    TermsOfUseDialog.newInstance(null, false)
                        .show(((FragmentActivity) getActivity()).getSupportFragmentManager(),
                            TermsOfUseDialog.class.getSimpleName());
                }*/
            } else if (preference.getTitleRes() == R.string.about_privacy) {
                AnalyticsUtils.logEvent("(Settings) | Opened Privacy Policy");
            } else if (preference.getTitleRes() == R.string.action_faq) {
                MbHelpCenter.getInstance().start(getActivity());
            } else if (preference.getTitleRes() == R.string.action_contact_us) {
                MbHelpCenter.getInstance().showTicketing(getActivity());
                AnalyticsUtils.logEvent("(Acct Mgmt) | Contact us tapped");
            } else if (key.equalsIgnoreCase(getString(R.string.pref_remote_privacy_preferences))) {
                loadDataPreferenceUrl();
            } else if (FeatureFlag.FTC_MANAGE_CONTRACTS.isFeatureEnabled() &&
                    key.equalsIgnoreCase(getString(R.string.pref_key_manage_cancel_contracts))) {
                loadManageOrCancelContractsUrl();
            } else if (key.equalsIgnoreCase(getString(R.string.pref_key_manage_family_accounts))) {
                AnalyticsUtils.logEvent("(Settings) | Manage Family Accounts Tapped");
                onManageFamilyAccountsLink();
            }
            return false;
        }

        private void loadManageOrCancelContractsUrl() {
            IntentUtils.launchWebsite(activity, Constants.CONTRACT_CANCELLATION_URL, true);
        }

        private void loadDataPreferenceUrl() {
            loadingOverlay.show();
            ServiceLocator.getPrivacyPolicyDataSource().getPrivacyPreferenceUrl(new Callback() {
                @Override public void onSuccess(@NonNull String url) {
                    IntentUtils.launchWebsite(activity, url, true);
                    // wait for intent dialog to show before hiding the loading indicator
                    new Handler(Looper.getMainLooper()).post(() -> loadingOverlay.hide());
                }

                @Override public void onError(@NonNull Throwable error) {
                    loadingOverlay.hide();
                    ToastUtils.showServerErrorToast();
                }
            });
            AnalyticsUtils.logEvent("(Settings) | Opened Data Preferences");
        }

        private void onManageFamilyAccountsLink() {
            Intent manageAccountsIntent = new Intent(Intent.ACTION_VIEW);
            manageAccountsIntent.setData(Uri.parse(MANAGE_MEMBER_ACCOUNTS_URL));
            startActivity(manageAccountsIntent);
        }

        private void onFitbitLink() {
            Intent socialIntent = new Intent(getActivity(), FitbitAccessTokenActivity.class);
            startActivityForResult(socialIntent, SettingsActivity.REQUEST_CODE_OAUTH);
            AnalyticsUtils.logEvent("(Settings) | Opened Link Your Fitbit screen");
            AnalyticsUtils.logEvent("(Fitbit) | View Connect Page");
        }

        private void onStravaLink() {
            Intent socialIntent = new Intent(getActivity(), StravaAccessTokenActivity.class);
            startActivityForResult(socialIntent, SettingsActivity.REQUEST_CODE_STRAVA_OAUTH);
            AnalyticsUtils.logEvent("(Settings) | Opened Link Your Strava screen");
        }

        private void onGoogleFitLink() {
            AnalyticsUtils.logEvent("(Settings) | Opened Link Your Google Fit screen");
            Intent socialIntent = new Intent(getActivity(), GoogleFitAccessTokenActivity.class);
            startActivityForResult(socialIntent, SettingsActivity.REQUEST_CODE_GOOGLE_FIT);
        }

        private void onClickFeedback() {
            AppFeedbackDialogEventTracker.trackAppFeedbackViewedEvent(
                    OriginView.SETTINGS,
                    null
            );
            final GenericVerticalCtaDialog rateDialog = GenericVerticalCtaDialog.newInstance(getString(R.string.message_rate_dialog),
                Arrays.asList(
                    new CTAButtonData(getString(R.string.rate_love_it), true, () -> {
                        AppFeedbackDialogEventTracker.trackAppFeedbackInteractedEvent(
                                LIKE_IT_BUTTON,
                                OriginView.APP_RATING_POPUP,
                                null
                        );
                        DialogUtilsKtKt.launchInAppPlaystoreReview(this.activity);
                        return Unit.INSTANCE;
                    }),
                    new CTAButtonData(getString(R.string.rate_hate_it), true, () -> {
                        AppFeedbackDialogEventTracker.trackAppFeedbackInteractedEvent(
                                NEEDS_IMPROVEMENT_BUTTON,
                                OriginView.APP_RATING_POPUP,
                                null
                        );
                        SharedPreferencesUtils.setLastAskedAppFeedback(Calendar.getInstance());
                        feedbackIntent(getActivity());
                        return Unit.INSTANCE;
                    })
                ), () -> {
                        AppFeedbackDialogEventTracker.trackAppFeedbackInteractedEvent(
                                CANCEL_BUTTON,
                                OriginView.APP_RATING_POPUP,
                                null
                        );
                        return Unit.INSTANCE;
                    }
                    );
            rateDialog.changeCtaButtonsColor(ContextCompat.getColor(getActivity(), R.color.orange));
            rateDialog.show(((SettingsActivity) getActivity()).getSupportFragmentManager(), Constants.RATE_DIALOG_FRAGMENT_TAG);
        }

        private void onSelectUnitsClicked() {

            final List<String> units = Arrays.asList(getResources().getStringArray(R.array.distance_units));
            final List<String> unitsValues = Arrays.asList(getResources().getStringArray(R.array.distance_unit_values));

            setCurrentDistanceUnitsPreference();

            RadioButtonListAdapter adapter = new RadioButtonListAdapter(getActivity(), units, currentDistanceUnitPreferencePos);
            final GenericListDialog distanceDialog = new GenericListDialog();
            distanceDialog.setTitle(getString(R.string.distance_units_pref_title));
            distanceDialog.setAdapter(adapter);

            distanceDialog.setButton(getString(R.string.cancel), result -> distanceDialog.dismiss());

            distanceDialog.setOnItemSelectedListener(result -> {
                SharedPreferencesUtils.getUserSpecificSharedPreferences().edit().putString(SharedPreferencesUtils.PREF_DISTANCE_UNITS, unitsValues.get(result)).apply();
                distanceDialog.dismiss();
            });

            distanceDialog.show(((SettingsActivity) getActivity()).getSupportFragmentManager(), "");
        }

        private void setCurrentDistanceUnitsPreference() {
            List<String> unitValues = new ArrayList<>(Arrays.asList(getResources().getStringArray(R.array.distance_unit_values)));

            String distancePreference = SharedPreferencesUtils.getUserSpecificSharedPreferences().getString(SharedPreferencesUtils.PREF_DISTANCE_UNITS, "auto");

            currentDistanceUnitPreferencePos = 0;
            for (int i = 0; i < unitValues.size(); i++) {
                if (unitValues.get(i).equals(distancePreference)) {
                    currentDistanceUnitPreferencePos = i;
                    break;
                }
            }
        }

        private void onSelectCalendarClicked() {
            setCurrentCalendarPreference();

            RadioButtonListAdapter adapter = new RadioButtonListAdapter(getActivity(), calendarNamesList, currentCalendarPreferencePos);
            final GenericListDialog calendarDialog = new GenericListDialog();
            calendarDialog.setTitle(getString(R.string.pref_title_user_calendar));
            calendarDialog.setAdapter(adapter);

            calendarDialog.setButton(getString(R.string.cancel), result -> calendarDialog.dismiss());

            calendarDialog.setOnItemSelectedListener(result -> {
                SharedPreferencesUtils.getUserSpecificSharedPreferences().edit().putString(SharedPreferencesUtils.CALENDAR_ID_PREFERENCE, "" + calendarIdList.get(result)).apply();
                calendarDialog.dismiss();
            });

            calendarDialog.show(((SettingsActivity) getActivity()).getSupportFragmentManager(), "");
        }


        private void setCurrentCalendarPreference() {

            String calendarPreference = SharedPreferencesUtils.getUserSpecificSharedPreferences().getString(SharedPreferencesUtils.CALENDAR_ID_PREFERENCE, "-1");

            currentCalendarPreferencePos = -1;
            for (int i = 0; i < calendarIdList.size(); i++) {
                if (calendarIdList.get(i).equals(calendarPreference)) {
                    currentCalendarPreferencePos = i;
                    break;
                }
            }
        }

        @Override
        public void onActivityResult(int requestCode, int resultCode, Intent data) {
            super.onActivityResult(requestCode, resultCode, data);
            refreshLinkedAccountsStatus();
            switch (requestCode) {
                case REQUEST_CODE_OAUTH:
                    if (resultCode == RESULT_OK) {
                        Snackbar.make(rootView, getString(R.string.fitbit_context_helper_title), Snackbar.LENGTH_SHORT).show();
                        setConnectionResult();
                    }
                    break;
                case REQUEST_CODE_STRAVA_OAUTH:
                    if (resultCode == RESULT_OK) {
                        Snackbar.make(rootView, getString(R.string.strava_context_helper_title), Snackbar.LENGTH_SHORT).show();
                    }
                    break;
                case REQUEST_CODE_GOOGLE_FIT:
                    if (resultCode == RESULT_OK) {
                        Snackbar.make(rootView, getString(R.string.google_fit_context_helper_title), Snackbar.LENGTH_SHORT).show();
                        setConnectionResult();
                    }
                    break;
                case ScheduleProfileContainerFragment.EDIT_PROFILE_REQUEST:
                    getActivity().setResult(resultCode);
                    break;
            }
        }

        private void setConnectionResult() {
            if (getActivity() != null) {
                getActivity().setResult(Activity.RESULT_OK);
            }
        }

        private void disableUserPreferencesForGuestUser() {
            PreferenceScreen screen = getPreferenceScreen();

            // Remove the "Log out" or "Log in" button, depending on the guest user state
            Preference loginPreferenceToRemove = findPreference(getString(
                MBAuth.isGuestUser() ? R.string.action_logout : R.string.log_in_or_create_an_account
            ));
            if (loginPreferenceToRemove != null) screen.removePreference(loginPreferenceToRemove);

            // Disable the guest user preferences
            if (MBAuth.isGuestUser()) {
                for (Pair<Integer, Integer> resPair : USER_ONLY_PREFERENCES) {
                    CharSequence categoryKey = getString(resPair.getFirst());
                    Integer preferenceKeyRes = resPair.getSecond();
                    CharSequence preferenceKey = preferenceKeyRes == null ? null :
                        getString(preferenceKeyRes);

                    if (preferenceKey == null) {
                        Preference categoryPref = findPreference(categoryKey);
                        if (categoryPref != null) categoryPref.setEnabled(false);
                    } else {
                        PreferenceCategory category = (PreferenceCategory) screen.findPreference(categoryKey);
                        if (category != null) {
                            Preference childPref = category.findPreference(preferenceKey);
                            if (childPref != null) childPref.setEnabled(false);
                        }
                    }
                }
            }
        }

        private void setUpIntegrationClickListeners() {
            setOnPreferenceClickListener(getString(R.string.pref_key_fitbit));
            setOnPreferenceClickListener(getString(R.string.pref_key_strava));
            setOnPreferenceClickListener(getString(R.string.pref_key_google_fit));
        }

        private void setUpMoreCategoryClickListeners() {
            setOnPreferenceClickListener(getString(R.string.pref_key_distance_units));
            setOnPreferenceClickListener(getString(R.string.pref_share_app));
            setOnPreferenceClickListener(getString(R.string.about_terms));
            setOnPreferenceClickListener(getString(R.string.nav_feedback_text));
            setOnPreferenceClickListener(getString(R.string.pref_help_center));
            setOnPreferenceClickListener(getString(R.string.about_privacy));
            setOnPreferenceClickListener(getString(R.string.action_faq));
            setOnPreferenceClickListener(getString(R.string.action_contact_us));

            Preference gdprPreference = findPreference(getString(R.string.pref_remote_privacy_preferences));

            // remove GDPR setting for US clients
            boolean removed = gdprPreference == null;
            if (!removed && MBAuth.isGuestUser()) {
                removed = getPreferenceScreen().removePreference(gdprPreference);
            }

            if (!removed) {
                // set the click listener if we were unable to remove it
                gdprPreference.setOnPreferenceClickListener(this);
            }
        }

        private void setUpClickListeners() {
            setOnPreferenceClickListener(getString(R.string.log_in_or_create_an_account));
            setOnPreferenceClickListener(getString(R.string.pref_key_account));
            Preference manageCancelContractsPref = findPreference(getString(R.string.pref_key_manage_cancel_contracts));
            if (manageCancelContractsPref != null) {
                if (FeatureFlag.FTC_MANAGE_CONTRACTS.isFeatureEnabled()) {
                    setOnPreferenceClickListener(getString(R.string.pref_key_manage_cancel_contracts));
                } else {
                    getPreferenceScreen().removePreference(manageCancelContractsPref);
                }
            }
            setOnPreferenceClickListener(getString(R.string.pref_key_wallet));
            setOnPreferenceClickListener(getString(R.string.action_live_chat));
            setOnPreferenceClickListener(getString(R.string.action_logout));
            setOnPreferenceClickListener(getString(R.string.pref_key_manage_family_accounts));
        }

        private void setOnPreferenceClickListener(String preferenceKey) {
            Preference preference = findPreference(preferenceKey);
            if (preference != null) preference.setOnPreferenceClickListener(this);
        }

        private void enableNotifications(boolean enable) {
            boolean isGuestUser = MBAuth.isGuestUser();

            findPreference(getString(R.string.pref_key_ratings_reviews)).setEnabled(!isGuestUser && enable);
            findPreference(getString(R.string.pref_key_activity_dashboard_notification)).setEnabled(!isGuestUser && FitBitAPI.hasToken() && enable);
            findPreference(getString(R.string.pref_key_signin_notifications)).setEnabled(!isGuestUser && enable);
            findPreference(getString(R.string.pref_key_push_notifications)).setEnabled(enable);
            findPreference(getString(R.string.pref_key_notification_sound)).setEnabled(enable);
            findPreference(getString(R.string.pref_key_notification_vibrate)).setEnabled(enable);
            findPreference(getString(R.string.pref_key_notification_phone_led)).setEnabled(enable);

            SharedPreferencesUtils.getUserSpecificSharedPreferences().edit().putBoolean(SharedPreferencesUtils.NOTIFICATIONS_PREFERENCE, enable).apply();
        }

        @Override public void onAttach(Activity activity) {
            super.onAttach(activity);
            this.activity = (SettingsActivity) activity;
        }

        private void onPushNotificationPermissionResult(Boolean isGranted) {
            if (isGranted) {
                viewModel.handlePushNotificationPreferenceChanged(true);
            } else {
                if (!shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)) {
                    DialogUtilsKtKt.showNotificationPermissionRequiredDialog(activity.getSupportFragmentManager(),
                            new RequestPushNotificationPermissionDialogClickListener() {
                                @Override
                                public void onPositiveButtonClicked() {
                                    /*
                                      If the user navigates to the notification settings screen, we need to
                                      set this flag to true so that we can check if the user has granted the
                                      notification permission when they navigate back to the settings screen.
                                     */
                                    didNavigateToNotificationSettings = true;
                                }

                                @Override
                                public void onNegativeButtonClicked() {
                                    /*
                                      If the user denies the notification permission, we need to update the
                                      push notification preference to false.
                                     */
                                    ((CheckBoxPreference) findPreference(getString(R.string.pref_key_push_notifications))).setChecked(false);
                                    viewModel.handlePushNotificationPreferenceChanged(false);
                                }
                            });
                }
            }
        }
    }
}
