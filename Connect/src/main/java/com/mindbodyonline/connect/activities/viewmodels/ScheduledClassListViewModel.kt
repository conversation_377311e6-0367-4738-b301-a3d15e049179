package com.mindbodyonline.connect.activities.viewmodels

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mindbodyonline.android.api.sales.MBSalesApi
import com.mindbodyonline.android.api.sales.model.pos.cart.CartAbandonReason
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.adapters.filters.BookableOnlineClassFilter
import com.mindbodyonline.connect.adapters.filters.FilterUtils
import com.mindbodyonline.connect.adapters.filters.IFilter
import com.mindbodyonline.connect.adapters.filters.QualifiedClassFilter
import com.mindbodyonline.connect.analytics.EventsPathIdentifier
import com.mindbodyonline.connect.analytics.OriginComponent
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.analytics.checkout.BookingEventTracker
import com.mindbodyonline.connect.analytics.classes.FilterTracker
import com.mindbodyonline.connect.analytics.classes.extractTrackingNames
import com.mindbodyonline.connect.analytics.classes.isContaining
import com.mindbodyonline.connect.analytics.classes.trackClassesTabViewedEvent
import com.mindbodyonline.connect.analytics.getOriginViewFromDisplayMode
import com.mindbodyonline.connect.analytics.viewschedule.trackFilterDialogOpened
import com.mindbodyonline.connect.common.Resource
import com.mindbodyonline.connect.common.repository.LocationDataSource
import com.mindbodyonline.connect.common.repository.PerformanceName
import com.mindbodyonline.connect.quickbook.QuickBookDialog
import com.mindbodyonline.connect.quickbook.QuickBookViewModelV2.QuickbookFailureReason
import com.mindbodyonline.connect.tealium.InventoryType
import com.mindbodyonline.connect.tealium.PageName
import com.mindbodyonline.connect.tealium.TrackingHelperUtils
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.analytics.key
import com.mindbodyonline.connect.utils.api.gateway.model.LocationDetailsAttributes
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference
import com.mindbodyonline.connect.utils.getUserIdForDependentUser
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.data.services.locator.ServiceLocator.perfMeasureRepo
import com.mindbodyonline.domain.BookabilityStatus
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.Location
import com.mindbodyonline.domain.LocationWithDetails
import com.mindbodyonline.domain.dataModels.schedule.ClassAttributes
import com.mindbodyonline.domain.dataModels.schedule.ScheduleState
import com.mindbodyonline.domain.dataModels.schedule.ScheduleStateLegacy
import com.mindbodyonline.domain.datamapper.toCto
import com.mindbodyonline.domain.quickbook.CTOQuickBookResponse
import com.mindbodyonline.domain.usecase.FetchScheduledClassesAndEnrolments
import com.mindbodyonline.ui.common.views.TopBarMenuItem
import com.mindbodyonline.views.DisplayMode
import com.mindbodyonline.views.dialog.familyaccounts.FamilyAccount
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.zip
import kotlinx.coroutines.launch
import java.util.Calendar

class ScheduledClassListViewModel(
    private val fetchScheduledClassesAndEnrolmentsUseCase: FetchScheduledClassesAndEnrolments = FetchScheduledClassesAndEnrolments(),
    private val locationRepository: LocationDataSource = ServiceLocator.locationRepository
) : ViewModel() {

    private val _scheduleStateLegacy = MutableStateFlow(ScheduleStateLegacy())
    val scheduleStateLegacy = _scheduleStateLegacy.asStateFlow()

    private val _scheduleState = MutableStateFlow(ScheduleState())
    val scheduleState = _scheduleState.asStateFlow()

    private val _location = MutableStateFlow<Location?>(null)
    val location = _location.asStateFlow()

    private val _locationDetails = MutableStateFlow<LocationDetailsAttributes?>(null)

    val locationWithDetails: Flow<LocationWithDetails> =
        _location.filterNotNull()
            .zip(_locationDetails.filterNotNull()) { location, details ->
                LocationWithDetails(location, details)
            }

    var goToDate: MutableState<Calendar?> = mutableStateOf(null)
    private var startDate: Calendar = Calendar.getInstance()
    private var endDate: Calendar = Calendar.getInstance()

    private val _showDatePickerDialog = MutableStateFlow(false)
    val showDatePickerDialog = _showDatePickerDialog.asStateFlow()

    private val _showFilterDialog = MutableStateFlow(false)
    val showFilterDialog = _showFilterDialog.asStateFlow()

    val allFilters: Set<IFilter<ClassTypeObject>> =
        linkedSetOf(QualifiedClassFilter(), BookableOnlineClassFilter())
    val currentFilters: MutableSet<IFilter<ClassTypeObject>> = LinkedHashSet()
    private val _filterCount: MutableState<Int> = mutableIntStateOf(0)

    var originView: OriginView? = null
    var originComponent: OriginComponent? = null

    val menuItems = listOf(
        TopBarMenuItem(
            iconResId = R.drawable.ic_filter_icon,
            badgeCount = _filterCount,
            onClick = {
                trackFilterDialogOpened(OriginView.BUSINESS_SCHEDULE, null, location.value)
                _showFilterDialog.value = true
            }
        ),
        TopBarMenuItem(
            iconResId = R.drawable.ic_calendar_bottom_nav_outlined,
            onClick = {
                _showDatePickerDialog.value = true
            }
        )
    )

    fun initialize(locationReference: LocationReference?) {
        viewModelScope.launch {
            _location.value = locationReference?.let { locationRepository.getLocation(it, true) }
            _locationDetails.value =
                locationReference?.let { locationRepository.fetchLocationDetails(it) }
        }
    }

    fun showDatePickerDialog(show: Boolean) {
        _showDatePickerDialog.value = show
    }

    fun showFilterDialog(show: Boolean) {
        _showFilterDialog.value = show
    }

    fun updateFilterCount(count: Int) {
        _filterCount.value = count
    }

    fun fetchClassesLegacy(location: Location?, startDate: Calendar, endDate: Calendar) {
        if (location == null) {
            _scheduleState.value = ScheduleState(
                error = "Business does not exist"
            )
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            fetchScheduledClassesAndEnrolmentsUseCase.invokeLegacy(
                location = location,
                startDate = startDate,
                endDate = endDate
            ).collect { result ->
                _scheduleStateLegacy.value = when (result) {
                    is Resource.Success -> {
                        ScheduleStateLegacy(
                            classScheduleItems = result.data ?: listOf()
                        )
                    }

                    is Resource.Error -> {
                        ScheduleStateLegacy(
                            error = result.message
                        )
                    }

                    is Resource.Loading -> {
                        ScheduleStateLegacy(isLoading = true)
                    }
                }
            }

        }
    }

    private fun fetchClasses(
        location: Location?,
        startDate: Calendar,
        endDate: Calendar,
        filters: MutableSet<IFilter<ClassTypeObject>>,
    ) {
        if (location == null) {
            _scheduleState.value = ScheduleState(
                error = "Business does not exist"
            )
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            fetchScheduledClassesAndEnrolmentsUseCase(
                location = location,
                startDate = startDate,
                endDate = endDate,
                filters = filters,
            ).collect { result ->
                _scheduleState.value = when (result) {
                    is Resource.Success -> {
                        ScheduleState(
                            classScheduleItems = result.data ?: listOf()
                        )
                    }

                    is Resource.Error -> {
                        ScheduleState(
                            error = result.message
                        )
                    }

                    is Resource.Loading -> {
                        ScheduleState(isLoading = true)
                    }
                }
            }

        }
    }

    fun onDateRangeSelected(startDate: Calendar? = null, endDate: Calendar? = null) {
        startDate?.let { this.startDate = startDate }
        endDate?.let { this.endDate = endDate }
        trackClassesTabViewedEvent(
            getOriginViewFromDisplayMode(DisplayMode.LOCATIONSCHEDULE),
            originComponent,
            EventsPathIdentifier.BUSINESS_SCHEDULE_CLASSES.key,
            location.value,
            allFilters.extractTrackingNames()
                .map { FilterTracker.invoke(it, currentFilters.isContaining(it)) },
            this.startDate.time,
            this.endDate.time,
            scheduleState.value.classScheduleItems.size,
        )
        goToDate.value = this.startDate.clone() as Calendar
        viewModelScope.launch {
            fetchClasses(
                location = location.value,
                startDate = <EMAIL>,
                endDate = <EMAIL>,
                filters = currentFilters,
            )
        }
    }

    fun quickBookClass(
        classAttributes: ClassAttributes,
        selectedFamilyAccount: FamilyAccount? = null,
        showDialogCallback: (QuickBookDialog) -> Unit,
        onFailureCallback: (QuickbookFailureReason?) -> Unit,
        onSuccessCallback: (CTOQuickBookResponse?) -> Unit,
    ) {
        classAttributes.toCto()?.let { cto ->
            var staffName: String? = null
            if (classAttributes.staff != null) {
                staffName = classAttributes.staff.displayName
            }

            TrackingHelperUtils.trackCheckoutBook(
                classAttributes.location, classAttributes.course?.name,
                if (classAttributes.isEnrolment) InventoryType.ENROLLMENT else InventoryType.CLASS,
                PageName.CLASS_LIST, staffName, cto?.gatewayId
            )

            val originComponent = if (classAttributes.isEnrolment) OriginComponent.EVENT_CARD else OriginComponent.CLASS_TIME_CARD
            BookingEventTracker.trackCtoBookTap(getOriginViewFromDisplayMode(DisplayMode.LOCATIONSCHEDULE), originComponent, cto)

            val requiresPayment = classAttributes.status?.id == BookabilityStatus.PAYMENT_REQUIRED
                    || classAttributes.status?.id == BookabilityStatus.PAY_WAITLIST

            val quickbookDialog =
                QuickBookDialog.newInstance(
                    cto,
                    selectedFamilyAccount,
                    getOriginViewFromDisplayMode(DisplayMode.LOCATIONSCHEDULE),
                    OriginComponent.CLASS_TIME_CARD
                )
            perfMeasureRepo.startPerformanceMeasure(System.currentTimeMillis(), PerformanceName.BOOK_TO_COMPLETE_OR_ABANDON)
            perfMeasureRepo.startPerformanceMeasure(System.currentTimeMillis(), PerformanceName.BOOK_TO_QB_READY)
            quickbookDialog.setSuccessCallback(onSuccessCallback)
            quickbookDialog.setSelectedFamilyMemberCallback { result: Map<String?, Any?>? ->
                onFamilyAccountSelected(
                    classAttributes,
                    quickbookDialog,
                    result,
                    showDialogCallback,
                    onFailureCallback,
                    onSuccessCallback
                )
            }
            quickbookDialog.setFailureCallback { failureResult: QuickbookFailureReason? ->
                onFailureCallback(failureResult)
            }

            quickbookDialog.setCancelCallback({
                if (requiresPayment) {
                    val reason = CartAbandonReason()
                    reason.setAsAbandon("User dismissed quickbook")
                    classAttributes.location?.siteId?.let {
                        MBSalesApi.deleteCart(it, reason, null, null, getUserIdForDependentUser())
                    }
                }
            })

            showDialogCallback(quickbookDialog)
        }
    }

    private fun onFamilyAccountSelected(
        classAttributes: ClassAttributes,
        quickBook: QuickBookDialog,
        result: Map<String?, Any?>?,
        showDialogCallback: (QuickBookDialog) -> Unit,
        onFailureCallback: (QuickbookFailureReason?) -> Unit,
        onSuccessCallback: (CTOQuickBookResponse?) -> Unit,
    ) {
        result?.let {
            quickBook.dismiss()
            val familyAccount = result[QuickBookDialog.KEY_SELECTED_USER] as FamilyAccount?
            familyAccount?.let {
                QuickBookDialog.SELECTED_USER_TAG = QuickBookDialog.TAG + familyAccount.id
                SharedPreferencesUtils.setSelectedUserId(if ((familyAccount.isPrimaryUser)) "" else familyAccount.id)
                quickBookClass(classAttributes, familyAccount, showDialogCallback, onFailureCallback, onSuccessCallback)
            }
        }
    }

    fun setCurrentFilters(filters: Set<IFilter<ClassTypeObject>?>) {
        if (FilterUtils.logFilterChanges(currentFilters, filters)) {
            currentFilters.clear()
            currentFilters.addAll(filters.filterNotNull())
            onDateRangeSelected()
            updateFilterCount(currentFilters.size)
        }
    }
}
