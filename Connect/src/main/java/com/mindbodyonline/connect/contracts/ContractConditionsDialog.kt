package com.mindbodyonline.connect.contracts

import android.graphics.Typeface
import android.os.Bundle
import android.text.Html
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import com.mindbodyonline.android.api.sales.model.pos.packages.CatalogPackage
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.analytics.view_pricing.ViewPricingEventTracker
import com.mindbodyonline.connect.databinding.ViewContractTermsBinding
import com.mindbodyonline.connect.ftc.ContractScreenshotHelper
import com.mindbodyonline.connect.ftc.data.FTCScreenShotSource
import com.mindbodyonline.domain.Location
import com.mindbodyonline.framework.abvariant.ABHelperUtils
import com.mindbodyonline.framework.abvariant.FeatureFlag
import java.util.Locale

class ContractConditionsDialog : ContractDialogs() {
    private var _binding: ViewContractTermsBinding? = null
    private val binding get() = _binding!!
    private var location: Location? = null

    companion object {
        @JvmStatic
        @JvmOverloads
        fun newInstance() = ContractConditionsDialog()
    }

    override fun setContractData(catalogPackage: CatalogPackage?, location: Location?) {
        this.location = location
        super.setContractData(catalogPackage)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = ViewContractTermsBinding.inflate(inflater, container, false)
        if (FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled()) {
            binding.root.setBackgroundColor(requireContext().resources.getColor(R.color.white, null))
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding.toolbar.toolbar) {
            setTitle(getString(R.string.contracts_terms_title).uppercase(Locale.getDefault()))
            setNavigationIcon(ContextCompat.getDrawable(context, R.drawable.ic_close_icon_grey))
            setNavigationContentDescription(resources.getString(R.string.close_button_text))
            setNavigationOnClickListener {
                setFragmentResult(KEY_CONTRACT_USER_INTERACTION, bundleOf(KEY_BUNDLE_CONTRACT_USER_INTERACTION to ContractDialogUserInteraction.FLOW_INTERRUPTED))
                dismiss()
            }
        }
        setContractConditions()
        setClickListeners()
    }

    private fun setContractConditions() {
        contractPackage?.let {
            setTermsAndConditionsText(it.contractTemplate.contractTextHtml ?: "")
            val sb = SpannableStringBuilder()
                .append(getString(R.string.contracts_signature_banner_text))
                .append(" ")
                .append(SpannableString(it.name).apply {
                    setSpan(StyleSpan(Typeface.BOLD), 0, it.name.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
                })
            binding.termsBanner.setText(sb, TextView.BufferType.SPANNABLE)
        }
    }

    private fun setTermsAndConditionsText(contractTemplateHtml: String) {
        if (FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled()) {
            binding.termsTextFtc.apply {
                visibility = View.VISIBLE
                text = Html.fromHtml(contractTemplateHtml)
            }
            binding.agreeToTermsAndConditions.visibility = View.VISIBLE
        } else {
            binding.termsText.apply {
                visibility = View.VISIBLE
                text = Html.fromHtml(contractTemplateHtml)
            }
        }
    }

    override fun onDestroy() {
        _binding = null
        super.onDestroy()
    }

    private fun setClickListeners() {
        binding.termsAccept.setOnClickListener {
            if (ABHelperUtils.shouldLogFTCAuditForUSStudio(location?.countryCode)) {
                captureScreenshotForFTC()
            }
            ViewPricingEventTracker.trackContractTNCAgreeClickEvent(location, contractPackage)
            setFragmentResult(KEY_CONTRACT_USER_INTERACTION,
                    bundleOf(KEY_BUNDLE_CONTRACT_USER_INTERACTION to ContractDialogUserInteraction.TERMS_ACCEPTED))
            dismiss()
        }
    }

    // We need to capture and upload the full screenshot of this screen for FTC Audit log
    private fun captureScreenshotForFTC() {
        this.view?.let {
            ContractScreenshotHelper.captureDialogScreenshot(
                source = FTCScreenShotSource.CONTRACT_TERMS,
                contractId = contractPackage?.id.toString(),
                studioId = location?.siteId.toString(),
                view = it,
                location = OriginView.CONTRACT_TERMS_AND_CONDITIONS
            )
        }
    }
}
