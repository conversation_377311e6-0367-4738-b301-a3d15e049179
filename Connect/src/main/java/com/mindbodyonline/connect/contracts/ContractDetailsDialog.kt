package com.mindbodyonline.connect.contracts

import android.graphics.Typeface
import android.os.Bundle
import android.text.Html
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.ViewModelProvider
import com.mindbodyonline.android.api.sales.model.pos.packages.CatalogPackage
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.analytics.view_pricing.ViewPricingEventTracker
import com.mindbodyonline.connect.common.utilities.visible
import com.mindbodyonline.connect.contracts.ContractDialogUserInteraction.DATE_CHANGED
import com.mindbodyonline.connect.contracts.viewmodels.ContractDetailsViewModel
import com.mindbodyonline.connect.databinding.ViewContractDetailsBinding
import com.mindbodyonline.connect.databinding.ViewContractDetailsOldBinding
import com.mindbodyonline.connect.ftc.ContractScreenshotHelper
import com.mindbodyonline.connect.ftc.data.FTCScreenShotSource
import com.mindbodyonline.connect.utils.PaymentUtils
import com.mindbodyonline.connect.utils.runIfNotNull
import com.mindbodyonline.connect.utils.time.DateFormatUtils
import com.mindbodyonline.domain.Location
import com.mindbodyonline.framework.abvariant.ABHelperUtils
import com.mindbodyonline.framework.abvariant.FeatureFlag
import java.math.BigDecimal
import java.util.Calendar
import java.util.Locale

class ContractDetailsDialog : ContractDialogs() {
    private lateinit var detailsViewModel: ContractDetailsViewModel
    private var _binding: ViewContractDetailsBinding? = null
    private var _bindingOld: ViewContractDetailsOldBinding? = null
    private val binding get() = if (FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled()) _binding!! else _bindingOld!!
    private var location: Location? = null

    companion object {
        @JvmStatic
        fun newInstance() = ContractDetailsDialog()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        detailsViewModel = ViewModelProvider(this).get(ContractDetailsViewModel::class.java)
        childFragmentManager.setFragmentResultListener(KEY_CONTRACT_UPDATE_DATE_SHOWN, this) { _, bundle ->
            (bundle.get(KEY_BUNDLE_CONTRACT_USER_INTERACTION) as? ContractDialogUserInteraction)?.let {
                handleUserInteraction(it)
            }
        }
    }

    private fun handleUserInteraction(userInteraction: ContractDialogUserInteraction) {
        when (userInteraction) {
            is DATE_CHANGED -> {
                detailsViewModel.contractDateSelected = userInteraction.calendar
                val newContractPackage =
                    contractPackage
                        ?.deepCopy()
                        ?.apply { this.contractTemplate.startDate = userInteraction.calendar.time }
                newContractPackage?.let { safeContractPackage ->
                    if (FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled()) {
                        binding.contractDetailsStartDate.text =
                            getString(
                                R.string.contracts_details_start_date_value_ftc,
                                DateFormatUtils.format(
                                    userInteraction.calendar.time,
                                    "MMM dd, yyyy",
                                ),
                            )
                        setRenewalText(safeContractPackage)
                    } else {
                        binding.contractDetailsStartDate.text =
                            getString(
                                R.string.contracts_details_start_date_value,
                                DateFormatUtils.format(userInteraction.calendar.time, "MM/dd/yy"),
                            )
                        binding.contractsRenewsGroup.visible =
                            (safeContractPackage.contractTemplate?.isAutoRenewing)
                                .takeIf { true }
                                ?.also {
                                    binding.contractDetailsRenewsDate.text =
                                        safeContractPackage.displayRenewalDate(requireContext())
                                } == true
                    }
                    binding.contractDetailsDueDate.text =
                        safeContractPackage.fetchDueInformationString(requireContext())
                }
            }

            else -> {}
        }
    }

    private fun setRenewalText(safeContractPackage: CatalogPackage) {
        if (safeContractPackage.contractTemplate?.isAutoRenewing == true) {
            binding.contractDetailsRenewsDate.text = if (FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled()) {
                safeContractPackage.getFtcTermsConditionsAutoRenewalDetailsText(requireContext())
            } else {
                safeContractPackage.displayRenewalDate(requireContext())
            }
        } else {
            binding.contractDetailsRenewsDate.visible = false
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        if (FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled()) {
            _binding = ViewContractDetailsBinding.inflate(inflater, container, false)
        } else {
            _bindingOld = ViewContractDetailsOldBinding.inflate(inflater, container, false)
        }
        return binding.root
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        setContractDetails()
        setClickListeners()
    }

    override fun setContractData(
        catalogPackage: CatalogPackage?,
        location: Location?,
    ) {
        this.location = location
        super.setContractData(catalogPackage)
    }

    private fun setContractDetails() {
        runIfNotNull(contractPackage, location) { safeContractPackage, safeLocation ->
            with(binding) {
                contractTitle.text = safeContractPackage.name
                siteName.text = safeLocation.name
                descriptionText.text = Html.fromHtml(safeContractPackage.retrieveRawDescription())
                if (FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled()) {
                    setContractTermsAndConditionsText(
                        requireContext(),
                        safeContractPackage,
                        safeLocation,
                    )
                }
                val itemsString =
                    StringBuilder().apply {
                        safeContractPackage.items
                            .filterNotNull()
                            .forEachIndexed { index, catalogItem ->
                                if (index != 0) append("\n")
                                append(catalogItem.name)
                            }
                    }
                if (FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled() && safeContractPackage.contractTemplate?.isAutoRenewing == true) {
                    itemsString
                        .append("\n")
                        .append(getString(R.string.auto_renewal_recurring))
                }
                itemsText.text = itemsString.toString()

                contractDetailsChange.visibility =
                    if (safeContractPackage.retrieveStartDateType().datesAllowed != null) {
                        View.VISIBLE
                    } else {
                        View.INVISIBLE
                    }

                if (FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled()) {
                    contractDetailsStartDate.text =
                        getString(
                            R.string.contracts_details_start_date_value_ftc,
                            DateFormatUtils.format(
                                safeContractPackage.contractTemplate.startDate,
                                "MMM dd, yyyy",
                            ),
                        )
                } else {
                    contractDetailsStartDate.text =
                        getString(
                            R.string.contracts_details_start_date_value,
                            DateFormatUtils.format(
                                safeContractPackage.contractTemplate.startDate,
                                "MM/dd/yy",
                            ),
                        )
                }
                contractsRecurringPaymentGroup.visible =
                    (safeContractPackage.pricing?.autopayPrice?.compareTo(BigDecimal.ZERO) == 1)
                        .takeIf { true }
                        ?.also {
                            val qualifierString =
                                getString(R.string.contracts_details_recurring_amount_qualifier)
                            val sb =
                                SpannableStringBuilder()
                                    .append(safeContractPackage.getFormattedAmount(safeLocation))
                                    .append(" ")
                                    .append(
                                        SpannableString(qualifierString).apply {
                                            setSpan(
                                                StyleSpan(Typeface.ITALIC),
                                                0,
                                                qualifierString.length,
                                                Spannable.SPAN_INCLUSIVE_INCLUSIVE,
                                            )
                                        },
                                    )
                            contractDetailsPaymentAmount.setText(sb, TextView.BufferType.SPANNABLE)
                            contractDetailsDueDate.text =
                                safeContractPackage.fetchDueInformationString(requireContext())
                        } == true
                contractDetailsTotal.text =
                    PaymentUtils.getFormattedCurrency(
                        safeContractPackage.pricing.discountPrice
                            ?: BigDecimal.ZERO,
                        location?.locale ?: Locale.getDefault(),
                        false,
                    )
                if (FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled() && this is ViewContractDetailsBinding) {
                    this.setLocation(location, safeContractPackage)
                    setRenewalText(safeContractPackage)
                } else {
                    contractsRenewsGroup.visible =
                        (safeContractPackage.contractTemplate?.isAutoRenewing)
                            .takeIf { true }
                            ?.also {
                                contractDetailsRenewsDate.text =
                                    safeContractPackage.displayRenewalDate(requireContext())
                            } == true
                }
                groupContractDuration.visible =
                    safeContractPackage.displayDuration(requireContext())?.let {
                        contractDetailsDuration.text = it
                        true
                    } == true
            }
        }
    }

    private fun ViewContractDetailsBinding.setLocation(
        location: Location?,
        safeContractPackage: CatalogPackage,
    ) {
        contractDetailsDiscountGroup.visible =
            safeContractPackage.pricing.let {
                var discount = BigDecimal.ZERO
                runIfNotNull(it.basePrice, it.discountPrice) { base, discounted ->
                    discount = base - discounted
                }
                (discount > BigDecimal.ZERO).also {
                    it.takeIf { true }?.also {
                        contractDetailsDiscount.text =
                            PaymentUtils.getFormattedCurrency(
                                discount,
                                location?.locale ?: Locale.getDefault(),
                                false,
                            )
                    }
                }
            }
    }

    private fun setClickListeners() {
        binding.contractDetailsNext.setOnClickListener {
            if (ABHelperUtils.shouldLogFTCAuditForUSStudio(location?.countryCode)) {
                binding.contractDetailsProgress.visibility = View.VISIBLE
                captureScreenshotForFTC {
                    binding.contractDetailsProgress.visibility = View.GONE
                    proceedWithContractAcceptance()
                }
            } else {
                proceedWithContractAcceptance()
            }
        }
        binding.contractDetailsChange.setOnClickListener {
            ContractDateSelectionDialog().apply {
                contractPackage?.let {
                    this.setDates(detailsViewModel.retrieveProperStartDates(Calendar.getInstance(),
                            it.retrieveStartDateType()))
                }
            }.show(childFragmentManager, ContractDateSelectionDialog::class.java.simpleName)
        }
    }

    private fun proceedWithContractAcceptance() {
        ViewPricingEventTracker.trackContractDetailsNextClickEvent(location, contractPackage)
        setFragmentResult(
            KEY_CONTRACT_USER_INTERACTION,
            bundleOf(
                KEY_BUNDLE_CONTRACT_USER_INTERACTION to
                    ContractDialogUserInteraction.DETAILS_ACCEPTED(detailsViewModel.contractDateSelected),
            ),
        )
        dismiss()
    }

    // We need to capture and upload the full scrollable screenshot of this screen for FTC Audit log
    private fun captureScreenshotForFTC(onComplete: (() -> Unit)) {
        this.view?.let {
            ContractScreenshotHelper.captureDialogScreenshot(
                source = FTCScreenShotSource.CONTRACT_DETAILS,
                contractId = contractPackage?.id.toString(),
                studioId = location?.siteId.toString(),
                view = it,
                location = OriginView.CONTRACT_DETAILS,
                scrollView = binding.contractDetailsScrollView,
                bottomView = binding.contractDetailsNextContainer,
                onComplete,
            )
        }
    }

    override fun onDestroy() {
        _binding = null
        super.onDestroy()
    }
}
