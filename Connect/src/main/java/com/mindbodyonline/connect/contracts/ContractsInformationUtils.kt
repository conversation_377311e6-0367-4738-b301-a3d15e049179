package com.mindbodyonline.connect.contracts

import android.content.Context
import com.mindbodyonline.android.api.sales.model.pos.cart.Cart
import com.mindbodyonline.android.api.sales.model.pos.packages.CartPackage
import com.mindbodyonline.android.api.sales.model.pos.packages.CatalogPackage
import com.mindbodyonline.android.util.SafeGson
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.common.utilities.addEnglishOrdinal
import com.mindbodyonline.connect.common.utilities.currentlySetToLastDayOfMonth
import com.mindbodyonline.connect.common.utilities.toCalendar
import com.mindbodyonline.connect.utils.PaymentUtils
import com.mindbodyonline.connect.utils.runIfNotNull
import com.mindbodyonline.connect.utils.time.DateFormatUtils
import com.mindbodyonline.domain.Location
import java.math.BigDecimal
import java.util.*

fun CatalogPackage.fetchDueInformationString(context: Context): String? =
    when (BillingFrequencyType.fromTypeString(this.contractTemplate.billingFrequencyType)) {
        BillingFrequencyType.MONTH -> {
            contractTemplate?.billingFrequency?.let { billingFrequency ->
                if (billingFrequency == 1) {
                    contractTemplate?.startDate?.let { startDate ->
                        val lastDateChosen = startDate.toCalendar().currentlySetToLastDayOfMonth()
                        if (lastDateChosen) {
                            context.resources.getString(R.string.contracts_end_of_month)
                        } else {
                            context.resources.getString(R.string.contracts_of_every_month,
                                    Calendar.getInstance().apply {
                                        time = startDate
                                    }.get(Calendar.DAY_OF_MONTH).addEnglishOrdinal()
                            )
                        }
                    }
                } else {
                    context.resources.getString(R.string.contracts_every_x_months, billingFrequency.toString())
                }
            }
        }
        BillingFrequencyType.WEEK -> {
            contractTemplate?.billingFrequency?.let { billingFrequency ->
                if (billingFrequency == 1) {
                    context.resources.getString(R.string.contracts_every_week)
                } else {
                    context.resources.getString(R.string.contracts_every_x_weeks, billingFrequency.toString())
                }
            }
        }
        BillingFrequencyType.YEAR -> {
            contractTemplate?.billingFrequency?.let { billingFrequency ->
                if (billingFrequency == 1) {
                    context.resources.getString(R.string.contracts_every_year)
                } else {
                    context.resources.getString(R.string.contracts_every_x_years, billingFrequency.toString())
                }
            }
        }
        BillingFrequencyType.OPTION_RUNS_OUT -> context.getString(R.string.contracts_until_pricing_options_run_out)
        else -> null
    }

fun CatalogPackage.displayRenewalDate(context: Context): String? {
    var renewingDateOrText: String? = null
    runIfNotNull(contractTemplate.durationDays, contractTemplate.startDate) { durationInDays, startDate ->
        if (durationInDays > 0) {
            val renewingDate = Calendar.getInstance().apply { time = startDate }
            when (contractTemplate.isMonthToMonth) {
                true -> renewingDate.add(Calendar.MONTH, 1)
                else -> renewingDate.add(Calendar.DATE, durationInDays)
            }
            renewingDateOrText = DateFormatUtils.format(renewingDate.time, "MM/dd/yy")
        } else {
            val numberBillingCycles = contractTemplate?.totalNumberOfBillingCycles ?: 0
            if (numberBillingCycles > 1) {
                renewingDateOrText = context.resources.getQuantityString(R.plurals.contracts_num_renews_when_pricing_option_runs_out,
                        numberBillingCycles, numberBillingCycles)
            } else {
                renewingDateOrText = context.getString(R.string.contracts_renews_until_pricing_options_run_out_one_zero)
            }
        }
    }
    return renewingDateOrText
}

fun CatalogPackage.getFtcTermsConditionsAutoRenewalDetailsText(context: Context): String? {
    var renewingDateOrText: String? = null
    runIfNotNull(contractTemplate.durationDays, contractTemplate.startDate) { durationInDays, startDate ->
        if (durationInDays > 0) {
            val renewingDate = Calendar.getInstance().apply { time = startDate }
            when (contractTemplate.isMonthToMonth) {
                true -> renewingDate.add(Calendar.MONTH, 1)
                else -> renewingDate.add(Calendar.DATE, durationInDays)
            }
            renewingDateOrText =
                context.resources.getString(R.string.contracts_renews_on_date, DateFormatUtils.format(renewingDate.time, "MMM dd, yyyy"))
        } else {
            val numberBillingCycles = contractTemplate?.totalNumberOfBillingCycles ?: 0
            renewingDateOrText =
                if (numberBillingCycles > 1) {
                    context.resources.getQuantityString(
                        R.plurals.contracts_num_renews_when_pricing_option_runs_out_ftc,
                        numberBillingCycles,
                        numberBillingCycles,
                    )
                } else {
                    context.getString(R.string.contracts_renews_until_pricing_options_run_out_one_zero_ftc)
                }
        }
    }
    return renewingDateOrText
}

fun CatalogPackage.getTermsAndConditions(
    context: Context,
    location: Location,
): String? {
    if (contractTemplate?.isAutoRenewing != true) {
        return null
    }
    var termsAndConditionsText: CharSequence? = null
    runIfNotNull(
        contractTemplate.durationDays,
        contractTemplate.startDate,
    ) { durationInDays, _ ->
        val amount = getFormattedAmount(location = location)
        if (durationInDays > 0) {
            val isFirstOrLastPaymentFree =
                contractTemplate.metadata.any {
                    (it.key == "FirstPaymentFree" && it.value == "True") ||
                        (it.key == "LastPaymentFree" && it.value == "True")
                }
            val duration =
                displayTermsAndConditionsDuration(context) ?: ""
            termsAndConditionsText =
                context.getString(
                    R.string.auto_renewal_set_date_terms_and_conditions,
                    amount,
                    if (isFirstOrLastPaymentFree) " (" + context.getString(R.string.less_applicable_discounts) + ")" else "",
                    duration,
                )
        } else {
            val numberBillingCycles = contractTemplate?.totalNumberOfBillingCycles ?: 0
            val numPayments =
                context.resources.getQuantityString(
                    R.plurals.num_payments,
                    numberBillingCycles,
                    numberBillingCycles,
                )
            termsAndConditionsText =
                context.getString(
                    R.string.auto_renewal_every_payment_terms_and_conditions,
                    amount,
                    numPayments,
                )
        }
    }
    return termsAndConditionsText?.toString()
}

private fun CatalogPackage.displayTermsAndConditionsDuration(context: Context): String? {
    var durationString: String? = null
    runIfNotNull(
        contractTemplate.billingFrequency,
        contractTemplate.totalNumberOfBillingCycles,
        contractTemplate?.billingFrequencyType,
    ) { billingFrequency, numberBillingCycles, billingFrequencyType ->
        val totalNumBillingFreqTypes = numberBillingCycles * billingFrequency
        if (totalNumBillingFreqTypes == 0) return@runIfNotNull

        durationString =
            when (BillingFrequencyType.fromTypeString(billingFrequencyType)) {
                BillingFrequencyType.WEEK -> getWeekDurationString(context, totalNumBillingFreqTypes)
                BillingFrequencyType.MONTH -> getMonthDurationString(context, if (this.contractTemplate?.isMonthToMonth == true) 1 else totalNumBillingFreqTypes)
                BillingFrequencyType.YEAR -> getYearDurationString(context, totalNumBillingFreqTypes)
                BillingFrequencyType.OPTION_RUNS_OUT ->
                    getOptionRunsOutDurationString(
                        context,
                        numberBillingCycles,
                    )

                else -> null
            }
    }
    return durationString
}

private fun getWeekDurationString(
    context: Context,
    totalNumBillingFreqTypes: Int,
): String =
    if (totalNumBillingFreqTypes == 1) {
        context.getString(R.string.week)
    } else {
        context.resources.getQuantityString(
            R.plurals.num_weeks,
            totalNumBillingFreqTypes,
            totalNumBillingFreqTypes,
        )
    }

private fun getMonthDurationString(
    context: Context,
    totalNumBillingFreqTypes: Int,
): String =
    if (totalNumBillingFreqTypes == 1) {
        context.getString(R.string.month)
    } else {
        context.resources.getQuantityString(
            R.plurals.num_months,
            totalNumBillingFreqTypes,
            totalNumBillingFreqTypes,
        )
    }

private fun getYearDurationString(
    context: Context,
    totalNumBillingFreqTypes: Int,
): String =
    if (totalNumBillingFreqTypes == 1) {
        context.getString(R.string.year)
    } else {
        context.resources.getQuantityString(
            R.plurals.num_years,
            totalNumBillingFreqTypes,
            totalNumBillingFreqTypes,
        )
    }

private fun getOptionRunsOutDurationString(
    context: Context,
    numberBillingCycles: Int,
): String =
    if (numberBillingCycles == 1) {
        context.getString(R.string.year)
    } else {
        context.resources.getQuantityString(
            R.plurals.num_years,
            numberBillingCycles,
            numberBillingCycles,
        )
    }

fun CatalogPackage.displayDuration(context: Context): String? {
    var durationString: String? = null
    runIfNotNull(
        contractTemplate.billingFrequency,
        contractTemplate.totalNumberOfBillingCycles,
        contractTemplate?.billingFrequencyType,
    ) { billingFrequency, numberBillingCycles, billingFrequencyType ->
        val totalNumBillingFreqTypes = numberBillingCycles * billingFrequency
        if (totalNumBillingFreqTypes == 0) return@runIfNotNull
        when (BillingFrequencyType.fromTypeString(billingFrequencyType)) {
            BillingFrequencyType.WEEK -> {
                durationString =
                    context.resources.getQuantityString(
                        R.plurals.num_weeks,
                        totalNumBillingFreqTypes,
                        totalNumBillingFreqTypes,
                    )
            }

            BillingFrequencyType.MONTH -> {
                val months =
                    if (this.contractTemplate?.isMonthToMonth == true) 1 else totalNumBillingFreqTypes
                durationString =
                    context.resources.getQuantityString(R.plurals.num_months, months, months)
            }

            BillingFrequencyType.YEAR -> {
                durationString =
                    context.resources.getQuantityString(
                        R.plurals.num_years,
                        totalNumBillingFreqTypes,
                        totalNumBillingFreqTypes,
                    )
            }

            BillingFrequencyType.OPTION_RUNS_OUT -> {
                durationString =
                    context.resources.getQuantityString(
                        R.plurals.num_years,
                        numberBillingCycles,
                        numberBillingCycles,
                    )
            }

            else -> {}
        }
    }
    return durationString
}

fun CatalogPackage.hasRecurringPayment(): Boolean = (this.pricing?.autopayPrice?.compareTo(BigDecimal.ZERO) == 1)

/**
 * Extension function to determine if the contract is an intro offer
 */
fun CatalogPackage.isIntroOffer(): Boolean = this.templates
    ?.find { it.templateType == TEMPLATE_TYPE_CONTRACT }
    ?.metadata?.find { it.key == METADATA_INTRO_OFFER_TYPE }?.let {
        it.value != NO_INTRO_OFFER_VALUE
    } ?: false

fun CatalogPackage.isIntroOfferNewConsumer(): Boolean = this.templates
    ?.find { it.templateType == TEMPLATE_TYPE_CONTRACT }
    ?.metadata?.find { it.key == METADATA_INTRO_OFFER_TYPE }?.let {
        it.value == NEW_CLIENT_INTRO_OFFER_VALUE
    } ?: false

fun Cart.retrieveContractCartPackage(): CartPackage? = this.packages?.filter {
    it.catalogPackage?.contractTemplate != null
}?.first()

fun CatalogPackage.retrieveRawDescription(): String? = this.contractTemplate?.metadata?.filter {
    it.key == "Description"
}?.first()?.value

fun CatalogPackage.retrieveStartDateType(): ContractStartType = this.templates
    ?.find { it.templateType == TEMPLATE_TYPE_CONTRACT }
    ?.metadata?.find { it.key == METADATA_CONTRACT_START_TYPE }?.let {
        ContractStartType.fromTypeString(it.value)
    } ?: ContractStartType.UNKNOWN

/**
 * Makes and returns a deep copy of the catalog package
 */
fun CatalogPackage.deepCopy(): CatalogPackage = SafeGson.fromJson(SafeGson.toJson(this), CatalogPackage::class.java)

fun CatalogPackage.getFormattedAmount(location: Location) = PaymentUtils
    .getFormattedCurrency(pricing.autopayPrice
        ?: BigDecimal.ZERO, location.locale, false)
