package com.mindbodyonline.connect.contracts

import android.content.Context
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.StyleSpan
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.core.widget.NestedScrollView
import com.mindbodyonline.android.api.sales.model.pos.packages.CatalogPackage
import com.mindbodyonline.android.views.SimpleExpandableTextView
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.databinding.ViewContractDetailsBinding
import com.mindbodyonline.connect.databinding.ViewContractDetailsOldBinding
import com.mindbodyonline.domain.Location

// Common binding accessors for both contract details binding types
typealias ContractDetailsBinding = Any

// Title components
val ContractDetailsBinding.contractTitle: TextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractTitle
            is ViewContractDetailsOldBinding -> this.contractTitle
            else -> throw IllegalArgumentException("Wrong Binding")
        }

val ContractDetailsBinding.siteName: TextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.siteName
            is ViewContractDetailsOldBinding -> this.siteName
            else -> throw IllegalArgumentException("Wrong Binding")
        }

// Start date components
val ContractDetailsBinding.contractDetailsStartDate: TextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsStartDate
            is ViewContractDetailsOldBinding -> this.contractDetailsStartDate
            else -> throw IllegalArgumentException("Wrong Binding")
        }

val ContractDetailsBinding.contractDetailsChange: TextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsChange
            is ViewContractDetailsOldBinding -> this.contractDetailsChange
            else -> throw IllegalArgumentException("Wrong Binding")
        }

val ContractDetailsBinding.contractDetailsProgress: ProgressBar
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsProgress
            is ViewContractDetailsOldBinding -> this.contractDetailsProgress
            else -> throw IllegalArgumentException("Wrong Binding")
        }

val ContractDetailsBinding.contractDetailsScrollView: NestedScrollView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsScrollView
            is ViewContractDetailsOldBinding -> this.contractDetailsScrollView
            else -> throw IllegalArgumentException("Wrong Binding")
        }

val ContractDetailsBinding.contractDetailsNextContainer: ViewGroup
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsNextContainer
            is ViewContractDetailsOldBinding -> this.contractDetailsNextContainer
            else -> throw IllegalArgumentException("Wrong Binding")
        }

// Description components
val ContractDetailsBinding.descriptionText: SimpleExpandableTextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.descriptionText
            is ViewContractDetailsOldBinding -> this.descriptionText
            else -> throw IllegalArgumentException("Wrong Binding")
        }

// Items components
val ContractDetailsBinding.itemsText: TextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.itemsText
            is ViewContractDetailsOldBinding -> this.itemsText
            else -> throw IllegalArgumentException("Wrong Binding")
        }

// Recurring payment components
val ContractDetailsBinding.contractsRecurringPaymentGroup: Group
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractsRecurringPaymentGroup
            is ViewContractDetailsOldBinding -> this.contractsRecurringPaymentGroup
            else -> throw IllegalArgumentException("Wrong Binding")
        }

val ContractDetailsBinding.contractDetailsPaymentAmount: TextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsPaymentAmount
            is ViewContractDetailsOldBinding -> this.contractDetailsPaymentAmount
            else -> throw IllegalArgumentException("Wrong Binding")
        }

val ContractDetailsBinding.contractDetailsDueDate: TextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsDueDate
            is ViewContractDetailsOldBinding -> this.contractDetailsDueDate
            else -> throw IllegalArgumentException("Wrong Binding")
        }

// Duration components
val ContractDetailsBinding.groupContractDuration: Group
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.groupContractDuration
            is ViewContractDetailsOldBinding -> this.groupContractDuration
            else -> throw IllegalArgumentException("Wrong Binding")
        }

val ContractDetailsBinding.contractDetailsDuration: TextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsDuration
            is ViewContractDetailsOldBinding -> this.contractDetailsDuration
            else -> throw IllegalArgumentException("Wrong Binding")
        }

// Total price components
val ContractDetailsBinding.contractDetailsTotal: TextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsTotal
            is ViewContractDetailsOldBinding -> this.contractDetailsTotal
            else -> throw IllegalArgumentException("Wrong Binding")
        }

// Renews components for old binding
val ContractDetailsBinding.contractsRenewsGroup: Group
    get() =
        when (this) {
            is ViewContractDetailsBinding -> throw IllegalArgumentException("Not available in new binding")
            is ViewContractDetailsOldBinding -> this.contractsRenewsGroup
            else -> throw IllegalArgumentException("Wrong Binding")
        }

val ContractDetailsBinding.contractDetailsRenewsDate: TextView
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsRenewsDate
            is ViewContractDetailsOldBinding -> this.contractDetailsRenewsDate
            else -> throw IllegalArgumentException("Wrong Binding")
        }

// Button component
val ContractDetailsBinding.contractDetailsNext: View
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.contractDetailsNext
            is ViewContractDetailsOldBinding -> this.contractDetailsNext
            else -> throw IllegalArgumentException("Wrong Binding")
        }

// Root view accessor
val ContractDetailsBinding.root: View
    get() =
        when (this) {
            is ViewContractDetailsBinding -> this.root
            is ViewContractDetailsOldBinding -> this.root
            else -> throw IllegalArgumentException("Wrong Binding")
        }

// only available in new binding
fun ContractDetailsBinding.setContractTermsAndConditionsText(
    context: Context,
    safeContractPackage: CatalogPackage,
    safeLocation: Location,
) {
    if (this is ViewContractDetailsBinding) {
        val tcText =
            safeContractPackage.getTermsAndConditions(context, safeLocation) ?: return
        val tcSpannedText =
            SpannableStringBuilder()
                .append(context.getString(R.string.by_clicking_next_you_agree_to_pay))
                .append(" ")
                .append(
                    SpannableString(tcText).apply {
                        setSpan(
                            StyleSpan(Typeface.BOLD),
                            0,
                            tcText.length,
                            Spannable.SPAN_INCLUSIVE_INCLUSIVE,
                        )
                    },
                )
        contractTermsAndConditions.setText(tcSpannedText, TextView.BufferType.SPANNABLE)
    }
}
