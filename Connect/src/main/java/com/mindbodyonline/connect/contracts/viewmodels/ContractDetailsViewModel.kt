package com.mindbodyonline.connect.contracts.viewmodels

import androidx.lifecycle.ViewModel
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.common.utilities.advanceToBeginningNextMonth
import com.mindbodyonline.connect.common.utilities.cloneCast
import com.mindbodyonline.connect.common.utilities.setDayOfMonth
import com.mindbodyonline.connect.contracts.ContractStartType
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbAction
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbLogger
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbStatus
import java.util.Calendar

class ContractDetailsViewModel: ViewModel() {

    companion object {
        const val MAX_START_DAY_SELECTION = 90
    }

    var contractDateSelected : Calendar? = null

    /**
     * Returns a list of possible start dates for a contract based on the contract start type going
     * out [MAX_START_DAY_SELECTION] days from the calendar object passed in.
     * @param calendar The calendar object to use as a starting point for the possible start dates.
     * @param startDateType The contract start type from the enum [ContractStartType].
     */
    fun retrieveProperStartDates(calendar: Calendar, startDateType: ContractStartType): List<Calendar>{
        if (startDateType.datesAllowed.isNullOrEmpty()) return emptyList()
        val possibleStartSelectionDates = mutableListOf<Calendar>()
        val maxCalendar = calendar.cloneCast().apply {
            add(Calendar.DAY_OF_YEAR, MAX_START_DAY_SELECTION)
        }
        val workingCalendar = calendar.cloneCast()
        while (!workingCalendar.after(maxCalendar)){
            startDateType.datesAllowed.forEach { dateAllowed ->
                workingCalendar.setDayOfMonth(dateAllowed)
                if (!workingCalendar.after(maxCalendar) && !workingCalendar.before(calendar)){
                    possibleStartSelectionDates.add(workingCalendar.cloneCast())
                }
            }
            workingCalendar.advanceToBeginningNextMonth()
        }
        return possibleStartSelectionDates
    }

}
