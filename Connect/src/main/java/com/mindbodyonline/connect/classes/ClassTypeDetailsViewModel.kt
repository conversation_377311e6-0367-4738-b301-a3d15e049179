package com.mindbodyonline.connect.classes

import androidx.lifecycle.*
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.analytics.BOOK_BUTTON
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.analytics.classorenrollmentdetails.ClassOrEnrollmentDetailsEventTracker
import com.mindbodyonline.connect.common.components.RatingCardViewModel
import com.mindbodyonline.connect.common.utilities.cloneCast
import com.mindbodyonline.connect.common.utilities.removeBookingFromCache
import com.mindbodyonline.connect.common.utilities.toJson
import com.mindbodyonline.connect.tealium.LoginTrackingUtils
import com.mindbodyonline.connect.utils.*
import com.mindbodyonline.connect.utils.api.APIWorkflowUtil
import com.mindbodyonline.connect.utils.api.APIWorkflowUtil.RequiredFieldsStatus
import com.mindbodyonline.connect.utils.api.ReviewRefType
import com.mindbodyonline.connect.utils.api.connv1.adjustedEndCal
import com.mindbodyonline.connect.utils.api.connv1.adjustedStartCal
import com.mindbodyonline.connect.utils.api.connv1.isMBLivestream
import com.mindbodyonline.connect.utils.api.dynamicpricing.DynamicPricingApi
import com.mindbodyonline.connect.utils.api.dynamicpricing.DynamicPricingToken
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI
import com.mindbodyonline.connect.utils.api.gateway.model.BookingRefJsonModel
import com.mindbodyonline.connect.utils.api.gateway.model.InventoryRefJsonModel
import com.mindbodyonline.connect.utils.api.gateway.model.QuestionAttributes
import com.mindbodyonline.connect.utils.api.gateway.model.ReviewSummaryData
import com.mindbodyonline.connect.utils.api.gateway.serialization.GatewayResourceType
import com.mindbodyonline.connect.utils.api.gateway.serialization.getAttributes
import com.mindbodyonline.connect.utils.api.gateway.serialization.getRelationshipAttributes
import com.mindbodyonline.connect.utils.api.toDomainModel
import com.mindbodyonline.connect.utils.api.toLocationReference
import com.mindbodyonline.connect.widgets.v3.refreshWidget
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.data.services.http.MbDataService
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.domain.*
import com.mindbodyonline.domain.apiModels.VisitCancelModel
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.*
import java.util.concurrent.TimeUnit

class ClassTypeDetailsViewModel : ViewModel() {
    private val TAG = "ClassTypeDetailsViewMod"
    private val locationRepository = ServiceLocator.locationRepository

    private val initializeFromClass = MutableLiveData<InitializeFromClassData?>()
    private val initializeFromLocation = MutableLiveData<InitializeFromLocationData?>()
    private val location = MutableLiveData<Location?>()
    private val requiredFields = MutableLiveData<RequiredFieldsStatus?>()
    private val pricingToken = MutableLiveData<DynamicPricingToken?>()
    private val visitCancelStatus = MutableLiveData<VisitCancelModel?>()
    private val classInformation = MutableLiveData<ClassTypeObject?>()
    private val waitlistCanceled = MutableLiveData<Boolean?>()
    private val rosterCanceled = MutableLiveData<Boolean?>()
    private val paymentStatus = MutableLiveData<ClassPaymentStatus?>()
    private val studioReviews = MutableLiveData<Array<Rating>?>()
    private val liveStreamUrl = MutableLiveData<String?>()
    private val reviewSummaryData = MutableLiveData<ReviewSummaryData?>()
    private val reviewsListData = MutableLiveData<List<RatingCardViewModel>>()
	private var shownEventAlreadySent = false
    private var classTypeObject: ClassTypeObject? = null

    @kotlin.jvm.JvmField
    var isNavigatedFromProfile: Boolean = false

    @kotlin.jvm.JvmField
    var selectedUserId: String = ""

    @kotlin.jvm.JvmField
    var originView: String = ""

    @kotlin.jvm.JvmField
    var originComponent: String = ""

    val userName by lazy {
        MBAuth.getUser()?.firstName.orEmpty()
    }

    val liveStreamViewstate: LiveData<LiveStreamViewState> = classInformation.map { cto ->
        cto?.takeIf { shouldShowLiveStream(it) }?.let {
            if (!it.isInLivestreamWindow()) {
                startTimerForLivestreamSwitch(it)
            }
            LiveStreamViewState(enabled = it.isInLivestreamWindow(), showLiveStreamState = it.isBooked)
        } ?: LiveStreamViewState(showLiveStreamState = false)
    }

    fun getInitializeFromClass(): LiveData<InitializeFromClassData?> = initializeFromClass
    fun getInitializeFromLocation(): LiveData<InitializeFromLocationData?> = initializeFromLocation
    fun getLocation(): LiveData<Location?> = location
    fun getRequiredFields(): LiveData<RequiredFieldsStatus?> = requiredFields
    fun getPricingToken(): LiveData<DynamicPricingToken?> = pricingToken
    fun getVisitCancelStatus(): LiveData<VisitCancelModel?> = visitCancelStatus
    fun getClassInformation(): LiveData<ClassTypeObject?> = classInformation
    fun getWaitlistCanceled(): LiveData<Boolean?> = waitlistCanceled
    fun getRosterCanceled(): LiveData<Boolean?> = rosterCanceled
    fun getPaymentStatus(): LiveData<ClassPaymentStatus?> = paymentStatus
    fun getStudioReviews(): LiveData<Array<Rating>?> = studioReviews
    fun getLivestreamUrl(): LiveData<String?> = liveStreamUrl
    fun getReviewSummaryData(): LiveData<ReviewSummaryData?> = reviewSummaryData
    fun getReviewsList(): MutableLiveData<List<RatingCardViewModel>> = reviewsListData


    fun runInitializeFromClassRefJson(classRefJson: String, inventorySource: String) {
        viewModelScope.launch {
            val fullClass = ServiceLocator.classesRepository.getClassDetails(
                    classRefJson, inventorySource, false)

            if (fullClass == null) {
                initializeFromClass.postValue(null)
            } else {
                if (fullClass.hasVisits() && SharedPreferencesUtils.isCalendarSyncEnabled()) {
                    CalendarUtils.addUpdateClassVisit(ConnectApp.getInstance(),
                            DomainObjectUtils.convertToClassTypeVisit(fullClass), fullClass.location)
                }

                if (isActive) {
                    val location = ServiceLocator.locationRepository.getLocation(fullClass.location.toLocationReference())
                    location?.also {
                        APIWorkflowUtil.getRequiredFieldsStatus(location.siteId) { requiredFieldsStatus ->
                            initializeFromClass.postValue(InitializeFromClassData(
                                    fullClass,
                                    location,
                                    requiredFieldsStatus
                            ))
                            classInformation.postValue(fullClass)
                        }
                    } ?: initializeFromClass.postValue(null)
                }
            }
        }
    }

    fun runInitializeFromClass(siteId: Int, classInstanceId: Long) {
        viewModelScope.launch {
            val fullClass = ServiceLocator.classesRepository.getClassDetails(
                    siteId, classInstanceId, false, viewModelScope)

            if (fullClass == null) {
                initializeFromClass.postValue(null)
            } else if (isActive) {
                if (fullClass.hasVisits() && SharedPreferencesUtils.isCalendarSyncEnabled()) {
                    CalendarUtils.addUpdateClassVisit(ConnectApp.getInstance(),
                            DomainObjectUtils.convertToClassTypeVisit(fullClass), fullClass.location)
                }

                val location = ServiceLocator.locationRepository.getLocation(fullClass.location.toLocationReference())
                location?.also {
                    APIWorkflowUtil.getRequiredFieldsStatus(location.siteId) { requiredFieldsStatus ->
                        initializeFromClass.postValue(InitializeFromClassData(
                                fullClass,
                                location,
                                requiredFieldsStatus
                        ))
                        fullClass.location.gatewayId = location.gatewayId
                        classInformation.postValue(fullClass)
                    }

                } ?: initializeFromClass.postValue(null)
            }
        }
    }

    /**
     * @param classInstanceId a transient dependency
     */
    fun runInitializeFromLocation(masterLocationId: Int, classInstanceId: Long) {
        locationRepository.getLocation(masterLocationId, { location ->
            APIWorkflowUtil.getRequiredFieldsStatus(location.siteId) { requiredFieldsStatus ->
                initializeFromLocation.postValue(InitializeFromLocationData(
                        location,
                        requiredFieldsStatus,
                        classInstanceId
                ))
            }
        }, {
            initializeFromLocation.postValue(null)
        })
    }

    fun fetchLocation(masterLocationId: Int) {
        locationRepository.getLocation(masterLocationId, location::postValue, {
            location.postValue(null)
        })
    }

    fun updateRequiredFields(siteId: Int) {
        APIWorkflowUtil.getRequiredFieldsStatus(siteId) {
            requiredFields.postValue(it)
        }
    }

    fun updateUser() {
        ServiceLocator.userRepository.getUser({ user ->
            user.isVerified.let {
                if (it) LoginTrackingUtils.logUserVerified()
            }
        }, { }, forceRefresh = true)
    }

    fun fetchPricingToken(siteId: Int, classTypeId: Int) {
        DynamicPricingApi.getPricingToken(siteId, classTypeId, {
            pricingToken.postValue(it)
        }, {
            pricingToken.postValue(null)
        })
    }

    fun fetchVisitCancelStatus(visit: Visit, location: Location) {
        SwamisAPI.INSTANCE.getScheduleCancellable(
                SwamisAPI.MB_INVENTORY_SOURCE,
                BookingRefJsonModel(visit.siteVisitId, location.siteId),
                successListener = { response ->
                    visitCancelStatus.postValue(response.data?.attributes?.toDomainModel())
                },
                errorListener = {
                    visitCancelStatus.postValue(null)
                }
        )
    }

    fun refreshClassInformation(classInstanceId: Long, location: Location) {
        viewModelScope.launch {
            val fullClass = ServiceLocator.classesRepository.getClassDetails(
                    location.siteId, classInstanceId, false, viewModelScope)
            if (fullClass != null) {
                if (fullClass.hasVisits() && SharedPreferencesUtils.isCalendarSyncEnabled()) {
                    CalendarUtils.addUpdateClassVisit(ConnectApp.getInstance(),
                            DomainObjectUtils.convertToClassTypeVisit(fullClass), location)
                }
            }
            classInformation.postValue(fullClass)
        }
    }

    fun refreshEventInformation(siteId: Int, siteLocationId: Int, eventInstanceId: Long) {
        MbDataService.getServiceInstance().loadEnrollmentService()
            .getSingleDayEnrollments(siteId, siteLocationId, null, eventInstanceId,
                    null, null, null, { events ->
                val classTypeObject = events.firstOrNull()
                if (classTypeObject != null) {
                    if (classTypeObject.hasVisits() && SharedPreferencesUtils.isCalendarSyncEnabled()) {
                        CalendarUtils.addUpdateClassVisit(ConnectApp.getInstance(),
                                DomainObjectUtils.convertToClassTypeVisit(classTypeObject), classTypeObject.location)
                    }
                }
                classInformation.postValue(classTypeObject)
            }, {
                classInformation.postValue(null)
            })
    }

    fun cancelFromWaitlist(waitlistId: Int, location: Location) {
        MbDataService.getServiceInstance().loadClassService()
            .removeClientFromWaitlist(waitlistId, location.siteId, {
                SharedPreferencesUtils.incrementOrDecrementTotalBookings(false)
                waitlistCanceled.postValue(true)
            }, {
                waitlistCanceled.postValue(false)
            })
    }

    fun cancelFromRoster(siteVisitId: Long, location: Location, visit: ClassTypeVisit) {
        SwamisAPI.INSTANCE.deleteBooking(
                bookingRefJson = BookingRefJsonModel(siteVisitId, location.siteId, SwamisAPI.MB_INVENTORY_SOURCE, visit.ProgramType).toJson(),
                successListener = {
                    visit.removeBookingFromCache()
                    refreshWidget(ConnectApp.getInstance())
                    rosterCanceled.postValue(true)
                    viewModelScope.coroutineContext.cancelChildren()
                },
                errorListener = {
                    rosterCanceled.postValue(false)
                }
        )
    }

    fun fetchClassPaymentStatus(classTypeObject: ClassTypeObject) {
        MbDataService.getServiceInstance().loadClassService()
            .getPaymentStatus(classTypeObject.id, classTypeObject.location.siteId, {
                paymentStatus.postValue(it)
            }, {
                paymentStatus.postValue(null)
            })
    }

    fun fetchEnhanceReviewSummary(classDescriptionId: Int, location: Location) {
        SwamisAPI.INSTANCE.getEnhanceReviewSummaryByCourseResponse(
                inventoryReference = InventoryRefJsonModel(
                        mb_class_description_id = classDescriptionId,
                        mb_site_id = location.siteId,
                        mb_location_id = location.siteLocationId,
                        mb_master_location_id = location.id,
                        inventory_source = SwamisAPI.MB_INVENTORY_SOURCE
                ).toJson(),
                successListener = { response ->
                    val responseData = response.data?.attributes?.toDomainModel(
                            response,
                            response.data.getRelationshipAttributes(
                                    response,
                                    GatewayResourceType.TYPE_RESPONSE_SUMMARIES
                            )
                    )
                    reviewSummaryData.postValue(responseData)
                },
                errorListener = {
                    reviewSummaryData.postValue(null)
                }
        )
    }

    fun fetchEnhanceReviewsByCourse(
            size: Int,
            number: Int,
            classDescriptionId: Int,
            location: Location
    ) {
        SwamisAPI.INSTANCE.getEnhanceReviewsResponse(
                size = size,
                number = number,
                reviewRefType = ReviewRefType.REVIEW_BY_COURSE,
                referenceJson = InventoryRefJsonModel(
                        mb_class_description_id = classDescriptionId,
                        mb_site_id = location.siteId,
                        mb_location_id = location.siteLocationId,
                        mb_master_location_id = location.id,
                        inventory_source = SwamisAPI.MB_INVENTORY_SOURCE
                ).toJson(),
                successListener = { response ->
                    val ratingCardViewModelList = mutableListOf<RatingCardViewModel>()
                    response.data?.takeIf { it.isNotEmpty() }?.forEach { data ->
                        val reviewsResponseMap = data.getAttributes<QuestionAttributes>(response.included,
                                GatewayResourceType.TYPE_RESPONSE)
                        val responseMap = reviewsResponseMap.toList().sortedBy { (_, value) -> value.position }.toMap()
                        data.attributes?.toDomainModel(
                                data.id,
                                location.id,
                                responseMap
                        )?.let {
                            ratingCardViewModelList.add(it)
                        }
                    }

                    reviewsListData.postValue(ratingCardViewModelList)
                }
        ) {
            reviewsListData.postValue(emptyList())
        }
    }

    fun reportAbuse(reviewId: Long) {
        //Report abuse API Success/Error handling no required MVP.
        SwamisAPI.INSTANCE.reviewReportAbuse(reviewId,
                successListener = {
                    MBLog.d(TAG, "Report abuse success!!")
                }, errorListener = {
            MBLog.d(TAG, "Report abuse error!!")
        })
    }

	fun sendClassDetailsEvent(event: ClassDetailsEvents) {
		val location = initializeFromClass.value?.location ?: initializeFromLocation.value?.location ?: location.value
		//There are just way too many initialization paths for location in this class (3). Hack to make
		//sure we only send the event once.
		if (event is ClassDetailsEvents.ClassDetailsShown) {
			if (shownEventAlreadySent) {
				return
			} else {
				shownEventAlreadySent = true
			}
		}
		val dataMap: MutableMap<String, Any> = mutableMapOf<String, Any>().apply {
			location?.siteId?.let { put(AnalyticsUtils.META_STUDIO_ID, it) }
			location?.id?.let { put(AnalyticsUtils.META_LOCATION_ID, it) }
			location?.inventorySource?.let { put(AnalyticsUtils.META_INVENTORY_SOURCE, it) }
            classInformation.value?.id?.let { put(AnalyticsUtils.META_CLASS_ID, it) }
            classInformation.value?.name?.let { put(AnalyticsUtils.META_CLASS_NAME, it) }
		}
		event.selectedIndex?.let { dataMap.put(AnalyticsUtils.META_SELECTION_INDEX, it + 1) }
        classTypeObject = classInformation.value
        when (event) {
            is ClassDetailsEvents.ClassDetailsShown -> {
                // This check is added as class details is called from many places,
                // for which we have not added the originView and originComponent values.
                if (originView.isNullOrBlank().not() && originComponent.isNullOrBlank().not() && classTypeObject != null) {
                    ClassOrEnrollmentDetailsEventTracker.trackClassOrEnrollmentDetailsViewedEvent(
                            originView = originView,
                            originComponent = originComponent,
                            classTypeObject = classTypeObject!!,
                    )
                }
            }
            is ClassDetailsEvents.NextSelected -> {
                if (classTypeObject != null) {
                    ClassOrEnrollmentDetailsEventTracker.trackClassOrEnrollmentDetailsInteractedEvent(
                            originView = if (classTypeObject is Enrollment) OriginView.EVENT_DETAILS
                            else OriginView.CLASS_DETAILS,
                            key = BOOK_BUTTON,
                            classTypeObject = classTypeObject!!
                    )
                }
            }
            is ClassDetailsEvents.AddressSelected,
            ClassDetailsEvents.BusinessDetailsSelected,
            ClassDetailsEvents.ClassDetailsShareSelected,
            ClassDetailsEvents.ClassDetailsStaffBioSelected,
            ClassDetailsEvents.MapSelected,
            ClassDetailsEvents.ReadMoreSelected,
            ClassDetailsEvents.ReviewsSeeAllSelected -> {
                AnalyticsUtils.logEvent(event.eventName, dataMap)
            }

            is ClassDetailsEvents.ReviewsReadMoreSelected -> {
                AnalyticsUtils.logEvent(event.eventName, dataMap)
            }
        }
	}

    private fun startTimerForLivestreamSwitch(cto: ClassTypeObject) {
        viewModelScope.launch {
            val now = Calendar.getInstance()
            delay(cto.adjustedStartCal().timeInMillis - now.timeInMillis -
                    TimeUnit.MINUTES.toMillis(Constants.LIVESTREAM_BUTTON_ENABLED_WINDOW_MINUTES.toLong()))
            if (isActive) {
                classInformation.postValue(classInformation.value)
            }
        }
    }

    private fun ClassTypeObject.isInLivestreamWindow(): Boolean {
        val now = Calendar.getInstance()
        return adjustedEndCal().after(now) && now.after(adjustedStartCal().cloneCast().apply {
            this[Calendar.MINUTE] -= Constants.LIVESTREAM_BUTTON_ENABLED_WINDOW_MINUTES
        })
    }

    private fun shouldShowLiveStream(cto: ClassTypeObject) =
        cto.isBooked && cto.isMBLivestream() && !cto.hasClassPassed() && (!isFamilyAccountType() || isFamilyAccountType() && (isNavigatedFromProfile || cto.isSignedIn || cto.isFlexOptionEnabled))

    fun joinLiveStreamSelected(classTypeObject: ClassTypeObject) {
        val streamUrl = classTypeObject.takeIf { it.isInLivestreamWindow() }
            ?.visits?.firstOrNull()?.livestreamMeetingLink?.takeIf { it.isNotBlank() }
        liveStreamUrl.postValue(streamUrl)
    }

    class InitializeFromClassData(val classTypeObject: ClassTypeObject,
                                  val location: Location?,
                                  val requiredFieldsStatus: APIWorkflowUtil.RequiredFieldsStatus?)

    class InitializeFromLocationData(val location: Location,
                                     val requiredFieldsStatus: RequiredFieldsStatus?,
                                     val classInstanceId: Long)

    class LiveStreamViewState(val enabled: Boolean = false, val showLiveStreamState: Boolean = true)

	sealed class ClassDetailsEvents(val eventName: String, var selectedIndex: Int? = null) {
		object ClassDetailsShown : ClassDetailsEvents("class_details_screen_view")
		object ClassDetailsShareSelected : ClassDetailsEvents("class_details_screen_share_tap")
		object ClassDetailsStaffBioSelected : ClassDetailsEvents("class_details_screen_staff_bio_tap")

		object ReadMoreSelected : ClassDetailsEvents("class_details_screen_read_more_tap")
		object MapSelected : ClassDetailsEvents("class_details_screen_map_tap")
		object AddressSelected : ClassDetailsEvents("class_details_screen_address_tap")
		object BusinessDetailsSelected : ClassDetailsEvents("class_details_screen_business_details_tap")
		object ReviewsSeeAllSelected : ClassDetailsEvents("class_details_screen_reviews_see_all_tap")
		object NextSelected : ClassDetailsEvents("class_details_screen_next_tap")
		open class ReviewsReadMoreSelected(selectedIndex: Int)
			: ClassDetailsEvents("class_details_screen_reviews_read_more_tap", selectedIndex)
	}
}
