package com.mindbodyonline.connect.favorites;

import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.TextView;

import com.mindbodyonline.android.util.TaskCallback;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.activities.details.ClassTypeDetailsActivity;
import com.mindbodyonline.connect.adapters.filters.BookableOnlineClassFilter;
import com.mindbodyonline.connect.adapters.filters.FilterUtils;
import com.mindbodyonline.connect.adapters.filters.IFilter;
import com.mindbodyonline.connect.adapters.filters.QualifiedClassFilter;
import com.mindbodyonline.connect.analytics.OriginComponent;
import com.mindbodyonline.connect.analytics.OriginView;
import com.mindbodyonline.connect.analytics.EventsPathIdentifier;
import com.mindbodyonline.connect.analytics.favorites.FavoritesEventTrackerKt;
import com.mindbodyonline.connect.common.utilities.ViewUtilKt;
import com.mindbodyonline.connect.favorites.business.FavoriteBusinessesFragment;
import com.mindbodyonline.connect.favorites.business.FavoriteBusinessesFragment.FavoriteBusinessesFragmentContract;
import com.mindbodyonline.connect.favorites.staff.FavoriteStaffFragment;
import com.mindbodyonline.connect.favorites.viewmodels.FavoriteViewModelFactory;
import com.mindbodyonline.connect.favorites.viewmodels.FavoritesViewModel;
import com.mindbodyonline.connect.fragments.ClassListFragment;
import com.mindbodyonline.connect.fragments.RoutineVisitFragment;
import com.mindbodyonline.connect.fragments.custom.MainFragment;
import com.mindbodyonline.connect.utils.AnalyticsUtils;
import com.mindbodyonline.connect.utils.Constants;
import com.mindbodyonline.connect.utils.DeviceUtils;
import com.mindbodyonline.connect.utils.DialogUtils;
import com.mindbodyonline.connect.utils.time.DateFormatUtils;
import com.mindbodyonline.data.StaticInstance;
import com.mindbodyonline.data.services.MBAuth;
import com.mindbodyonline.data.services.MBStaticCache;
import com.mindbodyonline.data.services.locator.ServiceLocator;
import com.mindbodyonline.domain.ClassTypeObject;
import com.mindbodyonline.framework.abvariant.DevelopmentFlag;
import com.mindbodyonline.views.DisplayMode;
import com.mindbodyonline.views.dates.DayCellView.ColorScheme;
import com.mindbodyonline.views.dialog.CalendarDayPickerDialog;
import com.mindbodyonline.views.dialog.FilterDialog;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayout.Tab;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.Locale;
import java.util.Set;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import kotlin.Unit;

import static com.mindbodyonline.connect.analytics.AnalyticsConstantsKt.BUSINESSES_TAB;
import static com.mindbodyonline.connect.analytics.AnalyticsConstantsKt.CLASSES_TAB;
import static com.mindbodyonline.connect.analytics.AnalyticsConstantsKt.STAFF_TAB;

public class FavoritesFragment extends MainFragment implements FavoriteBusinessesFragmentContract {
    public static final String FAVORITE_CLASS_FILTERS_DIALOG_TAG = "FAVORITE_CLASS_FILTER_DIALOG";
    public static final int FAVORITE_CLASSES_PAGE = 1;
    public static final String[] TAB_LIST = {"Businesses", "Classes", "Staff"};
    public static final String FAVORITES_ANALYTICS_PREFIX = "(Favorites) | ";

    /**
     * The {@link PagerAdapter} that will provide
     * fragments for each of the sections. We use a
     * {@link FragmentPagerAdapter} derivative, which
     * will keep every loaded fragment in memory. If this becomes too memory
     * intensive, it may be best to switch to a
     * {@link FragmentStatePagerAdapter}.
     */
    SectionsPagerAdapter mSectionsPagerAdapter;

    /**
     * The {@link ViewPager} that will host the section contents.
     */
    ViewPager mViewPager;
    private ClassListFragment mFavoriteClassesFragment;
    private FavoriteBusinessesFragment mFavoriteBusinessesFragment;
    private FavoriteStaffFragment favoriteStaffFragment;
    private ArrayList<Fragment> fragments;

    private TabLayout tabLayout;
    private static final QualifiedClassFilter qualifiedClassFilter = new QualifiedClassFilter();
    private static final BookableOnlineClassFilter bookableOnlineClassFilter = new BookableOnlineClassFilter();
    private Set<IFilter<ClassTypeObject>> currentFilters = new LinkedHashSet<>();
    private Set<IFilter<ClassTypeObject>> allFilters = new LinkedHashSet<>();

    private TextView filterCountText;
    private long pageLoadTime;
    private boolean hasLoggedClassTiming;

    private View calendarButton;
    private View doneButton;
    private View filterButton;

    private FavoritesViewModel viewModel;

    private @Nullable OriginView originView;
    private @Nullable OriginComponent originComponent;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.activity_favorites, container, false);

        tabLayout = rootView.findViewById(R.id.favorites_pager_tabindicator);
        mViewPager = rootView.findViewById(R.id.favorites_viewpager);
        //Avoid refreshing data unnecessarily
        mViewPager.setOffscreenPageLimit(2);

        ((ViewStub) rootView.findViewById(R.id.favorites_filter)).inflate(); // Inflate the filter button, see XML for details
        calendarButton = rootView.findViewById(R.id.calendar_filter);
        doneButton = rootView.findViewById(R.id.action_bar_done);
        filterButton = rootView.findViewById(R.id.favorites_filter);
        filterButton.setContentDescription(getString(R.string.filter_ada_description));
        ImageView filterIcon = filterButton.findViewById(R.id.filter_icon);
        filterIcon.setColorFilter(R.color.black);
        filterCountText = rootView.findViewById(R.id.filter_count_text);

        allFilters.add(qualifiedClassFilter);
        allFilters.add(bookableOnlineClassFilter);

        return rootView;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        fragments = new ArrayList<>();
        mFavoriteBusinessesFragment = new FavoriteBusinessesFragment();
        mFavoriteBusinessesFragment.setContract(this);

        favoriteStaffFragment = new FavoriteStaffFragment();
        favoriteStaffFragment.setReorderFavoritesCallback(() -> {
            reorderModeEnabled(true);
            return Unit.INSTANCE;
        });

        FavoriteViewModelFactory viewModelFactory = new FavoriteViewModelFactory(ServiceLocator.getFavoriteLocationRepository(), this, null);
        viewModel = new ViewModelProvider(this, viewModelFactory).get(FavoritesViewModel.class);
        subscribeViewModels();

        calendarButton.setOnClickListener(v -> {
            FavoritesEventTrackerKt.trackFavoritesCalendarClick();
            calendarButton.setEnabled(false);
            CalendarDayPickerDialog dlg = new CalendarDayPickerDialog();
            dlg.manualSelect(mFavoriteClassesFragment.getSelectedDate());
            dlg.setDateSelectedListener(result -> {
                mFavoriteClassesFragment.setSelectedDate(result);
                mFavoriteClassesFragment.goToDate(result);
                AnalyticsUtils.logEvent(FavoritesFragment.FAVORITES_ANALYTICS_PREFIX + "Calendar day selected",
                    "Date selected", DateFormatUtils.ISO_DATE_FORMAT.format(result), "Origin", "Calendar picker");
            });
            AnalyticsUtils.logEvent(FAVORITES_ANALYTICS_PREFIX + "Calendar icon tapped");
            dlg.show(getChildFragmentManager(), Constants.CALENDAR_DAY_PICKER_TAG);

            calendarButton.postDelayed(() -> calendarButton.setEnabled(true), Constants.ACTIONBAR_ITEM_DISABLE_TIME_IN_MS);
        });

        filterButton.setOnClickListener(v -> {
            FavoritesEventTrackerKt.trackFavoritesFilterClick();
            showFilterDialog();
        });

        doneButton.setOnClickListener(v -> {
            reorderModeEnabled(false);
        });

        mFavoriteClassesFragment = ClassListFragment.newInstance(ColorScheme.ORANGE, null, null, EventsPathIdentifier.FAVORITE_CLASSES);
        mFavoriteClassesFragment.setDisplayMode(DisplayMode.FAVORITE);
        mFavoriteClassesFragment.setAnalyticsPrefix(FavoritesFragment.FAVORITES_ANALYTICS_PREFIX);
        mFavoriteClassesFragment.setOnWeekPagerShownChangeHandler(shown -> {
            if (shown == null || !shown) {
                calendarButton.setVisibility(View.GONE);
            } else if (mViewPager != null && mViewPager.getCurrentItem() == FAVORITE_CLASSES_PAGE) {
                calendarButton.setVisibility(View.VISIBLE);
            }
        });

        mFavoriteClassesFragment.setOnClassPageLoadFinishedCallback(ignored -> {
            if (pageLoadTime != 0 && !hasLoggedClassTiming) {
                AnalyticsUtils.logTimingEvent("Favorites",
                    AnalyticsUtils.SCREEN_API_LOAD_CATEGORY,
                    "Classes", SystemClock.elapsedRealtime() - pageLoadTime);
                AnalyticsUtils.logTimingEvent("Favorites",
                    AnalyticsUtils.SCREEN_API_LOAD_CATEGORY,
                    "Classes (Average per favorite business)",
                    (SystemClock.elapsedRealtime() - pageLoadTime) / MBStaticCache.getInstance().getFavoriteBusinesses().length);
                hasLoggedClassTiming = true;
            }
        });

        mFavoriteClassesFragment.setEditFilterOptionsButtonCallback(ignored -> showFilterDialog());

        mFavoriteClassesFragment.setClearFiltersButtonCallback(ignored -> clearCurrentFilters());

        fragments.add(mFavoriteBusinessesFragment);
        fragments.add(mFavoriteClassesFragment);
        fragments.add(favoriteStaffFragment);

        final TaskCallback<Void> onRefreshCallback = result -> viewModel.fetchFavoriteBusinesses(true);

        mFavoriteBusinessesFragment.setOnRefreshCallback(onRefreshCallback);
        mFavoriteClassesFragment.setRefreshBusinessFavoritesCallback(onRefreshCallback);

        // Create the adapter that will return a fragment for each of the three
        // primary sections of the app.
        mSectionsPagerAdapter = new SectionsPagerAdapter(getChildFragmentManager());

        // Set up the ViewPager with the sections adapter.
        mViewPager.setAdapter(mSectionsPagerAdapter);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                if (tabLayout != null && tabLayout.getTabCount() > 0) {
                    TabLayout.Tab tab = tabLayout.getTabAt(position);
                    if (tab != null) tab.select();

                    configureMenuBarItems(position);
                    if (position == FAVORITE_CLASSES_PAGE) {
                        if (MBStaticCache.getInstance().favoritesChanged()) {
                            mFavoriteClassesFragment.setLocations(MBStaticCache.getInstance().getFavoriteBusinesses());
                            MBStaticCache.getInstance().setFavoritesChanged(false);
                        }
                    }
                    AnalyticsUtils.logEvent(FAVORITES_ANALYTICS_PREFIX + TAB_LIST[position] + " tab tapped", "Tab", TAB_LIST[position]);
                }

                // When switching tabs, exit reorder mode
                reorderModeEnabled(false);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override public void onTabSelected(Tab tab) {
                ViewUtilKt.setStyleForTab(tab, Typeface.BOLD);
            }

            @Override public void onTabUnselected(Tab tab) {
                trackTabClicked();
                ViewUtilKt.setStyleForTab(tab, Typeface.NORMAL);
            }

            @Override public void onTabReselected(Tab tab) {}
        });

        tabLayout.setupWithViewPager(mViewPager);

        if (savedInstanceState == null) {
            Bundle extras = getArguments();
            if (extras != null) {
                int tab = extras.getInt(Constants.KEY_BUNDLE_SELECTED_TAB);
                int currentTab = mViewPager.getCurrentItem();
                if (tab != currentTab) {
                    mViewPager.setCurrentItem(tab);
                }
            }
        }

        if (DevelopmentFlag.DEVELOPMENT_REMOVE_FAV_BUSINESSES_CALL_DURING_APP_LAUNCH.isFeatureEnabled()) {
            viewModel.fetchFavoriteBusinesses(true);
        } else {
            //We rely on the tour activity to query favorite businesses for us.
            viewModel.fetchFavoriteBusinesses(false);
        }
    }

    public void initializeTrackingData(@Nullable OriginView originView,
        @Nullable OriginComponent originComponent) {
        this.originView = originView;
        this.originComponent = originComponent;
        trackingDataInitializationSideEffects();
    }

    private void trackingDataInitializationSideEffects() {
        // sending events path identifier so this fragment could be reused
        mFavoriteBusinessesFragment.initializeTracking(
            originView,
            originComponent,
            EventsPathIdentifier.FAVORITE_BUSINESSES
        );
        favoriteStaffFragment.initializeTrackingData(
            originView,
            originComponent,
            EventsPathIdentifier.FAVORITE_STAFF
        );
    }

    private void trackTabClicked() {
        int currentPosition = tabLayout.getSelectedTabPosition();
        String tabName;
        switch (currentPosition) {
            case 0:
                tabName = BUSINESSES_TAB;
                break;
            case 1:
                tabName = CLASSES_TAB;
                break;
            case 2:
                tabName = STAFF_TAB;
                break;
            default:
                return;
        }
        FavoritesEventTrackerKt.trackFavoritesTabSelected(tabName);
    }

    private void subscribeViewModels() {
        viewModel.getFavoriteBusinesses().observe(getViewLifecycleOwner(), favoriteBusinesses -> {
            if (favoriteBusinesses != null) {
                mFavoriteClassesFragment.setLocations(favoriteBusinesses);
                mFavoriteBusinessesFragment.setRefresh(false);
                configureMenuBarItems(getTab());
            } else {
                mFavoriteBusinessesFragment.showEmptyLayout(true, DeviceUtils.dataConnectionAvailable(getActivity()));
                mFavoriteBusinessesFragment.setRefresh(false);
            }
        });
    }

    private void configureMenuBarItems(int pageNum) {
        if (pageNum == FAVORITE_CLASSES_PAGE) {
            if (calendarButton != null) {
                if (MBStaticCache.getInstance().getFavoriteBusinesses().length > 0) {
                    calendarButton.setVisibility(View.VISIBLE);
                    filterButton.setVisibility(MBAuth.isGuestUser() ? View.GONE : View.VISIBLE);
                    setFilterCount(currentFilters.size());
                } else {
                    calendarButton.setVisibility(View.GONE);
                    filterButton.setVisibility(View.GONE);
                }
            }
            reorderModeEnabled(false);
        } else {
            if (calendarButton != null) {
                calendarButton.setVisibility(View.GONE);
                filterButton.setVisibility(View.GONE);
            }
        }
    }

    public void clearCurrentFilters() {
        if (!currentFilters.isEmpty()) {
            currentFilters.clear();
            setFilterCount(currentFilters.size());
            mFavoriteClassesFragment.setFilters(currentFilters, allFilters);
            mFavoriteClassesFragment.notifyDataSetChanged();
        }
    }

    public void setCurrentFilters(Set<IFilter<ClassTypeObject>> filters) {
        if (FilterUtils.logFilterChanges(currentFilters, filters)) {
            currentFilters.clear();
            currentFilters.addAll(filters);
            mFavoriteClassesFragment.setFilters(currentFilters, allFilters);
            mFavoriteClassesFragment.notifyDataSetChanged();
            setFilterCount(currentFilters.size());
        }
    }

    private void setFilterCount(int count) {
        if (filterCountText != null) {
            filterCountText.setText(count == 0 ? "" : String.format(Locale.getDefault(), "(%d)", count));
        }
    }

    private void showFilterDialog() {
        FilterDialog<ClassTypeObject> fd = new FilterDialog<>();
        fd.setFilters(allFilters, currentFilters);
        fd.setSaveFiltersCallback(this::setCurrentFilters);
        fd.show(getActivity().getSupportFragmentManager(), FAVORITE_CLASS_FILTERS_DIALOG_TAG);
    }

    public void setTab(int tab) {
        mViewPager.setCurrentItem(tab);
    }

    public int getTab() {
        return mViewPager.getCurrentItem();
    }

    public boolean isFiltered() {
        return !currentFilters.isEmpty();
    }

    @Override
    public void onPause() {
        filterButton.setVisibility(View.GONE);
        calendarButton.setVisibility(View.GONE);
        if (mFavoriteClassesFragment != null) mFavoriteClassesFragment.onPause();
        if (mFavoriteBusinessesFragment != null) mFavoriteBusinessesFragment.onPause();

        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
        pageLoadTime = SystemClock.elapsedRealtime();

        if (MBStaticCache.getInstance().favoritesChanged()) {
            //This is needed because the list doesn't get properly updated when a favorite changes
            pageLoadTime = SystemClock.elapsedRealtime();
            pullFavoritesFromStaticCache();
        }

        configureMenuBarItems(getTab());

        mFavoriteBusinessesFragment.onResume();
    }

    public void pullFavoritesFromStaticCache() {
        if (mFavoriteBusinessesFragment != null) {
            if (!MBAuth.isGuestUser() && getActivity() != null && DeviceUtils.dataConnectionAvailable(getActivity())) {
                viewModel.fetchFavoriteBusinesses(false);
            } else {
                mFavoriteBusinessesFragment.showEmptyLayout(true, false);
            }
        }
        if (mFavoriteClassesFragment != null) {
            mFavoriteClassesFragment.setLocations(MBStaticCache.getInstance().getFavoriteBusinesses());
            mFavoriteClassesFragment.notifyDataSetChanged();
            mFavoriteClassesFragment.refresh(true);
        }
        MBStaticCache.getInstance().setFavoritesChanged(false);
    }

    public void refresh() {
        mFavoriteClassesFragment.refresh();
    }

    @Override
    public void applyTopOffset(int topOffset, @NotNull View root) {
        View view = root.findViewById(R.id.toolbar);
        view.setPadding(view.getPaddingLeft(), topOffset, view.getPaddingRight(), view.getPaddingBottom());
    }

    @Override public void reorderModeEnabled(boolean enabled) {
        doneButton.setVisibility(enabled ? View.VISIBLE : View.GONE);
        if (mFavoriteBusinessesFragment != null) {
            mFavoriteBusinessesFragment.setReorderMode(enabled);
        }
        if (favoriteStaffFragment != null) {
            favoriteStaffFragment.setReorderModeEnabled(enabled);
        }
    }

    /**
     * A {@link FragmentPagerAdapter} that returns a fragment corresponding
     * to
     * one of the sections/tabs/pages.
     */
    public class SectionsPagerAdapter extends FragmentStatePagerAdapter {

        public SectionsPagerAdapter(FragmentManager fm) {
            super(fm, FragmentStatePagerAdapter.BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        }

        @Override
        public Fragment getItem(int position) {
            return fragments.get(position);
        }

        @Override
        public int getCount() {
            return fragments.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            if (getItem(position) instanceof FavoriteBusinessesFragment) {
                return getString(R.string.businesses_page_title);
            } else if (getItem(position) instanceof RoutineVisitFragment) {
                return getString(R.string.favorite_routines_title);
            } else if (getItem(position) instanceof ClassListFragment) {
                return getString(R.string.classes_page_title);
            } else if (getItem(position) instanceof FavoriteStaffFragment) {
                return getString(R.string.staff_page_title);
            }
            return getString(R.string.tab) + " " + position;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case ClassTypeDetailsActivity.CLASS_DETAILS_REQUEST_CODE:
                DialogUtils.showClassOrEventConfirmationDialog(
                        getActivity(),
                        StaticInstance.selectedClassTypeObject,
                        null,
                        null,
                        null,
                        null,
                        resultCode,
                        false,
                        0L,
                        null
                );
                break;
        }
        StaticInstance.selectedClassTypeObject = null;
    }
}


