package com.mindbodyonline.connect.quickbook;

import com.mindbodyonline.android.util.TaskCallback;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.utils.api.APIWorkflowUtil;
import com.mindbodyonline.domain.ClassPaymentStatus;
import com.mindbodyonline.domain.PaymentOptions;
import com.mindbodyonline.domain.checkout.CheckoutMode;
import com.mindbodyonline.framework.abvariant.FeatureFlag;

import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;

import androidx.annotation.StringRes;

import static com.mindbodyonline.domain.BookabilityStatus.ClassStatus;

public interface QuickBookContract {
    interface QBPaymentsMessaging {
        // TODO: these two should be moved into a Repository style class
        // NOTE: these should simply fetch the fields and not perform any UI work
        void getRequiredFields(int siteId, TaskCallback<APIWorkflowUtil.RequiredFieldsStatus> callback);

        boolean hasAtLeastOnePaymentMethod();

        boolean hasStripeUIPaymentsMethod();

        BigDecimal getAmountToChargeToCC();

        void getPaymentStatus(TaskCallback<ClassPaymentStatus> callback);

        void setPaymentStatus(ClassPaymentStatus paymentStatus);

        void setViewState(ViewState viewState);

        boolean selectedPricingOptionsIncludeDspo();

        void getPassesAvailable(TaskCallback<PaymentOptions> callback);

        boolean hasRecurringPayment();
    }

    interface ViewHandler {
        // FIXME: too much coupling
        void setQbPaymentsMessaging(QBPaymentsMessaging qbPaymentsMessaging);

        /**
         * @return success (true) or failure (false)
         */
        boolean determineViewState(CheckoutMode mode, @ClassStatus int status, int siteId, boolean forceNoUnpaidBooking, boolean isWaitlistableAndDependentUser);

        /**
         * @return last ViewState evaluated by {@link ViewHandler#determineViewState(CheckoutMode, int, int, boolean)}
         */
        ViewState getCurrentViewState();

        void setViewState(@NotNull ViewState viewState);
    }

    // TODO: migrate showPaymentMethods outside of this enum. Should be determined dynamically
    enum ViewState {
        NO_PRICING_OPTIONS(R.string.call_business, R.string.no_pricing_options_text, 0, false, false, false),
        ADD_A_CARD(R.string.add_a_credit_card_message, 0, 0, true, false, false),
        A_LA_CARTE(R.string.buy, R.string.order_summary_legal_template, 0, true, false, false),
        CONTRACT(R.string.buy, FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled()? R.string.order_summary_contract_message_ftc: R.string.order_summary_contract_message, 0, true, false, false),
        SIGN_LIABILITY(R.string.action_review_liability_short, R.string.order_summary_liability_legal, 0, true, false, false),
        SIGN_LIABILITY_FREE(R.string.action_review_liability_short, R.string.order_summary_liability_legal, 0, false, false, false),
        GET_PHONE_NUMBER_FREE(R.string.action_review_required_fields, R.string.order_summary_required_fields_legal, 0, false, false, false),
        GET_PHONE_NUMBER(R.string.action_review_required_fields, R.string.order_summary_required_fields_legal, 0, true, false, false),
        REQUIRE_PAYMENT_BOOK(R.string.sign_up_buy, R.string.order_summary_legal_template, 0, true, true, false),
        REQUIRE_PAYMENT_BOOK_DSPO(R.string.sign_up_buy, R.string.order_summary_legal_template, R.string.pay_booking_cancelation_subtext, true, true, false),
        REQUIRE_PAYMENT_WAIT_LIST(R.string.book_waitlist_text, R.string.order_summary_legal_template, 0, true, true, false),
        USE_AVAILABLE_PASSES_BOOK(R.string.book_text, R.string.passes_available_message, 0, false, true, true),
        USE_AVAILABLE_PASSES_WAIT_LIST(R.string.action_waitlist_class, R.string.passes_available_message, 0, false, true, true),
        BOOK_FOR_FREE(R.string.free_class_button_action_text, R.string.message_class_free, 0, false, true, false),
        BOOK_NOW_PAY_ON_ARRIVAL(R.string.action_book_class, R.string.message_unpaid, 0, false, true, false),
        UNAVAILABLE(R.string.call_business, R.string.call_to_book_message, 0, false, false, false),
        WAIT_LIST(R.string.action_waitlist_class, R.string.waitlistable_message, 0, false, true, false),
        PAY_AND_WAIT_LIST(R.string.book_waitlist_text, R.string.pay_waitlist_message, 0, true, true, false),
        ALREADY_BOOKED_FOR_USER(R.string.call_business, R.string.user_name_booked_header_text, 0, false, false, false),
        ALREADY_BOOKED_AT_SAME_TIME(R.string.call_business, R.string.user_name_booked_at_same_time_header_text, 0, false, false, false),
        APM(R.string.sign_up_buy, R.string.ideal_book_text, 0, true, false, false),
        APM_A_LA_CARTE(R.string.buy, R.string.ideal_book_text, 0, true, false, false),
        APM_REQUIRE_PAYMENT_WAIT_LIST(R.string.book_waitlist_text, R.string.ideal_book_text, 0, true, false, false),
        APM_APPT(R.string.sign_up_buy, R.string.ideal_book_text, 0, true, false, false),
        APM_CONTRACT(R.string.buy, FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled()? R.string.order_summary_legal_template_ftc : R.string.ideal_book_text, 0, true, false, false),
        REQUIRE_PAYMENT_WAIT_LIST_V2(R.string.book_waitlist_text, R.string.waitlist_sms_consent_message, R.string.order_summary_legal_template, true, true, false),
        WAIT_LIST_V2(R.string.action_waitlist_class, R.string.waitlist_sms_consent_message, R.string.waitlistable_message, false, true, false),
        PAY_AND_WAIT_LIST_V2(R.string.book_waitlist_text, R.string.waitlist_sms_consent_message, R.string.pay_waitlist_message, true, true, false),
        USE_AVAILABLE_PASSES_WAIT_LIST_V2(R.string.action_waitlist_class, R.string.waitlist_sms_consent_message, R.string.passes_available_message, false, true, true);


        public final @StringRes int actionText;
        public final @StringRes int descriptorText;
        public final @StringRes int extraText;
        public final boolean showPaymentMethods;
        public final boolean showPasses;


        // hmmm, it feels weird to use this when we have showPaymentMethods. It's really only used
        // to distinguish between not showing payments from an error or not showing payments because
        // it's free
        public final boolean canAddUserToSchedule;

        ViewState(
            @StringRes int actionText,
            @StringRes int descriptorText,
            @StringRes int extraText,
            boolean showPaymentMethods,
            boolean canAddUserToSchedule,
            boolean showPasses) {

            this.actionText = actionText;
            this.descriptorText = descriptorText;
            this.extraText = extraText;
            this.showPaymentMethods = showPaymentMethods;
            this.canAddUserToSchedule = canAddUserToSchedule;
            this.showPasses = showPasses;
        }
    }
}
