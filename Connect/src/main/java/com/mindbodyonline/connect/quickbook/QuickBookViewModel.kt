package com.mindbodyonline.connect.quickbook

import android.os.Bundle
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.android.volley.VolleyError
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.android.api.sales.MBSalesApi
import com.mindbodyonline.android.api.sales.model.payments.PaymentMethod
import com.mindbodyonline.android.api.sales.model.pos.cart.Cart
import com.mindbodyonline.android.api.sales.model.pos.catalog.CatalogItem
import com.mindbodyonline.android.api.sales.model.pos.catalog.CatalogItemOrPackageContainer
import com.mindbodyonline.android.api.sales.model.pos.packages.CatalogPackage
import com.mindbodyonline.android.util.SafeGson
import com.mindbodyonline.android.util.api.request.MBRequest
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.adapters.models.CatalogItemOrPassWrapper
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.analytics.checkout.BookingEventTracker
import com.mindbodyonline.connect.common.repository.BookingResult
import com.mindbodyonline.connect.common.repository.PerformanceName
import com.mindbodyonline.connect.common.utilities.awaitCallbackResponse
import com.mindbodyonline.connect.common.utilities.getList
import com.mindbodyonline.connect.common.utilities.putList
import com.mindbodyonline.connect.contracts.ContractTrackingEvent
import com.mindbodyonline.connect.ftc.FTCContractScreenshotManager
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbAction
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbLogger
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbStatus
import com.mindbodyonline.connect.quickbook.QuickBookContract.ViewState
import com.mindbodyonline.connect.quickbook.QuickBookViewModel.CheckoutMetaDataType.AppointmentMetaData
import com.mindbodyonline.connect.quickbook.QuickBookViewModel.CheckoutMetaDataType.ClassMetaData
import com.mindbodyonline.connect.utils.*
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayUserPurchaseInfoResponse
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference
import com.mindbodyonline.connect.utils.api.toLocationReference
import com.mindbodyonline.connect.utils.viewbinding.CartUtils
import com.mindbodyonline.connect.widgets.v3.refreshWidget
import com.mindbodyonline.data.services.MBStaticCache
import com.mindbodyonline.data.services.http.MbDataService
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.domain.*
import com.mindbodyonline.domain.apiModels.AddClientToClassModel
import com.mindbodyonline.domain.apiModels.AppointmentBookedResponse
import com.mindbodyonline.domain.checkout.CheckoutMode
import com.mindbodyonline.domain.pos.util.CartItemUtil
import com.mindbodyonline.framework.abvariant.DevelopmentFlag
import com.mindbodyonline.pickaspot.domain.*
import com.mindbodyonline.views.dialog.MaterialOptionDialog
import com.mindbodyonline.views.dialog.familyaccounts.FamilyAccount
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.annotations.NotNull
import java.io.File
import java.math.BigDecimal
import java.util.*
import kotlin.time.DurationUnit
import kotlin.time.ExperimentalTime
import kotlin.time.toDuration

/**
 * Makes some API calls for [QuickBookDialog]
 */
class QuickBookViewModel : ViewModel() {

    companion object {
        @JvmStatic
        fun sendQuickBookOpenedEvent(data: Map<String, Any>) {
            AnalyticsUtils.logEvent("(Quickbook) | Quickbook opened", data)
        }
    }

    data class BusinessDetailsSuccessData(
            val paymentMethod: PaymentMethod,
            val cart: Cart,
            val showAttributionSurvey: Boolean,
            val orderId: Long,
    )

    interface Callback {
        fun fetchedPricingOptions(catalogItems: List<CatalogItem>)
        fun onAppointmentBooked(response: AppointmentBookedResponse)
        fun onAppointmentBookingError(error: VolleyError)
    }

    private val _classTypeObject = MutableLiveData<ClassTypeObjectResponse?>()
    val classTypeObject: LiveData<ClassTypeObjectResponse?> = _classTypeObject

    private val _addClientToClassResult = MutableLiveData<VolleyError?>()
    val addClientToClassResult: LiveData<VolleyError?> = _addClientToClassResult

    private val _flexDialogShowPassOptionsClicked = MutableLiveData<Boolean>()
    val flexDialogShowPassOptionsClicked: LiveData<Boolean> = _flexDialogShowPassOptionsClicked

    private val _flexDialogLearnMoreClicked = MutableLiveData<Boolean>()
    val flexDialogLearnMoreClicked: LiveData<Boolean> = _flexDialogLearnMoreClicked

    private val TIMEOUT_SECONDS = 60

    var classIsPickASpot = false

    private var currentUserEmail: String? = null

    /**
     * A reference to a list for holding requests. Used for cancelling active ones.
     */
    val requests = mutableListOf<MBRequest<*>>()

    var firstTimePurchase: Boolean = false

    var callback: Callback? = null
        set(value) {
            field = value
            if (value != null) {
                cache.fetchedPricingOptions?.let { value.fetchedPricingOptions(it) }
                cache.appointmentBooked?.let { value.onAppointmentBooked(it) }
                cache.appointmentBookingError?.let { value.onAppointmentBookingError(it) }
            }
        }

    private var timerTask: Job? = null

    private val _selectedAccount = MutableLiveData<FamilyAccount?>()
    val selectedAccount: LiveData<FamilyAccount?> = _selectedAccount

    fun fetchUserEmail() {
        viewModelScope.launch(Dispatchers.IO) {
            ServiceLocator.userRepository.getUser(
                { userResponse ->
                    currentUserEmail = userResponse.userEmail
                },
                {
                    // Nothing to do in the error state
                },
                false
            )
        }
    }

    fun getUser() {
        viewModelScope.launch {
            ServiceLocator.userRepository.getUser(
                    { userResponse ->
                        with(userResponse) {
                            // Updating user email value
                            currentUserEmail = userResponse.userEmail
                            _selectedAccount.postValue(
                                    FamilyAccount(
                                            id = id.toString(),
                                            firstName = firstName,
                                            lastName = lastName)
                            )
                        }
                    },
                    {
                        _selectedAccount.postValue(null)
                    },
                    false
            )
        }
    }

    fun getAppointmentPricingOptions(
            appointment: AppointmentType,
            time: Pair<Calendar, Calendar>,
            location: Location) {

        val timeRange = TimeRange().apply {
            setStart(time.first)
            setEnd(time.second)
        }

        val searchDefinition = if (DevelopmentFlag.DEVELOPMENT_SAME_DAY_BOOKING_FIX.isFeatureEnabled()) {
            CartItemUtil.buildSearchDefinitionNoTimeZone(appointment, timeRange, location)
        } else {
            CartItemUtil.buildSearchDefinition(appointment, timeRange, location)
        }

        requests.add(MBSalesApi.getAppointmentCatalogFeed(location.siteId, searchDefinition, {
            // we do not currently support packages
            cache.fetchedPricingOptions(
                    it?.items?.mapNotNull(CatalogItemOrPackageContainer::getItem) ?: emptyList())
        }, {
            cache.fetchedPricingOptions(emptyList())
        }))
    }

    fun getDefaultCatalogItem(items: List<CatalogItem>): Pair<CatalogItem?, Boolean> {
        val hasDspo = items.any(CartItemUtil::isDspo)
        return if (hasDspo) {
            items.filter { CartItemUtil.isDspo(it) || it.isIntroOffer }
        } else {
            val hasIntroOffer = items.any { it.isIntroOffer }
            if (hasIntroOffer) items.filter { it.isIntroOffer } else items
        }.minByOrNull(CatalogItem::getSalePrice) to hasDspo
    }

    fun saveState(outState: Bundle) {
        cache.fetchedPricingOptions?.let {
            outState.putList(Constants.KEY_BUNDLE_CATALOG_ITEMS, it) { s, catalogItem ->
                putString(s, SafeGson.toJson(catalogItem))
            }
        }

        outState.putBoolean(Constants.KEY_BUNDLE_APPT_RESPONSE, cache.appointmentBooked != null)

        cache.appointmentBookingError?.let {
            outState.putString(Constants.KEY_BUNDLE_ERROR, SafeGson.toJson(it))
        }
    }

    fun restoreState(savedState: Bundle?) {
        if (savedState == null) return
        cache.fetchedPricingOptions = savedState.getList(Constants.KEY_BUNDLE_CATALOG_ITEMS, {
            SafeGson.fromJson(getString(it, null), CatalogItem::class.java)
        }, null)

        if (savedState.getBoolean(Constants.KEY_BUNDLE_APPT_RESPONSE, false)) {
            cache.appointmentBooked = AppointmentBookedResponse()
        }

        cache.appointmentBookingError = SafeGson.fromJson(savedState.getString(Constants.KEY_BUNDLE_ERROR), VolleyError::class.java)
    }

    // keeps results until a callback is set
    private val cache = object : Callback {
        var fetchedPricingOptions: List<CatalogItem>? = null
        var appointmentBooked: AppointmentBookedResponse? = null
        var appointmentBookingError: VolleyError? = null

        override fun fetchedPricingOptions(catalogItems: List<CatalogItem>) {
            callback?.fetchedPricingOptions(catalogItems) ?: run {
                fetchedPricingOptions = catalogItems
            }
        }

        override fun onAppointmentBooked(response: AppointmentBookedResponse) {
            callback?.onAppointmentBooked(response) ?: run {
                refreshWidget(ConnectApp.getInstance())
                appointmentBooked = response
            }
        }

        override fun onAppointmentBookingError(error: VolleyError) {
            callback?.onAppointmentBookingError(error) ?: run {
                appointmentBookingError = error
            }
        }
    }

    fun fetchClassDetails(siteId: Int, classInstanceId: Long, isInitialCall: Boolean = false) {
        viewModelScope.launch {
            val classDetails = ServiceLocator.classesRepository.getClassDetails(
                    siteId, classInstanceId, false, viewModelScope)
            _classTypeObject.postValue(ClassTypeObjectResponse(classDetails, isInitialCall))
            classDetails?.location?.let {
                    val isPickASpot = ServiceLocator.pickASpotApiInterface.isPickASpotClass(
                            classInstanceId = ClassInstanceId(classInstanceId.toString()),
                            classLocation = ClassLocation(ClassLocationId(it.siteLocationId.toString()),
                                    ClassSiteId(siteId.toString())))
                    classIsPickASpot = isPickASpot is IsPickASpotClassResult.True
                }
        }
    }

    /**
     * Retrieves the User purchase information given a valid location based on that User.
     */
    fun retrieveUserPurchaseInformation(location: Location?) = location
        ?.toLocationReference()?.let { locationReference ->
            viewModelScope.launch(Dispatchers.IO) {
                withContext(Dispatchers.IO) {
                    fetchUserPurchaseInformation(locationReference = locationReference)?.data?.attributes?.let {
                        firstTimePurchase = it.firstTimePurchaser ?: false
                    }
                }
            }
        }

    class ClassTypeObjectResponse(val classTypeObject: ClassTypeObject?, val initialCall: Boolean = false)

    fun addClientToClass(cto: ClassTypeObject, location: Location, selectedPassOrCatalog: CatalogItemOrPassWrapper?) {
        viewModelScope.launch {
            val model: AddClientToClassModel
            model = if (selectedPassOrCatalog != null) {
                val passOption = selectedPassOrCatalog.catalogItemOrPass as PaymentOption
                AddClientToClassModel(cto.id.toLong(), passOption.passOptionId,
                        passOption.subscriberId, passOption.membershipPassId)
            } else {
                AddClientToClassModel(cto.id.toLong())
            }

            if (cto.isWaitlistable) {
                requests.add(MbDataService
                    .getServiceInstance()
                    .loadClassService()
                    .addClientToWaitlist(location, model,
                            {
                                AnalyticsUtils.logTimingEndEvent("Class booking confirmation",
                                        AnalyticsUtils.USER_ACTION_CATEGORY,
                                        if (cto.requiresPayment()) "With purchase" else "Without purchase", true)
                                _addClientToClassResult.postValue(null)
                            }, _addClientToClassResult::postValue))
            } else {
                requests.add(MbDataService
                    .getServiceInstance()
                    .loadClassService()
                    .addClientToClass(location.siteId, model,
                            {
                                AnalyticsUtils.logTimingEndEvent("Class booking confirmation",
                                        AnalyticsUtils.USER_ACTION_CATEGORY,
                                        if (cto.requiresPayment()) "With purchase" else "Without purchase", true)

                                MBStaticCache.getInstance().setFavoritesChanged(true)
                                refreshWidget(ConnectApp.getInstance())
                                _addClientToClassResult.postValue(null)
                            }, _addClientToClassResult::postValue))
            }
        }
    }

    fun getFlexLimitDialog() = MaterialOptionDialog().apply {
        this.isCancelable = false
        setText(
                R.string.flex_limit_reached_title,
                R.string.flex_limit_reached_message,
                R.string.explore_classes,
                R.string.flex_update_payment,
                R.string.learn_more)
        setButton1Callback { _flexDialogShowPassOptionsClicked.postValue(false) }
        setButton2Callback { _flexDialogShowPassOptionsClicked.postValue(true) }
        setClickableLinkCallback {
            _flexDialogLearnMoreClicked.postValue(true)
            this.dismiss()
        }
    }

    fun getZeroDollarPassOptions(catalogItems: List<CatalogItem>) = catalogItems.filter { it.salePrice.toDouble() == 0.0 }

    @ExperimentalTime
    fun loaderTriggered(loading: Boolean) {
        when {
            !loading -> {
                timerTask?.cancel()
                timerTask = null
                ServiceLocator.perfMeasureRepo.stopPerformanceMeasure(System.currentTimeMillis(), PerformanceName.BOOK_TO_QB_READY)?.let {
                    BreadcrumbsUtils.breadcrumbPerformanceMetric(time = it, metric = PerformanceName.BOOK_TO_QB_READY)
                }
            }
            loading && (timerTask == null) ->
                timerTask = viewModelScope.timer(startDelay = TIMEOUT_SECONDS.toDuration(DurationUnit.SECONDS)) {
                    BreadcrumbsUtils.breadCrumbUITimeout(QuickBookDialog.TAG)
                }
        }
    }

    fun setSelectedAccount(familyAccount: FamilyAccount) {
        _selectedAccount.postValue(familyAccount)
    }

    fun trackCheckoutComplete(location: Location?, cart: Cart?, checkoutType: CheckoutMetaDataType, selectedCatalogueItem: CatalogItemOrPassWrapper?) {
        sendBookingResultPerformanceMetrics(true)
        val dataMap = mutableMapOf(
                AnalyticsUtils.KEY_QB_PROGRAM_TYPE to if (checkoutType is AppointmentMetaData) AnalyticsUtils.QB_PROGRAM_APPT_VALUE
                else AnalyticsUtils.QB_PROGRAM_CLASS_VALUE,
                AnalyticsUtils.KEY_STUDIO_ID to (location?.siteId
                        ?: AnalyticsUtils.QB_UNKNOWN_VALUE),
                AnalyticsUtils.KEY_QB_INVENTORY_PRICE to PaymentUtils.getFormattedCurrency(cart?.cartTotal
                        ?: BigDecimal.ZERO, location?.locale ?: Locale.getDefault()),
                AnalyticsUtils.KEY_QB_CONSUMER_FEE_AMOUNT to PaymentUtils.getFormattedCurrency(CartUtils.sumServiceFees(cart),
                        location?.locale ?: Locale.getDefault()),
                AnalyticsUtils.KEY_QB_SALES_TAX to PaymentUtils.getFormattedCurrency(CartUtils.sumTaxes(cart),
                        location?.locale ?: Locale.getDefault()),
                AnalyticsUtils.KEY_QB_PAYMENT_METHOD to CartUtils.fetchPaymentTypes(cart)
        )

        if (checkoutType is ClassMetaData) {
            dataMap.put(AnalyticsUtils.KEY_BOOKABILITY_STATUS, checkoutType.cto?.getClassBookedStatusForAnalytics(checkoutType.classPaymentStatus))
            dataMap.put(AnalyticsUtils.KEY_QB_IS_FREE, checkoutType.cto?.isFreeToEnroll.toString())
            AnalyticsUtilsKt.logUserBookedEvent(
                    location = location,
                    inventoryType = "class",
                    classTypeObject = classTypeObject.value?.classTypeObject,
                    catalogueItem = selectedCatalogueItem
            )
        }
        AnalyticsUtils.logBusinessEventWithMap("(Quickbook) | Complete", location, dataMap as Map<String, Any>)
    }

    fun trackCheckoutDismissed(location: Location?, cart: Cart?, isFree: Boolean) {
        sendBookingResultPerformanceMetrics(false)
        val dataMap = mutableMapOf(
                AnalyticsUtils.KEY_STUDIO_ID to (location?.siteId
                        ?: AnalyticsUtils.QB_UNKNOWN_VALUE),
                AnalyticsUtils.KEY_QB_INVENTORY_PRICE to PaymentUtils.getFormattedCurrency(cart?.cartTotal
                        ?: BigDecimal.ZERO, location?.locale ?: Locale.getDefault()),
                AnalyticsUtils.KEY_QB_CONSUMER_FEE_AMOUNT to PaymentUtils.getFormattedCurrency(CartUtils.sumServiceFees(cart),
                        location?.locale ?: Locale.getDefault()),
                AnalyticsUtils.KEY_QB_SALES_TAX to PaymentUtils.getFormattedCurrency(CartUtils.sumTaxes(cart),
                        location?.locale ?: Locale.getDefault()),
                AnalyticsUtils.KEY_QB_PAYMENT_METHOD to CartUtils.fetchPaymentTypes(cart),
                AnalyticsUtils.KEY_QB_IS_FREE to isFree
        )
        AnalyticsUtils.logEvent("(Quickbook) | Quickbook dismissed", dataMap as Map<String, Any>?)

    }

    public fun sendPerformanceBookCTASelected() {
        ServiceLocator.perfMeasureRepo.startPerformanceMeasure(System.currentTimeMillis(),
                PerformanceName.QB_BOOK_CTA_TO_COMPLETE_OR_ABANDON)
    }

    private fun sendBookingResultPerformanceMetrics(completed: Boolean) {
        val bookingResult = BookingResultValue(if (completed) BookingResult.SUCCESS else BookingResult.ABANDON)
        ServiceLocator.perfMeasureRepo.stopPerformanceMeasure(System.currentTimeMillis(), PerformanceName.BOOK_TO_COMPLETE_OR_ABANDON)?.let {
            BreadcrumbsUtils.breadcrumbPerformanceMetric(time = it,
                    metric = PerformanceName.BOOK_TO_COMPLETE_OR_ABANDON,
                    additionalData = listOf(bookingResult))
        }
        ServiceLocator.perfMeasureRepo.stopPerformanceMeasure(System.currentTimeMillis(), PerformanceName.QB_BOOK_CTA_TO_COMPLETE_OR_ABANDON)?.let {
            BreadcrumbsUtils.breadcrumbPerformanceMetric(time = it,
                    metric = PerformanceName.QB_BOOK_CTA_TO_COMPLETE_OR_ABANDON,
                    additionalData = listOf(bookingResult))
        }
    }

    private suspend fun fetchUserPurchaseInformation(locationReference: LocationReference) =
        try {
            awaitCallbackResponse<GatewayUserPurchaseInfoResponse> { success, error ->
                SwamisAPI.INSTANCE.getUserPurchaseInfo(
                        locationReference = locationReference,
                        successListener = success,
                        errorListener = error
                )
            }
        } catch (apiException: Exception) {
            null
        }

    fun trackContractEvent(@NotNull trackingEvent: ContractTrackingEvent, location: Location?, catalogPackage: CatalogPackage, orderId: Long?) {
        runIfNotNull(location, catalogPackage) { safeLocation, safePackage ->
            val dataMap = mutableMapOf(
                    AnalyticsUtils.KEY_CONTRACT_NAME to safePackage.name,
                    AnalyticsUtils.KEY_CONTRACT_ID to safePackage.id,
                    AnalyticsUtils.KEY_STUDIO_ID to (safeLocation.siteId),
                    AnalyticsUtils.KEY_CONTRACT_DISCOUNT_AMOUNT to (safePackage.pricing?.discountPrice
                            ?: AnalyticsUtils.QB_UNKNOWN_VALUE),
                    AnalyticsUtils.KEY_CONTRACT_RECURRING_AMOUNT to (safePackage.pricing?.autopayPrice
                            ?: AnalyticsUtils.QB_UNKNOWN_VALUE)
            )
            orderId?.let {
                dataMap.put(AnalyticsUtils.KEY_ORDER_ID, it);
            }
            AnalyticsUtils.logEvent(trackingEvent.eventKey, dataMap as Map<String, Any>?)
        }
    }

    fun getCurrentUserEmail(): String? {
        return currentUserEmail
    }

    fun trackQuickBookCTAClick(
        checkoutMode: CheckoutMode,
        viewState: ViewState,
        location: Location?,
        classTypeObject: ClassTypeObject?,
        catalogItem: CatalogItem?,
        catalogPackage: CatalogPackage?,
        appointmentType: AppointmentType?,
        timeRange: Pair<Calendar, Calendar>?,
        appointmentBookabilityStatus: AppointmentBookabilityStatus?
    ) {
        when (checkoutMode) {
            CheckoutMode.ALACARTE -> BookingEventTracker.trackAlaCarteQuickBookDialogCTAInteractedEvent(
                key = QuickBookUtils.mapToMixpanelEventKey(viewState),
                location = location,
                catalogItem = catalogItem
            )

            CheckoutMode.CONTRACT -> BookingEventTracker.trackContractQuickBookDialogCTAInteractedEvent(
                key = QuickBookUtils.mapToMixpanelEventKey(viewState),
                location = location,
                catalogPackage = catalogPackage
            )

            CheckoutMode.APPT -> BookingEventTracker.trackAppointmentQuickBookDialogCTAInteractedEvent(//todo
                key = QuickBookUtils.mapToMixpanelEventKey(viewState),
                location = location,
                appointmentType = appointmentType,
                timeRange = timeRange,
                appointmentBookabilityStatus = appointmentBookabilityStatus
            );
            CheckoutMode.CLASS -> BookingEventTracker.trackClassQuickBookDialogCTAInteractedEvent(
                key = QuickBookUtils.mapToMixpanelEventKey(viewState),
                location = location,
                classTypeObject = classTypeObject
            );
        }
    }

    fun cancelPaymentIntent(location: Location, paymentIntentId: String) {
        viewModelScope.launch {
            MBSalesApi.cancelPaymentIntent(location.siteId, paymentIntentId, { response ->
                // Nothing to do in the success state
                MBLog.d("Stripe_WebPayment_Logs", "PaymentIntent cancelled successfully")
            }, {
                MBLog.d("Stripe_WebPayment_Logs", "PaymentIntent cancelling failed")
                // Nothing to do in the error state
            })
        }
    }

    fun confirmContractUpload(
        subscriberId: Int,
        contractId: Int,
        userId: Int,
        cartId: String,
        orderId: Long?
    ) {
        MBLog.i("FTCAuditLog", "requestUploadConfirmation called in viewmodel")
        FTCContractScreenshotManager.requestUploadConfirmation(
            subscriberId = subscriberId.toString(),
            contractId = contractId.toString(),
            userId = userId.toString(),
            cartId = cartId,
            orderId = orderId.toString()
        )
    }

    fun logScreenshotCaptureEvent(
        status: FTCBreadCrumbStatus,
        userId: Int?,
        cartId: String?,
        catalogPackageId: Int,
        siteId: Int?,
        exception: Exception? = null
    ) {
        FTCBreadCrumbLogger.logFTCBreadCrumb(
            action = FTCBreadCrumbAction.CAPTURE_SCREENSHOT,
            status = status,
            location = OriginView.QUICK_BOOK_DIALOG.viewName,
            userId = userId.toString(),
            cartID = cartId,
            contractID = catalogPackageId.toString(),
            studioId = siteId.toString(),
            errorMessage = exception?.message
        )
    }

    sealed class CheckoutMetaDataType {
        class ClassMetaData(val cto: ClassTypeObject?, val classPaymentStatus: ClassPaymentStatus?) : CheckoutMetaDataType()
        class AppointmentMetaData : CheckoutMetaDataType()
    }
}