package com.mindbodyonline.connect.quickbook;

import static android.text.format.DateUtils.FORMAT_ABBREV_MONTH;
import static android.text.format.DateUtils.FORMAT_SHOW_DATE;
import static android.text.format.DateUtils.FORMAT_SHOW_WEEKDAY;
import static android.view.View.GONE;
import static android.view.View.VISIBLE;
import static com.mindbodyonline.connect.adapters.SimpleCatalogItemRecyclerAdapter.DATA_TYPE_CATALOG;
import static com.mindbodyonline.connect.adapters.SimpleCatalogItemRecyclerAdapter.DATA_TYPE_PASS;
import static com.mindbodyonline.connect.adapters.SimpleCatalogItemRecyclerAdapter.DATA_TYPE_UNKNOWN;
import static com.mindbodyonline.connect.stripe.StripePaymentActivity.EXTRA_PAYMENT_DATA;
import static com.mindbodyonline.connect.stripe.StripePaymentActivity.EXTRA_PAYMENT_STATUS;
import static com.mindbodyonline.connect.utils.AnalyticsEventConstantsKt.ORDER_VALUE_KEY;
import static com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_CLASS_TIME_ZONE_ID;
import static com.mindbodyonline.connect.utils.Constants.KEY_BUNDLE_SELECTED_FAMILY_ACCOUNT;
import static com.mindbodyonline.connect.utils.FamilyAccountsUtilsKt.isDependentUserSelected;
import static com.mindbodyonline.connect.utils.FamilyAccountsUtilsKt.isFamilyAccountType;
import static com.mindbodyonline.domain.ClassPaymentStatus.HAS_PAYMENT_METHOD;
import static com.mindbodyonline.domain.ClassPaymentStatus.REQUIRES_PAYMENT;
import static com.mindbodyonline.framework.abvariant.ABEventKeysKt.SEARCH_AUTO_SUGGEST_USER_PURCHASED;
import static com.mindbodyonline.framework.abvariant.ABEventKeysKt.SEARCH_FULL_TEXT_USER_PURCHASED;
import static com.mindbodyonline.framework.abvariant.ABEventKeysKt.SEARCH_RESULTS_USER_PURCHASED_EVENT;
import static com.mindbodyonline.framework.abvariant.ABEventKeysKt.SEARCH_USER_PURCHASED;
import static com.mindbodyonline.framework.abvariant.ABEventKeysKt.USER_PURCHASED_EVENT;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Point;
import android.graphics.PorterDuff;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Html;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.text.method.LinkMovementMethod;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewGroup.LayoutParams;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.ColorInt;
import androidx.annotation.ColorRes;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.android.volley.Request;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.google.android.material.snackbar.Snackbar;
import com.mindbodyonline.android.api.sales.MBSalesApi;
import com.mindbodyonline.android.api.sales.model.HttpResponseMessage;
import com.mindbodyonline.android.api.sales.model.MBApiErrorResponse;
import com.mindbodyonline.android.api.sales.model.enums.CSeriesTemplateKeys;
import com.mindbodyonline.android.api.sales.model.enums.CServiceCategoryType;
import com.mindbodyonline.android.api.sales.model.payments.CartPaymentItem;
import com.mindbodyonline.android.api.sales.model.payments.PaymentConfiguration;
import com.mindbodyonline.android.api.sales.model.payments.PaymentMethod;
import com.mindbodyonline.android.api.sales.model.payments.ServiceFee;
import com.mindbodyonline.android.api.sales.model.pos.AddPackageToCartRequest;
import com.mindbodyonline.android.api.sales.model.pos.ConsumerCheckoutRequest;
import com.mindbodyonline.android.api.sales.model.pos.DspoRequest;
import com.mindbodyonline.android.api.sales.model.pos.cart.Cart;
import com.mindbodyonline.android.api.sales.model.pos.cart.CartAbandonReason;
import com.mindbodyonline.android.api.sales.model.pos.cart.CartItem;
import com.mindbodyonline.android.api.sales.model.pos.cart.Metadata;
import com.mindbodyonline.android.api.sales.model.pos.cart.Templates;
import com.mindbodyonline.android.api.sales.model.pos.catalog.CatalogItem;
import com.mindbodyonline.android.api.sales.model.pos.packages.CartPackage;
import com.mindbodyonline.android.api.sales.model.pos.packages.CatalogPackage;
import com.mindbodyonline.android.api.sales.model.search.DistanceUnit;
import com.mindbodyonline.android.util.SafeGson;
import com.mindbodyonline.android.util.TaskCallback;
import com.mindbodyonline.android.util.api.request.MBRequest;
import com.mindbodyonline.android.util.log.MBLog;
import com.mindbodyonline.connect.BuildConfig;
import com.mindbodyonline.connect.R;
import com.mindbodyonline.connect.activities.workflow.LiabilityReleaseActivity;
import com.mindbodyonline.connect.activities.workflow.RequiredFieldsActivity;
import com.mindbodyonline.connect.adapters.models.CatalogItemOrPassWrapper;
import com.mindbodyonline.connect.analytics.OriginComponent;
import com.mindbodyonline.connect.analytics.OriginView;
import com.mindbodyonline.connect.analytics.checkout.BookingEventTracker;
import com.mindbodyonline.connect.analytics.checkout.CheckoutEventTracker;
import com.mindbodyonline.connect.analytics.checkout.CheckoutKey;
import com.mindbodyonline.connect.analytics.checkout.QuickbookEventTracker;
import com.mindbodyonline.connect.analytics.checkout.ServiceType;
import com.mindbodyonline.connect.analytics.payment.APMEventTracker;
import com.mindbodyonline.connect.common.repository.LocationRepository;
import com.mindbodyonline.connect.common.utilities.OrderUtilKt;
import com.mindbodyonline.connect.contracts.ContractTrackingEvent;
import com.mindbodyonline.connect.contracts.ContractsInformationUtilsKt;
import com.mindbodyonline.connect.fragments.custom.MBDialogFragment;
import com.mindbodyonline.connect.ftc.FTCContractScreenshotManager;
import com.mindbodyonline.connect.ftc.data.FTCScreenShotSource;
import com.mindbodyonline.connect.ftc.logs.FTCBreadCrumbStatus;
import com.mindbodyonline.connect.quickbook.QuickBookContract.QBPaymentsMessaging;
import com.mindbodyonline.connect.quickbook.QuickBookContract.ViewHandler;
import com.mindbodyonline.connect.quickbook.QuickBookContract.ViewState;
import com.mindbodyonline.connect.quickbook.QuickBookViewModel.CheckoutMetaDataType.AppointmentMetaData;
import com.mindbodyonline.connect.quickbook.QuickBookViewModel.CheckoutMetaDataType.ClassMetaData;
import com.mindbodyonline.connect.quickbook.QuickBookViewModel.ClassTypeObjectResponse;
import com.mindbodyonline.connect.quickbook.QuickBookViewModelV2.QuickbookFailureReason;
import com.mindbodyonline.connect.sca.SCAAuthorizationActivity;
import com.mindbodyonline.connect.stripe.CustomDialogAlert;
import com.mindbodyonline.connect.stripe.PaymentStatus;
import com.mindbodyonline.connect.stripe.SharedPaymentDataViewModel;
import com.mindbodyonline.connect.stripe.SharedViewModelFactory;
import com.mindbodyonline.connect.stripe.StripePaymentActivity;
import com.mindbodyonline.connect.stripe.StripePaymentData;
import com.mindbodyonline.connect.stripe.StripeWebViewActivity;
import com.mindbodyonline.connect.tealium.LoginTrackingUtils;
import com.mindbodyonline.connect.tealium.TealiumHelper;
import com.mindbodyonline.connect.tealium.TrackingHelperUtils;
import com.mindbodyonline.connect.utils.AnalyticsUtils;
import com.mindbodyonline.connect.utils.AnalyticsUtilsKt;
import com.mindbodyonline.connect.utils.AndroidUtils;
import com.mindbodyonline.connect.utils.BreadcrumbsUtils;
import com.mindbodyonline.connect.utils.CalendarUtils;
import com.mindbodyonline.connect.utils.Constants;
import com.mindbodyonline.connect.utils.DialogUtils;
import com.mindbodyonline.connect.utils.DomainObjectUtils;
import com.mindbodyonline.connect.utils.ExtensionFunctionsKt;
import com.mindbodyonline.connect.utils.FamilyAccountsUtilsKt;
import com.mindbodyonline.connect.utils.GeoLocationUtils;
import com.mindbodyonline.connect.utils.IntentUtils;
import com.mindbodyonline.connect.utils.MBPhoneUtils;
import com.mindbodyonline.connect.utils.PaymentKeyManager;
import com.mindbodyonline.connect.utils.PaymentUtils;
import com.mindbodyonline.connect.utils.ScreenshotUtil;
import com.mindbodyonline.connect.utils.SharedPreferencesUtils;
import com.mindbodyonline.connect.utils.Switches;
import com.mindbodyonline.connect.utils.TimeUtils;
import com.mindbodyonline.connect.utils.ToastUtils;
import com.mindbodyonline.connect.utils.Utils;
import com.mindbodyonline.connect.utils.api.APIWorkflowUtil;
import com.mindbodyonline.connect.utils.api.APIWorkflowUtil.RequiredFieldsStatus;
import com.mindbodyonline.connect.utils.api.ModelTranslationKt;
import com.mindbodyonline.connect.utils.api.dynamicpricing.DynamicPricingApi;
import com.mindbodyonline.connect.utils.api.dynamicpricing.UserDspoIdentifier;
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI;
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationReference;
import com.mindbodyonline.connect.utils.time.DateFormatUtils;
import com.mindbodyonline.connect.utils.userflow.UserCheckoutFlowTracking;
import com.mindbodyonline.connect.utils.viewbinding.CartUtils;
import com.mindbodyonline.data.StaticInstance;
import com.mindbodyonline.data.services.MBAuth;
import com.mindbodyonline.data.services.MBStaticCache;
import com.mindbodyonline.data.services.http.MbDataService;
import com.mindbodyonline.data.services.locator.ServiceLocator;
import com.mindbodyonline.domain.AppointmentBookabilityStatus;
import com.mindbodyonline.domain.AppointmentType;
import com.mindbodyonline.domain.BookabilityStatus;
import com.mindbodyonline.domain.BookabilityStatus.ClassStatus;
import com.mindbodyonline.domain.ClassPaymentStatus;
import com.mindbodyonline.domain.ClassTypeObject;
import com.mindbodyonline.domain.Enrollment;
import com.mindbodyonline.domain.FavoriteClass;
import com.mindbodyonline.domain.Location;
import com.mindbodyonline.domain.PaymentOption;
import com.mindbodyonline.domain.PaymentOptions;
import com.mindbodyonline.domain.StaffReference;
import com.mindbodyonline.domain.TimeRange;
import com.mindbodyonline.domain.apiModels.AppointmentBookedResponse;
import com.mindbodyonline.domain.apiModels.BookAppointmentModel;
import com.mindbodyonline.domain.apiModels.ErrorCodeResponse;
import com.mindbodyonline.domain.checkout.CheckoutMode;
import com.mindbodyonline.domain.connv1.extensions.PassOptionsExtensionKt;
import com.mindbodyonline.domain.dataModels.PricingReference;
import com.mindbodyonline.domain.pos.util.CartItemUtil;
import com.mindbodyonline.domain.quickbook.AppointmentQuickBookResponse;
import com.mindbodyonline.domain.quickbook.CTOQuickBookResponse;
import com.mindbodyonline.domain.quickbook.DealQuickBookResponse;
import com.mindbodyonline.framework.abvariant.ABHelperUtils;
import com.mindbodyonline.framework.abvariant.FeatureFlag;
import com.mindbodyonline.views.dialog.ConfirmationHUD;
import com.mindbodyonline.views.dialog.GenericCtaDialog;
import com.mindbodyonline.views.dialog.GenericErrorDialog;
import com.mindbodyonline.views.dialog.MaterialOptionDialog;
import com.mindbodyonline.views.dialog.OrderSummaryDialog;
import com.mindbodyonline.views.dialog.SelectCreditCardDialog;
import com.mindbodyonline.views.dialog.SimpleCatalogItemPickerDialog;
import com.mindbodyonline.views.dialog.SplitCreditCardsDialog;
import com.mindbodyonline.views.dialog.familyaccounts.FamilyAccount;
import com.mindbodyonline.views.dialog.familyaccounts.FamilyAccountSelectionDialogFragment;
import com.mindbodyonline.views.dialog.viewmodels.SelectCCInitializer;
import com.stripe.android.googlepaylauncher.GooglePayEnvironment;
import com.stripe.android.googlepaylauncher.GooglePayLauncher;
import com.stripe.android.paymentsheet.PaymentSheet;
import com.stripe.android.paymentsheet.PaymentSheetResult;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import kotlin.Pair;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.functions.Function1;
import kotlin.text.StringsKt;

/**
 * A dialog that pops up like a bottom sheet to allow a full point of sale workflow.
 * <p>
 * The book button state relies on 3 data types:
 * {@link BookabilityStatus}
 * {@link RequiredFieldsStatus}
 * {@link ClassPaymentStatus}
 * <p>
 * NOTE: To book a user into a class or appointment, they need to have a pass on file
 * If they do not, we attempt to purchase the pass and then book the user into the class/appointment
 * afterwards unless unpaid bookings are allowed
 */
public class QuickBookDialog extends MBDialogFragment implements QBPaymentsMessaging,
    QuickBookViewModel.Callback {
    public static final String TAG = "QuickBookDialog";
    // The initial back off for waiting for a created order to be processed (in milliseconds)
    // NOTE: backoff is a misnomer here since we do not increase the delay between calls
    public static final long BACKOFF_INITIAL_DELAY = 3000;
    public static final String KEY_SELECTED_USER = "selectedUser";
    public static final String KEY_CLASS_TYPE_OBJECT = "classTypeObject";
    private static final long POST_DELAY_MS = 300;
    // The max number of CatalogItemOrPackages we get back
    // NOTE: This is a paged endpoint but we only get the first page, hence "max number"
    private static final int NUM_RESULTS_PER_PAGE = 40;
    private static final int REQUEST_CODE_SCA_PAYMENT = 722;
    // The ErrorCode for Flex Visits Limit reached from the Connect /user/{userId/Visits API call
    private static final int FLEX_LIMIT_ERROR_CODE = 367;

    public static String SELECTED_USER_TAG = null;
    public static LocationRepository locationRepository = ServiceLocator.getLocationRepository();
    private final BigDecimal CONVENIENCE_FEE = new BigDecimal(".50");
    // the information required from the user prior to being able to purchase anything at that location
    public @Nullable
    RequiredFieldsStatus requiredFieldsStatus;
    // A catalog item the user picked before opening quickbook
    // For example, location > intro offers > picked one > view classes for PO > quickbook
    // NOTE: I don't think we have to hold onto this after initialization. I don't think it's worth looking into though
    private CatalogItem catalogItem;
    // a flag to alleviate UI operations while the dialog is unavailable
    private boolean isPaused = true;
    // a complimentary flag to the previous one to dismiss the dialog as soon as it becomes available again
    private boolean pendingDismissal = false;
    // self explanatory. We show a dialog if we find a cheaper option than the LMO though
    private boolean isLastMinuteOfferWorkflow;
    // if not null, we are trying to book the user for a contract
    private CatalogPackage catalogPackage;
    // if not null, we are trying to book the user into a class
    private ClassTypeObject classTypeObject;
    // if not null, we are trying to book the user into an appointment
    private AppointmentType appointmentType;
    private AppointmentBookabilityStatus appointmentBookabilityStatus;

    private AppointmentBookedResponse appointmentBookedResponse;

    // the time we are trying to book the user into for the appointment
    private Pair<Calendar, Calendar> timeRange;
    // the appointment's staff (may be fetched using getFirstAvailableStaffForTimeRange which seems bad)
    private String staffName;
    // the list of catalog items the user can choose from
    // NOTE: we store these here to know if we can display a dialog for the user to pick another one
    // NOTE 2: this will be of size one for a la carte workflow
    private List<CatalogItem> catalogItems = new ArrayList<>();
    // the catalog item that is in the cart right now (hence why it is a CartItem [NOTE: a CartItem wraps a CatalogItem])
    private CartItem selectedCartItem;
    // straight forward. It is important to keep this in sync with the server after modifications (eg, changing catalog item or payment)
    private Cart cart;
    // the way the user wants to pay for the cart's contents
    private PaymentMethod chosenPaymentMethod;
    // location settings for what payments are accepted (eg, Visa and Master Card only)
    private PaymentConfiguration paymentConfiguration;
    // a flag for whether or not the user would like to apply their current gift card balance to the cart
    // NOTE: this should be refactored later into split payment on the cart (ie, we shouldn't care that it's a gift card. It's just another payment method)
    private boolean userSelectApplyGC = true;
    // used to stop the infinite polling loop after an order has been created
    // NOTE: Although we prevent the user from closing the dialog, any number of things can cause the device to close our application while we're polling
    private boolean cancelPolling;
    // literally whether or not runPurchaseApiCalls has been run already. We should work to remove this flag
    private boolean ranPurchaseApiCalls = false;
    // an atomic flag for whether or not we have the catalog items populated so we can show a picker for them
    private AtomicBoolean waitingForPurchaseApiToFinish = new AtomicBoolean();
    // an atomic flag for when the user has clicked to show the catalog item picker
    private AtomicBoolean passPurchaseOptionPickerDialogWaiting = new AtomicBoolean();
    // I'd suggest looking into the IntDef in the class for this one. It's fairly self explanatory
    private ClassPaymentStatus bookablePaymentStatus;

    // which QuickBook variation we are showing
    // Kyle would like to make these different quick book implementations because it hasn't been easy to read when altogether
    private CheckoutMode checkoutMode = CheckoutMode.CLASS;
    // the location we have the cart open at
    private Location location;

    // the origin view if supplied
    private @Nullable OriginView sourceOriginView;
    private @Nullable OriginComponent sourceOriginComponent;

    private boolean showFamilyAccountForCatalogItem = false;
    private FamilyAccount selectedFamilyAccount;

    private TextView classTypes;
    private TextView scheduleNameText;
    private TextView classLocationText;
    private TextView dayDateText;
    private TextView scheduleItemTimeText;
    private TextView itemExpirationText;
    private TextView quickBookSubText;
    private TextView apmStatusText;

    private ProgressBar vw_loading;
    private View vw_close;
    private View vw_mainPayContainer;
    private View btn_selectPricingOption;
    private View btn_selectPricingOptionIcon;
    private TextView tv_pricingOptionName;
    private TextView tv_pricingOptionPrice;
    private TextView tv_pricingOptionSlashPrice;
    private TextView tv_totalPrice;
    private TextView tv_orderDetailsSubTotal;
    private TextView tv_orderDetailsDiscount;
    private TextView tv_orderDetailsTotal;
    private TextView tv_orderDetailsTotalLabel;
    private TextView tv_orderDetailsFee;
    private TextView tv_orderDetailsTax;
    private TextView tv_orderDetailsFeeLabel;

    private View btn_paymentButton;
    private TextView tv_paymentButton;
    private ImageView image_paymentButton;
    private TextView tv_paymentType;
    private TextView btn_purchase;
    private TextView tv_tos;
    private View btn_useGC;
    private TextView tv_gcBalance;
    private TextView tv_no_payStatus;
    private TextView tv_no_paySubtext;
    private View tv_no_payDivider;
    private View vw_no_payContainer;
    private TextView btn_no_payButton;
    private TextView tv_current_pass_name;
    private TextView tvMember;
    private View vw_current_pass_click_area;
    private View vw_pass_name_divider;
    private View vw_current_pass_label;
    private View orderTotal;
    private View orderDetailServiceFeeRow;
    private View orderPaymentDetailsContainer;
    private View clBookForLayout;
    private View contractDetailsLayout;
    private TextView tvContractStartDate;
    private TextView tvContractPaymentAmount;
    private TextView tvContractDueDate;
    private TextView tvContractDuration;
    private TextView tvContractRenews;
    private View gpContractRenews;
    private View gpContractRecurring;
    private View gpDetailsDiscount;
    private View gpContractDuration;
    private PaymentKeyManager keyManager;
    private boolean isGooglePayReady = false;
    private boolean suppressPurchaseConfirmationDialog = false;

    private View vwNonContractHeaderGroup;

    // any available passes the user has on file
    private PaymentOptions passOptions;

    private ViewHandler viewHandler;
    // a flag to set up the UI for the current catalog items after the dialog has been created
    private boolean postponeSettingCatalogItems = false;

    // region Callbacks

    private TaskCallback<QuickBookViewModel.BusinessDetailsSuccessData> businessDetailsSuccessCallback;

    // a callback for after we have booked the user into a class (may be waitlisted)
    private TaskCallback<CTOQuickBookResponse> successCallback;

    // a callback for after a deal has been purchase
    private TaskCallback<DealQuickBookResponse> dealSuccessCallback;

    // a callback for after we have booked the user into an appointment at the requested time range
    private TaskCallback<AppointmentQuickBookResponse> appointmentSuccessCallback;

    // a callback for after we have booked the user into a contract
    private TaskCallback<CatalogPackage> contractSuccessCallback;

    // any failure case will go through this callback
    private TaskCallback<QuickbookFailureReason> failureCallback;

    // a callback after user selects family member
    private TaskCallback<Map<String, Object>> familyMemberSelectionCallback;

    // A reference to which pass (owned or currently purchasing) that will be used to book the user into the class/appointment
    // NOTE: this may or may not be in the cart (since it might be an available pass) as opposed to
    //  selectedCartItem which is guaranteed to be in the cart already
    private CatalogItemOrPassWrapper selectedPassOrCatalog = null;

    private PricingReference pricingReference = null;

    private QuickBookViewModel viewModel;

    private MaterialOptionDialog flexLimitDialog;

    private PaymentSheet paymentSheet;

    private GooglePayLauncher googlePayLauncher;

    private String paymentIntentId = "";

    private boolean isFirstTimeUIRefresh = true;

    private final Response.ErrorListener apiCallErrorListener = volleyError -> {
        boolean dismissOnFailure = true;
        if (volleyError != null && volleyError.networkResponse != null && volleyError.networkResponse.statusCode == 400) {
            String data = new String(volleyError.networkResponse.data);
            ErrorCodeResponse dataGson = SafeGson.fromJson(data, ErrorCodeResponse.class);

            String errorMessage = null;
            Integer errorCode = 0;
            if (dataGson != null) {
                errorMessage = parseErrorResponse(dataGson);
                errorCode = dataGson.ErrorCode;
            } else {
                MBApiErrorResponse error = SafeGson.fromJson(data, MBApiErrorResponse.class);
                if (error != null) {
                    errorMessage = error.getDetail();
                }
            }

            if (errorCode.equals(FLEX_LIMIT_ERROR_CODE)) {
                dismissOnFailure = false;
                setLoading(false);
                flexLimitDialog = viewModel.getFlexLimitDialog();
                flexLimitDialog.show(getChildFragmentManager(), "FlexLimitDialog");
            } else if (errorMessage != null) {
                BreadcrumbsUtils.INSTANCE.recordHandledException(new Exception("ServerErrorToast"),
                    errorMessage,
                    LoginTrackingUtils.getLoggedInState());
                ToastUtils.show(errorMessage);
            } else {
                BreadcrumbsUtils.INSTANCE.recordHandledException(new Exception("ServerErrorToast"),
                    "apiCallErrorListener",
                    LoginTrackingUtils.getLoggedInState());
                ToastUtils.show(getString(R.string.server_error_dialog_message));
            }

        } else if (volleyError instanceof ClassUnavailableError) {
            BreadcrumbsUtils.INSTANCE.recordHandledException(new Exception("ServerErrorToast"),
                getString(R.string.class_unavailable_error_message),
                LoginTrackingUtils.getLoggedInState());
            ToastUtils.show(getString(R.string.class_unavailable_error_message));
        } else if (failureCallback == null) {
            ToastUtils.showServerErrorToast();
        } else if (volleyError != null) {
            // Ensures MBSalesAPI errors are also sent as breadcrumbs
            BreadcrumbsUtils.INSTANCE
                .breadcrumbTriggerVolleyError(volleyError, "");
        }
        failureCallback(dismissOnFailure, QuickbookFailureReason.OTHER);
    };

    // This will be assigned before each class booking, and will fall back on the default
    private @NotNull Response.ErrorListener lastSetClassBookingErrorListener = apiCallErrorListener;

    private final TaskCallback<Map<PaymentMethod, BigDecimal>> splitCcCallback = new TaskCallback<Map<PaymentMethod, BigDecimal>>() {
        @Override
        public void onTaskComplete(final Map<PaymentMethod, BigDecimal> splitResult) {
            setLoading(true);
            apmStatusText.setVisibility(GONE);
            if (splitResult.size() == 1) {
                PaymentMethod selectedPaymentMethod = splitResult.keySet().iterator().next();
                chosenPaymentMethod = selectedPaymentMethod;
                if (chosenPaymentMethod.isCreditCard())
                    setSingleCCPaymentMethod(selectedPaymentMethod);
                else if (isAlternatePaymentMethod(chosenPaymentMethod)) {
                    //Removing previous intent Id if present
                    paymentIntentId = "";
                    // Fire event for alternate payment method selection
                    APMEventTracker.trackAPMSelectEvent(chosenPaymentMethod.getName(),
                            (isStripePollingFlowEnabled()),
                            location, cart, catalogPackage);
                    logStripeBreadCrumb(new HashMap<String, Object>() {{
                        put("step", "payment_method_selection");
                    }}, paymentIntentId, checkoutMode == CheckoutMode.CONTRACT);
                    if (checkoutMode == CheckoutMode.CONTRACT) {
                        setPaymentMethodForContract(selectedPaymentMethod);
                    } else {
                        setPaymentMethod(selectedPaymentMethod);
                    }
                }
            } else {
                PaymentUtils.setCCPaymentMethodsAndAmountsForCart(viewModel.getRequests(), cart,
                    splitResult, location.getSiteId(),location.getId(), succeeded -> {
                        if (succeeded == null || !succeeded) {
                            BreadcrumbsUtils.INSTANCE
                                .breadcrumbGenericError("Error adding payment methods");
                            apiCallErrorListener.onErrorResponse(null);
                            return;
                        }

                        refreshCart(result -> setLoading(false));
                    });
            }
            if (chosenPaymentMethod.isAlternatePayment() && isStripePollingFlowEnabled()) {
                quickBookSubText.setText(getString(R.string.apm_message_for_polling, PaymentUtils.getTotalPollingTimeInMinutes()));
                quickBookSubText.setVisibility(VISIBLE);
            }
        }
    };
    private final View.OnClickListener pricingOptionsOnListener = new OnClickListener() {
        @Override public void onClick(View v) {
            if (checkoutMode == CheckoutMode.CLASS || checkoutMode == CheckoutMode.APPT) {
                if (waitingForPurchaseApiToFinish.get()) {
                    passPurchaseOptionPickerDialogWaiting.set(true);
                    setLoading(true);
                } else {
                    showPassPricingOptionsPicker();
                }
            }
        }
    };

    private SharedPaymentDataViewModel sharedPaymentDataViewModel;

    public static QuickBookDialog newInstance(@NotNull ClassTypeObject classTypeObject, FamilyAccount selectedFamilyAccount,
                                              @Nullable OriginView originView, @Nullable OriginComponent originComponent) {
        return newInstance(classTypeObject.getId(), classTypeObject.getLocation().getSiteId(),
                classTypeObject.getDspoPrice() != null,
                classTypeObject instanceof Enrollment ? CServiceCategoryType.Enrollment : CServiceCategoryType.Class,
                classTypeObject.getPricingReference(), selectedFamilyAccount, classTypeObject.getTimeZone(), originView, originComponent);
    }

    // endregion

    /**
     * @param classInstanceId the instance id of the class. This will be fetched later as a {@link
     * FavoriteClass}
     * @param siteId the site id the class is at
     * @param isLastMinuteOfferWorkflow whether or not this is a LMO
     * @param programType the {@link CServiceCategoryType}. We won't know this because we will use a
     * {@link FavoriteClass}
     * @param originView optionally add the origin view
     * @return a new instance of the {@link QuickBookDialog}
     */
    public static QuickBookDialog newInstance(long classInstanceId, int siteId,
                                              boolean isLastMinuteOfferWorkflow, @NotNull CServiceCategoryType programType,
                                              PricingReference pricingReference, FamilyAccount selectedFamilyAccount,
                                              TimeZone classTimeZone,
                                              @Nullable OriginView originView,
                                              @Nullable OriginComponent originComponent) {
        QuickBookDialog dialog = new QuickBookDialog();
        Bundle bundle = new Bundle();
        bundle.putLong(Constants.KEY_BUNDLE_CLASS_INSTANCE_ID, classInstanceId);
        bundle.putInt(Constants.KEY_BUNDLE_SITEID, siteId);
        bundle.putBoolean(Constants.KEY_BUNDLE_IS_LMO_WORKFLOW, isLastMinuteOfferWorkflow);
        bundle.putInt(Constants.KEY_BUNDLE_PROGRAM_TYPE, programType.ordinal());
        if (pricingReference != null) {
            bundle.putString(Constants.KEY_BUNDLE_PRICING_REFERENCE, SafeGson.toJson(pricingReference));
        }
        bundle.putParcelable(KEY_BUNDLE_SELECTED_FAMILY_ACCOUNT, selectedFamilyAccount);
        if (classTimeZone != null) {
            bundle.putString(KEY_BUNDLE_CLASS_TIME_ZONE_ID, classTimeZone.toZoneId().toString());
        }
        if (originView != null) {
            bundle.putSerializable(Constants.KEY_BUNDLE_ORIGIN_VIEW, originView);
            bundle.putSerializable(Constants.KEY_BUNDLE_ORIGIN_COMPONENT, originComponent);
        }
        dialog.setArguments(bundle);
        return dialog;
    }

    public static QuickBookDialog newInstance(Location location, @Nullable String catalogItem, AppointmentBookabilityStatus bookabilityStatus,
                                              AppointmentType appointmentType, String instructorName, boolean isRequest,
                                              CharSequence notesAndInstructions, Calendar selectedTime, int durationInMinutes,
                                              @Nullable OriginView originView,
                                              @Nullable OriginComponent originComponent) {

        QuickBookDialog dialog = new QuickBookDialog();
        Bundle bundle = new Bundle();
        bundle.putParcelable(Constants.KEY_BUNDLE_LOCATION, location);
        if (catalogItem != null) {
            bundle.putString(Constants.KEY_BUNDLE_CATALOG_ITEM_ID, catalogItem);
        }
        bundle.putLong(Constants.KEY_BUNDLE_CALENDAR_START_TIME, selectedTime.getTimeInMillis());
        bundle.putInt(Constants.KEY_BUNDLE_DURATION, durationInMinutes);
        bundle.putString(Constants.KEY_BUNDLE_STAFF_NAME, instructorName);
        bundle.putBoolean(Constants.KEY_BUNDLE_IS_REQUEST, isRequest);
        bundle.putString(Constants.KEY_BUNDLE_NOTES_AND_INSTRUCTIONS,
            notesAndInstructions == null ? null : notesAndInstructions.toString());

        if (originView != null) {
            bundle.putSerializable(Constants.KEY_BUNDLE_ORIGIN_VIEW, originView);
        }
        if (originComponent != null) {
            bundle.putSerializable(Constants.KEY_BUNDLE_ORIGIN_COMPONENT, originComponent);
        }
        bundle.putParcelable(Constants.KEY_BUNDLE_APPOINTMENT_TYPE, appointmentType);
        bundle.putSerializable(Constants.KEY_BUNDLE_APPOINTMENT_BOOKABILITY_STATUS, bookabilityStatus);

        dialog.setArguments(bundle);
        return dialog;
    }

    private void showPassPricingOptionsPicker() {
        passPurchaseOptionPickerDialogWaiting.set(false);
        final SimpleCatalogItemPickerDialog dialog = new SimpleCatalogItemPickerDialog();
        // If a class is virtual, then block the Purchase a Pass section, unless there are zero dollar passes that can be selected
        if (classTypeObject != null && classTypeObject.isVirtual()) {
            dialog.setCatalogItems(viewModel.getZeroDollarPassOptions(catalogItems), location, passOptions, selectedPassOrCatalog);
        } else {
            dialog.setCatalogItems(catalogItems, location, passOptions, selectedPassOrCatalog);
        }
        // check if the selected payment method is ideal or not | If ideal then disable the Gift Card option
        if (chosenPaymentMethod != null && isAlternatePaymentMethod(chosenPaymentMethod)) {
            dialog.disableGiftCard();
        } else {
            // This code is only for cases where gift card is enabled.
            dialog.setGiftCardTotal(paymentConfiguration.getTotalGCBalance(), location.getLocale());
            // Set gift card unchecked for dependent family account
            // setting the apply gift card flag status based on the gift card presence in cart.
            userSelectApplyGC = cart != null && cart.hasGCPaymentItems();
            dialog.setUseGiftCardChecked(isDependentUserSelected() ? false : userSelectApplyGC);
        }
        if (flexLimitDialog != null) {
            dialog.setOnDismissListener(dialog1 -> flexLimitDialog.dismiss());
        }
        dialog.setItemSelectedCallback(itemResult -> {
            if (flexLimitDialog != null) flexLimitDialog.dismiss();
            switch (itemResult != null ? itemResult.getPaymentType() : DATA_TYPE_UNKNOWN) {
                case DATA_TYPE_PASS:
                    if (!itemResult.equals(selectedPassOrCatalog) || flexLimitDialog != null) {
                        selectedPassOrCatalog = itemResult;
                        if (null != bookablePaymentStatus) {
                            bookablePaymentStatus.setCode(HAS_PAYMENT_METHOD);
                        }
                        if (null != viewHandler) {
                            viewHandler.setViewState(ViewState.USE_AVAILABLE_PASSES_BOOK);
                        }
                        refreshUIBasedOnViewState();
                    }
                    break;
                case DATA_TYPE_CATALOG:
                    if (!itemResult.equals(selectedPassOrCatalog) || userSelectApplyGC != dialog.useGiftCardButtonChecked()) {
                        userSelectApplyGC = dialog.useGiftCardButtonChecked();
                        btn_useGC.setActivated(userSelectApplyGC);
                        selectedPassOrCatalog = itemResult;
                        if (null != bookablePaymentStatus) {
                            bookablePaymentStatus.setCode(REQUIRES_PAYMENT);
                        }
                        determineViewState();
                        refreshOrSetSelectedCatalogItem((CatalogItem) itemResult.getCatalogItemOrPass());
                    }
                    break;
                default:
                    MBLog.e(TAG, "Catalog/Pass Item picker returned back a data type of unknown");
                    break;

            }
        });
        dialog.show(getChildFragmentManager(), "QuickBookCatalogItemSelectionDialog");
    }

    public void setSuppressPurchaseConfirmationDialog(boolean suppress) {
        suppressPurchaseConfirmationDialog = suppress;
    }
    // region MBDialogFragment implementation

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Fragment child = getChildFragmentManager().findFragmentByTag(SplitCreditCardsDialog.class.getSimpleName());
        viewModel = new ViewModelProvider(this).get(QuickBookViewModel.class);
        getSelectedFamilyAccount();
        // Fetching user email as it will be used for stripe payments
        viewModel.fetchUserEmail();
        instantiateObservers();

        if (child instanceof SplitCreditCardsDialog) {
            attachSplitListeners((SplitCreditCardsDialog) child);
        }
        keyManager = new PaymentKeyManager(getContext());
        if (BuildConfig.DEBUG)
            com.stripe.android.PaymentConfiguration.init(getContext(), keyManager.getKeyForCountryAndEnvironment(Constants.US, Constants.DEVELOPMENT));
        else
            com.stripe.android.PaymentConfiguration.init(getContext(), keyManager.getKeyForCountryAndEnvironment(Constants.US, Constants.PRODUCTION));
        paymentSheet = new PaymentSheet(this, this::onPaymentSheetResult);

        googlePayLauncher = createGooglePayLauncher();
        sharedPaymentDataViewModel = new ViewModelProvider(requireActivity(), new SharedViewModelFactory())
                .get(SharedPaymentDataViewModel.class);
    }

    private GooglePayLauncher createGooglePayLauncher() {
        GooglePayEnvironment googleEnvironment;
        if (BuildConfig.DEBUG) {
            googleEnvironment = GooglePayEnvironment.Test;
        } else {
            googleEnvironment = GooglePayEnvironment.Production;
        }
        return new GooglePayLauncher(
                this,
                new GooglePayLauncher.Config(
                        googleEnvironment,
                        Constants.MERCHANT_COUNTRY_CODE,
                        Constants.MERCHANT_DISPLAY_NAME,
                        false,
                        new GooglePayLauncher.BillingAddressConfig(),
                        false,
                        true
                ),
                this::onGooglePayReady,
                this::onGooglePayResult
        );
    }

    // This method is to set value to selectedAccount in view model. When previous class sends
    // null value in argument we need to fetch user data and set it as default selected account
    private void getSelectedFamilyAccount() {
        if (FamilyAccountsUtilsKt.isFamilyAccountType()) {
            if (getArguments() != null && getArguments().getParcelable(KEY_BUNDLE_SELECTED_FAMILY_ACCOUNT) != null) {
                viewModel.setSelectedAccount(getArguments().getParcelable(KEY_BUNDLE_SELECTED_FAMILY_ACCOUNT));
            } else {
                viewModel.getUser();
            }
        }
    }

    private void instantiateObservers() {
        viewModel.getClassTypeObject().observe(this, this::handleClassTypeObject);
        viewModel.getAddClientToClassResult().observe(this, nullIfSuccess -> {
            if (nullIfSuccess == null) {
                refreshClassAndTriggerSuccess();
            } else {
                AnalyticsUtils.logClassTypeBooking(classTypeObject, bookablePaymentStatus, true);
                lastSetClassBookingErrorListener.onErrorResponse(nullIfSuccess);
            }
        });
        viewModel.getFlexDialogShowPassOptionsClicked().observe(this, showPassOptions -> {
            if (showPassOptions) {
                showPassPricingOptionsPicker();
            } else {
                dismiss();
            }
        });
        viewModel.getFlexDialogLearnMoreClicked().observe(this, learnMoreClicked -> {
            if (learnMoreClicked) {
                IntentUtils.launchWebsite(getActivity(),
                    getActivity().getResources().getString(R.string.flex_limit_learn_more_address));
                dismiss();
            }
        });

        viewModel.getSelectedAccount().observe(this, selectedAccount -> {
            if (selectedAccount != null) {
                tvMember.setText(String.format(getContext().getString(R.string.concatenate_string), selectedAccount.getFirstName(), selectedAccount.getLastName()));
            }
        });
    }

    @NotNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = new Dialog(getActivity(), R.style.DialogSlideAnim);

        Window dialogWindow = dialog.getWindow();
        if (dialogWindow != null) {
            dialogWindow.setLayout(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
            dialogWindow.setGravity(Gravity.BOTTOM);
        }

        if (savedInstanceState == null) {
            Bundle extras = getArguments();
            if (extras != null) {
                pricingReference = SafeGson.fromJson(extras.getString(Constants.KEY_BUNDLE_PRICING_REFERENCE), PricingReference.class);
            }
        }

        Display display = dialog.getWindow().getWindowManager().getDefaultDisplay();
        Point size = new Point();
        display.getSize(size);
        int width = size.x;
        WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
        params.width = width;

        dialog.setCancelable(true);
        dialog.setCanceledOnTouchOutside(true);
        dialog.getWindow().setAttributes(params);

        return dialog;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.view_quickbook, container, false);

        classTypes = root.findViewById(R.id.class_row_type);
        scheduleNameText = root.findViewById(R.id.class_name);
        classLocationText = root.findViewById(R.id.class_location);
        dayDateText = root.findViewById(R.id.class_date);
        scheduleItemTimeText = root.findViewById(R.id.class_time);
        itemExpirationText = root.findViewById(R.id.alacarte_item_expiration);

        vw_close = root.findViewById(R.id.close);
        vw_mainPayContainer = root.findViewById(R.id.quickbook_main_container);
        btn_selectPricingOption = root.findViewById(R.id.quickbook_popt_button);
        btn_selectPricingOptionIcon = root.findViewById(R.id.edit_pricing_option_icon);
        tv_pricingOptionName = root.findViewById(R.id.quickbook_popt_name);
        tv_pricingOptionPrice = root.findViewById(R.id.quickbook_popt_price);
        tv_pricingOptionSlashPrice = root.findViewById(R.id.quickbook_popt_slash_price);
//        vw_discountSlash = root.findViewById(R.id.quickbook_discount_slash);
        tv_totalPrice = root.findViewById(R.id.quickbook_order_total);
        btn_paymentButton = root.findViewById(R.id.quickbook_payment_selector);
        tv_paymentButton = root.findViewById(R.id.quickbook_payment_selector_text);
        image_paymentButton = root.findViewById(R.id.quickbook_payment_selector_image);
        tv_paymentType = root.findViewById(R.id.card_type);
        btn_purchase = root.findViewById(R.id.quickbook_book_button);
        vw_loading = root.findViewById(R.id.quickbook_loading);

        tv_tos = root.findViewById(R.id.quickbook_tos);
        tv_tos.setMovementMethod(LinkMovementMethod.getInstance());

        quickBookSubText = root.findViewById(R.id.quickbook_tos_sub_text);
        quickBookSubText.setMovementMethod(LinkMovementMethod.getInstance());
        apmStatusText = root.findViewById(R.id.text_apm_status);

        orderPaymentDetailsContainer = root.findViewById(R.id.quickbook_order_total_details_container);
        orderTotal = root.findViewById(R.id.quickbook_order_total_row);
        orderDetailServiceFeeRow = root.findViewById(R.id.order_service_fee_row);
        tv_orderDetailsSubTotal = root.findViewById(R.id.order_detail_subtotal_amount);
        tv_orderDetailsDiscount = root.findViewById(R.id.order_detail_discount_amount);
        tv_orderDetailsTotal = root.findViewById(R.id.order_detail_total_amount);
        tv_orderDetailsTotalLabel = root.findViewById(R.id.order_detail_total_label);
        tv_orderDetailsFee = root.findViewById(R.id.order_detail_fee_amount);
        tv_orderDetailsTax = root.findViewById(R.id.order_detail_tax_amount);
        tv_orderDetailsFeeLabel = root.findViewById(R.id.order_detail_fee_label);

        vw_no_payContainer = root.findViewById(R.id.quickbook_main_nopay_container);

        tv_no_payStatus = root.findViewById(R.id.quickbook_nopay_bookability_status);
        tv_no_payStatus.setMovementMethod(LinkMovementMethod.getInstance());

        tv_no_paySubtext = root.findViewById(R.id.quickbook_nopay_bookability_sub_text);
        tv_no_payDivider = root.findViewById(R.id.quickbook_nopay_bookability_divider);

        btn_no_payButton = root.findViewById(R.id.quickbook_book_nopay_button);
        tv_current_pass_name = root.findViewById(R.id.quickbook_current_pass_name);
        vw_current_pass_click_area = root.findViewById(R.id.quickbook_current_pass_click_view_area);
        vw_pass_name_divider = root.findViewById(R.id.quickbook_current_pass_divider);

        btn_useGC = root.findViewById(R.id.quickbook_giftcard_button);
        tv_gcBalance = root.findViewById(R.id.quickbook_gc_balance_value);
        vw_current_pass_label = root.findViewById(R.id.quickbook_current_pass_label);
        tvMember = root.findViewById(R.id.tv_member_name);
        clBookForLayout = root.findViewById(R.id.cl_book_for_layout);
        contractDetailsLayout = root.findViewById(R.id.contract_details_layout);
        vwNonContractHeaderGroup = root.findViewById(R.id.non_contract_header_items_group);
        tvContractStartDate = root.findViewById(R.id.contract_details_start_date);
        tvContractPaymentAmount = root.findViewById(R.id.contract_details_payment_amount);
        tvContractDueDate = root.findViewById(R.id.contract_details_due_date);
        tvContractDuration = root.findViewById(R.id.contract_details_duration);
        tvContractRenews = root.findViewById(
                FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled() ?
                        R.id.contract_details_renews_date_ftc : R.id.contract_details_renews_date);
        gpContractRenews = root.findViewById(
                FeatureFlag.FTC_TERMS_AND_CONDITIONS.isFeatureEnabled() ?
                        R.id.contracts_renews_group_ftc : R.id.contracts_renews_group);
        gpContractRecurring = root.findViewById(R.id.contracts_recurring_payment_group);
        gpDetailsDiscount = root.findViewById(R.id.order_detail_discount_group);
        gpContractDuration = root.findViewById(R.id.contracts_duration_group);

        vw_loading.getIndeterminateDrawable().setColorFilter(
            getColor(R.color.progress_bar_color),
            PorterDuff.Mode.SRC_IN);

        viewHandler = new QuickBookViewHandler(this);
        viewModel.restoreState(savedInstanceState);
        viewModel.setCallback(this);

        tv_pricingOptionSlashPrice.setPaintFlags(tv_pricingOptionSlashPrice.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);

        // region assigning click listeners
        vw_close.setOnClickListener(v -> dismiss());
        // endregion

        return root;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (postponeSettingCatalogItems) {
            postponeSettingCatalogItems = false;
            setCatalogItems(catalogItems);
        }
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        checkForValidInitialDataAndContinue();

        Window dialogWindow = getDialog() != null ? getDialog().getWindow() : null;
        if (dialogWindow != null) {
            dialogWindow.setLayout(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
            dialogWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        viewModel.saveState(outState);
    }

    private void checkForValidInitialDataAndContinue() {
        Bundle arguments = getArguments();
        if (arguments == null) {
            finishActivityCreatedInitialization();
            return;
        }
        sourceOriginView = AndroidUtils.extractSerializableFromArguments(arguments, Constants.KEY_BUNDLE_ORIGIN_VIEW, OriginView.class);
        sourceOriginComponent = AndroidUtils.extractSerializableFromArguments(arguments, Constants.KEY_BUNDLE_ORIGIN_COMPONENT, OriginComponent.class);
        appointmentType = AndroidUtils.extractParcelableFromArguments(arguments, Constants.KEY_BUNDLE_APPOINTMENT_TYPE, AppointmentType.class);
        appointmentBookabilityStatus = AndroidUtils.extractSerializableFromArguments(
                arguments, Constants.KEY_BUNDLE_APPOINTMENT_BOOKABILITY_STATUS,
                AppointmentBookabilityStatus.class
        );
        if (appointmentType != null) {
            location = arguments.getParcelable(Constants.KEY_BUNDLE_LOCATION);
            staffName = arguments.getString(Constants.KEY_BUNDLE_STAFF_NAME);
            catalogItem = SafeGson.fromJson(arguments.getString(Constants.KEY_BUNDLE_CATALOG_ITEM_ID), CatalogItem.class);
            checkoutMode = CheckoutMode.APPT;

            long startTime = arguments.getLong(Constants.KEY_BUNDLE_CALENDAR_START_TIME, 0);
            int duration = arguments.getInt(Constants.KEY_BUNDLE_DURATION, 0);
            if (startTime != 0 && duration != 0) {
                timeRange = new Pair<>(Calendar.getInstance(), Calendar.getInstance());
                timeRange.getFirst().setTimeInMillis(startTime);
                timeRange.getSecond().setTimeInMillis(startTime);
                timeRange.getSecond().add(Calendar.MINUTE, duration);
            }
            viewModel.retrieveUserPurchaseInformation(location);

            finishActivityCreatedInitialization();
        } else if (classTypeObject == null && catalogItem == null) {
            long classInstanceId = arguments.getLong(Constants.KEY_BUNDLE_CLASS_INSTANCE_ID, 0);
            int siteId = arguments.getInt(Constants.KEY_BUNDLE_SITEID, 0);
            isLastMinuteOfferWorkflow = arguments.getBoolean(Constants.KEY_BUNDLE_IS_LMO_WORKFLOW);
            if (classInstanceId != 0 && siteId != 0) {
                viewModel.fetchClassDetails(siteId, classInstanceId, true);
            } else {
                BreadcrumbsUtils.INSTANCE
                    .breadcrumbGenericError("Could not init QuickBookDialog, invalid cto or site id");
                apiCallErrorListener.onErrorResponse(null);
            }
        }
    }

    private void handleClassTypeObject(ClassTypeObjectResponse returnedClassTypeObject) {
        if (returnedClassTypeObject == null || returnedClassTypeObject.getClassTypeObject() == null) {
            apiCallErrorListener.onErrorResponse(new ClassUnavailableError());
            setLoading(false);
            return;
        }
        if (returnedClassTypeObject.getInitialCall()) {
            classTypeObject = returnedClassTypeObject.getClassTypeObject();
            viewModel.retrieveUserPurchaseInformation(classTypeObject.getLocation());
            int masterLocationId = classTypeObject.getLocation().getId();
            locationRepository.getLocation(masterLocationId, result -> {
                location = result;
                if (Looper.myLooper() != Looper.getMainLooper()) {
                    new Handler(Looper.getMainLooper()).post(() ->
                        finishActivityCreatedInitialization());
                } else {
                    finishActivityCreatedInitialization();
                }
                return Unit.INSTANCE;
            }, error -> {
                location = classTypeObject.getLocation();
                finishActivityCreatedInitialization();
                return Unit.INSTANCE;
            }, false, false);
        } else {
            classTypeObject.setVisits(returnedClassTypeObject.getClassTypeObject().getVisits());
            AnalyticsUtils.logClassTypeBooking(classTypeObject, bookablePaymentStatus, false);
            classTypeObject = returnedClassTypeObject.getClassTypeObject();
            if ((classTypeObject.hasVisits() || classTypeObject.onTheWaitlist()) && !classTypeObject.hasClassPassed() &&
                SharedPreferencesUtils.isCalendarSyncEnabled()) {
                CalendarUtils.addUpdateClassVisit(getContext(),
                    DomainObjectUtils.convertToClassTypeVisit(classTypeObject), classTypeObject.getLocation());
            }
            intermediateSuccessCallback(classTypeObject);
        }
        setLoading(false);
    }

    private void finishActivityCreatedInitialization() {
        assignClickListeners();

        if (checkoutMode != null) {
            switch (checkoutMode) {
                case CLASS:
                    setClassTypeObject(classTypeObject);
                    break;
                case APPT:
                    setAppointmentType(appointmentType);
                    break;
                case ALACARTE:
                    setCatalogItem(location, catalogItem);
                    if (FamilyAccountsUtilsKt.isFamilyAccountType() && showFamilyAccountForCatalogItem) {
                        setFamilyMember(selectedFamilyAccount);
                        displayBookForLayoutForCatalogItems(showFamilyAccountForCatalogItem);
                    }
                    break;
                case CONTRACT:
                    setCatalogPackage(location, catalogPackage);
            }
        } else {
            dismiss();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        viewHandler.setQbPaymentsMessaging(this);

        switch (requestCode) {
            case Constants.REQUEST_CODE_REQUIRED_FIELDS:
                if (requiredFieldsStatus == null) {
                    AnalyticsUtils.reportOrThrowException(new RuntimeException("Completed required fields workflow without required fields"));
                    determineViewState();
                    return;
                }
                if (requiredFieldsStatus.requiresLiability) {
                    SharedPreferencesUtils.setIgnoreLiability(false);
                }
                if (resultCode == Activity.RESULT_OK || resultCode == LiabilityReleaseActivity.RESULT_UPLOAD_FAILED) {
                    requiredFieldsStatus = new RequiredFieldsStatus();
                    determineViewState();
                } else if (getView() != null) {
                    Snackbar.make(getView(), getString(R.string.required_fields_error, location.getStudioName()), Snackbar.LENGTH_LONG).show();
                    setLoading(false);
                }
                break;
            case REQUEST_CODE_SCA_PAYMENT:
                runCartItemCheckout(false);
                break;
            default:
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isPaused = false;
        viewHandler.setQbPaymentsMessaging(this);
        viewModel.setCallback(this);

        if (pendingDismissal) {
            pendingDismissal = false;
            dismissAllowingStateLoss();
        }
    }

    @Override
    public void onPause() {
        isPaused = true;
        viewHandler.setQbPaymentsMessaging(null);
        viewModel.setCallback(null);
        cancelRequests();
        super.onPause();
    }

    private void assignClickListeners() {
        btn_selectPricingOption.setOnClickListener(pricingOptionsOnListener);

        btn_useGC.setOnClickListener(v -> {
            userSelectApplyGC = !userSelectApplyGC;
            btn_useGC.setActivated(userSelectApplyGC);
            refreshOrSetSelectedCatalogItem(selectedCartItem.getItem());
        });

        btn_paymentButton.setOnClickListener(v -> showSelectPaymentMethodDialog());

        clBookForLayout.setOnClickListener(v -> showFamilyMembersDialog());

        orderTotal.setOnClickListener(v -> {
            OrderSummaryDialog dialog = new OrderSummaryDialog();
            dialog.setData(selectedCartItem, paymentConfiguration, cart, location.getLocale());
            dialog.show(getChildFragmentManager(), Constants.QB_ORDER_SUMMARY_DIALOG);
        });

        btn_purchase.setOnClickListener(v -> {
            viewModel.trackQuickBookCTAClick(
                    checkoutMode,
                    viewHandler.getCurrentViewState(),
                    location,
                    classTypeObject,
                    catalogItem,
                    catalogPackage,
                    appointmentType,
                    timeRange,
                    appointmentBookabilityStatus
            );
            boolean hasPaymentMethod = chosenPaymentMethod != null;
            if (requiredFieldsStatus != null && requiredFieldsStatus.requiresSomething()) {
                getRequiredField(requiredFieldsStatus);
            } else if (chosenPaymentMethod == null && getAmountToChargeToCC().signum() > 0) {
                showSelectPaymentMethodDialog();
            } else if (hasPaymentMethod) {
                if (checkoutMode == CheckoutMode.CONTRACT && ABHelperUtils.INSTANCE.shouldLogFTCAuditForUSStudio(location.getCountryCode())) {
                    MBLog.i("FTCAuditLog", "captureScreenshotAndUpload called in QuickBookDialog");
                    captureScreenshotAndUpload();
                }
                boolean isContractMode = checkoutMode == CheckoutMode.CONTRACT;
                if (chosenPaymentMethod.isIdeal())
                    getPaymentSheetAppearance(isContractMode, Constants.IDEAL);
                else if (chosenPaymentMethod.isBanContact())
                    getPaymentSheetAppearance(isContractMode, Constants.BANCONTACT);
                else if (chosenPaymentMethod.isKlarna())
                    getPaymentSheetAppearance(isContractMode, Constants.KLARNA);
                else if (chosenPaymentMethod.isGooglePay())
                    getPaymentSheetAppearance(isContractMode, Constants.GOOGLE_PAY);
                else if (chosenPaymentMethod.isTwint())
                    getPaymentSheetAppearance(isContractMode, Constants.TWINT);
                else if (chosenPaymentMethod.isFpx())
                    getPaymentSheetAppearance(isContractMode, Constants.FPX);
                else if (chosenPaymentMethod.isAlipay())
                    getPaymentSheetAppearance(isContractMode, Constants.ALIPAY);
                else if (chosenPaymentMethod.isPayNow())
                    getPaymentSheetAppearance(isContractMode, Constants.PAYNOW);
                else if (chosenPaymentMethod.isWeChatPAY())
                    getPaymentSheetAppearance(isContractMode, Constants.WECHATPAY);
                else if (chosenPaymentMethod.isCreditCard()) {
                    boolean isClassMode = checkoutMode == CheckoutMode.CLASS;
                    boolean isOtherMode = checkoutMode == CheckoutMode.ALACARTE || checkoutMode == CheckoutMode.APPT || checkoutMode == CheckoutMode.CONTRACT;
                    getDialog().setCanceledOnTouchOutside(false);
                    vw_close.setEnabled(false);
                    viewModel.sendPerformanceBookCTASelected();
                    if (isClassMode) {
                        checkClassBookabilityAndPurchase();
                        AnalyticsUtils.logTimingStartEvent("Class booking confirmation");
                    } else if (isOtherMode) {
                        runCartItemCheckout();
                    }
                }
            } else if (checkoutMode != null) {
                if (checkoutMode == CheckoutMode.CONTRACT &&
                        ABHelperUtils.INSTANCE.shouldLogFTCAuditForUSStudio(location.getCountryCode())) {
                    MBLog.i("FTCAuditLog", "captureScreenshotAndUpload called in QuickBookDialog");
                    captureScreenshotAndUpload();
                }
                // This block will get triggered when the amount is 0$.
                getDialog().setCanceledOnTouchOutside(false);
                vw_close.setEnabled(false);
                runCartItemCheckout();
                viewModel.sendPerformanceBookCTASelected();
            }
            apmStatusText.setVisibility(GONE);
        });

        btn_no_payButton.setOnClickListener(view -> {
            viewModel.trackQuickBookCTAClick(
                    checkoutMode,
                    viewHandler.getCurrentViewState(),
                    location,
                    classTypeObject,
                    catalogItem,
                    catalogPackage,
                    appointmentType,
                    timeRange,
                    appointmentBookabilityStatus
            );
            if (requiredFieldsStatus != null && requiredFieldsStatus.requiresSomething()) {
                getRequiredField(requiredFieldsStatus);
            } else if (viewHandler.getCurrentViewState().showPaymentMethods || !viewHandler.getCurrentViewState().canAddUserToSchedule) {
                getContext().startActivity( MBPhoneUtils.getInternationalCallIntent(location,
                    ServiceLocator.getDeviceRepository().getNetworkCountryCode(false)));
            } else if (checkoutMode == CheckoutMode.APPT) {
                // This should never happen. Report an error and close the dialog
                AnalyticsUtils.reportOrThrowException(new IllegalStateException("No pay with appointment quickbook"));
                ToastUtils.showServerErrorToast();
                dismiss();
            } else {
                getDialog().setCanceledOnTouchOutside(false);
                vw_close.setEnabled(false);
                addClientToClass();
                AnalyticsUtils.logTimingStartEvent("Class booking confirmation");
                viewModel.sendPerformanceBookCTASelected();
            }
        });
        vw_current_pass_click_area.setOnClickListener(pricingOptionsOnListener);
    }

    private void showFamilyMembersDialog() {
        FamilyAccount selectedFamilyAccount = viewModel.getSelectedAccount().getValue();
        if (selectedFamilyAccount != null) {
            FamilyAccountSelectionDialogFragment familyAccountDialog =
                FamilyAccountSelectionDialogFragment.newInstance(selectedFamilyAccount);
            familyAccountDialog.show(
                getChildFragmentManager(),
                FamilyAccountSelectionDialogFragment.class.getSimpleName());

            familyAccountDialog.onAccountSelected = (Function1<FamilyAccount, Unit>) familyAccount -> {
                if (familyMemberSelectionCallback != null) {
                    AnalyticsUtils.logEvent("(Quickbook) | Book For Switched User");
                    Map<String, Object> data = new HashMap<>();
                    data.put(KEY_SELECTED_USER, familyAccount);
                    data.put(KEY_CLASS_TYPE_OBJECT, classTypeObject);
                    familyMemberSelectionCallback.onTaskComplete(data);
                }
                return Unit.INSTANCE;
            };
        }
    }

    private void refreshPassOption() {
        if (tv_current_pass_name.getVisibility() == View.VISIBLE && null != selectedPassOrCatalog) {
            tv_current_pass_name.setText(getString(R.string.quickbook_dialog_pass_name,
                ((PaymentOption) selectedPassOrCatalog.getCatalogItemOrPass()).getName()));
        }
    }

    // endregion

    public QuickBookDialog setSuccessCallback(TaskCallback<CTOQuickBookResponse> successCallback) {
        this.successCallback = successCallback;
        return this;
    }

    public QuickBookDialog setDealSuccessCallback(TaskCallback<DealQuickBookResponse> dealSuccessCallback) {
        this.dealSuccessCallback = dealSuccessCallback;
        return this;
    }

    public QuickBookDialog setBusinessDetailsSuccessCallback(TaskCallback<QuickBookViewModel.BusinessDetailsSuccessData> successCallback) {
        this.businessDetailsSuccessCallback = successCallback;
        return this;
    }

    public QuickBookDialog setSelectedFamilyMemberCallback(TaskCallback<Map<String, Object>> familyMemberSelectionCallback) {
        this.familyMemberSelectionCallback = familyMemberSelectionCallback;
        return this;
    }

    public QuickBookDialog setAppointmentSuccessCallback(TaskCallback<AppointmentQuickBookResponse> successCallback) {
        this.appointmentSuccessCallback = successCallback;
        return this;
    }

    public QuickBookDialog setContractSuccessCallback(TaskCallback<CatalogPackage> successCallback) {
        this.contractSuccessCallback = successCallback;
        return this;
    }

    private void intermediateSuccessCallback(CatalogPackage catalogPackage) {
        if (contractSuccessCallback != null) {
            contractSuccessCallback.onTaskComplete(catalogPackage);
        }
        setNotCancelled(true);
        checkDismissalState();
    }

    private void intermediateSuccessCallback(AppointmentType appointmentType, AppointmentBookedResponse response) {
        viewModel.trackCheckoutComplete(location, cart, new AppointmentMetaData(), selectedPassOrCatalog);
        CheckoutEventTracker.trackAppointmentEvent(
                CheckoutKey.BOOKING,
                location,
                cart,
                appointmentType,
                null,
                OriginView.QUICK_BOOK_DIALOG
        );
        if (viewModel.getFirstTimePurchase()) {
            appointmentBookedResponse = response;
            if (appointmentSuccessCallback != null) {
                appointmentSuccessCallback.onTaskComplete(
                        new AppointmentQuickBookResponse(
                                appointmentBookedResponse,
                                cart,
                                chosenPaymentMethod,
                                true,
                                cart != null ? cart.getOrderId() : 0L
                        )
                );
                setNotCancelled(true);
                checkDismissalState();
            }
        } else {
            if (appointmentSuccessCallback != null) {
                appointmentSuccessCallback.onTaskComplete(
                        new AppointmentQuickBookResponse(
                                response,
                                cart,
                                chosenPaymentMethod,
                                false,
                                cart != null ? cart.getOrderId() : 0L
                        )
                );
            }
        }

        TealiumHelper.trackCheckoutAppointment(location, appointmentType, cart, passOptions, staffName);
        setNotCancelled(true);
    }

    @Override public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);

        //Here we need to reset shared preference of selected family accounts when dialog dismisses.
        if (FamilyAccountsUtilsKt.isFamilyAccountType()) {
            SharedPreferencesUtils.setSelectedUserId("");
            SELECTED_USER_TAG = null;
        }
        if (!isNotCancelled()) {
            viewModel.trackCheckoutDismissed(location, cart, classTypeObject != null && classTypeObject.isFreeToEnroll());
        }
    }

    /**
     * the callback that the dialog used to have on the view. It's just a wrapper for our success listener now
     * More specifically, we do a lot of analytics and push the class's visit into the cache
     * (since it now has an upcoming visit and our cache would otherwise be out of date)
     */
    private void intermediateSuccessCallback(ClassTypeObject result) {
        switch (checkoutMode) {
            case CLASS:
                classTypeObject = result;
                if(FeatureFlag.PICK_A_SPOT.isFeatureEnabled() && viewModel.getClassIsPickASpot()) {
                    classTypeObject.setPASClass(true);
                }

                // This is so we don't rely on a refresh of the schedule to trigger a home change
                if (classTypeObject.hasVisits() || classTypeObject.onTheWaitlist()) {
                    MBStaticCache.getInstance().addUpcomingVisit(
                        DomainObjectUtils.convertToClassTypeVisit(classTypeObject));

                    StaticInstance.refreshDataAfterBooking();
                }
                viewModel.trackCheckoutComplete(location, cart, new ClassMetaData(classTypeObject, bookablePaymentStatus), selectedPassOrCatalog);

                String passName = null;
                Integer passId = null;
                if (selectedPassOrCatalog != null && selectedPassOrCatalog.getCatalogItemOrPass() != null) {
                    try {
                        passName = ((PaymentOption) selectedPassOrCatalog.getCatalogItemOrPass()).getName();
                        passId = ((PaymentOption) selectedPassOrCatalog.getCatalogItemOrPass()).getPassOptionId();
                    } catch (ClassCastException ex) {
                    }
                }
                if (passName != null && !passName.isEmpty()) {
                    TealiumHelper.trackCheckoutPassAutoSelect(location, classTypeObject, cart, passName, passId);
                } else {
                    TealiumHelper.trackCheckout(location, classTypeObject, cart, passOptions);
                }
                CheckoutEventTracker.trackClassEvent(
                        CheckoutKey.BOOKING,
                        location,
                        cart,
                        classTypeObject,
                        pricingReference,
                        null
                );
                break;
            case ALACARTE:
                if (!isPaused) {
                    // gift card and deal purchase has a special handling for confirmation screen
                    if (!suppressPurchaseConfirmationDialog) {
                        // Pass purchased
                        DialogUtils.showItemPurchasedConfirmationDialog(
                                getContext(),
                                getFragmentManager(),
                                ConfirmationHUD.A_LA_CARTE_MODE,
                                location,
                                catalogItem,
                                pricingReference,
                                chosenPaymentMethod,
                                cart,
                                null,
                                viewModel.getFirstTimePurchase(),
                                cart != null ? cart.getOrderId() : 0L,
                                null
                        );
                    }
                    setNotCancelled(true);
                    checkDismissalState();

                }
                break;
            default:
        }

        UserDspoIdentifier[] dspoTokens = CartItemUtil.getUserDspoIdentifiers(MBAuth.getUser(), cart);
        if (!Utils.isEmpty(dspoTokens)) {
            // Don't really care about the result, can run in the background
            DynamicPricingApi.postTrackPricingTokenPurchases(dspoTokens, null, null);
        }

        if (viewModel.getFirstTimePurchase()) {
            if (successCallback != null) {
                successCallback.onTaskComplete(
                        new CTOQuickBookResponse(
                                classTypeObject,
                                cart,
                                chosenPaymentMethod,
                                pricingReference,
                                true,
                                cart != null ? cart.getOrderId() : 0L,
                                viewHandler.getCurrentViewState()
                        )
                );
            }
            if (businessDetailsSuccessCallback != null) {
                businessDetailsSuccessCallback.onTaskComplete(
                        new QuickBookViewModel.BusinessDetailsSuccessData(
                                chosenPaymentMethod,
                                cart,
                                true,
                                cart != null ? cart.getOrderId() : 0L
                        )
                );
            }
            if (dealSuccessCallback != null) {
                dealSuccessCallback.onTaskComplete(
                        new DealQuickBookResponse(
                                true,
                                cart != null ? cart.getOrderId() : 0L
                        )
                );
            }
            setNotCancelled(true);
            checkDismissalState();
        } else {
            if (successCallback != null) {
                successCallback.onTaskComplete(
                        new CTOQuickBookResponse(
                                classTypeObject,
                                cart,
                                chosenPaymentMethod,
                                pricingReference,
                                false,
                                cart != null ? cart.getOrderId() : 0L,
                                viewHandler.getCurrentViewState()
                        )
                );
            }
            if (businessDetailsSuccessCallback != null) {
                businessDetailsSuccessCallback.onTaskComplete(
                        new QuickBookViewModel.BusinessDetailsSuccessData(
                                chosenPaymentMethod,
                                cart,
                                false,
                                cart != null ? cart.getOrderId() : 0L
                        )
                );
            }
            setNotCancelled(true);
            checkDismissalState();
        }
    }

    /**
     * A lightweight wrapper around our failure callback that dismisses the dialog beforehand
     */
    private void failureCallback(boolean dismissOnFailure, QuickbookFailureReason failureReason) {
        if (isPaused) {
            pendingDismissal = true;
        } else {
            if (dismissOnFailure) {
                dismiss();
            }
            if (failureCallback != null) {
                failureCallback.onTaskComplete(failureReason);
            }
        }
    }

    private void checkDismissalState() {
        if (isPaused) {
            pendingDismissal = true;
        } else {
            dismiss();
        }
    }

    // knock-on effect of fetching the view state which may make some API calls
    private void setClassTypeObject(ClassTypeObject object) {
        classTypeObject = object;
        location = object.getLocation();
        checkoutMode = CheckoutMode.CLASS;

        if (getView() != null) {
            updateHeader(object);
            location = object.getLocation();
            btn_useGC.setVisibility(GONE);
            determineViewState();
            displayBookForLayout();
        }
    }

    private void displayBookForLayout() {
        clBookForLayout.setVisibility(shouldShowBookForLayout() ? VISIBLE : GONE);
    }

    // We need to show book for layout when its a family account and class is not flex and waistlistable
    private boolean shouldShowBookForLayout() {
        return FamilyAccountsUtilsKt.isFamilyAccountType() && !isFlexOptionEnabled() && (classTypeObject != null && (!classTypeObject.isWaitlistable() || (classTypeObject.isWaitlistable() && FamilyAccountsUtilsKt.isDependentUserSelected())));
    }

    private boolean isFlexOptionEnabled() {
        return classTypeObject != null && classTypeObject.isFlexClass() && MBAuth.getUser() != null && MBAuth.getUser().isFlexSubscriber();
    }

    /**
     * This function displays book for layout for catalog item in passes flow
     *
     * @param showFamilyAccountForCatalogItem if it is true then display bookfor layout
     */
    private void displayBookForLayoutForCatalogItems(Boolean showFamilyAccountForCatalogItem) {
        clBookForLayout.setVisibility(FamilyAccountsUtilsKt.isFamilyAccountType() && showFamilyAccountForCatalogItem ? VISIBLE : GONE);
    }

    // knock-on effect of fetching the view state which may make some API calls
    private void setAppointmentType(AppointmentType appointmentType) {
        if (getView() != null) {
            scheduleNameText.setText(appointmentType.getName());
            classTypes.setVisibility(GONE);
            classLocationText.setText(location.getCombinedSiteLocationName().trim());

            String start = TimeUtils.getTimeFormat(getContext(), false).format(timeRange.getFirst());
            String end = TimeUtils.getTimeFormat(getContext()).format(timeRange.getSecond());
            String classTime = getString(R.string.class_time_string, start, end);

            TimeZone tz = TimeZone.getTimeZone(location.getTimezoneId());

            if (tz != TimeZone.getDefault()) {
                String timeZoneString = tz.getDisplayName(
                    TimeZone.getDefault().inDaylightTime(timeRange.getFirst().getTime()),
                    TimeZone.SHORT
                );

                classTime += " " + timeZoneString;
            }
            setDayDateText(timeRange.getFirst().getTime());
            scheduleItemTimeText.setText(getString(R.string.time_with_instructor, classTime, staffName));

            determineViewState();
        }
    }

    private void updateHeader(ClassTypeObject object) {
        scheduleNameText.setText(object.getName());

        if (object instanceof Enrollment) {
            classTypes.setText(R.string.event_tag);
            classTypes.setVisibility(VISIBLE);
        } else if (!Utils.isEmpty(object.getClassTypes())) {
            classTypes.setText(TextUtils.join("/", object.getClassTypes()));
            classTypes.setVisibility(VISIBLE);
        } else {
            classTypes.setVisibility(GONE);
        }

        String classTime;

        if (object.getStartTime().equals(object.getEndTime())) {
            classTime = getString(R.string.to_be_determined_short);
        } else {
            String start = TimeUtils.getTimeFormat(getContext(), false).format(object.getStartDate());
            String end = object.getShortEndTime(getContext());
            classTime = getString(R.string.class_time_string, start, end).toLowerCase();

           TimeZone classTimeZone = TimeZone.getTimeZone(object.getLocation().getTimezoneId());
            if (classTimeZone != null && !ExtensionFunctionsKt.matches(classTimeZone, TimeZone.getDefault(), object.getStartDate())) {
                String timeZoneString = classTimeZone.getDisplayName(
                    classTimeZone.inDaylightTime(object.getStartDate()),
                    TimeZone.SHORT
                );
                classTime += " " + timeZoneString;
            }
        }

        if (object.getStaff() != null) {
            String instructor = object.getStaff().toString();
            if (instructor != null && !StringsKt.isBlank(instructor)) {
                classTime = getString(R.string.time_with_instructor, classTime, instructor);
            }
        }

        scheduleItemTimeText.setText(classTime);
        setDayDateText(object.getStartDate());

        // TODO: distanceKm is always null. This should be fetched in ClassListFragment if we want it
        setClassLocationText(object.getLocation(), object.getDistanceInKm(), DistanceUnit.KILOMETERS);
    }

    // A misnomer. This initializes the a la carte flow similar to setClassTypeObject an setAppointmentType
    //  (plus it has the same glorious knock-on effect)
    public void initializeCatalogItem(Location location, CatalogItem item) {
        dayDateText.setVisibility(View.GONE);
        checkoutMode = CheckoutMode.ALACARTE;
        viewModel.retrieveUserPurchaseInformation(location);
        if (item == null) return;

        // TODO: set header catalog item for a la carte
        scheduleNameText.setText(item.getName());
        setClassLocationText(location, null, DistanceUnit.MILES);

        String expirationString;
        try {
            expirationString = item.getExpirationString();
        } catch (Exception e) {
            expirationString = null;
        }
        if (!TextUtils.isEmpty(expirationString)) {
            itemExpirationText.setText(getString(R.string.expiration_field, expirationString));
            itemExpirationText.setVisibility(VISIBLE);
        } else {
            itemExpirationText.setVisibility(GONE);
        }

        if (CartItemUtil.isGiftCard(item)) {
            scheduleItemTimeText.setText(getString(R.string.value_gc_concat, PaymentUtils.getFormattedCurrency(
                CartItemUtil.getGiftCardCreditAmount(item), location.getLocale())).toUpperCase());
        } else if (item.getSessionCount() < Constants.UNLIMITED_SESSION_THRESHOLD) {
            scheduleItemTimeText.setText(getResources().getQuantityString(R.plurals.numberOfPasses, item.getSessionCount(),
                item.getSessionCount(), getString(R.string.visit_class_type_string_capitalized)));
        } else {
            scheduleItemTimeText.setText(R.string.unlimited_class_pass);
        }

        //Faking it
        catalogItems = new ArrayList<>(1);
        catalogItems.add(item);

        this.location = location;
        btn_selectPricingOption.setVisibility(GONE);
        btn_useGC.setVisibility(VISIBLE);
        determineViewState();
    }

    private void initializeCatalogPackage(@NotNull Location location, @NotNull CatalogPackage catalogPackage) {
        scheduleNameText.setText(catalogPackage.getName());
        classLocationText.setText(location.getName());
        vwNonContractHeaderGroup.setVisibility(GONE);
        btn_selectPricingOption.setVisibility(GONE);
        contractDetailsLayout.setVisibility(VISIBLE);
        Boolean hasRecurringPayments = ContractsInformationUtilsKt.hasRecurringPayment(catalogPackage);
        gpContractRecurring.setVisibility(hasRecurringPayments ? VISIBLE : GONE);
        if (hasRecurringPayments) {
            tvContractPaymentAmount.setText(PaymentUtils.getFormattedCurrency(
                catalogPackage.getPricing().getAutopayPrice(),
                (location != null && location.getLocale() != null) ? location.getLocale() : Locale.getDefault(),
                false));
            tvContractDueDate.setText(ContractsInformationUtilsKt.fetchDueInformationString(catalogPackage, requireContext()));
        }
        if (catalogPackage.getContractTemplate() != null) {
            if (catalogPackage.getContractTemplate().getStartDate() != null) {
                tvContractStartDate.setText(DateFormatUtils.format(
                    catalogPackage.getContractTemplate().getStartDate(), "MM/dd/yy"));
            }
            Boolean isAutoRenewing = catalogPackage.getContractTemplate().isAutoRenewing();
            gpContractRenews.setVisibility(isAutoRenewing ? VISIBLE : GONE);
            if (isAutoRenewing) {
                tvContractRenews.setText(ContractsInformationUtilsKt.displayRenewalDate(catalogPackage, requireContext()));
            }
        }
        String durationString = ContractsInformationUtilsKt.displayDuration(catalogPackage, requireContext());
        if (durationString != null) {
            tvContractDuration.setText(durationString);
        } else {
            gpContractDuration.setVisibility(GONE);
        }
        determineViewState();
    }

    private void setClassLocationText(Location location, BigDecimal distance, DistanceUnit distanceUnit) {
        String locationString = location.getCombinedSiteLocationName().trim();
        if (distance != null) {
            String distanceString = GeoLocationUtils.getNumDistanceString(
                distanceUnit, distance.doubleValue(), 1, true);
            if (!locationString.isEmpty()) {
                locationString = getString(R.string.location_name_and_distance, locationString, distanceString);
            } else {
                locationString = distanceString;
            }
        }
        classLocationText.setText(locationString);
    }

    /**
     * Sets the date text field in the format of Tuesday, Feb 26
     */
    private void setDayDateText(Date classDate) {
        dayDateText.setVisibility(View.VISIBLE);
        dayDateText.setText(DateUtils.formatDateTime(
            getContext(),
            classDate.getTime(),
            FORMAT_SHOW_WEEKDAY | FORMAT_SHOW_DATE | FORMAT_ABBREV_MONTH));
    }

    /**
     * Used for checking out an a la carte catalog item
     * Ideally, this should be another {@link #newInstance} so we don't start API calls before the UI is ready
     */
    public void setCatalogItem(
            Location location,
            CatalogItem item
    ) {
        catalogItem = item;
        this.location = location;
        checkoutMode = CheckoutMode.ALACARTE;

        if (getView() != null) {
            initializeCatalogItem(location, item);
        }
    }

    public void setCatalogItemTrackingData(
            OriginView originView,
            OriginComponent originComponent
    ) {
        this.sourceOriginView = originView;
        this.sourceOriginComponent = originComponent;
    }

    /**
     * Another bolt on, this time to use for checking out a contract
     */
    public void setCatalogPackage(
            @NotNull Location location,
            @NotNull CatalogPackage catalogPackage
    ) {
        this.catalogPackage = catalogPackage;
        catalogPackage.getContractTemplate().setAgreementTermsAccepted(true);
        this.location = location;
        checkoutMode = CheckoutMode.CONTRACT;
        if (getView() != null) {
            initializeCatalogPackage(location, catalogPackage);
        }
    }

    public void setCatalogPackageTrackingData(
            OriginView originView
    ) {
        this.sourceOriginView = originView;
    }

    /**
     * Sets familyAccount value when user comes from passes flow
     *
     * @param shouldShowFamilyAccount is a boolean variable if it is true then shows book for layout
     * @param selectedFamilyAccount holds selected family member provided from passes flow
     */
    public void setSelectedFamilyAccount(Boolean shouldShowFamilyAccount, FamilyAccount selectedFamilyAccount) {
        showFamilyAccountForCatalogItem = shouldShowFamilyAccount;
        this.selectedFamilyAccount = selectedFamilyAccount;
    }

    /**
     * This method checks the card to see whether it is an exchange card or not.
     * If it is, it will check the balance, and if not sufficient, will ask the
     * user if they want to try anyway or select an additional card.
     *
     * @param paymentResult the payment method the user is trying to add.
     * @param continueNoSplitCallback this callback is used for when the user chooses
     * not to split their MB Card with another card, effectively
     * ignoring the balance warning.
     */
    private void checkMBCardBalance(final PaymentMethod paymentResult, @NotNull final TaskCallback continueNoSplitCallback) {
        if (getAmountToChargeToCC().compareTo(BigDecimal.ZERO) <= 0 || paymentResult == null) {
            continueNoSplitCallback.onTaskComplete();
            return;
        }
        if (paymentResult.isExchangeCard() && getAmountToChargeToCC().compareTo(paymentResult.getBalance()) > 0) {
            MaterialOptionDialog dialog = new MaterialOptionDialog();
            dialog.setText(
                R.string.not_enough_balance,
                R.string.not_enough_mb_card_balance_message,
                R.string.insufficient_balance_select_payment_button,
                R.string.insufficient_balance_mbcard_continue);
            dialog.setCancelCallback(result -> setSingleCCPaymentMethod(chosenPaymentMethod));
            dialog.setButton1Callback(result -> {
                if (Utils.safeDouble(paymentResult.getBalance()) <= 0) {
                    showSelectPaymentMethodDialog();
                } else {
                    addOrFixAmountForPaymentMethod(paymentResult, paymentResult.getBalance(), cartPaymentItem -> refreshCart(ignored -> {
                        showSelectPaymentMethodDialog();
                        setLoading(false);
                    }));
                }
                dialog.setNotCancelled(true);
                dialog.dismiss();
            });
            dialog.setButton2Callback(result -> {
                continueNoSplitCallback.onTaskComplete();
                dialog.setNotCancelled(true);
                dialog.dismiss();
            });
            dialog.show(getChildFragmentManager(), "NotEnoughMBBalanceDialog");
        } else {
            continueNoSplitCallback.onTaskComplete();
        }
    }

    /**
     * Either updates the {@param paymentResult} to account for the given {@param amount} of the cart total
     * or adds it to the cart to cover the same amount
     */
    private void addOrFixAmountForPaymentMethod(PaymentMethod paymentResult, BigDecimal amount, final TaskCallback<CartPaymentItem> taskCallback) {
        setLoading(true);
        if (cart != null) {
            CartPaymentItem existingPayment = cart.findPaymentItem(paymentResult.getToken());
            if (existingPayment != null) {
                existingPayment.getConsumption().setAmount(amount);
                viewModel.getRequests().add(MBSalesApi.updateCartPaymentMethod(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), existingPayment,
                    cartPaymentItem -> {
                        if (taskCallback != null) {
                            taskCallback.onTaskComplete(cartPaymentItem);
                        } else {
                            setLoading(false);
                        }
                    }, apiCallErrorListener));
                return;
            }
        }

        //Run if there is no existing payment
        viewModel.getRequests().add(PaymentUtils.addSetPaymentMethod(location.getSiteId(), location.getId(),
            paymentResult, amount, cartPaymentItem -> {
                if (taskCallback != null) {
                    taskCallback.onTaskComplete(cartPaymentItem);
                } else {
                    setLoading(false);
                }
            }, apiCallErrorListener));
    }

    /**
     * The cart must be refreshed immediately before this method is called.
     */
    private void showSelectPaymentMethodDialog() {
        QuickbookEventTracker.trackQuickbookEditPaymentButtonInteractedEvent(
                location
        );
        SelectCreditCardDialog dialog = new SelectCreditCardDialog();
        dialog.passData(new SelectCCInitializer(paymentConfiguration, chosenPaymentMethod, location,
                checkoutMode, splitCcCallback, this::processPaymentMethodAddition,
                result -> paymentConfiguration = result,isGooglePayReady,shouldHideAllApms()));
        dialog.show(getChildFragmentManager(), SelectCreditCardDialog.TAG);
    }

    private boolean shouldHideAllApms(){
        return checkoutMode == CheckoutMode.CONTRACT && getAmountToChargeToCC().compareTo(BigDecimal.ZERO) == 0;
    }


    private void attachSplitListeners(SplitCreditCardsDialog dialog) {
        dialog.setSuccessCallback(splitCcCallback);
        dialog.setAddedPaymentMethodListener(this::processPaymentMethodAddition);
        dialog.setUpdatedAvailablePaymentsCallback(result -> paymentConfiguration = result);
    }

    private void applyGiftcardBalanceAndRefresh(final TaskCallback callback) {
        setLoading(true);

        final TaskCallback setGCAction = result -> {
            setLoading(true);
            if (selectedCartItem != null && BigDecimal.ZERO.compareTo(selectedCartItem.getPricing().getPaymentAmount()) < 0) {
                PaymentUtils.setGCPaymentMethods(PaymentUtils.getGiftCardPaymentMethods(paymentConfiguration.getPaymentMethods()),
                        selectedCartItem.getPricing().getPaymentAmount(), location.getSiteId(), location.getId(),
                    gcResult -> refreshCart(cartResult -> callback.onTaskComplete()), apiCallErrorListener);
            } else {
                refreshCart(cartResult -> callback.onTaskComplete());
            }
        };

        if (cart != null && cart.getPayments().length > 0) {
            setLoading(true);
            final AtomicInteger reqCountdown = new AtomicInteger(cart.getPayments().length);
            for (CartPaymentItem item : cart.getPayments()) {
                MBSalesApi.removePaymentMethodFromCart(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), item.getId(),
                    httpResponseMessage -> {
                        if (reqCountdown.decrementAndGet() == 0) {
                            setGCAction.onTaskComplete();
                        }
                    }, apiCallErrorListener);
            }
        } else {
            setGCAction.onTaskComplete();
        }
    }

    // The old way of waiting for multiple requests to finish
    private void checkLocalReqsFinished(List<MBRequest> paymentReqs, TaskCallback callback) {
        for (MBRequest req : paymentReqs) {
            if (!req.isDone()) {
                return;
            }
        }
        callback.onTaskComplete();
    }

    // Attempts to book the user into the class before purchasing a pass
    // NOTE: I don' know that it's accurate to run cart item checkout when they have a pass selected
    private void checkClassBookabilityAndPurchase() {
        setLoading(true);
        if (isPassCurrentlySelected()) {
            //Try to add them to class first
            addClientToClass(error -> {

                if (error != null && error.networkResponse != null && error.networkResponse.statusCode == 400) {
                    String data = new String(error.networkResponse.data);

                    ErrorCodeResponse dataGson = SafeGson.fromJson(data, ErrorCodeResponse.class);

                    //Class is full, or other options.  601 means needs payment.
                    if (dataGson != null && dataGson.ErrorCode / 100 == 6 && dataGson.ErrorCode != 601) {
                        terminalCheckoutFailure(parseErrorResponse(dataGson));
                    } else {
                        runCartItemCheckout();
                    }
                } else {
                    BreadcrumbsUtils.INSTANCE.breadcrumbGenericError("Could not add client to class");
                    apiCallErrorListener.onErrorResponse(null);
                }
            });
        } else {
            runCartItemCheckout();
        }
    }

    // called when we are adding a new payment method to the cart
    private void processPaymentMethodAddition(final PaymentMethod paymentResult) {
        apmStatusText.setVisibility(GONE);
        if (paymentResult.getType().equals(PaymentConfiguration.TYPE_CREDIT_CARD)) {
            checkMBCardBalance(paymentResult, result -> setSingleCCPaymentMethod(paymentResult));
        } else {
            //This will trigger the handling of all the payments.  Its tricky with
            //  giftcards so better to wrap it with all the UI updating.

            //Default to use the GC method you just added.
            userSelectApplyGC = true;
            btn_useGC.setActivated(true);
            refreshOrSetSelectedCatalogItem(selectedCartItem.getItem());
        }
    }

    // Updates the UI for the catalog item (eg, after the user changes whether or not they'd like to
    // use their gift card balance) and then adds it to the cart if it's not there already, removing
    // any other catalog item in the process
    private void refreshOrSetSelectedCatalogItem(final CatalogItem result) {
        if (!checkFlexZeroDollarOptions() && (result == null || isPassCurrentlySelected())) {
            setLoading(false);
            return;
        }

        //Try discount first, fall back on online price
        BigDecimal salePrice = result.getSalePrice();
        BigDecimal priceAfterGCDiscount = salePrice.subtract(userSelectApplyGC ? paymentConfiguration.getTotalGCBalance() : BigDecimal.ZERO);
        priceAfterGCDiscount = priceAfterGCDiscount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : priceAfterGCDiscount;

        tv_pricingOptionName.setText(result.getName());
        btn_useGC.setVisibility(salePrice.compareTo(BigDecimal.ZERO) != 0 && paymentConfiguration.getTotalGCBalance().compareTo(BigDecimal.ZERO) > 0 && checkoutMode == CheckoutMode.ALACARTE ? VISIBLE : GONE);
        if (cart!=null && cart.hasGCPaymentItems()) {
            tv_pricingOptionPrice.setText(PaymentUtils.getFormattedCurrency(priceAfterGCDiscount,
                    location.getLocale(), true) + (priceAfterGCDiscount.compareTo(salePrice) == 0 ? "" : "*"));
        } else {
            tv_pricingOptionPrice.setText(PaymentUtils.getFormattedCurrency(salePrice,
                    location.getLocale(), true));
        }
        //This is an IAP check, without it we will wind up showing a blank PassPricingOption selection dialog
        //because IAP requirement is that we not show purchase pass section and a 0 price option is a purchase pass option
        if (!checkFlexZeroDollarOptions() && classTypeObject != null && classTypeObject.isVirtual() && priceAfterGCDiscount.compareTo(BigDecimal.ZERO) == 0) {
            btn_selectPricingOptionIcon.setVisibility(GONE);
            btn_selectPricingOption.setClickable(false);
        }

        if (isPaymentCurrentlySelected() && viewHandler.getCurrentViewState().descriptorText != 0) {
            ViewState viewState = viewHandler.getCurrentViewState();
            tv_tos.setVisibility(View.VISIBLE);
            tv_tos.setText(Html.fromHtml(getString(viewState.descriptorText, getString(viewState.actionText))));
        } else {
            tv_tos.setVisibility(View.GONE);
        }

        tv_pricingOptionSlashPrice.setVisibility(priceAfterGCDiscount.compareTo(salePrice) == 0 ? GONE : VISIBLE);

        if (selectedCartItem != null) {
            setLoading(true);
            if (selectedCartItem.getItem().equals(result)) {
                reapplyPaymentToCartItem();
            } else {
                MBSalesApi.removeItemFromCart(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), selectedCartItem.getId(),
                    aVoid -> addItemToCart(result), apiCallErrorListener);
            }
        } else {
            if (catalogPackage != null) {
                addPackageToCart();
            } else {
                addItemToCart(result);
            }
        }
    }

    /**
     * Sets selected family account in viewmodel and if its null then fetches primary user and updates
     * family account.
     *
     * @param selectedFamilyMember object of FamilyAccount type which is nullable.
     */
    public void setFamilyMember(FamilyAccount selectedFamilyMember) {
        if (selectedFamilyMember != null) {
            viewModel.setSelectedAccount(selectedFamilyMember);
        } else {
            viewModel.getUser();
        }
    }

    // Opens additional UI to allow the user to input the required fields for booking
    public void getRequiredField(@NotNull RequiredFieldsStatus result) {
        StaticInstance.requiredFieldsStatus = result;
        StaticInstance.liabilityRelease = result.liabilityRelease;

        if (classTypeObject != null && classTypeObject.isWaitlistable()) {
            handleSmsConsentRequired(result);
            return;
        }

        if (result.requiresPhone) {
            handlePhoneRequired(result);
        } else if (result.requiresLiability) {
            handleLiabilityWaiverRequired(result);
        }
    }

    private void handleSmsConsentRequired(RequiredFieldsStatus result) {
        handlePhoneRequired(result);
    }

    private void handlePhoneRequired(RequiredFieldsStatus result) {
        AnalyticsUtils.logBusinessEvent("(Quickbook) | tap event", location, "Tap Event", "Review required fields");
        Intent requiredFieldsIntent;
        if (!FamilyAccountsUtilsKt.isFamilyAccountType() && FamilyAccountsUtilsKt.getUserIdForDependentUser() == null) {
            requiredFieldsIntent =
                    RequiredFieldsActivity.newIntent(
                            requireActivity(),
                            ModelTranslationKt.toLocationReference(location),
                            null,
                            null
                    );
        } else {
            FamilyAccount familyAccount = viewModel.getSelectedAccount().getValue();
            String dependentUserFirstName = familyAccount == null ? "" : familyAccount.getFirstName();
            String dependentUserLastName = familyAccount == null ? "" : familyAccount.getLastName();
            requiredFieldsIntent = RequiredFieldsActivity.newIntent(
                    requireActivity(),
                    ModelTranslationKt.toLocationReference(location),
                    dependentUserFirstName,
                    dependentUserLastName
            );
        }

        StaticInstance.requiredFieldsStatus = result;
        startActivityForResult(requiredFieldsIntent, Constants.REQUEST_CODE_REQUIRED_FIELDS);
    }

    private void handleLiabilityWaiverRequired(RequiredFieldsStatus result) {
        AnalyticsUtils.logBusinessEvent("(Quickbook) | tap event", location, "Tap Event", "Review Liability Waiver");
        StaticInstance.liabilityRelease = result.liabilityRelease;

        Intent liabilityIntent;
        if (!FamilyAccountsUtilsKt.isFamilyAccountType() && FamilyAccountsUtilsKt.getUserIdForDependentUser() == null) {
            liabilityIntent = LiabilityReleaseActivity.newIntent(
                    getActivity(),
                    location,
                    OriginView.QUICK_BOOK_DIALOG,
                    OriginComponent.REVIEW_LIABILITY_WAIVER_BUTTON,
                    appointmentType,
                    classTypeObject
            );
        } else {
            FamilyAccount familyAccount = viewModel.getSelectedAccount().getValue();
            String dependentUserFirstName = familyAccount == null ? "" : familyAccount.getFirstName();
            String dependentUserLastName = familyAccount == null ? "" : familyAccount.getLastName();
            liabilityIntent = LiabilityReleaseActivity.newIntent(
                    getActivity(),
                    location,
                    dependentUserFirstName,
                    dependentUserLastName,
                    OriginView.QUICK_BOOK_DIALOG,
                    OriginComponent.REVIEW_LIABILITY_WAIVER_BUTTON,
                    appointmentType,
                    classTypeObject);
        }
        startActivityForResult(liabilityIntent, Constants.REQUEST_CODE_REQUIRED_FIELDS);
        SharedPreferencesUtils.setIgnoreLiability(true);
    }

    /**
     * Fetches the available payment methods from the server and call the method to populate the
     * view with the returned payment methods. If no payment methods are available, the entire
     * bottom section is replaced with an 'add card' button.
     * <p>
     * NOTE: this method also deletes the current cart.
     * <p>
     * TODO: pretty sure the first two calls can be run in parallel while the last one should wait
     * for both
     */
    public void runPurchaseApiCalls() {
        if (ranPurchaseApiCalls) {
            refreshUIBasedOnViewState();
            return;
        } else {
            ranPurchaseApiCalls = true;
        }

        waitingForPurchaseApiToFinish.set(true);

        setLoading(true);

        CartAbandonReason reason = new CartAbandonReason().setAsReset("Quickbook checkout initialization");

        viewModel.getRequests().add(MBSalesApi.deleteCart(location.getSiteId(), reason,
            httpResponseMessage -> viewModel.getRequests().add(PaymentUtils.getAllPaymentMethodsForSite(location.getSiteId(),
                paymentConfiguration -> {
                    setPayments(paymentConfiguration);
                    userSelectApplyGC = paymentConfiguration.getTotalGCBalance().compareTo(BigDecimal.ZERO) > 0d;
                    if (viewHandler.getCurrentViewState() != null && !viewHandler.getCurrentViewState().showPasses) {
                        btn_useGC.setActivated(userSelectApplyGC);
                        tv_gcBalance.setText(PaymentUtils.getFormattedCurrency(
                            paymentConfiguration.getTotalGCBalance(), location.getLocale(), true
                        ));
                    }
                    if (checkoutMode == CheckoutMode.CLASS) {
                        int programTypeOrdinal = CServiceCategoryType.Class.ordinal();
                        if (getArguments() != null) {
                            int argumentOrdinal = getArguments().getInt(Constants.KEY_BUNDLE_PROGRAM_TYPE, programTypeOrdinal);
                            if (0 <= argumentOrdinal && argumentOrdinal < CServiceCategoryType.values().length) {
                                programTypeOrdinal = argumentOrdinal;
                            }
                        }

                        CServiceCategoryType type = CServiceCategoryType.values()[programTypeOrdinal];

                        //DSPOs are only for classes right now
                        final AtomicInteger callCount = new AtomicInteger(
                            type == CServiceCategoryType.Class && Switches.ENABLE_DYNAMIC_PRICING ? 2 : 1
                        );
                        viewModel.getRequests().add(MBSalesApi.getScheduleItemCatalogFeed(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), classTypeObject.getId(), type,
                            0, NUM_RESULTS_PER_PAGE,
                            response -> {
                                catalogItems.addAll(CartItemUtil.getCatalogItems(response.getItems()));
                                if (callCount.decrementAndGet() <= 0) {
                                    setCatalogItems(catalogItems);
                                }
                            }, error -> {
                                if (callCount.decrementAndGet() <= 0) {
                                    setCatalogItems(catalogItems);
                                }
                            }));
                        if (type == CServiceCategoryType.Class && Switches.ENABLE_DYNAMIC_PRICING) {
                            //Get DSPO for class and insert it into the pricing option list
                            viewModel.getRequests().add(ServiceLocator.getConnV3Api().getDynamicServicePricingOptions(location.getSiteId(), classTypeObject.getId(),
                                response -> {
                                    if (response.getData() != null) {
                                        catalogItems.add(response.getData());
                                    }
                                    if (callCount.decrementAndGet() <= 0) {
                                        setCatalogItems(catalogItems);
                                    }
                                    return Unit.INSTANCE;
                                }, error -> {
                                    if (callCount.decrementAndGet() <= 0) {
                                        setCatalogItems(catalogItems);
                                    }
                                    return Unit.INSTANCE;
                                }));
                        }
                    } else if (checkoutMode == CheckoutMode.CONTRACT) {
                        if (catalogPackage != null && catalogPackage.getItems() != null) {
                            setCatalogItems(Arrays.asList(catalogPackage.getItems()));
                        }
                    } else if (checkoutMode == CheckoutMode.ALACARTE) {
                        setCatalogItems(catalogItems);
                    } else if (checkoutMode == CheckoutMode.APPT) {
                        viewModel.getAppointmentPricingOptions(appointmentType, timeRange, location);
                    }
                    refreshUIBasedOnViewState();
                }, apiCallErrorListener, location.getLocale())), apiCallErrorListener, FamilyAccountsUtilsKt.getUserIdForDependentUser()));
    }

    private boolean checkFlexZeroDollarOptions() {
        // If this is a Flex user but there is a zero dollar option, use that instead of burning a pass
        return MBAuth.getUser().isFlexSubscriber() &&
            classTypeObject != null && classTypeObject.isFlexClass() &&
            viewModel.getZeroDollarPassOptions(catalogItems).size() > 0;
    }

    // sets the payment configuration. See the paymentConfiguration field for more info
    private void setPayments(PaymentConfiguration availablePaymentsForCart) {
        paymentConfiguration = availablePaymentsForCart;

        if (!paymentConfiguration.getPaymentMethods().isEmpty()) {
            for (PaymentMethod method : paymentConfiguration.getCreditCards()) {
                PaymentMethod backupMethod = null;
                if (paymentConfiguration.isCardAccepted(location.isExchangeApproved(), method) &&
                    //Check if exchange card, location approved and balance over 0
                    !(method.isExchangeCard() && (!location.isExchangeApproved() || BigDecimal.ZERO.compareTo(method.getBalance()) >= 0)) &&
                    !method.isExpired()) {
                    if (method.isExchangeCard()) {
                        chosenPaymentMethod = method;
                        break;
                    } else if (SharedPreferencesUtils.getLastUsedPaymentMethodToken().equals(method.getUniqueIdentifier())) {
                        chosenPaymentMethod = method;
                    } else {
                        //No previously used and no exchange cards
                        backupMethod = method;
                    }
                }
                if (chosenPaymentMethod == null) chosenPaymentMethod = backupMethod;
            }
            if (chosenPaymentMethod != null) return;
        }

        //No accepted payment methods
        if (getAmountToChargeToCC().signum() > 0) {
            tv_paymentButton.setText(getContext().getString(R.string.please_add_payment_method));
        }
    }

    // Removes all other payment methods (except gift cards) from the cart and sets this one (or
    // tries to find the method if null). Then we determine the view state (which may make additional API calls)
    private void setSingleCCPaymentMethod(@Nullable PaymentMethod method) {
        if (getContext() == null) return;
        //If null, remove all CC payment methods
        if (method == null) {
            if (cart != null && cart.getPayments().length > 0) {
                List<CartPaymentItem> gcItems = new ArrayList<>();
                for (CartPaymentItem item : cart.getPayments()) {
                    if (!item.getPaymentMethod().getType().equals(PaymentConfiguration.TYPE_GIFT_CARD)) {
                        setLoading(true);
                        viewModel.getRequests().add(MBSalesApi.removePaymentMethodFromCart(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), item.getId(),
                            httpResponseMessage -> checkRequestsFinished(), apiCallErrorListener));
                    } else {
                        gcItems.add(item);
                    }
                }
                cart.setPayments(gcItems.toArray(new CartPaymentItem[gcItems.size()]));
            }
            //Contracts may require a payment to be added for recurring payments even when there is nothing due today.
            if (checkoutMode == CheckoutMode.CONTRACT && catalogPackage != null && ContractsInformationUtilsKt.hasRecurringPayment(catalogPackage)) {
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    addContractPaymentMethod();
                }, POST_DELAY_MS);
            } else {
                checkRequestsFinished();
            }
            // update view state in case the gift card can cover the cart
            determineViewState();
        } else {
            chosenPaymentMethod = method;
            SharedPreferencesUtils.setLastUsedCCToken(method.getUniqueIdentifier());

            setLoading(true);
            if (cart == null) {
                viewModel.getRequests().add(PaymentUtils.addSetPaymentMethod(location.getSiteId(),location.getId(),
                    method, getAmountToChargeToCC(),
                    o -> refreshCart(result -> checkRequestsFinished()), apiCallErrorListener));
            } else {
                final TaskCallback setCCAction = result -> {
                    setLoading(true);
                    if (!cart.hasGCPaymentItems()) {
                        viewModel.getRequests().add(PaymentUtils.addPaymentMethod(location.getSiteId(),location.getId(),
                                method, getAmountToChargeToCC(),
                                paymentItem -> {
                                    if (checkoutMode == CheckoutMode.CONTRACT) {
                                        addContractPaymentMethod();
                                    } else {
                                        refreshCart(refreshResult -> checkRequestsFinished());
                                    }
                                }, apiCallErrorListener));
                    } else {
                        viewModel.getRequests().add(PaymentUtils.addSetPaymentMethod(location.getSiteId(),location.getId(),
                                method, getAmountToChargeToCC(),
                                o -> refreshCart(refreshCart -> checkRequestsFinished()), apiCallErrorListener));
                    }
                };
                final AtomicInteger reqPaymentCountdown = new AtomicInteger(cart.getPayments().length);
                boolean found = false;
                boolean alreadyPayment = true;
                CartPaymentItem existingPayment = null;
                for (CartPaymentItem item : cart.getPayments()) {
                    if (isAlternatePaymentMethodType(item.getPaymentMethod().getType())) {
                        alreadyPayment = false;
                        viewModel.getRequests().add(MBSalesApi.removePaymentMethodFromCart(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), item.getId(),
                                httpResponseMessage -> {
                                    if (reqPaymentCountdown.decrementAndGet() == 0) {
                                        setCCAction.onTaskComplete();
                                    }
                                }, apiCallErrorListener));
                    } else if (!item.getPaymentMethod().getType().equals(PaymentConfiguration.TYPE_GIFT_CARD) &&
                            item.getPaymentMethod().getToken().equals(chosenPaymentMethod.getToken())) {
                        found = true;
                        existingPayment = item;
                    } else if (!item.getPaymentMethod().getType().equals(PaymentConfiguration.TYPE_GIFT_CARD)) {
                        alreadyPayment = false;
                        viewModel.getRequests().add(MBSalesApi.removePaymentMethodFromCart(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), item.getId(),
                                httpResponseMessage -> {
                                    if (reqPaymentCountdown.decrementAndGet() == 0) {
                                        setCCAction.onTaskComplete();
                                    }
                                }, apiCallErrorListener));
                    }
                }

                if (!found && alreadyPayment) {
                    setCCAction.onTaskComplete();
                }

                if (found) {
                    viewModel.getRequests().add(MBSalesApi.updateCartPaymentMethod(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), existingPayment,
                            cartPaymentItem -> refreshCart(result -> checkRequestsFinished()), apiCallErrorListener));
                }
            }
        }

        if (chosenPaymentMethod != null) {
            if (checkoutMode == CheckoutMode.CONTRACT) {
                if (chosenPaymentMethod.isCreditCard()) {
                    setupPaymentButtonCreditCard();
                } else if (chosenPaymentMethod != null && chosenPaymentMethod.isIdeal()) {
                    setupPaymentButtonAPM(chosenPaymentMethod, R.string.quickbook_add_payment_button_default_text, R.string.ideal);
                } else if (chosenPaymentMethod != null && chosenPaymentMethod.isBanContact()) {
                    setupPaymentButtonAPM(chosenPaymentMethod, R.string.quickbook_add_payment_button_default_text, R.string.bancontact);
                } else if (chosenPaymentMethod != null && chosenPaymentMethod.isKlarna()) {
                    setupPaymentButtonAPM(chosenPaymentMethod, R.string.quickbook_add_payment_button_default_text, R.string.klarna);
                } else if (chosenPaymentMethod != null && chosenPaymentMethod.isGooglePay()) {
                    setupPaymentButtonAPM(chosenPaymentMethod, R.string.quickbook_add_payment_button_default_text, R.string.google_pay);
                }
            } else {
                BigDecimal salePrice = selectedCartItem.getItem().getSalePrice();
                BigDecimal priceAfterGCDiscount = salePrice.subtract(userSelectApplyGC ? paymentConfiguration.getTotalGCBalance() : BigDecimal.ZERO);
                priceAfterGCDiscount = priceAfterGCDiscount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : priceAfterGCDiscount;

                tv_pricingOptionName.setText(selectedCartItem.getItem().getName());
                btn_useGC.setVisibility(salePrice.compareTo(BigDecimal.ZERO) != 0 && paymentConfiguration.getTotalGCBalance().compareTo(BigDecimal.ZERO) > 0 && checkoutMode == CheckoutMode.ALACARTE ? VISIBLE : GONE);
                if (cart != null && cart.hasGCPaymentItems()) {
                    tv_pricingOptionPrice.setText(PaymentUtils.getFormattedCurrency(priceAfterGCDiscount,
                            location.getLocale(), true) + (priceAfterGCDiscount.compareTo(salePrice) == 0 ? "" : "*"));
                } else {
                    // Refreshing gift card view state
                    if (btn_useGC.getVisibility() == VISIBLE) {
                        btn_useGC.setActivated(userSelectApplyGC);
                    }
                    tv_pricingOptionPrice.setText(PaymentUtils.getFormattedCurrency(salePrice,
                            location.getLocale(), true));
                }
                if (chosenPaymentMethod.isCreditCard()) {
                    setupPaymentButtonCreditCard();
                } else if (chosenPaymentMethod != null && chosenPaymentMethod.isIdeal()) {
                    setupPaymentButtonAPM(chosenPaymentMethod, R.string.quickbook_add_payment_button_default_text, R.string.ideal);
                } else if (chosenPaymentMethod != null && chosenPaymentMethod.isBanContact()) {
                    setupPaymentButtonAPM(chosenPaymentMethod, R.string.quickbook_add_payment_button_default_text, R.string.bancontact);
                } else if (chosenPaymentMethod != null && chosenPaymentMethod.isKlarna()) {
                    setupPaymentButtonAPM(chosenPaymentMethod, R.string.quickbook_add_payment_button_default_text, R.string.klarna);
                } else if (chosenPaymentMethod != null && chosenPaymentMethod.isGooglePay()) {
                    setupPaymentButtonAPM(chosenPaymentMethod, R.string.quickbook_add_payment_button_default_text, R.string.google_pay);
                } else if (chosenPaymentMethod != null && chosenPaymentMethod.isTwint()) {
                    setupPaymentButtonAPM(chosenPaymentMethod, R.string.quickbook_add_payment_button_default_text, R.string.twint);
                }
            }
        } else {
            tv_paymentButton.setText(getString(R.string.quickbook_add_payment_button_default_text));
        }
    }

    private void setupPaymentButtonAPM(PaymentMethod chosenPaymentMethod, int defaultTextResId, int typeTextResId) {
        image_paymentButton.setImageResource(chosenPaymentMethod == null ? 0 : PaymentUtils.getCCIconResource(chosenPaymentMethod.getType(), true));
        tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(typeTextResId));
    }

    private void setupPaymentButtonCreditCard(){
        tv_paymentButton.setVisibility(VISIBLE);
        tv_paymentButton.setText(chosenPaymentMethod == null ? getString(
                R.string.quickbook_add_payment_button_default_text) : getString(R.string.truncated_card_number, chosenPaymentMethod.getCardLastFour()));
        image_paymentButton.setImageResource(chosenPaymentMethod == null ? 0 : PaymentUtils.getCCIconResource(chosenPaymentMethod.getCardType(), true));
        tv_paymentType.setText(chosenPaymentMethod == null ? null : chosenPaymentMethod.getCardType());
    }

    private void setPaymentMethod(@Nullable PaymentMethod method) {
        setLoading(true);
        final TaskCallback setPayAction = result -> {
            setLoading(true);
            addSetPaymentMethod(method);
        };
        if (cart != null && cart.getPayments().length > 0) {
            setLoading(true);
            final AtomicInteger reqCountdown = new AtomicInteger(cart.getPayments().length);
            for (CartPaymentItem item : cart.getPayments()) {
                MBSalesApi.removePaymentMethodFromCart(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), item.getId(),
                        httpResponseMessage -> {
                            if (reqCountdown.decrementAndGet() == 0) {
                                setPayAction.onTaskComplete();
                            }
                        }, apiCallErrorListener);
            }
        } else {
            setPayAction.onTaskComplete();
        }
        determineViewState();
        refreshUIBasedOnViewState();

        BigDecimal salePrice = selectedCartItem.getItem().getSalePrice();
        BigDecimal priceAfterGCDiscount = salePrice.subtract(BigDecimal.ZERO);
        priceAfterGCDiscount = priceAfterGCDiscount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : priceAfterGCDiscount;

        tv_pricingOptionName.setText(selectedCartItem.getItem().getName());
        btn_useGC.setVisibility(salePrice.compareTo(BigDecimal.ZERO) != 0 && paymentConfiguration.getTotalGCBalance().compareTo(BigDecimal.ZERO) > 0 && checkoutMode == CheckoutMode.ALACARTE ? VISIBLE : GONE);
        tv_pricingOptionPrice.setText(PaymentUtils.getFormattedCurrency(selectedCartItem.getItem().getSalePrice(),
                location.getLocale(), true) + (priceAfterGCDiscount.compareTo(salePrice) == 0 ? "" : "*"));
        btn_useGC.setVisibility(GONE);
        tv_paymentButton.setVisibility(GONE);
        tv_paymentButton.setText(chosenPaymentMethod == null ? getString(
                R.string.quickbook_add_payment_button_default_text) : getString(R.string.truncated_card_number, chosenPaymentMethod.getCardLastFour()));
        image_paymentButton.setImageResource(chosenPaymentMethod == null ? 0 : PaymentUtils.getCCIconResource(chosenPaymentMethod.getType(), true));
        if (method.isIdeal())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.ideal));
        else if (method.isBanContact())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.bancontact));
        else if (method.isKlarna())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.klarna));
        else if (method.isGooglePay())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.google_pay));
        else if (method.isTwint())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.twint));
        else if (method.isFpx())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.fpx));
        else if (method.isAlipay())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.alipay));
        else if (method.isPayNow())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.text_paynow));
        else if (method.isWeChatPAY())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.text_wechat));
    }

    Response.Listener<HttpResponseMessage> addSetPaymentMethod(PaymentMethod method) {
        viewModel.getRequests().add(PaymentUtils.addPaymentMethod(location.getSiteId(),location.getId(),
                method, getAmountToChargeToCC(),
                o -> refreshCart(result -> checkRequestsFinished()), apiCallErrorListener));
        return null;
    }

    private void setPaymentMethodForContract(@Nullable PaymentMethod method){
        setLoading(true);
        // we are disabling gift card as ideal payment doesn't support split payment
        userSelectApplyGC = false;

        // This task will be executed once other payments methods are removed
        final TaskCallback setIdealAction = result -> {
            setLoading(true);
            // Adding Ideal as Payment Method
            viewModel.getRequests().add(PaymentUtils.addPaymentMethod(location.getSiteId(),location.getId(),
                    method, getAmountToChargeToCC(),
                    paymentItem -> {
                        // Setting contract payment method
                        addContractPaymentMethod();
                    }, apiCallErrorListener));
        };

        // Removing other payment methods as Ideal is selected
        if (cart != null && cart.getPayments().length > 0) {
            setLoading(true);
            final AtomicInteger reqCountdown = new AtomicInteger(cart.getPayments().length);
            for (CartPaymentItem item : cart.getPayments()) {
                MBSalesApi.removePaymentMethodFromCart(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), item.getId(),
                        httpResponseMessage -> {
                            // Calling onTaskComplete as we removed all payment methods
                            if (reqCountdown.decrementAndGet() == 0) {
                                setIdealAction.onTaskComplete();
                            }
                        }, apiCallErrorListener);
            }
        } else {
            setIdealAction.onTaskComplete();
        }
        // Calling these functions to the set the view state which internally sets the UI part of our cart
        determineViewState();
        refreshUIBasedOnViewState();

        // Gift Card Option is not supported
        btn_useGC.setVisibility(GONE);

        // Hiding the payment button text which is used to show credit cards last 4 digits
        tv_paymentButton.setVisibility(GONE);

        // Setting Ideal payment name and icon
        image_paymentButton.setImageResource(chosenPaymentMethod == null ? 0 : PaymentUtils.getCCIconResource(chosenPaymentMethod.getType(), true));
        if (chosenPaymentMethod.isIdeal())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.ideal));
        else if (chosenPaymentMethod.isGooglePay())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.google_pay));
        else if (chosenPaymentMethod.isBanContact())
            tv_paymentType.setText(chosenPaymentMethod == null ? null : getString(R.string.bancontact));
    }
    /**
     * This adds a ContractPaymentMethod for recurring payments that is not the same as the standard payment
     * for what is owed today.
     */
    private void addContractPaymentMethod() {
        CartPackage contractPackage = ContractsInformationUtilsKt.retrieveContractCartPackage(cart);
        if (ContractsInformationUtilsKt.hasRecurringPayment(catalogPackage)) {
            contractPackage.setContractPaymentMethod(chosenPaymentMethod);
        }
        if (contractPackage != null) {
            viewModel.getRequests().add(MBSalesApi.updatePackageInCart(
                location.getSiteId(), contractPackage,
                FamilyAccountsUtilsKt.getUserIdForDependentUser(),
                response -> {
                    refreshCart(result -> checkRequestsFinished());
                },
                apiCallErrorListener
            ));
        }
    }

    private void addPackageToCart() {
        final AddPackageToCartRequest addPackageToCartRequest = new AddPackageToCartRequest();
        addPackageToCartRequest.setCatalogPackage(catalogPackage);
        viewModel.getRequests().add(MBSalesApi.addPackageToCart(location.getSiteId(),
            FamilyAccountsUtilsKt.getUserIdForDependentUser(),
            addPackageToCartRequest,
            responsePackage -> {
                viewModel.trackContractEvent(ContractTrackingEvent.CONTRACT_CARTED, location, catalogPackage, null);
                reapplyPaymentToCartItem();
            }, error -> {
                failureCallback.onTaskComplete(QuickbookFailureReason.OTHER);
            }));
    }

    // Syncs the cart with the server. Updates the UI to ensure we are displaying the most up-to-date information
    private void refreshCart(final TaskCallback<Cart> taskCallback) {
        viewModel.getRequests().add(MBSalesApi.getCart(location.getSiteId(),
            responseCart -> {
                if (getContext() == null) return;
                cart = responseCart;
                setPriceTexts(location.getLocale());
                btn_paymentButton.setVisibility(checkPayButtonVisibility());

                if (cart.getNumberOfCCPaymentItems() > 1) {
                    tv_paymentButton.setText(getString(R.string.split_label, cart.getNumberOfCCPaymentItems()));
                    image_paymentButton.setImageDrawable(null);
                    tv_paymentType.setText(null);
                } else if (cart.getNumberOfCCPaymentItems() == 1) {
                    //chosen payment method is the only CC
                    for (CartPaymentItem item : cart.getPayments()) {
                        if (item.getPaymentMethod().isCreditCard()) {
                            chosenPaymentMethod = item.getPaymentMethod();
                            break;
                        }
                    }

                    tv_paymentButton.setText(chosenPaymentMethod == null ? getString(
                        R.string.quickbook_add_payment_button_default_text) : getString(R.string.truncated_card_number, chosenPaymentMethod.getCardLastFour()));
                    image_paymentButton.setImageResource(chosenPaymentMethod == null ? 0 : PaymentUtils.getCCIconResource(chosenPaymentMethod.getCardType(), true));
                    tv_paymentType.setText(chosenPaymentMethod == null ? null : chosenPaymentMethod.getCardType());
                    determineViewState();
                }

                taskCallback.onTaskComplete(responseCart);
            }, apiCallErrorListener, FamilyAccountsUtilsKt.getUserIdForDependentUser()));
    }

    private int checkPayButtonVisibility() {
        int calculatedVisibility = getAmountToChargeToCC().compareTo(BigDecimal.ZERO) > 0 ? VISIBLE : GONE;
        if (calculatedVisibility == GONE && checkoutMode == CheckoutMode.CONTRACT && catalogPackage != null) {
            if (ContractsInformationUtilsKt.hasRecurringPayment(catalogPackage)) {
                calculatedVisibility = VISIBLE;
            }
        }
        return calculatedVisibility;
    }

    @Override
    public boolean hasAtLeastOnePaymentMethod() {
        return chosenPaymentMethod != null;
    }
    @Override
    public boolean hasStripeUIPaymentsMethod() {
        return chosenPaymentMethod != null ? isAlternatePaymentMethod(chosenPaymentMethod) : false;
    }

    @Override
    public BigDecimal getAmountToChargeToCC() {
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal gcTotal = cart == null ? BigDecimal.ZERO : cart.getTotalAppliedGiftCardBalance();

        if (selectedCartItem != null) {
            total = selectedCartItem.getTotal().subtract(gcTotal);
        } else if (cart != null) {
            if (cart.getTotals().getTotal() != null) {
                total = cart.getTotals().getTotal().subtract(gcTotal);
            } else {
                total = gcTotal.negate();
            }
        }

        return total.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : total;
    }

    @Override
    public boolean hasRecurringPayment() {
        if (checkoutMode == CheckoutMode.CONTRACT && catalogPackage != null) {
            return ContractsInformationUtilsKt.hasRecurringPayment(catalogPackage);
        } else {
            return false;
        }
    }

    @Override
    public void fetchedPricingOptions(@NotNull List<? extends CatalogItem> catalogItems) {
        // this is a safe cast because we are not adding items to the list
        setCatalogItems((List<CatalogItem>) catalogItems);
    }

    // sets which catalog items are available to the user
    // Also picks a default if none are selected yet
    // OR we can show an empty state (overriding the determine view state because the view handler doesn't handle that ... yet)
    public void setCatalogItems(@Nullable List<CatalogItem> items) {
        waitingForPurchaseApiToFinish.set(false);
        catalogItems = items;

        if (passPurchaseOptionPickerDialogWaiting.get()) {
            setLoading(false);
            showPassPricingOptionsPicker();
        }

        if (!isAdded()) {
            postponeSettingCatalogItems = true;
            return;
        }

        if (StaticInstance.selectedCatalogItem != null) {
            refreshOrSetSelectedCatalogItem(StaticInstance.selectedCatalogItem);
            StaticInstance.selectedCatalogItem = null;
        } else if (checkFlexZeroDollarOptions()) {
            CatalogItem catalogItem = viewModel.getZeroDollarPassOptions(catalogItems).get(0);
            selectedPassOrCatalog = new CatalogItemOrPassWrapper(catalogItem, DATA_TYPE_CATALOG);
            if (null != bookablePaymentStatus) {
                bookablePaymentStatus.setCode(REQUIRES_PAYMENT);
            }
            determineViewState();
            refreshOrSetSelectedCatalogItem((CatalogItem) selectedPassOrCatalog.getCatalogItemOrPass());
        } else if (items != null && !items.isEmpty()) {
            Pair<CatalogItem, Boolean> itemToHasDspo = viewModel.getDefaultCatalogItem(items);
            CatalogItem catalogItem = itemToHasDspo.getFirst();
            if (null == selectedPassOrCatalog) {
                selectedPassOrCatalog = new CatalogItemOrPassWrapper(catalogItem, DATA_TYPE_CATALOG);
            }
            refreshOrSetSelectedCatalogItem(catalogItem);

            if (isLastMinuteOfferWorkflow && !isPassCurrentlySelected()) {
                // Show dialog if DSPO is not the cheapest option
                if (itemToHasDspo.getSecond() && catalogItem.isIntroOffer()) {
                    showIntroPricingDialog();
                }
            } else if (!isPassCurrentlySelected() && catalogItem.isIntroOffer() &&
                (pricingReference != null && !pricingReference.isIntroOffer())) {
                showIntroPricingDialog();
            }
        } else if (checkoutMode != CheckoutMode.APPT && null == selectedPassOrCatalog) {
            // We need to alter the class status as unavailable, overwriting PAYMENT_REQUIRED,
            //  so the user isn't in a weird state where we're thinking we want them to pay
            //  when they can't.  Most likely a site setup issue, but still.
            classTypeObject.setStatus(new BookabilityStatus(BookabilityStatus.UNAVAILABLE, null));
            requiredFieldsStatus = new RequiredFieldsStatus();
            setViewState(ViewState.NO_PRICING_OPTIONS);
        } else if (null == selectedPassOrCatalog) {
            requiredFieldsStatus = new RequiredFieldsStatus();
            setViewState(ViewState.NO_PRICING_OPTIONS);
        } else {
            setLoading(false);
        }
    }

    private void showIntroPricingDialog() {
        AnalyticsUtils.logEvent("(Quickbook - Checkout) | Intro offer dialog shown");
        MaterialOptionDialog dialog = new MaterialOptionDialog();
        dialog.setText(
            R.string.intro_better_price_header,
            R.string.intro_better_price_message,
            R.string.ok_button_text, 0);
        dialog.show(getChildFragmentManager(), null);
    }

    // pushes the selected catalog item to the cart
    private void addItemToCart(CatalogItem item) {
        setLoading(true);

        Response.Listener<CartItem> listener = cartItem -> {
            selectedCartItem = cartItem;
            reapplyPaymentToCartItem();
        };

        // Monetization tracking ID for promoted offers
        item.setMonetizationMasterLocationID(location.getId());

        String dspoToken = item.getMetadataFromSeriesTemplate(CSeriesTemplateKeys.DYNAMIC_PRICING_TOKEN);
        if (!TextUtils.isEmpty(dspoToken) && classTypeObject != null) {
            viewModel.getRequests().add(MBSalesApi.addDspoToCart(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(),
                new DspoRequest(dspoToken, classTypeObject.getId()),
                listener, apiCallErrorListener));
        } else {
            viewModel.getRequests().add(MBSalesApi.addItemToCart(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), item,
                listener, apiCallErrorListener));
        }
    }

    // adjusts payment method amount allocation based on what gift card balance is available
    // NOTE: This resets to only a single chosen payment method or a default one
    // NOTE: and then we determine the view state (before finishing the API calls?! -- seems wrong)
    private void reapplyPaymentToCartItem() {
        Locale locale = location.getLocale();
        setPriceTexts(locale);
        if (userSelectApplyGC && paymentConfiguration.getTotalGCBalance().compareTo(BigDecimal.ZERO) > 0) {
            applyGiftcardBalanceAndRefresh(ignored ->
                checkMBCardBalance(chosenPaymentMethod, ignored1 ->
                    setSingleCCPaymentMethod(getAmountToChargeToCC().compareTo(BigDecimal.ZERO) > 0d ? chosenPaymentMethod : null)));
        } else {
            removeGiftCardsFromCart(ignored ->
                refreshCart(ignored1 ->
                    checkMBCardBalance(chosenPaymentMethod, ignored2 ->
                        setSingleCCPaymentMethod(getAmountToChargeToCC().compareTo(BigDecimal.ZERO) > 0d ? chosenPaymentMethod : null))));
        }
        btn_paymentButton.setVisibility(checkPayButtonVisibility());
        determineViewState();
    }

    // adjusts server version of cart, but does not sync the local one
    private void removeGiftCardsFromCart(final TaskCallback<Cart> taskCallback) {
        if (cart != null && cart.getTotalAppliedGiftCardBalance().compareTo(BigDecimal.ZERO) > 0) {
//                List<CartPaymentItem> leftovers = new ArrayList<>();
            final List<MBRequest> removeGCReqs = new ArrayList<>();
            for (CartPaymentItem item : cart.getPayments()) {
                if (item.getPaymentMethod().getType().equals(PaymentConfiguration.TYPE_GIFT_CARD)) {
                    setLoading(true);
                    removeGCReqs.add(MBSalesApi.removePaymentMethodFromCart(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), item.getId(),
                        httpResponseMessage -> checkLocalReqsFinished(removeGCReqs, taskCallback), apiCallErrorListener));
                }/* else {
                        leftovers.add(item);
                    }*/
            }
        } else {
            taskCallback.onTaskComplete(cart);
        }
    }

    private void runCartItemCheckout() {
        runCartItemCheckout(true);
    }

    // Runs the checkout, then waits 500ms, then begins polling the state of the cart
    private void runCartItemCheckout(boolean sendAnalytics) {
        setLoading(true);
        cancelPolling = false;
        if (sendAnalytics) {
            AnalyticsUtils.logTimingStartEvent("Purchase confirmation");
        }
        MBSalesApi.runCheckout(location.getSiteId(), FamilyAccountsUtilsKt.getUserIdForDependentUser(), new ConsumerCheckoutRequest(
                location.getId(), true, SCAAuthorizationActivity.SCA_CALLBACK_URL),
            httpResponseMessage -> new Handler(Looper.getMainLooper()).postDelayed(this::pollStateOfCart, 500), apiCallErrorListener);
    }

    private void captureScreenshotAndUpload() {
        try {
            if (getContext() == null || getView() == null || this.getDialog() == null || MBAuth.getUser() == null)
                return;
            viewModel.logScreenshotCaptureEvent(FTCBreadCrumbStatus.STARTED, MBAuth.getUser().getId(), cart.getId(),
                    catalogPackage.getId(), location.getSiteId(), null);
            Bitmap bitmap =
                    ScreenshotUtil.INSTANCE.captureFullDialogScreenShot(this.getDialog(), this.getView());
            MBLog.i("FTCAuditLog", "uploadFtcScreenshot called in QuickBookDialog");
            FTCContractScreenshotManager.INSTANCE.addScreenshotAndUpload(bitmap, getContext(),
                    String.valueOf(location.getSiteId()), String.valueOf(catalogPackage.getId()), cart.getId(),
                    String.valueOf(MBAuth.getUser().getId()), FTCScreenShotSource.QUICKBOOK_DIALOG);
            viewModel.logScreenshotCaptureEvent(FTCBreadCrumbStatus.COMPLETED, MBAuth.getUser().getId(), cart.getId(),
                    catalogPackage.getId(), location.getSiteId(), null);
        } catch (Exception e) {
            if (MBAuth.getUser() != null) {
                viewModel.logScreenshotCaptureEvent(FTCBreadCrumbStatus.FAILED, MBAuth.getUser().getId(), cart.getId(),
                        catalogPackage.getId(), location.getSiteId(), e);
            }
            MBLog.e("FTCAuditLog", "error in captureScreenshotAndUpload called in QuickBookDialog " + e.getMessage());
        }
    }

    // I think this method self-documents well
    private void pollStateOfCart() {
        if (getContext() == null || cancelPolling) return;
        viewModel.getRequests().add(MBSalesApi.getCart(location.getSiteId(), responseCart -> {
            if (responseCart != null && responseCart.getState() != null) {
                cart = responseCart;
                MBLog.d("MBDataCheckout", "State of cart: " + responseCart.getState().name() + "(" + responseCart.getState().ordinal() + ")");
                switch (responseCart.getState()) {
                    case Finalized:
                    case Created:
                    case Creating:  //workaround until we get a cart state endpoint,
                        // means that the cart was processed and there is now a new cart.
                        if (checkoutMode == CheckoutMode.CONTRACT
                                && ABHelperUtils.INSTANCE.shouldLogFTCAuditForUSStudio(location.getCountryCode()) && MBAuth.getUser() != null) {
                            MBLog.i("FTCAuditLog", "confirmContractUpload called in QuickBookDialog");
                            viewModel.confirmContractUpload(location.getSiteId(), catalogPackage.getId(), MBAuth.getUser().getId(), cart.getId(), cart.getOrderId());
                        }

                        AnalyticsUtils.runCheckoutAnalytics(cart, location, true);
                        ServiceLocator.getAbTestFramework().track(USER_PURCHASED_EVENT, ABHelperUtils.getUserAttributes());

                        String userFlowPath = UserCheckoutFlowTracking.Companion.getUserFlowPath();
                        if (userFlowPath.contains("searchClasses") || userFlowPath.contains("searchStudios")) {
                            ServiceLocator.getAbTestFramework().track(SEARCH_RESULTS_USER_PURCHASED_EVENT, ABHelperUtils.getUserAttributes());
                        }

                        if (userFlowPath.contains(UserCheckoutFlowTracking.UserFlowEvent.TermAutoSuggest.INSTANCE.getName())
                                || userFlowPath.contains(UserCheckoutFlowTracking.UserFlowEvent.StudioAutoSuggest.INSTANCE.getName())) {
                            ServiceLocator.getAbTestFramework().track(
                                    SEARCH_AUTO_SUGGEST_USER_PURCHASED,
                                    ABHelperUtils.getUserAttributes()
                            );
                        } else if (userFlowPath.contains(UserCheckoutFlowTracking.UserFlowEvent.FullTextSearch.INSTANCE.getName())) {
                            ServiceLocator.getAbTestFramework().track(
                                    SEARCH_FULL_TEXT_USER_PURCHASED,
                                    ABHelperUtils.getUserAttributes()
                            );
                        }

                        if (userFlowPath.contains(UserCheckoutFlowTracking.UserFlowEvent.AllActivitiesCategory.INSTANCE.getName())
                                || userFlowPath.contains(UserCheckoutFlowTracking.UserFlowEvent.CategoryVertical.INSTANCE.getName())
                                || userFlowPath.contains(UserCheckoutFlowTracking.UserFlowEvent.Search.INSTANCE.getName())) {
                            ServiceLocator.getAbTestFramework().track(
                                    SEARCH_USER_PURCHASED,
                                    ABHelperUtils.getUserAttributes()
                            );
                        }

                        Map<String, Object> data = new HashMap<>();
                        data.put(AnalyticsUtils.KEY_USER_FLOW_PATH, userFlowPath);
                        // Pendo
                        AnalyticsUtils.logEvent(AnalyticsUtils.APSALAR_USER_PURCHASED, data);

                        data.put(ORDER_VALUE_KEY, cart.getTotals().getSub());
                        String inventoryId = null, inventoryName = null, inventoryCategory = null;
                        if (classTypeObject != null) {
                            inventoryId = String.valueOf(classTypeObject.getId());
                            inventoryName = classTypeObject.getName();
                            inventoryCategory = classTypeObject.getCategory();
                        } else if (appointmentType != null) {
                            inventoryId = String.valueOf(appointmentType.getId());
                            inventoryName = appointmentType.getName();
                            inventoryCategory = appointmentType.getCategory();
                        }
                        // Tealium
                        TrackingHelperUtils.INSTANCE.trackUserPurchased(data, cart,
                                (CatalogItem) (selectedPassOrCatalog != null ? selectedPassOrCatalog.getCatalogItemOrPass() : null),
                                location, inventoryId, inventoryName, inventoryCategory, classTypeObject, pricingReference,
                                classTypeObject != null ? ServiceType.CLASS :
                                        (appointmentType != null ? ServiceType.APPOINTMENT : ServiceType.UNKNOWN));

                        CartItemUtil.showRemainingGiftCardBalanceSnackbar(getView(), cart, paymentConfiguration.getTotalGCBalance(), location.getLocale());

                        if (checkoutMode == CheckoutMode.CLASS) {
                            // only reachable when class status = REQUIRE_PAYMENT_BOOK
                            // not reachable when booking a class through pass
                            CheckoutEventTracker.trackClassEvent(
                                    CheckoutKey.CHECKOUT,
                                    location,
                                    cart,
                                    classTypeObject,
                                    pricingReference,
                                    viewModel.getFirstTimePurchase()
                            );
                            addClientToClass();
                        } else if (checkoutMode == CheckoutMode.APPT) {
                            // bookable appointments are always book and buy, otherwise unreachable here
                            CheckoutEventTracker.trackAppointmentEvent(
                                    CheckoutKey.CHECKOUT,
                                    location,
                                    cart,
                                    appointmentType,
                                    viewModel.getFirstTimePurchase(),
                                    OriginView.QUICK_BOOK_DIALOG
                            );
                            addClientToAppointment();
                        } else if (checkoutMode == CheckoutMode.CONTRACT && catalogPackage != null) {
                            viewModel.trackContractEvent(ContractTrackingEvent.CONTRACT_PURCHASE_COMPLETE, location, catalogPackage, cart.getOrderId());
                            // Contracts are always checkout events, not booking (QuickBookContract)
                            CheckoutEventTracker.trackContractEvent(
                                    CheckoutKey.CHECKOUT,
                                    location,
                                    cart,
                                    catalogPackage,
                                    viewModel.getFirstTimePurchase()
                            );
                            intermediateSuccessCallback(catalogPackage);
                        } else {
                            // ala carte is always purchase (QuickBookContract)
                            CheckoutEventTracker.trackAlaCarteEvent(
                                    CheckoutKey.CHECKOUT,
                                    location,
                                    cart,
                                    catalogItem,
                                    classTypeObject != null ? ServiceType.CLASS :
                                            (appointmentType != null ? ServiceType.APPOINTMENT : ServiceType.UNKNOWN),
                                    viewModel.getFirstTimePurchase()
                            );
                            intermediateSuccessCallback(classTypeObject);
                        }
                        break;
                    case PaymentAuthenticationRequired:
                        launchScaFlowOrTriggerFailure(responseCart);
                        break;
                    case PaymentAuthenticationFailed: // User failed to authorize SCA payment
                        terminalCheckoutFailure(getString(R.string.sca_authentication_failed));
                        break;
                    case OrderProcessingFailed:  //failure
                    case Abandoned:  //deserted cart
                    case Active: //this pollStateOfCart was called before checkout()
//                                    AnalyticsUtils.logEvent("Quickbook: Order processing failed");
                        showOrderProcessingFailedDialog();
                        break;
                    case Locked:
                    case OrderCreated:
                        new Handler(Looper.getMainLooper()).postDelayed(this::pollStateOfCart, BACKOFF_INITIAL_DELAY);
                }
            } else {
                terminalCheckoutFailure(null);
            }
        }, volleyError -> terminalCheckoutFailure(null), FamilyAccountsUtilsKt.getUserIdForDependentUser()));
    }

    private void launchScaFlowOrTriggerFailure(Cart cart) {
        // Find any SCA URLs that exist
        String scaUrlToSend = OrderUtilKt.getFirstAvailableSCARedirectUrl(cart);

        if (scaUrlToSend != null) {
            final MaterialOptionDialog scaPrompt = new MaterialOptionDialog();
            scaPrompt.setText(
                R.string.authentication_required_header,
                R.string.sca_authentication_required_message,
                R.string.continue_dialog_text,
                R.string.cancel
            );
            scaPrompt.setButton1Callback(result -> {
                startActivityForResult(
                    SCAAuthorizationActivity.newInstance(requireContext(), scaUrlToSend),
                    REQUEST_CODE_SCA_PAYMENT);
                scaPrompt.setNotCancelled(true);
                scaPrompt.dismiss();
            });
            scaPrompt.setHorizontalButtonStyle(true);
            scaPrompt.setCancelCallback(result -> terminalCheckoutFailure(getString(R.string.sca_authentication_failed)));
            scaPrompt.show(getChildFragmentManager(), null);
        } else {
            terminalCheckoutFailure(null);
        }
    }

    private void showOrderProcessingFailedDialog() {
        final MaterialOptionDialog dialog = new MaterialOptionDialog();
        dialog.setText(
            R.string.checkout_fail_title,
            R.string.checkout_fail_message,
            R.string.ok_button_text, -1);
        dialog.setButton1Callback(result -> dialog.dismiss());
        dialog.setCancelCallback(result -> setLoading(false));
        dialog.show(getChildFragmentManager(), "OrderProcessingFailedDialog");
    }

    // adds the client to the class with a default error handler
    private void addClientToClass() {
        Response.ErrorListener interruptError = error -> {
            AnalyticsUtils.logClassTypeBooking(classTypeObject, bookablePaymentStatus, true);
            apiCallErrorListener.onErrorResponse(error);
        };

        addClientToClass(interruptError);
    }

    // adds the client (user) to the class, refreshes the class, and then calls success listeners (or it can error)
    private void addClientToClass(final Response.ErrorListener errorListener) {
        setLoading(true);
        lastSetClassBookingErrorListener = errorListener;
        viewModel.addClientToClass(classTypeObject, location,
            isPassCurrentlySelected() ? selectedPassOrCatalog : null);
    }

    // adds the client (user) to the appointment, then calls onAppointmentBooked or onAppointmentBookingError
    private void addClientToAppointment() {
        StaffReference staffReference = appointmentType.getSelectedStaffReference();
        TimeRange range = new TimeRange();
        range.setStart(timeRange.getFirst());

        Calendar end = (Calendar) timeRange.getFirst().clone();
        end.add(Calendar.MINUTE, staffReference.getDuration());
        range.setEnd(end);

        //TODO make sure this staff
        final BookAppointmentModel model = new BookAppointmentModel(
            appointmentType.getStaffItems() != null && appointmentType.getStaffItems().size() == 1,
            appointmentType.getId(),
            staffReference.getStaffReference(),
            range,
            getArguments().getString(Constants.KEY_BUNDLE_NOTES_AND_INSTRUCTIONS, ""));

        // TODO: migrate out of class
        MbDataService.getServiceInstance().loadAppointmentService().bookAppointment(
            new BookAppointmentModel[]{model}, location, this::onAppointmentBooked,
            this::onAppointmentBookingError);
    }

    // logs and calls success listeners
    @Override
    public void onAppointmentBooked(@NotNull AppointmentBookedResponse response) {
        AnalyticsUtilsKt.logUserBookedEvent(location, "appointment", appointmentType, null, selectedPassOrCatalog);
        StaticInstance.refreshDataAfterBooking();
        intermediateSuccessCallback(appointmentType, response);
    }

    // shows an error dialog
    @Override
    public void onAppointmentBookingError(@NotNull VolleyError error) {
        MaterialOptionDialog errorDialog = new MaterialOptionDialog();
        errorDialog.setText(0, R.string.appointment_not_booked, R.string.ok_button_text, 0);
        errorDialog.setButton1Callback(DialogFragment::dismiss);
        errorDialog.setCancelCallback(result -> {
            errorDialog.dismiss();
            apiCallErrorListener.onErrorResponse(error);
        });
        errorDialog.show(getFragmentManager(), GenericErrorDialog.class.getName());
    }

    // refreshes the class and calls success listeners
    private void refreshClassAndTriggerSuccess() {
        viewModel.fetchClassDetails(location.getSiteId(), classTypeObject.getId(), false);
    }

    private void terminalCheckoutFailure(String message) {
        if (message != null) {
            ToastUtils.show(message);

            BreadcrumbsUtils.INSTANCE.breadcrumbGenericError("Checkout failure " + message);
            failureCallback(true, QuickbookFailureReason.OTHER);
        } else {
            BreadcrumbsUtils.INSTANCE.breadcrumbGenericError("Checkout failure");
            apiCallErrorListener.onErrorResponse(null);
        }
        setLoading(false);
    }

    private void checkRequestsFinished() {
        for (MBRequest req : viewModel.getRequests()) {
            if (!req.isDone()) {
                return;
            }
        }
        viewModel.getRequests().clear();
        setLoading(false);
    }

    // updates whether or not we are loading using the current view state
    public void setLoading(boolean loading) {
        QuickBookContract.ViewState viewState = viewHandler.getCurrentViewState();
        setLoading(loading, viewState);
    }

    /**
     * Show or hide the loading animation, with a side effect of also showing/hiding the
     * pay container and no pay container based upon view state rules
     */
    @SuppressLint({"UnsafeExperimentalUsageError", "UnsafeOptInUsageError"})
    private void setLoading(boolean loading, QuickBookContract.ViewState viewState) {
        if (null == viewState) return;
        viewModel.loaderTriggered(loading);
        vw_loading.setVisibility(loading ? VISIBLE : GONE);
        vw_mainPayContainer.setVisibility(!loading && viewState.showPaymentMethods ? VISIBLE : GONE);
        vw_no_payContainer.setVisibility(!loading && !viewState.showPaymentMethods ? VISIBLE : GONE);

        if (!loading && getDialog() != null) {
            getDialog().setCanceledOnTouchOutside(true);
            vw_close.setEnabled(true);
        }
    }

    public ClassPaymentStatus getBookablePaymentStatus() {
        return bookablePaymentStatus;
    }

    public void runBookableApiCalls(@NotNull final TaskCallback<ClassPaymentStatus> callback) {
        setLoading(true);
        viewModel.getRequests().add(MbDataService.getServiceInstance().loadClassService()
            .getPaymentStatus(classTypeObject.getId(), classTypeObject.getLocation().getSiteId(),
                classPaymentStatus -> {
                    bookablePaymentStatus = classPaymentStatus;
                    callback.onTaskComplete(classPaymentStatus);
                    checkRequestsFinished();
                }, apiCallErrorListener));
    }

    private void runPassOptionsApiCall(int siteId, TaskCallback<PaymentOptions> callback) {
        if (passOptions == null) {
            final AtomicReference<List<PaymentOption>> flexPasses = new AtomicReference<>();
            final AtomicReference<List<PaymentOption>> userPasses = new AtomicReference<>();
            // The gateway API team is working on an issue with classes not being found in CloudSearch.
            // In the meantime, we must avoid gateway API calls for classes/events with no inventory ref json.
            if (classTypeObject.getInventoryRefJson() != null) {
                viewModel.getRequests().add(
                    SwamisAPI.getInstance().getClassPasses(
                        new LocationReference(
                            SwamisAPI.MB_INVENTORY_SOURCE,
                            classTypeObject.getLocation().getInventoryReference()),
                        classTypeObject.getInventoryRefJson(),
                        paymentMethodPassesResponse -> {
                            flexPasses.set(ModelTranslationKt.toDomainModel(paymentMethodPassesResponse, true));
                            checkPassesCompleted(flexPasses, userPasses, callback);
                            return Unit.INSTANCE;
                        }, apiCallErrorListener));
            } else {
                flexPasses.set(new ArrayList<>());
            }

            viewModel.getRequests().add(MbDataService.getServiceInstance().loadClassService()
                .getClassPassOptions(classTypeObject.getId(),
                    siteId,
                    currentPassOptions -> {
                        if (currentPassOptions != null && currentPassOptions.getPassOptions() != null) {
                            userPasses.set(CollectionsKt.arrayListOf(currentPassOptions.getPassOptions()));
                        } else {
                            userPasses.set(Collections.emptyList());
                        }
                        checkPassesCompleted(flexPasses, userPasses, callback);
                    }, apiCallErrorListener));
        } else {
            checkIAPBlock(callback);
        }
    }

    /**
     * Combines 2 sources of pass options into a single object expected by the UI
     *
     * @param flexPasses the user's flex membership passes, if available
     * @param userPasses the user's standard class passes, if available
     * @param callback the successful callback to trigger once complete
     */
    private void checkPassesCompleted(AtomicReference<List<PaymentOption>> flexPasses,
        AtomicReference<List<PaymentOption>> userPasses, TaskCallback<PaymentOptions> callback) {

        if (flexPasses.get() == null || userPasses.get() == null) return;

        passOptions = PassOptionsExtensionKt.combineToDomainModel(flexPasses.get(), userPasses.get());
        if (PassOptionsExtensionKt.hasUsablePassOption(passOptions)) {
            classTypeObject.setStatus(
                classTypeObject.isWaitlistable() ?
                    new BookabilityStatus(BookabilityStatus.WAITLISTABLE, "") :
                    new BookabilityStatus(BookabilityStatus.BOOKABLE, ""));

            bookablePaymentStatus = new ClassPaymentStatus(HAS_PAYMENT_METHOD, "");

            // This will run through the passes check again, with the right assumed status
            determineViewState();
        } else {
            checkIAPBlock(callback);
        }
    }

    private void checkIAPBlock(TaskCallback<PaymentOptions> successCallback) {
        if (classTypeObject.isVirtual()) {
            if (classTypeObject.isFreeToEnroll() ||
                (classTypeObject.getDropInPrice() != null && classTypeObject.getDropInPrice().compareTo(BigDecimal.ZERO) == 0)) {
                successCallback.onTaskComplete(passOptions);
                return;
            }
            if (passOptions == null || passOptions.getPassOptions() == null || passOptions.getPassOptions().length == 0) {
                failureCallback(true, QuickbookFailureReason.IAP);
                return;
            }
        }
        successCallback.onTaskComplete(passOptions);
    }

    public void cancelRequests() {
        cancelPolling = true;
        for (Request req : viewModel.getRequests()) {
            req.cancel();
        }
        viewModel.getRequests().clear();
    }

    private String parseErrorResponse(ErrorCodeResponse response) {

        if (response != null && !TextUtils.isEmpty(response.Message)) {
            String errorTemplate = getContext().getString(R.string.failed_message);
            String errorMessage;

            switch (response.ErrorCode) {
                case 614:
                    if (classTypeObject instanceof Enrollment) {
                        errorMessage = (response.Message).replace(getContext().getString(R.string.class_text), getContext().getString(R.string.event));
                    } else {
                        errorMessage = response.Message;
                    }
                    errorTemplate += errorMessage;
                    break;
                case FLEX_LIMIT_ERROR_CODE:
                    errorTemplate = response.Message;
                    break;
                default:
                    errorMessage = response.Message;
                    errorTemplate += errorMessage;
                    break;
            }
            return errorTemplate;
        } else {
            return null;
        }
    }

    private void determineViewState() {
        @ClassStatus int classStatus = classTypeObject != null ? classTypeObject.getStatus().getId() : BookabilityStatus.ERROR;
        boolean isWaitlistableAndDependentUser = classTypeObject != null && classTypeObject.isWaitlistable() && FamilyAccountsUtilsKt.isDependentUserSelected();
        if (!viewHandler.determineViewState(checkoutMode, classStatus, location.getSiteId(), isLastMinuteOfferWorkflow, isWaitlistableAndDependentUser)) {
            failureCallback(true, QuickbookFailureReason.OTHER);
        }
    }

    @Override
    public void getRequiredFields(int siteId, TaskCallback<RequiredFieldsStatus> callback) {
        if (requiredFieldsStatus != null) {
            callback.onTaskComplete(requiredFieldsStatus);
        } else {
            APIWorkflowUtil.getRequiredFieldsStatus(siteId, result -> {
                requiredFieldsStatus = result;
                callback.onTaskComplete(result);
            });
        }
    }

    @Override
    public void getPaymentStatus(TaskCallback<ClassPaymentStatus> callback) {
        if (bookablePaymentStatus == null) {
            runBookableApiCalls(callback);
        } else {
            callback.onTaskComplete(bookablePaymentStatus);
        }
    }

    @Override
    public void getPassesAvailable(TaskCallback<PaymentOptions> callback) {
        runPassOptionsApiCall(location.getSiteId(), callback);
    }

    @Override
    public void setPaymentStatus(ClassPaymentStatus paymentStatus) {
        bookablePaymentStatus = paymentStatus;
    }

    private void refreshUIBasedOnViewState() {
        if (!isAdded()) {
            return;
        }
        refreshUIBasedOnViewState(viewHandler.getCurrentViewState());
    }

    private void refreshUIBasedOnViewState(@Nullable QuickBookContract.ViewState viewState) {
        if (null == viewState) {
            return;
        }
        CharSequence text = getString(viewState.actionText);
        CharSequence descriptorText = viewState.descriptorText == 0 ? null :
                Html.fromHtml(getString(viewState.descriptorText, text));
        CharSequence extraText = viewState.extraText == 0 ? null :
            Html.fromHtml(getString(viewState.extraText, text));
        if (viewState.showPaymentMethods) {
            btn_purchase.setText(text);
            tv_tos.setText(descriptorText);
            tv_tos.setVisibility(descriptorText != null ? View.VISIBLE : View.GONE);
            quickBookSubText.setText(extraText);
            quickBookSubText.setVisibility(extraText != null ? View.VISIBLE : View.GONE);
            tv_current_pass_name.setVisibility(View.GONE);
            vw_pass_name_divider.setVisibility(View.GONE);
        } else {
            if (viewState.showPasses && isPassCurrentlySelected()) {
                tv_current_pass_name.setVisibility(View.VISIBLE);
                vw_pass_name_divider.setVisibility(View.VISIBLE);
                tv_current_pass_name.setText(
                    getString(R.string.quickbook_dialog_pass_name,
                        ((PaymentOption) selectedPassOrCatalog.getCatalogItemOrPass()).getName()));
                vw_current_pass_label.setVisibility(View.VISIBLE);
            } else {
                if (isFamilyAccountType() && (viewState == ViewState.ALREADY_BOOKED_FOR_USER || viewState == ViewState.ALREADY_BOOKED_AT_SAME_TIME)) {
                    descriptorText = getString(viewState.descriptorText, viewModel.getSelectedAccount().getValue().getFirstName());
                }
                tv_current_pass_name.setVisibility(View.GONE);
                vw_pass_name_divider.setVisibility(View.GONE);
                vw_current_pass_label.setVisibility(View.GONE);
            }
            btn_no_payButton.setText(text);
            tv_no_payStatus.setText(descriptorText);

            if (extraText != null && FeatureFlag.WAITLIST_CONFIRMATION_REBRAND.isFeatureEnabled()) {
                tv_no_payDivider.setVisibility(VISIBLE);
                tv_no_paySubtext.setVisibility(VISIBLE);
                tv_no_paySubtext.setText(extraText);
            } else {
                tv_no_payDivider.setVisibility(GONE);
                tv_no_paySubtext.setVisibility(GONE);
            }

            tv_tos.setVisibility(descriptorText != null ? View.VISIBLE : View.GONE);
            setLoading(false, viewState); //Called for the side effect of show/hide of pay/no pay containers
        }
        if (isFirstTimeUIRefresh) {
            switch (checkoutMode) {
                case APPT:
                    BookingEventTracker.trackAppointmentQuickBookDialogViewedEvent(
                            sourceOriginView,
                            sourceOriginComponent,
                            location,
                            appointmentType,
                            timeRange,
                            appointmentBookabilityStatus
                    );
                    break;
                case CLASS:
                    BookingEventTracker.trackClassQuickBookDialogViewedEvent(
                            sourceOriginView,
                            sourceOriginComponent,
                            location,
                            classTypeObject
                    );
                    break;
                case ALACARTE:
                    BookingEventTracker.trackAlaCarteQuickBookDialogViewedEvent(
                            sourceOriginView,
                            sourceOriginComponent,
                            location,
                            catalogItem
                    );
                    break;
                case CONTRACT:
                    BookingEventTracker.trackContractQuickBookDialogViewedEvent(
                            sourceOriginView,
                            sourceOriginComponent,
                            location,
                            catalogPackage
                    );
            }
            isFirstTimeUIRefresh = false;
        }
    }

    @Override
    public void setViewState(QuickBookContract.ViewState viewState) {
        if (viewState.showPaymentMethods) {
            runPurchaseApiCalls();
        } else {
            PaymentOption usablePass = PassOptionsExtensionKt.getFirstUsablePass(passOptions);
            if (viewState.showPasses && usablePass != null) {
                selectedPassOrCatalog =
                    new CatalogItemOrPassWrapper(usablePass, DATA_TYPE_PASS);
                runPurchaseApiCalls();
            } else {
                refreshUIBasedOnViewState(viewState);
            }
        }
    }

    @Override
    public boolean selectedPricingOptionsIncludeDspo() {
        return selectedCartItem != null && selectedCartItem.getItem() != null &&
            CartItemUtil.isDspo(selectedCartItem.getItem());
    }

    private @ColorInt
    int getColor(@ColorRes int color) {
        return ContextCompat.getColor(getContext(), color);
    }

    public QuickBookDialog setFailureCallback(TaskCallback<QuickbookFailureReason> failureCallback) {
        this.failureCallback = failureCallback;
        return this;
    }


    private boolean isPassCurrentlySelected() {
        return (null != selectedPassOrCatalog && selectedPassOrCatalog.getPaymentType() == DATA_TYPE_PASS);
    }

    private boolean isPaymentCurrentlySelected() {
        return (null != selectedPassOrCatalog && selectedPassOrCatalog.getPaymentType() == DATA_TYPE_CATALOG);
    }

    /**
     * Set the text values for the order total.  If the consumer service fee feature test is disabled
     * or the user does not qualify, then just show the total price in the order total row.  If the
     * feature test is enabled and user qualifies, then instead show a subtotal, service fee, tax,
     * and order total in the order payment details container.
     */
    private void setPriceTexts(@NotNull Locale locale) {
        BigDecimal totalPrice;
        Double tax;
        BigDecimal subTotal;
        setServiceFeeVisibility(false);
        if (checkoutMode == CheckoutMode.CONTRACT) {
            tv_orderDetailsTotalLabel.setText(R.string.qb_order_details_total_label);
        }
        if (cart != null && !cart.hasGCPaymentItems()) {
            totalPrice = cart.getTotals().getTotal();
            tax = PaymentUtils.round(Utils.safeDouble(cart.getTotals().getTax()), 2);
            subTotal = cart.getTotals().getSub();
            setServiceFeeVisibility(true);
            displayPricesWithServiceFee(totalPrice, subTotal, locale, tax, cart.getTotals().getDiscount());
            initServiceFeeIconClickListener();
            tv_totalPrice.setText(PaymentUtils.getFormattedCurrency(totalPrice, locale, true));
        } else {
            totalPrice = getAmountToChargeToCC();
            if (checkoutMode != CheckoutMode.CONTRACT) {
                tax = PaymentUtils.round(Utils.safeDouble(selectedCartItem.getTax()), 2);
                BigDecimal subtotal = totalPrice.subtract(BigDecimal.valueOf(tax));
                tv_totalPrice.setText(PaymentUtils.getFormattedCurrency(subtotal, locale, true));
            } else {
                tv_totalPrice.setText(PaymentUtils.getFormattedCurrency(totalPrice, locale, true));
            }
        }
    }

    private void displayPricesWithServiceFee(BigDecimal totalPrice, BigDecimal subtotal, Locale locale, double tax, BigDecimal discount) {
        if (cart == null) return;
        List<ServiceFee> serviceFees = Arrays.asList(cart.getServiceFees());
        setServiceFeeVisibility(true);
        tv_orderDetailsSubTotal.setText(PaymentUtils.getFormattedCurrency(subtotal, locale, true));
        if (checkoutMode == CheckoutMode.CONTRACT && discount.compareTo(BigDecimal.ZERO) > 0) {
            gpDetailsDiscount.setVisibility(VISIBLE);
            tv_orderDetailsDiscount.setText(PaymentUtils.getFormattedCurrency(discount, locale, true));
        }
        BigDecimal totalFees = CartUtils.sumServiceFees(cart);
        if (totalFees.compareTo(BigDecimal.ZERO) > 0) {
            orderDetailServiceFeeRow.setVisibility(VISIBLE);
            tv_orderDetailsFeeLabel.setText(serviceFees.get(0).getDescription());
        } else {
            orderDetailServiceFeeRow.setVisibility(GONE);
        }
        tv_orderDetailsFee.setText(PaymentUtils.getFormattedCurrency(totalFees, locale, true));
        tv_orderDetailsTax.setText(PaymentUtils.getFormattedCurrency(tax, locale, true));
        tv_orderDetailsTotal.setText(PaymentUtils.getFormattedCurrency(cart.getTotals().getTotal(), locale, true));
    }

    private void setServiceFeeVisibility(boolean showServiceFeeRow) {
        if (showServiceFeeRow) {
            orderPaymentDetailsContainer.setVisibility(VISIBLE);
            orderTotal.setVisibility(GONE);
        } else {
            orderPaymentDetailsContainer.setVisibility(GONE);
            orderTotal.setVisibility(VISIBLE);
        }
    }

    private void initServiceFeeIconClickListener() {
        if (cart == null || cart.getServiceFees() == null || cart.getServiceFees().length == 0) {
            return;
        }
        orderDetailServiceFeeRow.setOnClickListener(v ->
            GenericCtaDialog.newInstance(
                cart.getServiceFees()[0].getDescription(),
                cart.getServiceFees()[0].getExtendedDescription(),
                getString(R.string.close)
            ).show(getChildFragmentManager(), Constants.QB_ORDER_SUMMARY_DIALOG));
    }

    /**
     * This is to distinguish amongst the different API failures, for notifying the user
     */
    private class ClassUnavailableError extends VolleyError {}

    private void getPaymentSheetAppearance(Boolean isForContract,String type) {
        viewModel.getRequests().add(MBSalesApi.getStripeUIPayment(location.getSiteId(),
                FamilyAccountsUtilsKt.getUserIdForDependentUser(), isForContract,type,
                stripePaymentResponse -> {
                    ArrayList<Templates> templates = stripePaymentResponse.getTemplates();
                    String paymentClientSecret = "";
                    String publishableKey = "";
                    for (Templates template : templates) {
                        ArrayList<Metadata> metadata = template.getMetadata();
                        for (Metadata meta : metadata) {
                            String key = meta.getKey();
                            String value = meta.getValue();

                            switch (key.toLowerCase(Locale.getDefault())) {
                                case "paymentclientsecret":
                                    paymentClientSecret = value;
                                    break;
                                case "publishablekey":
                                    publishableKey = value;
                                    break;
                                case "paymentintentid":
                                    paymentIntentId = value;
                                    break;
                            }
                        }
                    }
                    /*
                    * If the feature flag is enabled, then we will start the StripePaymentActivity which contains the polling approach
                    * Its not available for GooglePay only for Stripe UPE payments
                     */
                    if (isStripePollingFlowEnabled()) {
                        initStripePaymentWithPolling(paymentIntentId, paymentClientSecret, publishableKey, isForContract);
                    } else {
                        initStripePayment(paymentIntentId, paymentClientSecret, publishableKey, isForContract);
                    }
                }, apiCallErrorListener));
    }

    private void initStripePayment(String paymentIntentId, String paymentClientSecret, String publishableKey, boolean isForContract) {
        if (paymentClientSecret != null && (chosenPaymentMethod.isPayNow() || chosenPaymentMethod.isWeChatPAY())) {
            startWebViewPayment(paymentIntentId, paymentClientSecret, publishableKey, isForContract, false);
        } else if (paymentClientSecret != null && chosenPaymentMethod.isGooglePay()) {
            // In case of googlePay stripe initialisation happens before in onCreate().
            APMEventTracker.trackAPMPaymentStartEvent(paymentIntentId, chosenPaymentMethod.getName(), location, cart, catalogPackage, false);
            logStripeBreadCrumb(new HashMap<String, Object>() {{
                put("step", "PaymentSheet.open");
            }}, paymentIntentId, checkoutMode == CheckoutMode.CONTRACT);
            googlePayLauncher.presentForPaymentIntent(paymentClientSecret);
        } else if (paymentClientSecret != null && publishableKey != null) {
            // Initialising the stripe payment configuration
            com.stripe.android.PaymentConfiguration.init(getContext(), publishableKey);

            PaymentSheet.BillingDetails billingDetails = null;
            PaymentSheet.BillingDetailsCollectionConfiguration billingDetailsCollectionConfiguration = new PaymentSheet.BillingDetailsCollectionConfiguration();
            // Setting exclusive contract payments configurations
            if (isForContract || chosenPaymentMethod.isKlarna()) {
                billingDetails = new PaymentSheet.BillingDetails(null, viewModel.getCurrentUserEmail(), "", "");
                PaymentSheet.BillingDetailsCollectionConfiguration.CollectionMode paymentMode;
                if (isForContract)
                    paymentMode = PaymentSheet.BillingDetailsCollectionConfiguration.CollectionMode.Never;
                else
                    paymentMode = PaymentSheet.BillingDetailsCollectionConfiguration.CollectionMode.Automatic;

                billingDetailsCollectionConfiguration = new PaymentSheet.BillingDetailsCollectionConfiguration(
                        PaymentSheet.BillingDetailsCollectionConfiguration.CollectionMode.Automatic,
                        PaymentSheet.BillingDetailsCollectionConfiguration.CollectionMode.Automatic,
                        paymentMode,
                        PaymentSheet.BillingDetailsCollectionConfiguration.AddressCollectionMode.Automatic,
                        true
                );
            }
            APMEventTracker.trackAPMPaymentStartEvent(paymentIntentId, chosenPaymentMethod.getName(), location, cart, catalogPackage, false);

            PaymentSheet.Configuration configuration = new PaymentSheet.Configuration(Constants.MERCHANT_DISPLAY_NAME, null, null, null, billingDetails, null, isForContract, false, customStripeUI(), null, billingDetailsCollectionConfiguration);
            paymentSheet.presentWithPaymentIntent(paymentClientSecret, configuration);
            logStripeBreadCrumb(new HashMap<String, Object>() {{
                put("step", "PaymentSheet.open");
            }}, paymentIntentId, checkoutMode == CheckoutMode.CONTRACT);
        }
    }

    private void initStripePaymentWithPolling(String paymentIntentId, String paymentClientSecret, String publishableKey, boolean isForContract) {
        APMEventTracker.trackAPMPaymentStartEvent(paymentIntentId, chosenPaymentMethod.getName(), location, cart, catalogPackage, true);
        if (chosenPaymentMethod.isPayNow() || chosenPaymentMethod.isWeChatPAY()) {
            startWebViewPayment(paymentIntentId, paymentClientSecret, publishableKey, isForContract, true);
        } else {
            setLoading(true);
            startStripePaymentActivity(paymentIntentId, paymentClientSecret, publishableKey, isForContract);
        }
    }
    private final ActivityResultLauncher<Intent> paymentLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                setLoading(false);
                MBLog.d("Stripe_Polling_Logs", "ActivityResultLauncher: " + result);
                if (result.getResultCode() == Activity.RESULT_OK) {
                    // Handle successful payment
                    MBLog.d("Stripe_Polling_Logs", "ActivityResultLauncher: Checkout called");
                    runCartItemCheckout();
                } else if (result.getResultCode() == Activity.RESULT_CANCELED) {
                    // Handling time out & failure cases only. For other cases no need to show any updates on UI
                    PaymentStatus paymentStatus;
                    if (result.getData() != null) {
                        paymentStatus = result.getData().getParcelableExtra(EXTRA_PAYMENT_STATUS);
                        String paymentMethodName = PaymentUtils.getLocalPaymentMethodName(getContext(), chosenPaymentMethod);
                        if (paymentStatus.getStatus() != null && paymentStatus.getStatus() == PaymentStatus.Status.FAILED) {
                            apmStatusText.setText(getString(R.string.apm_checkout_failed, paymentMethodName));
                            quickBookSubText.setVisibility(GONE);
                            apmStatusText.setVisibility(VISIBLE);
                        } else if (paymentStatus.getStatus() != null && paymentStatus.getStatus() == PaymentStatus.Status.TIMED_OUT) {
                            apmStatusText.setText(getString(R.string.apm_checkout_timed_out, paymentMethodName));
                            quickBookSubText.setVisibility(GONE);
                            apmStatusText.setVisibility(VISIBLE);
                        }
                    }
                }
            }
    );

    private void startStripePaymentActivity(String paymentIntentId, String paymentClientSecret, String publishableKey, boolean isForContract) {
        Intent intent = new Intent(getContext(), StripePaymentActivity.class);
        sharedPaymentDataViewModel.setData(cart,catalogPackage,location);
        intent.putExtra(EXTRA_PAYMENT_DATA, new StripePaymentData(paymentClientSecret, publishableKey, paymentIntentId, chosenPaymentMethod.getName(), viewModel.getCurrentUserEmail(), isForContract, chosenPaymentMethod.isKlarna(), true));
        paymentLauncher.launch(intent);
    }
    private final ActivityResultLauncher<Intent> stripeWebViewLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            this::handleStripePaymentResult
    );


    private void startWebViewPayment(String paymentIntentId, String paymentClientSecret, String publishableKey, boolean isForContract, boolean isPollingEnabled) {
        if (getContext() != null) {
            sharedPaymentDataViewModel.setData(cart, catalogPackage, location);
            stripeWebViewLauncher.launch(StripeWebViewActivity.Companion.createNewIntent(getContext(),
                    new StripePaymentData(paymentClientSecret, publishableKey, paymentIntentId,
                            chosenPaymentMethod.getName(), viewModel.getCurrentUserEmail(), isForContract,
                            chosenPaymentMethod.isKlarna(), isPollingEnabled)));
        }
    }

    private void onPaymentSheetResult(final PaymentSheetResult paymentSheetResult) {
        if (paymentSheetResult instanceof PaymentSheetResult.Completed) {
            runCartItemCheckout();
            APMEventTracker.trackAPMPaymentSuccessEvent(paymentIntentId, chosenPaymentMethod.getName(), false, location, cart, catalogPackage);
            logStripeBreadCrumb(new HashMap<String, Object>() {{
                put("step", "PaymentSheet.Completion");
                put("result", "completed");
            }}, paymentIntentId, checkoutMode == CheckoutMode.CONTRACT);
        } else if (paymentSheetResult instanceof PaymentSheetResult.Canceled) {
            APMEventTracker.trackAPMPaymentCancelledEvent(paymentIntentId, chosenPaymentMethod.getName(), false, location, cart, catalogPackage);
            logStripeBreadCrumb(new HashMap<String, Object>() {{
                put("step", "PaymentSheet.Completion");
                put("result", "canceled");
            }}, paymentIntentId, checkoutMode == CheckoutMode.CONTRACT);
        } else if (paymentSheetResult instanceof PaymentSheetResult.Failed) {
            APMEventTracker.trackAPMPaymentFailEvent(paymentIntentId, chosenPaymentMethod.getName(), false, location, cart, catalogPackage);
            PaymentSheetResult.Failed failedResult = (PaymentSheetResult.Failed) paymentSheetResult;
            logStripeBreadCrumb(new HashMap<String, Object>() {{
                put("step", "PaymentSheet.Completion");
                put("error", failedResult.getError().getMessage());
                put("result", "failed");
            }}, paymentIntentId, checkoutMode == CheckoutMode.CONTRACT);
        }
    }

    private void onGooglePayResult(@NotNull GooglePayLauncher.Result result) {
        if (result instanceof GooglePayLauncher.Result.Completed) {
            runCartItemCheckout();
            APMEventTracker.trackAPMPaymentSuccessEvent(paymentIntentId, chosenPaymentMethod.getName(), false, location, cart, catalogPackage);
            logStripeBreadCrumb(new HashMap<String, Object>() {{
                put("step", "PaymentSheet.Completion");
                put("result", "completed");
            }}, paymentIntentId, checkoutMode == CheckoutMode.CONTRACT);
        } else if (result instanceof GooglePayLauncher.Result.Canceled) {
            APMEventTracker.trackAPMPaymentCancelledEvent(paymentIntentId, chosenPaymentMethod.getName(), false, location, cart, catalogPackage);
            logStripeBreadCrumb(new HashMap<String, Object>() {{
                put("step", "PaymentSheet.Completion");
                put("result", "canceled");
            }}, paymentIntentId, checkoutMode == CheckoutMode.CONTRACT);
        } else if (result instanceof GooglePayLauncher.Result.Failed) {
            APMEventTracker.trackAPMPaymentFailEvent(paymentIntentId, chosenPaymentMethod.getName(), false, location, cart, catalogPackage);
            GooglePayLauncher.Result.Failed failedResult = (GooglePayLauncher.Result.Failed) result;
            logStripeBreadCrumb(new HashMap<String, Object>() {{
                put("step", "PaymentSheet.Completion");
                put("error", failedResult.getError().getMessage());
                put("result", "failed");
            }}, paymentIntentId, checkoutMode == CheckoutMode.CONTRACT);
        }
    }

    private void logStripeBreadCrumb(HashMap<String, Object> attributes, String paymentIntentId, boolean isForContract) {
        attributes.put("paymentMethod", chosenPaymentMethod.getName());
        attributes.put("paymentIntentId", paymentIntentId);
        attributes.put("isFromPollingPaymentFlow", false);
        attributes.put("isContract", isForContract);
        // Recording the breadcrumb
        BreadcrumbsUtils.INSTANCE.recordStripeUPEBreadCrumb(attributes);
    }

    private void onGooglePayReady(boolean isReady) {
        isGooglePayReady = isReady;
    }

    private boolean isStripePollingFlowEnabled() {
        return FeatureFlag.STRIPE_PAYMENT_INTENT_STATUS_POLLING.isFeatureEnabled(ABHelperUtils.getAllUserAttributes()) && !chosenPaymentMethod.isGooglePay();
    }

    private void handleStripePaymentResult(ActivityResult result) {
        Intent data = result.getData();
        if (result.getResultCode() == Activity.RESULT_OK) {
            MBLog.d("Stripe_WebPayment_Logs", "Payment is successful. Proceeding with checkout.");
            runCartItemCheckout();
        } else if (result.getResultCode() == Activity.RESULT_CANCELED) {
            MBLog.d("Stripe_WebPayment_Logs", "Payment Process Cancelled or Failed.");
            cancelPaymentIntentIfPresent(data);
            PaymentStatus paymentStatus = result.getData() != null ? result.getData().getParcelableExtra(EXTRA_PAYMENT_STATUS) : null;
            if (paymentStatus == null) {
                return;
            }
            String paymentMethodName = PaymentUtils.getLocalPaymentMethodName(getContext(), chosenPaymentMethod);
            PaymentStatus.Status status = paymentStatus.getStatus();
            if (status == PaymentStatus.Status.FAILED) {
                apmStatusText.setText(getString(R.string.apm_checkout_failed, paymentMethodName));
                quickBookSubText.setVisibility(View.GONE);
                apmStatusText.setVisibility(View.VISIBLE);
            } else if (status == PaymentStatus.Status.TIMED_OUT) {
                apmStatusText.setText(getString(R.string.apm_checkout_timed_out, paymentMethodName));
                quickBookSubText.setVisibility(View.GONE);
                apmStatusText.setVisibility(View.VISIBLE);
            } else if (status == PaymentStatus.Status.CANCELED) {
                showStripePaymentFailureDialog();
            }
        }
    }

    private void cancelPaymentIntentIfPresent(Intent data) {
        if (data != null && data.hasExtra(StripeWebViewActivity.PAYMENT_INTENT_ID)) {
            String paymentIntentId = data.getStringExtra(StripeWebViewActivity.PAYMENT_INTENT_ID);
            if (paymentIntentId != null) {
                viewModel.cancelPaymentIntent(location, paymentIntentId);
                MBLog.d("Stripe_WebPayment_Logs", "Payment intent cancelled: " + paymentIntentId);
            }
        }
    }

    private void showStripePaymentFailureDialog() {
        if (getContext() != null) {
            CustomDialogAlert customDialog = new CustomDialogAlert(getContext(),
                    getString(R.string.heads_up), getString(R.string.you_have_not_been_charged));
            customDialog.show();
        }
    }

    private PaymentSheet.Appearance customStripeUI() {
        PaymentSheet.Appearance appearance = new PaymentSheet.Appearance.Builder()
                .colorsLight(new PaymentSheet.Colors(
                        getColor(R.color.primary_button_color),//button color
                        getColor(R.color.white),
                        getColor(R.color.white),
                        getColor(R.color.black),//border color
                        getColor(R.color.black),
                        getColor(R.color.black),//text color
                        getColor(R.color.black),// title text color
                        Color.rgb(115, 117, 123),
                        getColor(R.color.light_gray),//hint color
                        getColor(R.color.black),//close button color
                        getColor(R.color.red)))
                .colorsDark(new PaymentSheet.Colors(
                        getColor(R.color.primary_button_color),//button color
                        getColor(R.color.white),
                        getColor(R.color.white),
                        getColor(R.color.black),//border color
                        getColor(R.color.black),
                        getColor(R.color.black),//text color
                        getColor(R.color.black),// title text color
                        Color.rgb(115, 117, 123),
                        getColor(R.color.light_gray),//hint color
                        getColor(R.color.black),//close button color
                        getColor(R.color.red)))
                .shapes(new PaymentSheet.Shapes(
                        4.0f,
                        0.5f))
                .typography(new PaymentSheet.Typography(1.0f,
                        R.font.regular))// Assuming font ID is in your resources
                .primaryButton(new PaymentSheet.PrimaryButton()).build();
        return appearance;
    }

    boolean isAlternatePaymentMethod(PaymentMethod chosenPaymentMethod) {
        return chosenPaymentMethod.isIdeal() ||
                chosenPaymentMethod.isBanContact() ||
                chosenPaymentMethod.isKlarna() ||
                chosenPaymentMethod.isGooglePay() ||
                chosenPaymentMethod.isTwint() ||
                chosenPaymentMethod.isFpx() ||
                chosenPaymentMethod.isAlipay() ||
                chosenPaymentMethod.isPayNow() ||
                chosenPaymentMethod.isWeChatPAY();
    }

    boolean isAlternatePaymentMethodType(String type) {
        return type.equalsIgnoreCase(PaymentConfiguration.TYPE_IDEAL) ||
                type.equalsIgnoreCase(PaymentConfiguration.TYPE_BANCONTACT) ||
                type.equalsIgnoreCase(PaymentConfiguration.TYPE_KLARNA) ||
                type.equalsIgnoreCase(PaymentConfiguration.TYPE_GOOGLEPAY) ||
                type.equalsIgnoreCase(PaymentConfiguration.TYPE_TWINT) ||
                type.equalsIgnoreCase(PaymentConfiguration.TYPE_FPX) ||
                type.equalsIgnoreCase(PaymentConfiguration.TYPE_ALIPAY) ||
                type.equalsIgnoreCase(PaymentConfiguration.TYPE_PAYNOW) ||
                type.equalsIgnoreCase(PaymentConfiguration.TYPE_WECHATPAY);
    }
}
