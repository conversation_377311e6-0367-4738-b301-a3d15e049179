package com.mindbodyonline.connect.analytics.checkout

import com.mindbodyonline.analytics.Event
import com.mindbodyonline.analytics.EventType
import com.mindbodyonline.android.api.sales.model.pos.cart.Cart
import com.mindbodyonline.android.api.sales.model.pos.catalog.CatalogItem
import com.mindbodyonline.android.api.sales.model.pos.packages.CatalogPackage
import com.mindbodyonline.connect.analytics.Business
import com.mindbodyonline.connect.analytics.OriginComponent
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.utils.analytics.AnalyticsLocator
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI
import com.mindbodyonline.connect.utils.viewbinding.CartUtils
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.domain.AppointmentType
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.Enrollment
import com.mindbodyonline.domain.Location
import com.mindbodyonline.domain.dataModels.PricingReference
import java.math.BigDecimal
import java.util.Currency

object CheckoutEventTracker {

    @JvmStatic
    fun trackContractEvent(
            checkoutKey: CheckoutKey,
            location: Location?,
            cart: Cart?,
            catalogPackage: CatalogPackage? = null,
            // Default firstTimePurchase to null to filter it out in booking events
            firstTimePurchase: Boolean? = null,
    ) {
        trackEvent(
                checkoutKey = checkoutKey,
                location = location,
                cart = cart,
                firstTimePurchase = firstTimePurchase,
                purchaseType = ItemType.CONTRACT,
                contract = Contract(
                    id = catalogPackage?.id ?: -1,
                    name = catalogPackage?.name.orEmpty()
                ),
        )
    }

    @JvmStatic
    fun trackAlaCarteEvent(
            checkoutKey: CheckoutKey,
            location: Location?,
            cart: Cart?,
            catalogItem: CatalogItem?,
            serviceType: ServiceType,
            // Default firstTimePurchase to null to filter it out in booking events
            firstTimePurchase: Boolean? = null,
    ) {
        trackEvent(
                checkoutKey = checkoutKey,
                location = location,
                cart = cart,
                itemType = if (catalogItem?.isIntroOffer == true) ItemType.INTRO_OFFER else ItemType.PASS,
                serviceType = serviceType,
                isPromoted = catalogItem?.isPromotedInConnect == true,
                firstTimePurchase = firstTimePurchase,
        )
    }

    @JvmStatic
    @JvmOverloads
    fun trackAppointmentEvent(
        checkoutKey: CheckoutKey,
        location: Location?,
        cart: Cart? = null,
        appointmentType: AppointmentType? = null,
        firstTimePurchase: Boolean? = null,
        originView: OriginView? = null,
        originComponent: OriginComponent? = null,
        isRWCC: Boolean = false
    ) {
        trackEvent(
            checkoutKey = checkoutKey,
            location = location,
            cart = cart,
            serviceId = appointmentType?.id,
            serviceName = appointmentType?.name,
            serviceType = ServiceType.APPOINTMENT,
            firstTimePurchase = firstTimePurchase,
            originView = originView,
            originComponent = originComponent,
            isRWCC = isRWCC
        )
    }

    @JvmStatic
    @JvmOverloads
    fun trackClassEvent(
        checkoutKey: CheckoutKey,
        location: Location?,
        cart: Cart?,
        classTypeObject: ClassTypeObject? = null,
        pricingReference: PricingReference? = null,
        // Default firstTimePurchase to null to filter it out in booking events
        firstTimePurchase: Boolean? = null,
        originView: OriginView? = null,
        originComponent: OriginComponent? = null,
    ) {
        trackEvent(
            checkoutKey = checkoutKey,
            location = location,
            cart = cart,
            itemType = getPurchaseItemType(
                classTypeObject = classTypeObject,
                purchaseOption = pricingReference,
                cart = cart
            ),
            serviceId = classTypeObject?.id,
            serviceName = classTypeObject?.name,
            serviceType = if (classTypeObject is Enrollment?) ServiceType.ENROLLMENT else ServiceType.CLASS,
            pricingRef = pricingReference,
            firstTimePurchase = firstTimePurchase,
            originComponent = originComponent,
            originView = originView
        )
    }

    private fun trackEvent(
        checkoutKey: CheckoutKey,
        location: Location?,
        cart: Cart?,
        itemType: ItemType? = null,
        serviceId: Int? = null,
        serviceName: String? = null,
        serviceType: ServiceType = ServiceType.UNKNOWN,
        isPromoted: Boolean? = null,
        pricingRef: PricingReference? = null,
        // Default firstTimePurchase to null to filter it out in booking events
        firstTimePurchase: Boolean? = null,
        originView: OriginView? = null,
        originComponent: OriginComponent? = null,
        isRWCC: Boolean = false,
        purchaseType: ItemType? = null,
        contract: Contract? = null,
    ) {
        val inventorySource = location?.inventorySource ?: SwamisAPI.MB_INVENTORY_SOURCE

        val items = if (cart == null) {
            listOf(
                    Item(
                            id = null,
                            name = null,
                            itemType = itemType,
                            serviceId = serviceId,
                            serviceName = serviceName,
                            serviceType = serviceType,
                            isPromoted = isPromoted,
                            inventorySource = InventorySource.values()
                                    .firstOrNull { it.source == inventorySource.lowercase() },
                            price = pricingRef?.pricing?.online ?: pricingRef?.pricing?.retail
                    )
            )
        } else {
            cart.items?.map { cartItem ->
                Item(
                        id = cartItem?.item?.id?.toInt(),
                        name = cartItem?.item?.name,
                        itemType = itemType,
                        serviceId = serviceId,
                        serviceName = serviceName,
                        serviceType = serviceType,
                        isPromoted = isPromoted,
                        inventorySource = InventorySource.values()
                                .firstOrNull { it.source == inventorySource.lowercase() },
                        price = cartItem?.total
                )
            }
        }
        val checkoutEvent = CheckoutEvent(
            business = Business(
                customerId = location?.siteId ?: -1,
                customerName = location?.studioName ?: "",
                locationId = location?.siteLocationId ?: -1,
                locationName = location?.name ?: ""
            ),
            items = items ?: listOf(),
            order = if (cart == null || checkoutKey == CheckoutKey.BOOKING) null else Order(
                id = cart.orderId,
                currency = Currency.getInstance(location?.locale).currencyCode,
                paymentMethod = CheckoutPaymentMethod.mapToPaymentMethod(
                    CartUtils.fetchPaymentTypes(
                        cart
                    )
                ),
                grossValue = cart.totals?.total ?: BigDecimal(-1),
                netValue = cart.totals?.net ?: BigDecimal(-1),
            ),
            originComponent = originComponent,
            originView = originView,
            firstTimePurchase = firstTimePurchase,
            isRWCC = isRWCC,
            purchaseType = purchaseType ?: itemType,
            contract = contract,
        )
        AnalyticsLocator.analyticsTracker.track(
            Event(
                checkoutKey.key,
                EventType.ActionSuccess,
                checkoutEvent.metadata
            )
        )
    }

    private fun getPurchaseItemType(
            classTypeObject: ClassTypeObject?,
            purchaseOption: PricingReference?,
            cart: Cart?
    ): ItemType {
        return when {
            classTypeObject?.isFlexClass == true
                    && MBAuth.getUser()?.isFlexSubscriber == true -> ItemType.PACKAGE    // flex pass
            purchaseOption == null && cart != null -> ItemType.DYNAMIC_PRICING          // dspo
            classTypeObject?.isFreeToEnroll
                    ?: false -> ItemType.FREE                                           // free class
            purchaseOption == null -> ItemType.PAY_LATER                                // book and pay later
            purchaseOption.isSingleSession -> ItemType.DROP_IN                          // drop in
            purchaseOption.isPackage -> ItemType.PACKAGE                                // multi session package
            purchaseOption.isIntroOffer -> ItemType.INTRO_OFFER                         // intro offer
            else -> ItemType.PACKAGE                                                    // default to multi session package
        }
    }
}
