package com.mindbodyonline.connect.analytics.checkout

import com.mindbodyonline.android.api.sales.model.payments.PaymentConfiguration
import com.mindbodyonline.connect.analytics.BUSINESS
import com.mindbodyonline.connect.analytics.Business
import com.mindbodyonline.connect.analytics.CONTRACT
import com.mindbodyonline.connect.analytics.CURRENCY
import com.mindbodyonline.connect.analytics.FIRST_TIME_PURCHASE
import com.mindbodyonline.connect.analytics.GROSS_VALUE
import com.mindbodyonline.connect.analytics.ID
import com.mindbodyonline.connect.analytics.INVENTORY_SOURCE
import com.mindbodyonline.connect.analytics.IS_PROMOTED
import com.mindbodyonline.connect.analytics.IS_RESERVE_WITH_CREDIT_CARD
import com.mindbodyonline.connect.analytics.ITEMS
import com.mindbodyonline.connect.analytics.NAME
import com.mindbodyonline.connect.analytics.NET_VALUE
import com.mindbodyonline.connect.analytics.ORDER
import com.mindbodyonline.connect.analytics.ORIGIN_COMPONENT
import com.mindbodyonline.connect.analytics.ORIGIN_VIEW
import com.mindbodyonline.connect.analytics.OriginComponent
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.analytics.PAYMENT_METHOD
import com.mindbodyonline.connect.analytics.PRICE
import com.mindbodyonline.connect.analytics.PURCHASE_TYPE
import com.mindbodyonline.connect.analytics.SERVICE_ID
import com.mindbodyonline.connect.analytics.SERVICE_NAME
import com.mindbodyonline.connect.analytics.SERVICE_TYPE
import com.mindbodyonline.connect.analytics.TYPE
import com.mindbodyonline.connect.analytics.filterNullValues
import java.math.BigDecimal

enum class CheckoutKey(val key: String) {
    CHECKOUT("checkout"),
    BOOKING("booking")
}

enum class CheckoutPaymentMethod(val method: String) {
    CREDIT_CARD("credit_card"),
    GIFT_CARD("gift_card"),
    PASS("pass"),
    FLEX_PASS("flex_pass"),
    IDEAL("ideal"),
    BANCONTACT("bancontact"),
    ALI_PAY("alipay"),
    GOOGLE_PAY("google_pay"),
    TWINT("twint"),
    KLARNA("klarna"),
    FPX("fpx"),
    PAYNOW("paynow"),
    WECHAT("wechat"),
    NONE("none");

    companion object {
        fun mapToPaymentMethod(method: String?): CheckoutPaymentMethod {
            val paymentMethod: CheckoutPaymentMethod? = CheckoutPaymentMethod.values().firstOrNull {
                it.method == method
            }
            if (paymentMethod != null) {
                return paymentMethod
            }
            return when (method) {
                "credit card" -> CREDIT_CARD
                "gift card" -> GIFT_CARD
                "flex pass" -> FLEX_PASS
                PaymentConfiguration.TYPE_CREDIT_CARD -> CREDIT_CARD
                PaymentConfiguration.TYPE_GIFT_CARD -> GIFT_CARD
                PaymentConfiguration.TYPE_IDEAL -> IDEAL
                PaymentConfiguration.TYPE_ALIPAY -> ALI_PAY
                PaymentConfiguration.TYPE_BANCONTACT -> BANCONTACT
                PaymentConfiguration.TYPE_GOOGLEPAY -> GOOGLE_PAY
                PaymentConfiguration.TYPE_TWINT -> TWINT
                PaymentConfiguration.TYPE_KLARNA -> KLARNA
                PaymentConfiguration.TYPE_FPX -> FPX
                PaymentConfiguration.TYPE_PAYNOW -> PAYNOW
                PaymentConfiguration.TYPE_WECHATPAY -> WECHAT
                else -> NONE
            }
        }
    }
}

enum class ItemType(val type: String) {
    PASS("pass"),
    PACKAGE("package"),
    MEMBERSHIP("membership"),
    CONTRACT("contract"),
    DROP_IN("drop_in"),
    DYNAMIC_PRICING("dynamic_pricing"),
    FREE("free"),
    PAY_LATER("pay_later"),
    INTRO_OFFER("intro_offer")
}

enum class ServiceType(val type: String) {
    CLASS("class"),
    APPOINTMENT("appointment"),
    ENROLLMENT("enrollment"),
    UNKNOWN("unknown")
}

enum class InventorySource(val source: String) {
    BOOKER("booker"),
    MINDBODY("mb")
}

data class Item(
        val id: Int?,
        val name: String?,
        val itemType: ItemType?,
        val serviceId: Int?,
        val serviceName: String?,
        val serviceType: ServiceType?,
        val inventorySource: InventorySource?,
        val isPromoted: Boolean?,
        val price: BigDecimal? = null
) {
    val metadata: Map<String, Any?> = filterNullValues(mapOf(
            ID to id,
            NAME to name,
            TYPE to itemType?.type.orEmpty(),
            SERVICE_ID to serviceId,
            SERVICE_NAME to serviceName,
            SERVICE_TYPE to serviceType?.type.orEmpty(),
            INVENTORY_SOURCE to inventorySource?.source.orEmpty(),
            IS_PROMOTED to isPromoted,
            PRICE to price?.toDouble()
    ))
}

data class Order(
        val id: Long = -1,
        val paymentMethod: CheckoutPaymentMethod? = null,
        val currency: String = "",
        val grossValue: BigDecimal = BigDecimal(-1),
        val netValue: BigDecimal = BigDecimal(-1)
) {
    val metadata: Map<String, Any?> = mapOf(
            ID to id,
            PAYMENT_METHOD to paymentMethod?.method,
            CURRENCY to currency,
            GROSS_VALUE to grossValue.toDouble(),
            NET_VALUE to netValue.toDouble()
    )
}

data class Contract(
    val id: Int = -1,
    val name: String = "",
) {
    val metadata: Map<String, Any?> = filterNullValues(
        mapOf(
            ID to id,
            NAME to name,
        )
    )
}

data class CheckoutEvent(
    val originView: OriginView?,
    val originComponent: OriginComponent?,
    val business: Business,
    val items: List<Item> = listOf(),
    val order: Order? = null,
    // Default firstTimePurchase to null to filter it out in booking events
    val firstTimePurchase: Boolean? = null,
    val isRWCC: Boolean = false,
    val purchaseType: ItemType? = null,
    val contract: Contract? = null,
) {
    val metadata: Map<String, Any>
        get() = filterNullValues(
            mapOf(
                ORIGIN_VIEW to originView?.viewName,
                ORIGIN_COMPONENT to originComponent?.componentName,
                BUSINESS to business.metadata,
                ITEMS to items.map { item ->
                    item.metadata
                },
                ORDER to order?.metadata,
                FIRST_TIME_PURCHASE to firstTimePurchase,
                IS_RESERVE_WITH_CREDIT_CARD to isRWCC,
                PURCHASE_TYPE to purchaseType?.type.orEmpty(),
                CONTRACT to contract?.metadata,
            )
        )
}
