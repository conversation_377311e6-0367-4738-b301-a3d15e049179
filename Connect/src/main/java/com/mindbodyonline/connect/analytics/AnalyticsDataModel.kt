package com.mindbodyonline.connect.analytics

import java.util.Date
import java.util.Locale

enum class OriginView(val viewName: String) {
    HOME("home"),
    PROFILE("profile"),
    SEARCH_TAB("search_tab"),
    SEARCH_HOME("search"),
    BUSINESS_LISTING("business_search_results"),
    CLASS_LISTING("class_search_results"),
    DEALS_LISTING("deals_search_results"),
    MAP("search_results_map"),
    FAVORITES("favorites"),
    FAVORITE_BUSINESSES("favorite_businesses"),
    FAVORITE_CLASSES("favorite_classes"),
    FAVORITE_STAFF("favorite_staff"),
    DEALS_HOME("deals"),
    DEAL_DETAILS("deal_details"),
    PROFILE_SCHEDULE("profile_schedule"),
    PROFILE_PASSES("profile_passes"),
    PROFILE_ACTIVITY("profile_activity"),
    BUSINESS_DEALS("business_deals"),
    AUTO_SUGGEST_LIST("auto_suggest_list"),
    BUSINESS_DETAILS("business_details"),
    CLASS_DETAILS("class_details"),
    EVENT_DETAILS("event_details"),
    APPOINTMENT_DETAILS("appointment_details"),
    BUSINESS_SCHEDULE("business_schedule"),
    BUSINESS_SCHEDULE_CLASSES("business_schedule_classes"),
    BUSINESS_SCHEDULE_APPOINTMENTS("business_schedule_appointments"),
    STAFF_SCHEDULE("staff_schedule"),
    REBOOK_CLASSES("rebook_classes"),
    UPCOMING_EVENTS("upcoming_events"),
    QUALIFIED_CLASSES_FOR_PASS("qualified_classes_for_pass"),
    QUALIFIED_EVENTS_FOR_PASS("qualified_events_for_pass"),
    QUALIFIED_CLASSES_FOR_DEAL("qualified_classes_for_deal"),
    QUALIFIED_EVENTS_FOR_DEAL("qualified_events_for_deal"),
    AVAILABLE_SESSIONS_CLASSES("available_sessions_classes"),
    AVAILABLE_SESSIONS_EVENTS("available_sessions_events"),
    SELECT_A_BUSINESS("select_a_business"),
    BUSINESS_PRICING("business_pricing"),
    CLASS_CATEGORY_PRICING("class_category_pricing"),
    CONTRACT_DETAILS("contract_details"),
    CONTRACT_TERMS_AND_CONDITIONS("contract_terms_and_conditions"),
    APPOINTMENT_CATEGORY("appointment_category"),
    APPOINTMENT_STAFF_SELECTION("appointment_staff_selection"),
    APPOINTMENT_SLOT_SELECTION("appointment_slot_selection"),
    APPOINTMENT_BOOKING("appointment_booking"),
    STAFF_BIO("staff_bio"),
    SERVICE_BOOKED_CONFIRMATION("service_booked_confirmation"),
    SETTINGS("settings"),
    ADD_PAYMENT_METHOD("add_payment_method"),
    LIABILITY_WAIVER("liability_waiver"),
    POST_TRANSACTION_CONFIRMATION("post_transaction_confirmation"),
    APP_RATING_POPUP("app_rating_popup"),
    POST_TRANSACTION_CONFIRMATION_LEGACY("post_transaction_confirmation_legacy"),
    ADD_BILLING_ADDRESS_SCREEN("add_billing_address_screen"),
    SELECT_BILLING_ADDRESS_SCREEN("select_billing_address_screen"),
    ADD_CREDIT_CARD_SCREEN("add_credit_card_screen"),
    PAYMENT_METHODS_SCREEN("payment_methods_screen"),
    QUICK_BOOK_DIALOG("quick_book_dialog"),
    GIFT_CARD_PICKER("gift_card_picker"),
}

enum class OriginComponent(val componentName: String) {
    DISCOVERY_DEALS("deals_discovery_card"),
    DISCOVERY_FITNESS("fitness_discovery_card"),
    DISCOVERY_WELLNESS("wellness_discovery_card"),
    DISCOVERY_BEAUTY("beauty_discovery_card"),
    SIMILAR_HISTORY("similar_history_carousel"),
    SIMILAR_HISTORY_CARD("similar_history_card"),
    LMO("last_minute_offers_carousel"),
    LMO_CARD("last_minute_offers_card"),
    LMO_SURGE("last_minute_offers_surge_carousel"),
    LMO_SURGE_CARD("last_minute_offers_surge_card"),
    SPAS("spas_carousel"),
    CATEGORY_GROUP("category_group_carousel"),
    SEARCH_BAR("search_bar"),
    SEARCH_FOR_AUTO_SUGGEST_LIST_ITEM("search_for_auto_suggest_list_item"),
    AUTO_SUGGEST_LIST_ITEM("auto_suggest_list_item"),
    SPELL_CORRECT_ENTERED_TERM_LINK("entered_term_link"),
    POPULAR_SEARCH("popular_search_list_item"),
    RECENT_SEARCH("recent_search_list_item"),
    VERTICAL("vertical_card"),
    CATEGORY("category_button"),
    EXPLORE("explore_button"),
    EXPLORE_BUSINESSES("explore_businesses_button"),
    EXPLORE_CLASSES("explore_classes_button"),
    EXPLORE_VIRTUAL_CLASSES("explore_virtual_classes_button"),
    FAVORITE_BUSINESSES_STUDIO_CAROUSEL("book_favorite_studio_carousel"),
    UPCOMING_CLASSES("upcoming_carousel"),
    UPCOMING_CARD("upcoming_card"),
    INTRO_OFFERS("intro_offers_carousel"),
    INTRO_OFFERS_LIST("intro_offers_list"),
    NEAR_YOU_MAP("near_you_map_card"),
    RELAXATION_CATEGORY_GROUP("relaxation_card"),
    FLEXIBILITY_CATEGORY_GROUP("flexibility_card"),
    CARDIO_CATEGORY_GROUP("cardio_card"),
    STRENGTH_CATEGORY_GROUP("strength_card"),
    OUTDOOR_CATEGORY_GROUP("outdoor_card"),
    COMPETITION_CATEGORY_GROUP("competition_card"),
    LAST_MINUTE_OFFERS_LIST("last_minute_offers_list"),
    LAST_MINUTE_OFFERS_LIST_ITEM("last_minute_offers_list_item"),
    AUTO_SUGGEST_BUSINESS_LIST("auto_suggest_business_list"),
    AUTO_SUGGEST_CATEGORY_LIST("auto_suggest_category_list"),
    DEAL_DETAILS_BUY_BUTTON("deal_details_buy_button"),
    DEAL_DETAILS_MAP("deal_details_map_view"),
    DEAL_DETAILS_BUSINESS_CARD("deal_details_business_card"),
    BUSINESS_CARD("business_card"),
    FAVORITE_BUSINESS_OPTIONS_DIALOG("favorite_business_options_dialog"),
    ABOUT_BUSINESS_CARD("about_business_card"),
    CLASS_TIME_CARD("class_time_card"),
    APPOINTMENT_TIME_CARD("appointment_time_card"),
    APPOINTMENT_CARD("appointment_card"),
    EVENT_CARD("event_card"),
    CLASS_TIME_BUTTON("class_time_button"),
    USER_ACTION_LINK("user_action_link"),
    CLASS_CATEGORIES_LIST("class_categories_list"),
    CLASS_CATEGORIES_LIST_ITEM("class_categories_list_item"),
    CONTRACTS_LIST("contracts_list"),
    APPOINTMENT_CATEGORIES_LIST("appointment_categories_list"),
    APPOINTMENTS_LIST("appointments_list"),
    PRICING_LIST("pricing_list"),
    STAFFS_LIST("staffs_list"),
    TIME_SLOTS_LIST_ITEM("time_slots_list_item"),
    FAVORITES_BUSINESS_LIST_ITEM("favorites_business_list_item"),
    FAVORITES_STAFF_LIST_ITEM("favorites_staff_list_item"),
    ADD_CREDIT_CARD_BUTTON("add_credit_card_button"),
    NEXT_BUTTON("next_button"),
    BOOK_BUTTON("book_button"),
    VIEW_PRICING_BUTTON("view_pricing_button"),
    LIST_ITEM("list_item"),
    REVIEW_LIABILITY_WAIVER_BUTTON("review_liability_waiver_button"),
	BOTTOM_NAVIGATION_BAR("bottom_navigation_bar"),
	STAFF_CARD("staff_card"),
    WIDGET("widget"),
}

enum class EventsPathIdentifier {
	CLASS_LISTING_CLASSES,
	FAVORITE_CLASSES,
	FAVORITE_BUSINESSES,
	FAVORITE_STAFF,
	BUSINESS_SCHEDULE_CLASSES,
	BUSINESS_SCHEDULE_APPOINTMENTS,
	BUSINESS_PRICING_APPOINTMENTS,
	CATEGORY_LIST_APPOINTMENTS,
}

enum class AutoSuggestType(val type: String) {
    CATEGORY("category"),
    SUB_CATEGORY("sub_category"),
    SEARCH_FOR("search_for");

    companion object {
        fun safeValueOf(type: String?): AutoSuggestType? {
            return when (type) {
                "category" -> CATEGORY
                "sub_category" -> SUB_CATEGORY
                "search_for" -> SEARCH_FOR
                else -> null
            }
        }
    }
}

interface Origin {
    val originView: OriginView?
    val originComponent: OriginComponent?

    val metadata: Map<String, Any?>
        get() = mapOf(
                ORIGIN_VIEW to originView?.viewName.orEmpty(),
                ORIGIN_COMPONENT to originComponent?.componentName.orEmpty()
        )
}

open class LocationData(
    open val city: String,
    open val state: String,
    open val country: String,

    open val metadata: Map<String, Any> = mapOf(
        CITY to city,
        STATE to state,
        COUNTRY to country,
    ),
)

data class Filter(
        val startTime: Date,
        val endTime: Date,
        val sort: String,
        val categories: List<String>,
        val verticals: List<String>,
        val distance: Double,
        val dynamicPricingOnly: Boolean,
        val virtualOnly: Boolean,
        val favoritesOnly: Boolean,

        val metadata: Map<String, Any> = mapOf(
                START_TIME to startTime,
                END_TIME to endTime,
                SORT to sort.lowercase(Locale.getDefault()),
                CATEGORIES to categories,
                VERTICALS to verticals,
                DISTANCE to distance,
                DYNAMIC_PRICING_ONLY to dynamicPricingOnly,
                VIRTUAL_ONLY to virtualOnly,
                FAVORITES_ONLY to favoritesOnly,
        ),
)

open class SearchLocation(
    open val input: String,
    override val city: String,
    override val state: String,
    override val country: String,
    open val latitude: Double,
    open val longitude: Double,

    override val metadata: Map<String, Any> = mapOf(
        INPUT to input,
        CITY to city,
        STATE to state,
        COUNTRY to country,
        LATITUDE to latitude,
        LONGITUDE to longitude,
    ),
) : LocationData(city, state, country)

/**
 * When consuming this, don't forget to init with values.
 * Use emptyGlobalLocation() if need an empty object.
 */
data class GlobalLocation(
    override val input: String = "",
    override val city: String = "",
    override val state: String = "",
    override val country: String = "",
    override val latitude: Double = 0.0,
    override val longitude: Double = 0.0,
    val isCurrent: Boolean = false,

    override val metadata: Map<String, Any> = mapOf(
        INPUT to input,
        CITY to city,
        STATE to state,
        COUNTRY to country,
        LATITUDE to latitude,
        LONGITUDE to longitude,
        IS_CURRENT to isCurrent
    ),
) : SearchLocation(input, city, state, country, latitude, longitude) {
    companion object {
        // case of no location available, we need to return an empty object
        fun emptyGlobalLocation() = GlobalLocation()
    }
}

data class Business(
    val accountName: String = "",
    val accountId: Int = -1,
    val customerName: String = "",
    val customerId: Int = -1,
    val locationName: String = "",
    val locationId: Int = -1,
    var locationClassification: String? = null,
    val metadata: Map<String, Any?> = filterNullValues(
        mapOf(
            ACCOUNT_NAME to accountName,
            ACCOUNT_ID to accountId,
            CUSTOMER_NAME to customerName,
            CUSTOMER_ID to customerId,
            LOCATION_NAME to locationName,
            LOCATION_ID to locationId,
            LOCATION_CLASSIFICATION to locationClassification
        )
    ),
)

data class Service(
    val name: String?,
    val id: String?,
    val category: String?,
    val startTime: Date?,
    val endTime: Date?,
    val slotsLeft: Int? = null,
    val waitlistId: Int? = null,
    val waitListPosition: Int? = null,
    val isWaitListed: Boolean? = null,
    val isCancelled: Boolean? = null,
    val isBookable: Boolean? = null,
    val bookAbilityStatus: String? = null,
    val staffId: Long? = null,
    val capacity: Int? = null,
    val isTbd : Boolean? = null,
    val spotsReserved: Int? = null,
    val isVisit: Boolean? = null,
    val visitId: Long? = null,
    var isFree: Boolean? = null,
    var price: Double? = null,
    val metadata: Map<String, Any?> = filterNullValues(
        mapOf(
            NAME to name,
            ID to id,
            CATEGORY to category,
            START_TIME to startTime,
            END_TIME to endTime,
            SLOTS_LEFT to slotsLeft,
            WAITLIST_ID to waitlistId,
            WAIT_LIST_POSITION to waitListPosition,
            IS_WAITLISTED to isWaitListed,
            IS_CANCELLED to isCancelled,
            IS_BOOKABLE to isBookable,
            BOOK_ABILITY_STATUS to bookAbilityStatus,
            STAFF_ID to staffId,
            CAPACITY to capacity,
            IS_TBD to isTbd,
            SPOTS_RESERVED to spotsReserved,
            IS_VISIT to isVisit,
            VISIT_ID to visitId,
            IS_FREE to isFree,
            PRICE to price,
        )
    ),
)

data class DealData(
    val id: String,
    val name: String,
    val programType: String,
    val pricingOptionActivationType: String?,
    val introOfferType: String,
    val sessionCount: Int,
    val durationValue: String?,
    val price: Double?,
    val eligibility: Boolean,
    val subscriberProductId: Int,
    val itemType: String?,
    val durationType: String?,
    val servicePricingOptionType: String?,
    val metadata: Map<String, Any?> = filterNullValues(
        mapOf(
            ID to id,
            NAME to name,
            PROGRAM_TYPE to programType,
            PRICING_OPTION_ACTIVATION_TYPE to pricingOptionActivationType,
            INTRO_OFFER_TYPE to introOfferType,
            SESSION_COUNT to sessionCount,
            DURATION_VALUE to durationValue,
            PRICE to price,
            ELIGIBILITY to eligibility,
            SUBSCRIBER_PRODUCT_ID to subscriberProductId,
            ITEM_TYPE to itemType,
            DURATION_TYPE to durationType,
            SERVICE_PRICING_OPTION_TYPE to servicePricingOptionType,
        )
    ),
)

data class Rank(
    val rank: Int,
    val metadata: Map<String, Any> = mapOf(RANK to rank),
)
