package com.mindbodyonline.connect.analytics.shared_login

import com.mindbodyonline.connect.analytics.AUTH_CODE
import com.mindbodyonline.connect.analytics.AUTH_TYPE
import com.mindbodyonline.connect.analytics.ERROR_DESC
import com.mindbodyonline.connect.analytics.filterNullValues

data class SharedLoginEvent(
    val authType: String,
    val authCode: String? = null,
    val errorDesc: String? = null,
) {
    val metadata: Map<String, Any>
        get() = filterNullValues(
            mapOf(
                AUTH_TYPE to authType,
                AUTH_CODE to authCode,
                ERROR_DESC to errorDesc,
            )
        )
}
