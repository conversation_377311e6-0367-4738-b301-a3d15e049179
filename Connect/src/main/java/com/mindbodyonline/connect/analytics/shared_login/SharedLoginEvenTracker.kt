package com.mindbodyonline.connect.analytics.shared_login

import com.mindbodyonline.analytics.Event
import com.mindbodyonline.analytics.EventType
import com.mindbodyonline.connect.analytics.AUTHENTICATION
import com.mindbodyonline.connect.analytics.SHARED_UI
import com.mindbodyonline.connect.utils.analytics.AnalyticsLocator

object SharedLoginEvenTracker {

    fun trackSharedLoginInitiated() {
        AnalyticsLocator.analyticsTracker.track(
            Event(
                AUTHENTICATION,
                EventType.ActionStarted,
                SharedLoginEvent(
                    authType = SHARED_UI
                ).metadata
            )
        )
    }

    fun trackSharedLoginCancelled() {
        AnalyticsLocator.analyticsTracker.track(
            Event(
                AUTHENTICATION,
                EventType.ActionCancelled,
                SharedLoginEvent(
                    authType = SHARED_UI
                ).metadata
            )
        )
    }

    fun trackSharedLoginCompleted(
        authCode: String?
    ) {
        AnalyticsLocator.analyticsTracker.track(
            Event(
                AUTHENTICATION,
                EventType.ActionSuccess,
                SharedLoginEvent(
                    authType = SHARED_UI,
                    authCode = authCode
                ).metadata
            )
        )
    }

    fun trackSharedLoginFailed(
        authCode: String? = null,
        authError: String? = null,
    ) {
        AnalyticsLocator.analyticsTracker.track(
            Event(
                AUTHENTICATION,
                EventType.ActionFailed,
                SharedLoginEvent(
                    authType = SHARED_UI,
                    authCode = authCode,
                    errorDesc = authError,
                ).metadata
            )
        )
    }
}
