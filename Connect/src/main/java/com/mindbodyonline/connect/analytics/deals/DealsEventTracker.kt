package com.mindbodyonline.connect.analytics.deals

import com.mindbodyonline.analytics.Event
import com.mindbodyonline.analytics.EventType
import com.mindbodyonline.android.api.sales.model.pos.deals.Deal
import com.mindbodyonline.android.api.sales.model.pos.deals.DealLocation
import com.mindbodyonline.android.api.sales.model.search.SearchModel
import com.mindbodyonline.android.util.log.MBLog
import com.mindbodyonline.connect.analytics.Business
import com.mindbodyonline.connect.analytics.CARD
import com.mindbodyonline.connect.analytics.DealData
import com.mindbodyonline.connect.analytics.GlobalLocation
import com.mindbodyonline.connect.analytics.LocationData
import com.mindbodyonline.connect.analytics.OriginComponent
import com.mindbodyonline.connect.analytics.OriginView
import com.mindbodyonline.connect.analytics.Rank
import com.mindbodyonline.connect.analytics.Service
import com.mindbodyonline.connect.analytics.filterNullValues
import com.mindbodyonline.connect.analytics.toBusiness
import com.mindbodyonline.connect.analytics.toDealData
import com.mindbodyonline.connect.analytics.toGlobalLocationForEvent
import com.mindbodyonline.connect.analytics.toLocationData
import com.mindbodyonline.connect.analytics.toService
import com.mindbodyonline.connect.explore.filters.CategoriesGroup
import com.mindbodyonline.connect.tealium.TealiumHelper
import com.mindbodyonline.connect.tealium.TrackingHelperUtils
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.analytics.AnalyticsLocator
import com.mindbodyonline.data.services.locator.ServiceLocator
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.Location
import com.mindbodyonline.domain.dataModels.ConnectSearchModel
import com.mindbodyonline.framework.abvariant.ABHelperUtils
import com.mindbodyonline.framework.abvariant.DEAL_DETAILS_PURCHASED
import com.mindbodyonline.framework.abvariant.DEAL_DETAILS_VIEWED
import com.mindbodyonline.framework.abvariant.DevelopmentFlag

object DealsEventTracker {
    private val location: GlobalLocation
        get() {
            return SharedPreferencesUtils.getGlobalIndicatorLocation().toGlobalLocationForEvent()
        }

    @JvmStatic
    fun trackDealsSearch(searchModel: SearchModel) {
        val metadata = DealsSearchEvent(
                originView = OriginView.DEALS_LISTING,
                searchTerm = searchModel.query.orEmpty(),
                locationData = location,
                distance = searchModel.radius
        ).metadata

        // Mixpanel
        AnalyticsLocator.analyticsTracker.track(
            Event(OriginView.DEALS_HOME.viewName, EventType.Search, filterNullValues(metadata))
        )

        // Tealium
        TrackingHelperUtils.trackSearchEvent(searchModel as ConnectSearchModel, TealiumHelper.SearchType.DEALS)
    }

    @JvmStatic
    fun trackDealsSearchResults(query: String?, radius: Double?, count: Int) {
        val metadata = DealsSearchResultsEvent(
                searchTerm = query.orEmpty(),
                locationData = location,
                resultsCount = count,
                distance = radius
        ).metadata

        AnalyticsLocator.analyticsTracker.track(
            Event(
                OriginView.DEALS_LISTING.viewName,
                EventType.ScreenViewed,
                filterNullValues(metadata)
            )
        )
    }

    fun trackLMOsViewed(category: CategoriesGroup, resultsCount: Int) {
        val event = LMOListViewedEvent(location, resultsCount)
        val key = category.value
        AnalyticsLocator.analyticsTracker.track(
            Event(key, EventType.ScreenViewed, filterNullValues(event.metadata))
        )
    }

    fun trackDealsHomeViewed(state: DealsHomeState) {
        val event = DealsHomeViewedEvent(location, components = state.components)
        AnalyticsLocator.analyticsTracker.track(
            Event(
                OriginView.DEALS_HOME.viewName,
                EventType.ScreenViewed,
                filterNullValues(event.metadata)
            )
        )
    }

    fun trackDealsSearchResultListTap(
        key: DealsSearchResultListEventKey,
        rank: Int,
        deal: Deal,
        dealLocation: DealLocation,
        originComponent: OriginComponent? = null,
    ) {
        val event = DealsSearchResultListTapEvent(
            originView = DealsSearchResultListTapOriginView.DEALS_SEARCH_RESULTS,
            rank = Rank(rank),
            deal = deal.toDealData(),
            business = dealLocation.toBusiness(),
            businessLocation = dealLocation.toLocationData(),
            originComponent = originComponent
        )

        AnalyticsLocator.analyticsTracker.track(
            Event(name = key.value, type = EventType.Click, data = filterNullValues(event.metadata))
        )
    }

    fun trackIntroOfferInteractedEvent(deal: Deal) {
        trackDealsHomeInteracted(OriginComponent.INTRO_OFFERS, deal.toDealData(),
                business = deal.location.toBusiness(),
                businessLocation = deal.location.toLocationData(), null)
    }

    fun trackLMOInteractedEvent(cto: ClassTypeObject) {
        trackDealsHomeInteracted(OriginComponent.LAST_MINUTE_OFFERS_LIST, null, cto.location.toBusiness(),
                cto.toLocationData(), cto.toService())
    }

    private fun trackDealsHomeInteracted(originComponent: OriginComponent, dealData: DealData?,
                                 business: Business, businessLocation: LocationData,
                                 service: Service?) {
        val event = DealsHomeInteractedEvent(
            deal = dealData,
            originComponent = originComponent,
            business = business,
            service = service,
            businessLocation = businessLocation,
            location = location
        )

        AnalyticsLocator.analyticsTracker.track(
            Event(name = CARD, type = EventType.Click, data = filterNullValues(event.metadata))
        )
    }

    fun trackDealDetailsViewed(originView: OriginView?, originComponent: OriginComponent?, deal: Deal) {
        val event = DealDetailsViewedEvent(
                originView = originView,
                originComponent = originComponent,
                deal = deal.toDealData(),
                business = deal.location.toBusiness(),
                businessLocation = deal.location.toLocationData()
        )
        AnalyticsLocator.analyticsTracker.track(
            Event(
                OriginView.DEAL_DETAILS.viewName,
                EventType.ScreenViewed,
                filterNullValues(event.metadata)
            )
        )

        // Optimizely
        ServiceLocator.abTestFramework.track(
                DEAL_DETAILS_VIEWED,
                ABHelperUtils.getUserAttributes()
        )
    }

    fun trackDealDetailsInteracted(key: String, deal: Deal) {
        val event = DealDetailsInteractedEvent(
                originView = OriginView.DEAL_DETAILS,
                deal = deal.toDealData(),
                business = deal.location.toBusiness(),
                businessLocation = deal.location.toLocationData()
        )
        AnalyticsLocator.analyticsTracker.track(
            Event(
                key,
                EventType.Click,
                filterNullValues(event.metadata)
            )
        )
    }

    fun trackDealDetailsInteracted(component: OriginComponent, deal: Deal) {
        val event = DealDetailsInteractedEvent(
            originView = OriginView.DEAL_DETAILS,
            originComponent = component,
            deal = deal.toDealData(),
            business = deal.location.toBusiness(),
            businessLocation = deal.location.toLocationData()
        )
        AnalyticsLocator.analyticsTracker.track(
            Event(
                component.componentName,
                EventType.Click,
                filterNullValues(event.metadata)
            )
        )
    }

    fun trackStudioDealsViewed(resultsCount: Int, location: Location) {
        val event = DealsStudioViewedEvent(
                resultsCount = resultsCount,
                business = location.toBusiness(),
                businessLocation = location.toLocationData()
        )
        AnalyticsLocator.analyticsTracker.track(
            Event(
                OriginView.BUSINESS_DEALS.viewName,
                EventType.ScreenViewed,
                filterNullValues(event.metadata)
            )
        )
    }


    fun trackStudioDealCardInteracted(rank: Int, deal: Deal) {
        val event = StudioDealsCardInteractedEvent(
                originView = OriginView.BUSINESS_DEALS,
                rank = rank,
                deal = deal.toDealData(),
                business = deal.location.toBusiness(),
                businessLocation = deal.location.toLocationData()
        )
        AnalyticsLocator.analyticsTracker.track(
            Event(
                CARD,
                EventType.Click,
                filterNullValues(event.metadata)
            )
        )
    }

    fun trackDealDetailsPurchasedOptimizelyEvent() {
        ServiceLocator.abTestFramework.track(
                DEAL_DETAILS_PURCHASED,
                ABHelperUtils.getUserAttributes()
        )
    }
}
