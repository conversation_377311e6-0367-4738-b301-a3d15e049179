package com.mindbodyonline.connect.analytics

// Event names --> keys
const val SEARCH = "search"
const val SEE_ALL_BUTTON = "see_all_button"
const val BOOKING_CANCEL_BUTTON = "booking_cancel_button"
const val CARD = "card"
const val QUALIFYING_CLASSES_CARD = "qualifying_classes_card"
const val QUALIFYING_APPOINTMENTS_CARD = "qualifying_appointments_card"
const val ATTRIBUTION_SURVEY_COMPLETED = "attribution_survey_completed"

// View Schedule keys
const val CLASS_LISTING_CLASSES = "class_search_classes"
const val BUSINESS_SCHEDULE_CLASSES = "business_schedule_classes"
const val BUSINESS_SCHEDULE_APPOINTMENTS = "business_schedule_appointments"
const val BUSINESS_PRICING_APPOINTMENTS = "business_pricing_appointments"
const val CATEGORY_LIST_APPOINTMENTS = "category_list_appointments"

// Favorites
const val FAVORITES = "favorites"
const val BUSINESSES_TAB = "businesses_tab"
const val STAFF_TAB = "staff_tab"
const val FAVORITE_CLASSES = "favorite_classes"
const val FAVORITE_BUSINESS = "favorite_businesses"
const val FAVORITE_STAFF = "favorite_staff"

// Quickbook dialog keys
const val BOOK_BUTTON = "book_button"
const val BUY_BUTTON = "buy_button"
const val BOOK_AND_BUY_BUTTON = "book_and_buy_button"
const val REVIEW_LIABILITY_WAIVER_BUTTON = "review_liability_waiver_button"
const val NEXT_BUTTON = "next_button"
const val CALL_BUSINESS_BUTTON = "call_business_button"
const val WAITLIST_BUTTON = "waitlist_button"
const val WAITLIST_AND_BUY_BUTTON = "waitlist_and_buy_button"
const val ADD_CARD_BUTTON = "add_card_button"

// Origin
const val ORIGIN_VIEW = "origin_view"
const val ORIGIN_COMPONENT = "origin_component"

// Search engine and algorithm
const val CLASS_ALGORITHM = "class_algorithm"
const val LOCATION_ALGORITHM = "location_algorithm"
const val CLASS_SEARCH_ENGINE = "class_search_engine"
const val LOCATION_SEARCH_ENGINE = "location_search_engine"

// Location
const val INPUT = "input"
const val CITY = "city"
const val STATE = "state"
const val COUNTRY = "country"
const val LATITUDE = "latitude"
const val LONGITUDE = "longitude"
const val IS_CURRENT = "is_current"

// Filter
const val FILTER = "filter"
const val SEARCH_TERM = "search_term"
const val START_TIME = "start_time"
const val END_TIME = "end_time"
const val SORT = "sort"
const val CATEGORIES = "categories"
const val VERTICALS = "verticals"
const val DISTANCE = "distance"
const val DYNAMIC_PRICING_ONLY = "dynamic_pricing_only"
const val VIRTUAL_ONLY = "virtual_only"
const val FAVORITES_ONLY = "favorites_only"

// Listing
const val RESULTS_COUNT = "results_count"

// Business
const val BUSINESS = "business"
const val BUSINESS_LOCATION = "business_location"
const val ACCOUNT_ID = "account_id"
const val ACCOUNT_NAME = "account_name"
const val CUSTOMER_ID = "customer_id"
const val CUSTOMER_NAME = "customer_name"
const val LOCATION_ID = "location_id"
const val LOCATION_NAME = "location_name"
const val LOCATION_CLASSIFICATION = "location_classification"

// Service
const val SERVICE = "service"
const val SERVICE_ID = "service_id"
const val SERVICE_NAME = "service_name"
const val SERVICE_TYPE = "service_type"
const val CATEGORY = "category"
const val CATEGORY_ID = "category_id"
const val SLOTS_LEFT = "slots_left"
const val INVENTORY_SOURCE = "inventory_source"
const val WAITLIST_ID = "waitlist_id"
const val WAIT_LIST_POSITION = "waitlist_position"
const val IS_WAITLISTED = "is_waitlisted"
const val IS_CANCELLED = "is_cancelled"
const val IS_BOOKABLE = "is_bookable"
const val IS_BOOKABLE_ONLINE = "is_bookable_online"
const val BOOK_ABILITY_STATUS = "bookability_status"
const val STAFF_ID = "staff_id"
const val CAPACITY = "capacity"
const val IS_TBD = "is_tbd"
const val SPOTS_RESERVED = "spots_reserved"
const val IS_VISIT = "is_visit"
const val VISIT_ID = "visit_id"
const val IS_FREE = "is_free"
const val CANCELLATION_STATUS = "cancellation_status"
const val CANCELLABLE = "cancellable"
const val REQUESTED = "requested"
const val COMPLETED = "completed"
const val PURCHASE_TYPE = "purchase_type"
const val CONTRACT = "contract"

// Deal metadata
const val DEAL = "deal"
const val PROGRAM_TYPE = "program_type"
const val PRICING_OPTION_ACTIVATION_TYPE = "pricing_option_activation_type"
const val INTRO_OFFER_TYPE = "intro_offer_type"
const val SESSION_COUNT = "session_count"
const val DURATION_VALUE = "duration_value"
const val PRICE = "price"
const val PRICING = "pricing"
const val ELIGIBILITY = "eligibility"
const val SUBSCRIBER_PRODUCT_ID = "subscriber_product_id"
const val ITEM_TYPE = "item_type"
const val DURATION_TYPE = "duration_type"
const val DURATION = "duration"
const val SERVICE_PRICING_OPTION_TYPE = "service_pricing_option_type"

// Auto Suggest
const val AUTO_SUGGEST_TYPE = "auto_suggest_type"
const val AUTO_SUGGEST_NO_RESULTS = "auto_suggest_no_results"
const val IS_SPELL_CORRECTED = "is_spell_corrected"
const val IS_MULTI_WORD = "is_multi_word"
const val ENTERED_TERM = "entered_term"
const val SPELL_CORRECTED_BUSINESSES = "spell_corrected_businesses"
const val SPELL_CORRECTED_CATEGORIES = "spell_corrected_categories"
const val BUSINESS_NO_RESULTS = "businesses_no_results"
const val CATEGORIES_NO_RESULTS = "categories_no_results"
const val SEARCH_LOCATION = "search_location"

// Order
const val ORDER = "order"
const val PAYMENT_METHOD = "payment_method"
const val CURRENCY = "currency"
const val GROSS_VALUE = "gross_value"
const val NET_VALUE = "net_value"
const val NET_VALUE_USD = "net_value_usd"

// Home carousels
const val LMO = "last_minute_offers_carousel"
const val CATEGORY_GROUP = "category_group_carousel"
const val SPAS = "spas_carousel"
const val LMO_SURGE = "last_minute_offers_surge_carousel"
const val DISCOVERY = "discovery_carousel"
const val SIMILAR_HISTORY = "similar_history_carousel"
const val NEAR_YOU_MAP = "near_you_map_card"
const val INTRO_OFFERS = "intro_offers_carousel"
const val UPCOMING_CLASSES = "upcoming_carousel"
const val FAVORITE_BUSINESSES_STUDIO_CAROUSEL = "book_favorite_studio_carousel"
const val ENABLE_LOCATION_CARD = "enable_location_card"

// Deals home page views
const val INTRO_OFFERS_CAROUSEL = "intro_offers_carousel"
const val EXPLORE_CLASSES_BUTTON = "explore_classes_button"
const val EXPLORE_LAST_MINUTE_OFFERS_BUTTON = "explore_last_minute_offers_button"
const val EXPLORE_VIRTUAL_CLASSES_BUTTON = "explore_virtual_classes_button"
const val LAST_MINUTE_OFFERS_LIST = "last_minute_offers_list"

const val CARDS = "cards"
const val LOCATION = "location"
const val NAME = "name"
const val IS_ACTIVE = "is_active"
const val ID = "id"
const val TYPE = "type"
const val ITEMS = "items"
const val LIST_ITEM = "list_item"
const val RANK = "rank"
const val HOME = "home"
const val COMPONENTS = "components"
const val IS_PROMOTED = "is_promoted"
const val CLASSES = "classes"
const val CLASSES_TAB = "classes_tab"
const val APPOINTMENTS = "appointments"
const val APPOINTMENTS_TAB = "appointments_tab"
const val CONTRACTS_TAB = "contracts_tab"
const val IS_INTRO_OFFER = "is_intro_offer"
const val IS_DEAL = "is_deal"
const val CATALOG_ITEM = "catalog_item"
const val CATALOG_PACKAGE = "catalog_package"
const val CATALOG_ITEMS = "catalog_items"
const val CURRENT_LOCATION = "current location"
const val AGREE_BUTTON = "agree_button"
const val FIRST_TIME_PURCHASE = "first_time_purchase"
const val ATTRIBUTION_TYPE = "attribution_type"

//classes
const val DATE_BUTTON = "date_button"
const val START_DATE = "start_date"
const val END_DATE = "end_date"

// Business Details
const val VIEW_CLASSES_BUTTON = "view_classes_button"
const val VIEW_PRICING_BUTTON = "view_pricing_button"
const val VIEW_SCHEDULE_BUTTON = "view_schedule_button"
const val REVIEWS_COMPONENT = "reviews"
const val UPCOMING_CLASSES_COMPONENT = "upcoming_classes"
const val UPCOMING_EVENTS_COMPONENT = "upcoming_events"
const val GIFT_CARD = "gift_card"
const val BUSINESS_DESCRIPTION_EXPAND = "business_description_expand"
const val MAP = "map"
const val DEAL_DETAILS_DRAWER_EXPAND = "deal_details_drawer_expand"
const val SERVICE_CATEGORY = "service_category"

//View Schedule
const val BUSINESS_SCHEDULE = "business_schedule"

//ROUTINE SERVICES
const val FILTER_BUTTON = "filter_button"
const val CALENDAR_BUTTON = "calendar_button"

// Global property keys
const val APP = "app"
const val EXPERIMENTS = "experiments"
const val IS_LOGGED_IN = "is_logged_in"
const val IS_VERIFIED = "is_verified"

const val LOGGED_IN_USER = "logged_in_user"
const val USER_LOCATION = "user_location"
const val DEVICE_ID = "device_id"

// User Location
const val AREA_NAME = "area_name"
const val DISTRICT = "district"
const val POSTAL_CODE = "postal_code"
const val STATE_OR_PROVINCE = "state_or_province"
const val LOCATION_SERVICE_ENABLED = "location_service_enabled"

// Logged In User
const val UNIVERSAL_ID = "universal_id"
const val USER_COUNTRY = "user_country"
const val USER_ID = "user_id"

// Global property values
const val MINDBODY_APP = "mindbody_app"

// appointment
const val SERVICE_GROUP_NAME = "service_group_name"
const val SERVICE_GROUP_QUANTITY_INFO = "service_group_quantity_info"
const val STAFF = "staff"
const val STAFF_BIO = "staff_bio"
const val VIEW_AVAILABILITY_BUTTON = "view_availability_button"
const val IS_SLOT_AVAILABLE = "is_slot_available"
const val AVAILABLE_STAFF_COUNT = "available_staff_count"
const val SERVICE_AVAILABILITY = "service_availability"
const val APPOINTMENT_SLOT_SELECTION = "appointment_slot_selection"
const val ADD_CREDIT_CARD = "add_credit_card"
const val BOOKING_FLOW_TYPE = "booking_flow_type"
const val PHONE_NUMBER_UPDATE = "phone_number_update"
const val LIABILITY_WAIVER = "liability_waiver"
const val ERROR_CODE = "error_code"
const val ERROR_DESC = "error_desc"
const val STATUS_CODE = "status_code"
const val REQUEST_BUTTON = "request_button"
const val SERVICE_BOOKED_CONFIRMATION = "service_booked_confirmation"
const val SHOULD_VALIDATE_CREDIT_CARD = "should_validate_credit_card"
const val VALIDATE_CREDIT_CARD = "validate_credit_card"
const val IS_RESERVE_WITH_CREDIT_CARD = "is_reserve_with_credit_card"

// Share
const val SHARE_MODE = "share_mode"
const val SHARE_BUTTON = "share_button"

// Rokt
const val TRANSACTION_TYPE = "transaction_type"
const val CONFIRMATION_FLOW = "confirmation_flow"
const val CONFIRMATION_SCREEN = "confirmation_screen"
const val PLACEMENT_ID = "placement_id"
const val INTERACTION_TYPE = "interaction_type"
const val IS_ROKT_AD_ENABLED = "is_rokt_ad_enabled"
const val ROKT_AD = "rokt_ad"
const val SALE_ID = "sale_id"
const val IS_ATTRIBUTION_SURVEY_PRESENTED = "is_attribution_survey_presented"

// App feedback dialog
const val APP_RATING_POPUP = "app_rating_popup"
const val LIKE_IT_BUTTON = "like_it_button"
const val NEEDS_IMPROVEMENT_BUTTON = "needs_improvement_button"
const val CANCEL_BUTTON = "cancel_button"
const val NOT_NOW_BUTTON = "not_now_button"
const val IS_ROKT_AD_ENABLED_FOR_CONSUMER = "is_rokt_ad_enabled_for_consumer"

// APM Payment
const val PAYMENT_INTENT_ID = "payment_intent_id"
const val IS_RESULT_FROM_POLLING = "is_result_from_polling"

// Pick a spot
const val PICK_A_SPOT_BUTTON = "pick_a_spot_button"
const val IS_EDIT = "is_edit"

// Quickbook events
const val EDIT_PAYMENT_METHOD_BUTTON = "edit_payment_method_button"
const val ADD_CREDIT_CARD_BUTTON = "add_credit_card_button"
const val DONE_BUTTON = "done_button"
const val SAVE_BUTTON = "save_button"
const val STORE_CC_FOR_FUTURE = "store_cc_for_future"
const val ADD_BILLING_ADDRESS_BUTTON = "add_billing_address_button"

// favorites
const val BUSINESS_CARD = "business_card"
const val VIEW_INFO = "view_info"
const val STAFF_SCHEDULE = "staff_schedule"

//widget
const val WIDGET = "widget"
const val WIDGET_RELOADED = "widget_reloaded"
const val DEALS_NEAR_YOU_DESTINATION = "deals_near_you_destination"

// Shared login
const val AUTHENTICATION = "authentication"
const val AUTH_TYPE = "auth_type"
const val AUTH_CODE = "auth_code"
const val SHARED_UI = "shared_ui"
