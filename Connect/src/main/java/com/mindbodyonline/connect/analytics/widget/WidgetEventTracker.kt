package com.mindbodyonline.connect.analytics.widget

import com.mindbodyonline.analytics.Event
import com.mindbodyonline.analytics.EventType
import com.mindbodyonline.connect.analytics.WIDGET
import com.mindbodyonline.connect.analytics.WIDGET_RELOADED
import com.mindbodyonline.connect.utils.analytics.AnalyticsLocator

fun trackWidgetEnabled() {
    AnalyticsLocator.analyticsTracker.track(
        Event(
            name = WIDGET,
            type = EventType.ScreenViewed,
        ),
    )
}

fun trackWidgetReloaded() {
    AnalyticsLocator.analyticsTracker.track(
        Event(
            name = WIDGET_RELOADED,
            type = EventType.ActionStarted,
        ),
    )
}
