package com.mindbodyonline.data.repository

import com.mindbodyonline.connect.adapters.filters.IFilter
import com.mindbodyonline.connect.common.utilities.awaitCallbackApiResponse
import com.mindbodyonline.connect.utils.DateTimeUtils
import com.mindbodyonline.connect.utils.api.gateway.SwamisAPI
import com.mindbodyonline.connect.utils.api.gateway.model.GatewayBusinessSchedulesResponse
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationRefJson
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.dataModels.schedule.ScheduleAttributes
import com.mindbodyonline.domain.datamapper.toScheduleAttributes
import com.mindbodyonline.domain.repository.BusinessScheduleRepository
import java.util.Calendar

class BusinessScheduleRepositoryImpl(private val apiService: SwamisAPI = SwamisAPI.INSTANCE) : BusinessScheduleRepository {

    override suspend fun fetchBusinessScheduleData(
        locationRefJson: LocationRefJson?,
        startDate: Calendar,
        endDate: Calendar,
        filters: MutableSet<IFilter<ClassTypeObject>>?,
    ): List<ScheduleAttributes>? {

        val response = try {
            awaitCallbackApiResponse<GatewayBusinessSchedulesResponse> { listener, errorListener ->
                apiService.fetchBusinessSchedules(
                    DateTimeUtils.TIMESTAMP_FORMAT_UTC.format(startDate.time),
                    DateTimeUtils.TIMESTAMP_FORMAT_UTC.format(endDate.time),
                    locationRefJson,
                    filters,
                    listener,
                    { errorListener.onErrorResponse(it) }
                )
            }
        } catch (error: Exception) {
            throw error
        }

        return response?.data?.map {
            it.attributes.toScheduleAttributes()
        }
    }
}
