package com.mindbodyonline.data.services;

import android.net.Uri;
import android.text.TextUtils;
import android.util.Base64;

import com.mindbodyonline.android.util.api.service.oauth.OAuthAccessParams;
import com.mindbodyonline.android.util.api.service.oauth.OAuthAccessToken;

import org.jetbrains.annotations.NotNull;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * Enum that encapsulates the various OAuth2 connection parameters for the different providers
 * <p/>
 * We capture the following properties for the demo application
 * <p/>
 * clientId
 * clientSecret
 * scope
 * redirectUri
 * apiUrl
 * tokenServerUrl
 * authorizationServerEncodedUrl
 * accessMethod
 */
public enum OAuth2Params implements OAuthAccessParams {

    FITBIT_OAUTH2("51db878fb0f26ac0cbdfedee9f4fd867", //Consumer key
        "229MKX", //Client ID
        "465a723fa7f2f379b8dbc67e696f4fd6", //Client Secret
        "https://api.fitbit.com/oauth2/token",  //Token server URL
        "https://www.fitbit.com/oauth2/authorize", //Authorization server URL
        "activity nutrition profile settings sleep social weight heartrate location", //Scope
        "x-mindbodyconnect-oauth-fitbit://authcode",  //Redirect URI
        "https://api.fitbit.com"), //API URL

    STRAVA_OAUTH2("", //Consumer key
        "26979", //Client ID
        "85fe02ee70d1cf1a3baf959a25452a5602643abd", //Client Secret
        "https://www.strava.com/oauth/token",  //Token server URL
        "https://www.strava.com/oauth/authorize?approval_prompt=force", //Authorization server URL
        "read_all,profile:write,activity:read_all,activity:write", //Scope
        "x-mindbodyconnect-oauth-strava://authcode",  //Redirect URI
        "https://www.strava.com/api/v3"), //API URL

    LYMBER_OAUTH_DEV("", //Consumer key
        "7a128b7bad5631fea812488c0646cd564ef70e86aaf11e96398c4080b42dffe3", //Client ID
        "6d141922977cb366235d02db78e82fcc63a41a2a99d0abd8bc9f6d91a2672e4e", //Client Secret
        "https://dev.lymberapi.com/oauth/token?grant_type=client_credentials",  //Token server URL
        "", //Authorization server URL
        "", //Scope
        "",  //Redirect URI
        "https://dev.lymberapi.com"), //API URL

    LYMBER_OAUTH_PROD("", //Consumer key
        "c235faf53f097f81e277aca36ec92070d13d21d2e6384d63deeb54fe8edb672a", //Client ID
        "70665a0f2bdecec3a997721ff3dff0eed22711c28100bb5319ba6e0cd0c27476", //Client Secret
        "https://dpapi.mindbodyonline.com/oauth/token?grant_type=client_credentials",  //Token server URL
        "", //Authorization server URL
        "", //Scope
        "",  //Redirect URI
        "https://dpapi.mindbodyonline.com"), //API URL

    //Gateway OAuth runs through existing auth, so no need for data.  Just need to separate
    // out the oauth data service from our existing one.
    GATEWAY_OAUTH("", //Consumer key
        "", //Client ID
        "", //Client Secret
        "",  //Token server URL
        "", //Authorization server URL
        "", //Scope
        "",  //Redirect URI
        ""), //API URL

    //Gateway OAuth runs through existing auth, so no need for data.  Just need to separate
    // out the oauth data service from our existing one.
    IDENTITY_PROD("", //Consumer key
        "Mindbody.ConnectApp.Android", //Client ID
        "e5e7c6eb-1189-a2c9-dda4-7d4bd6d76292", //Client Secret
        "",  //Token server URL
        "", //Authorization server URL
        "openid profile email offline_access Mindbody.Api.Connect Mindbody.Api.Rest Mindbody.Api.Payments Mindbody.Identity.UserGateway Mindbody.Identity.BusinessLinks Identity.Legacy.Gateway Mindbody.Clients",  //Scope
        "x-mindbodyconnect-oauth-mindbody://authcode",  //Redirect URI
        ""), //API URL

    //Gateway OAuth runs through existing auth, so no need for data.  Just need to separate
    // out the oauth data service from our existing one.
    IDENTITY_DEV("", //Consumer key
        "Mindbody.ConnectApp.Android", //Client ID
        "01d15abd-828a-db40-9ce9-e5dd3ac17f2e", //Client Secret
        "",  //Token server URL
        "", //Authorization server URL
        "openid profile email offline_access Mindbody.Api.Connect.Dev Mindbody.Api.Rest.Dev Mindbody.Api.Payments.Dev Mindbody.Identity.UserGateway.Dev Mindbody.Identity.BusinessLinks.Dev Mindbody.Clients.Stg", //Scope
        "x-mindbodyconnect-oauth-mindbody://authcode",  //Redirect URI
        ""); //API URL

    private final String consumerKey;
    private String clientId;
    private String clientSecret;
    private String scope;
    private String redirectUri;
    private String apiUrl;

    private String tokenServerUrl;
    private String authorizationServerEncodedUrl;

    OAuth2Params(String consumerKey, String clientId, String clientSecret, String tokenServerUrl, String authorizationServerEncodedUrl, String scope, String redirectUri, String apiUrl) {
        this.consumerKey = consumerKey;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.tokenServerUrl = tokenServerUrl;
        this.authorizationServerEncodedUrl = authorizationServerEncodedUrl;
        this.scope = scope;
        this.redirectUri = redirectUri;
        this.apiUrl = apiUrl;
    }

    public String getClientId() {
        if (this.clientId == null || this.clientId.length() == 0) {
            throw new IllegalArgumentException("Please provide a valid clientId in the Oauth2Params class");
        }
        return clientId;
    }

    public String getClientSecret() {
        if (this.clientSecret == null || this.clientSecret.length() == 0) {
            throw new IllegalArgumentException("Please provide a valid clientSecret in the Oauth2Params class");
        }
        return clientSecret;
    }

    public String getScope() {
        return scope;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public String getTokenServerUrl() {
        return tokenServerUrl;
    }

    public String getAuthorizationServerEncodedUrl() {
        return authorizationServerEncodedUrl;
    }

    public String getUserId() {
//        return ""+(MBAuth.getUser() != null ? MBAuth.getUser().getId() : "fitbit_test_user");
        return "";
    }

    public Map<String, String> getRetrieveTokenHeaders() {
        Map<String, String> headers = new HashMap<>();

        String baseNoEncode = clientId + ":" + clientSecret;
        headers.put("Authorization", "Basic " + Base64.encodeToString(baseNoEncode.getBytes(), Base64.NO_WRAP));
        headers.put("Content-Type", "application/x-www-form-urlencoded");

        return headers;
    }

    @Override public Map<String, String> getApiClientHeaders() {
        // No 3rd parties have support for api client headers yet
        return new HashMap<>();
    }

    public String getRetrieveTokenBody(String authCode) {
        if (!TextUtils.isEmpty(authCode)) {
            try {
                return "client_id=" + clientId +
                    "&client_secret=" + clientSecret +
                    "&grant_type=authorization_code" +
                    "&redirect_uri=" + URLEncoder.encode(redirectUri, "UTF-8") +
                    "&code=" + authCode;
            } catch (UnsupportedEncodingException e) {
                return "";
            }
        } else {
            return "";
        }
    }

    public String getRefreshTokenBody(@NotNull OAuthAccessToken token) {
        return "grant_type=refresh_token&refresh_token=" + token.getRefreshToken().trim();
    }

    public String getAuthorizationUrl() {
        Uri uri = Uri.parse(authorizationServerEncodedUrl);
        Uri.Builder builder = uri.buildUpon();
        builder.appendQueryParameter("client_id", clientId);
        builder.appendQueryParameter("prompt", "consent");
        builder.appendQueryParameter("redirect_uri", redirectUri);
        builder.appendQueryParameter("response_type", "code");
        builder.appendQueryParameter("scope", scope);
        return builder.toString();
    }

    public String getConsumerKey() {
        return consumerKey;
    }

    public String getPreferencesStoreID() {
        return name();
    }
}

