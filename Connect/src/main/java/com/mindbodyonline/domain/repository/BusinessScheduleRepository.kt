package com.mindbodyonline.domain.repository

import com.mindbodyonline.connect.adapters.filters.IFilter
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationRefJson
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.dataModels.schedule.ScheduleAttributes
import java.util.Calendar

interface BusinessScheduleRepository {

    suspend fun fetchBusinessScheduleData(
        locationRefJson: LocationRefJson?,
        startDate: Calendar,
        endDate: Calendar,
        filters: MutableSet<IFilter<ClassTypeObject>>? = null,
    ): List<ScheduleAttributes>?
}
