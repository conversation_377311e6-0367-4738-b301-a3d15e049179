package com.mindbodyonline.domain.usecase

import com.mindbodyonline.connect.adapters.filters.IFilter
import com.mindbodyonline.connect.common.Resource
import com.mindbodyonline.connect.common.utilities.fromJson
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationRefJson
import com.mindbodyonline.connect.utils.api.toLocationReference
import com.mindbodyonline.data.repository.BusinessScheduleRepositoryImpl
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.Location
import com.mindbodyonline.domain.dataModels.schedule.ClassAttributes
import com.mindbodyonline.domain.dataModels.schedule.ScheduleAttributes
import com.mindbodyonline.domain.datamapper.toCto
import com.mindbodyonline.domain.repository.BusinessScheduleRepository
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Calendar

class FetchScheduledClassesAndEnrolments(private val repository: BusinessScheduleRepository = BusinessScheduleRepositoryImpl()) {

    fun invokeLegacy(location: Location, startDate: Calendar, endDate: Calendar): Flow<Resource<List<ClassTypeObject>>> = flow {
        emit(Resource.Loading())

        val locRefJson = location.toLocationReference().location_ref_json.fromJson<LocationRefJson>()

        try {
            val deferredResult: Deferred<List<ScheduleAttributes>?> = coroutineScope {
                async {
                    repository.fetchBusinessScheduleData(
                        locationRefJson = locRefJson,
                        startDate = startDate,
                        endDate = endDate
                    )
                }
            }
            val result = deferredResult.await()
            result?.forEach { it.attributes?.location = location }

            val resultMappedToCto = result?.mapNotNull { it.attributes?.toCto() }?.toMutableList() ?: mutableListOf()
            emit(Resource.Success(resultMappedToCto.sortedBy { it.startDate.time }))
        } catch (e: Exception) {
            emit(Resource.Error(e.message ?: ""))
        }
    }

    operator fun invoke(
        location: Location,
        startDate: Calendar,
        endDate: Calendar,
        filters: MutableSet<IFilter<ClassTypeObject>>,
    ): Flow<Resource<List<ClassAttributes>>> = flow {
        emit(Resource.Loading())

        val locRefJson = location.toLocationReference().location_ref_json.fromJson<LocationRefJson>()

        try {
            val deferredResult: Deferred<List<ScheduleAttributes>?> = coroutineScope {
                async {
                    repository.fetchBusinessScheduleData(
                        locationRefJson = locRefJson,
                        startDate = startDate,
                        endDate = endDate,
                        filters = filters,
                    )
                }
            }
            val result = deferredResult.await()
            result?.forEach { it.attributes?.location = location }

            val resultMapped = result?.mapNotNull { it.attributes?.copy(isEnrolment = it.subType == "Enrollment") }?.toMutableList() ?: mutableListOf()
            emit(Resource.Success(resultMapped.sortedBy { it.startTime }))
        } catch (e: Exception) {
            emit(Resource.Error(e.message ?: ""))
        }
    }
}
