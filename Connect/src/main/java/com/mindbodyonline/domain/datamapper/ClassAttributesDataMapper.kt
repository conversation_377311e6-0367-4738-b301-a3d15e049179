package com.mindbodyonline.domain.datamapper

import com.mindbodyonline.ConnectApp
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.common.utilities.fromJson
import com.mindbodyonline.connect.utils.BookabilityStatusUtils
import com.mindbodyonline.connect.utils.BookabilityStatusUtils.getActionStringResourceId
import com.mindbodyonline.connect.utils.BookabilityStatusUtils.hasClassPassed
import com.mindbodyonline.connect.utils.BookabilityStatusUtils.isBlocked
import com.mindbodyonline.connect.utils.BookabilityStatusUtils.isBooked
import com.mindbodyonline.connect.utils.PaymentUtils
import com.mindbodyonline.connect.utils.api.ISO_8601
import com.mindbodyonline.connect.utils.api.dynamicpricing.DynamicPricingToken
import com.mindbodyonline.connect.utils.api.dynamicpricing.cloudsearch.CloudSearchUtils
import com.mindbodyonline.connect.utils.api.gateway.model.ClassStatus
import com.mindbodyonline.connect.utils.api.gateway.model.CourseAttributes
import com.mindbodyonline.connect.utils.api.gateway.model.InventoryRefJsonModel
import com.mindbodyonline.connect.utils.isFamilyAccountType
import com.mindbodyonline.connect.utils.time.DateTimeFormatUtilsKt.formatDateWithTimeZone
import com.mindbodyonline.connect.utils.time.DateTimePattern
import com.mindbodyonline.data.dataModels.schedule.ClassAttributesDTO
import com.mindbodyonline.data.dataModels.schedule.PricingInfoDTO
import com.mindbodyonline.data.dataModels.schedule.PricingTokenDTO
import com.mindbodyonline.data.dataModels.schedule.PurchaseOptionDTO
import com.mindbodyonline.data.dataModels.schedule.ScheduleAttributesDTO
import com.mindbodyonline.data.dataModels.schedule.StaffDTO
import com.mindbodyonline.data.services.MBAuth
import com.mindbodyonline.domain.BookabilityStatus
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.FavoriteClass
import com.mindbodyonline.domain.Room
import com.mindbodyonline.domain.Staff
import com.mindbodyonline.domain.dataModels.PricingInfo
import com.mindbodyonline.domain.dataModels.PricingReference
import com.mindbodyonline.domain.dataModels.schedule.ClassAttributes
import com.mindbodyonline.domain.dataModels.schedule.ClassAttributesCourse
import com.mindbodyonline.domain.dataModels.schedule.PricingToken
import com.mindbodyonline.domain.dataModels.schedule.ScheduleAttributes
import com.mindbodyonline.ui.component.mbClass.ClassListItem
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone

fun ScheduleAttributesDTO.toScheduleAttributes() = ScheduleAttributes(
    subType = subType,
    attributes = attributes?.toClassAttributes(),
)

fun ClassAttributesDTO.toClassAttributes() = ClassAttributes(
    capacity = capacity,
    startTime = startTime,
    endTime = endTime,
    duration = duration,
    staff = staff?.toStaff(),
    course = course?.toClassAttributesCourse(),
    inventoryRefJson = inventoryRefJson,
    openings = openings,
    webOpenings = webOpenings,
    dpOpenings = dpOpenings,
    pricingReference = purchaseOption?.toPricingReference(),
    prerequisiteNotes = prerequisiteNotes,
    isFreeToEnroll = isFreeToEnroll,
    cancellationPolicy = cancellationPolicy,
    refundPolicy = refundPolicy,
    livestreamEnabled = livestreamEnabled,
    contentFormats = contentFormats,
    room = room?.toDomainModel(),
    status = status?.toBookabilityStatus(),
    pricingToken = pricingToken?.toPricingToken(),
)

fun CourseAttributes.toClassAttributesCourse() = ClassAttributesCourse(
    externalId = externalId,
    category = category,
    subcategory = subcategory,
    name = name,
    description = description,
    slug = slug,
    stockImage = stockImage,
    recordType = recordType,
    inventoryRefJson = inventoryRefJson,
    inventorySource = inventorySource,
    virtual = virtual,
    prereqs = prereqs,
    tags = tags
)

fun com.mindbodyonline.connect.utils.api.gateway.model.Room.toDomainModel() = Room(
    id ?: 0,
    name
)

fun ClassStatus.toBookabilityStatus() = BookabilityStatus(
    id ?: 0,
    status
)

fun StaffDTO.toStaff(): Staff {
    val nameParts = name?.split(" ")

    val firstName = nameParts?.firstOrNull()
    val lastName = nameParts?.lastOrNull()
    return Staff(
        /* id = */ inventoryRefJson?.fromJson<InventoryRefJsonModel>()?.mb_staff_id?.toLong() ?: 0,
        /* firstName = */ firstName,
        /* lastName = */  lastName,
        /* imageUrl = */  avatar,
        /* bio = */ bio,
        /* email = */ null,
        /* phone = */ null,
        /* gender = */ gender?.name,
        /* displayName = */ name,
    )
}

fun PurchaseOptionDTO.toPricingReference() = PricingReference(
    id = id,
    name = name,
    isPackage = isPackage,
    isDynamicallyPriced = isDynamicallyPriced,
    isIntroOffer = isIntroOffer,
    isSingleSession = isSingleSession,
    pricing = pricing?.toPricingInfo()
)

fun PricingInfoDTO.toPricingInfo() = PricingInfo(
    retail = retail,
    online = online
)

fun PricingTokenDTO.toPricingToken() = PricingToken(
    token = token,
    amount = amount,
    ttl = ttl,
    classTimeId = classTimeId,
    dropInPrice = dropInPrice,
    mindbodyId = mindbodyId,
    surge = surge,
    active = active
)

fun ClassAttributes.toCto(): ClassTypeObject? {
    if (course == null) return null

    val cto: ClassTypeObject = FavoriteClass()
    cto.id = inventoryRefJson?.fromJson<InventoryRefJsonModel>()?.mb_class_id ?: 0
    cto.name = course.name ?: ""
    cto.description = course.description
    cto.capacity = capacity ?: 0
    cto.numberRegistered = (capacity ?: 0) - (openings ?: 0)
    cto.prerequisiteNotes = prerequisiteNotes
    cto.classImageUrl = course.stockImage
    cto.location = location

    cto.startTime = startTime
    cto.endTime = endTime
    cto.startDate = ISO_8601.parse(startTime)
    cto.endDate = ISO_8601.parse(endTime)

    cto.staff = staff
    cto.room = room
    cto.status = status
    cto.classTypeId = 0
    cto.classDescriptionId = 0
    cto.cancellationPolicy = cancellationPolicy
    cto.refundPolicy = refundPolicy
    cto.category = course.category
    cto.subcategory = course.subcategory
    cto.setIsFreeToEnroll(isFreeToEnroll)
    cto.contentFormats = contentFormats?.toTypedArray()
    cto.inventoryRefJson = inventoryRefJson
    cto.inventorySource = location?.inventorySource
    cto.gatewayId = id
    cto.isVirtual = course.virtual ?: false
    cto.pricingReference = pricingReference
    CloudSearchUtils.assignPricingTokenToClassTypeObject(pricingToken?.toDynamicPricingToken(), cto)
    cto.setTimeZone(location?.timezoneId?.let { TimeZone.getTimeZone(it) } ?: TimeZone.getDefault())
    return cto
}

fun PricingToken.toDynamicPricingToken(): DynamicPricingToken {
    val result = DynamicPricingToken()
    result.token = token
    result.ttl = ttl
    result.classTimeId = classTimeId
    result.dropInPrice = dropInPrice.toBigDecimal()
    result.setAmount(amount.toBigDecimal())
    return result
}

fun ClassAttributes.toClassListItem(): ClassListItem {
    val actionStringRes = getActionButtonStringRes()

    // drop-in price is taken from pricing token first, only if it is null, pricingReference pricing is picked
    // check ClassTypeObject.setPricingReference() for reference
    dropInPrice = pricingToken?.dropInPrice ?: pricingReference?.pricing?.online?.toDouble()

    val item = ClassListItem(
        name = course?.name ?: "",
        ctaText = if (actionStringRes == -1) null else ConnectApp.getInstance().getString(actionStringRes),
        isPrimaryCta = actionStringRes == ClassTypeObject.ClassStatus.BOOKABLE.actionStringResId,
        showDynamicPrice = showTopPrice(),
        showOriginalPrice = showBottomPrice(),
        staffName = staff?.displayName ?: "",
        startTime = Calendar.getInstance().apply {
            time = ISO_8601.parse(startTime)
        },
        endTime = Calendar.getInstance().apply {
            time = ISO_8601.parse(endTime)
        },
        duration = duration ?: 0,
    )
    item.setClassTypes(this)
    item.setPricingData(this)
    val classTimeData = getClassTimeData()
    item.startTimeText = classTimeData.first
    item.timeZoneText = classTimeData.second
    return item
}

fun ClassAttributes.getActionButtonStringRes(): Int {
    val endDate = ISO_8601.parse(endTime)
    if (status == null || status.id == BookabilityStatus.ERROR) {
        return -1
    }

    return if (isFamilyAccountType()) {
        if (status.id == BookabilityStatus.BOOKED) {
            getCtaStringRes(course, status, staff, ClassTypeObject.ClassStatus.BOOKABLE.actionStringResId)
        } else {
            getCtaStringRes(course, status, staff, status.getActionStringResourceId(staff, endDate))
        }
    } else {
        getCtaStringRes(course, status, staff, status.getActionStringResourceId(staff, endDate))
    }
}

fun getCtaStringRes(course: ClassAttributesCourse?, bookabilityStatus: BookabilityStatus?, staff: Staff?, actionStringResourceId: Int): Int {
    var stringResourceId = actionStringResourceId
    if (bookabilityStatus != null && (bookabilityStatus.isBlocked() || BookabilityStatusUtils.isVirtual(course)) && !bookabilityStatus.isBooked()) {
        stringResourceId = when (bookabilityStatus.id) {
            BookabilityStatus.CLASS_FULL, BookabilityStatus.ONLINE_CAPACITY_FULL -> R.string.full_menu_title
            else -> if (BookabilityStatusUtils.isCancelled(staff)) R.string.cancelled_text else R.string.cta_details
        }
    }
    return stringResourceId
}


fun ClassAttributes.showTopPrice(): Boolean {
    val showTopAsFree = status?.isBooked() == false && isFreeToEnroll
    val showTopPrice = !showTopAsFree && status?.isBooked() == false && dropInPrice != null && status.id == BookabilityStatus.PAYMENT_REQUIRED
    return showTopPrice
}

fun ClassAttributes.showBottomPrice(): Boolean {
    val showBottomPrice = showTopPrice() && pricingToken?.surge != true
    return showBottomPrice
}

fun ClassListItem.setClassTypes(classAttributes: ClassAttributes) {
    var textToDisplay: String? = null
    val icon: Int? = if (BookabilityStatusUtils.isVirtual(classAttributes.course)) R.drawable.ic_virtual_icon else null

    if (classAttributes.isEnrolment) {
        textToDisplay = ConnectApp.getInstance().getString(R.string.event_tag)
    } else if (classAttributes.course?.category?.isNotBlank() == true) {
        textToDisplay =
            if (classAttributes.course.subcategory?.isNotBlank() == true) {
                ConnectApp.getInstance().getString(R.string.categories_text, classAttributes.course.category, classAttributes.course.subcategory)
            } else {
                classAttributes.course.category
            }
    }
    classTypeText = textToDisplay?.uppercase()
    classTypeIcon = icon
}

fun ClassListItem.setPricingData(classAttributes: ClassAttributes) {
    val showTopAsFree = classAttributes.status?.isBooked() == false && classAttributes.isFreeToEnroll
    val checkedLocale = classAttributes.location?.locale ?: Locale.getDefault()

    if (classAttributes.showTopPrice()) {
        val isIntroOffer = classAttributes.pricingReference?.isIntroOffer
        val isDropIn = classAttributes.pricingReference?.isSingleSession
        if (classAttributes.pricingToken?.amount == null) {
            val classIsFree = classAttributes.dropInPrice == 0.0
            dynamicPriceText =
                if (classIsFree)
                    ConnectApp.getInstance().getString(R.string.free_price)
                else
                    PaymentUtils.getFormattedCurrency(classAttributes.dropInPrice ?: 0.0, checkedLocale, true)

            highlightDynamicPrice = classIsFree
            originalPriceText = getOriginalPriceText(isIntroOffer == true, isDropIn == true)
        } else {
            originalPriceText =
                ConnectApp.getInstance().getString(
                    R.string.old_price,
                    PaymentUtils.getFormattedCurrency(classAttributes.dropInPrice ?: 0.0, checkedLocale)
                )

            dynamicPriceText =
                PaymentUtils.getFormattedCurrency(
                    classAttributes.pricingToken.amount, checkedLocale, true
                )

            highlightDynamicPrice = true
        }
        highlightOriginalPrice = isIntroOffer == true
    } else if (showTopAsFree) {
        dynamicPriceText = ConnectApp.getInstance().getString(R.string.free_price)
        highlightDynamicPrice = true
    }
}

fun getOriginalPriceText(isIntroOffer: Boolean, isDropIn: Boolean): String {
    var priceText = ""
    if (isIntroOffer) {
        priceText = ConnectApp.getInstance().getString(R.string.intro_offer_label)
    } else if (isDropIn) {
        priceText = ConnectApp.getInstance().getString(R.string.drop_in)
    }
    return priceText.uppercase(Locale.getDefault())
}

fun ClassAttributes.getClassTimeData(): Pair<String?, String?> {
    var startTimeText: String? = null
    var timezoneText: String? = null

    val startDate = ISO_8601.parse(startTime)
    val timezone = location?.timezoneId?.let {
        TimeZone.getTimeZone(it)
    } ?: TimeZone.getDefault()

    val locale = location?.locale ?: Locale.getDefault()

    if (startTime == endTime) {
        startTimeText = ConnectApp.getInstance().getString(R.string.to_be_determined_short)
    } else {
        if (TimeZone.getDefault() != timezone) {
            timezoneText = timezone.getDisplayName(
                timezone.inDaylightTime(startDate), TimeZone.SHORT
            )
        }

        startTimeText = formatDateWithTimeZone(
            startDate,
            DateTimePattern.SHORT_TIME_WITH_PERIOD_PATTERN,
            timezone,
            locale
        ).lowercase(Locale.getDefault())
    }
    return Pair(startTimeText, timezoneText)
}
