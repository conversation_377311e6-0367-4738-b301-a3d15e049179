<?xml version='1.0' encoding='UTF-8'?>
<resources xmlns:tools="http://schemas.android.com/tools" xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- String arrays -->
    <string-array name="rating_star_captions">
        <item>Awful</item>
        <item>Meh</item>
        <item>OK</item>
        <item>Good</item>
        <item>Loved it!</item>
    </string-array>

    <!-- Category / Sub-category constants -->
    <string name="fitness_vertical_title">Fitness</string>
    <string name="beauty_vertical_title">Beauty</string>
    <string name="wellness_vertical_title">Wellness</string>
    <string name="spin_searchable">Spin</string>
    <string name="spin_onscreen">Cycling</string>

    <!-- Deals bubble filters -->

    <string-array name="home_lmo_banner_copy">
        <item>Your workouts, your way</item>
        <item>Choose from a variety of fitness classes.</item>
        <item>Last Minute Offers</item>
    </string-array>

    <string-array name="home_intro_offer_banner_copy">
        <item>Deals to get started</item>
        <item>Ready to score a sweet deal?</item>
        <item>Intro Offers</item>
    </string-array>

    <string-array name="home_virtual_banner_copy">
        <item>Sweat from home!</item>
        <item>Stay in shape and support your favorite studios.</item>
        <item>Virtual Classes</item>
    </string-array>

    <string name="fitness_subvertical_yoga">Yoga</string>
    <string name="fitness_subvertical_group_exercise">Group</string>
    <string name="fitness_subvertical_personal_training">Personal Training</string>
    <string name="fitness_subvertical_personal_training_activity">Personal training</string>
    <string name="fitness_subvertical_pilates">Pilates</string>
    <string name="fitness_subvertical_barre">Barre</string>
    <string name="fitness_subvertical_spin_onscreen">@string/spin_onscreen</string>
    <string name="fitness_subvertical_bootcamp">Bootcamp</string>
    <string name="fitness_subvertical_dance_fitness">Dance Fitness</string>
    <string name="fitness_subvertical_strength_training">Strength Training</string>
    <string name="fitness_subvertical_pole_fitness">Pole Fitness</string>
    <string name="fitness_subvertical_pole_fitness_activity">Pole fitness</string>
    <string name="fitness_subvertical_suspension_training">Suspension Training</string>
    <string name="fitness_subvertical_sports">Sports</string>
    <string name="fitness_subvertical_gym">Gym Classes</string>
    <string name="fitness_subvertical_gym_activity">Gym classes</string>
    <string name="fitness_subvertical_mma">MMA</string>
    <string name="fitness_subvertical_running">Running</string>
    <string name="fitness_subvertical_circuit_training">Circuit\nTraining</string>
    <string name="fitness_subvertical_circuit_training_activity">Circuit training</string>
    <string name="fitness_subvertical_kickboxing">KickBoxing</string>
    <string name="fitness_subvertical_boxing">Boxing/\nKickboxing</string>
    <string name="fitness_subvertical_boxing_activity">Boxing/kickboxing</string>
    <string name="fitness_subvertical_crossfit">CrossFit®</string>
    <string name="fitness_subvertical_aerial">Aerial</string>
    <string name="fitness_subvertical_interval_training">Interval Training</string>
    <string name="fitness_subvertical_interval_training_activity">Interval training</string>
    <string name="fitness_subvertical_kettlebell">Kettlebell</string>
    <string name="fitness_subvertical_rock_climbing">Rock Climbing</string>
    <string name="fitness_subvertical_rock_climbing_activity">Rock climbing</string>
    <string name="fitness_subvertical_prenatal">Prenatal</string>
    <string name="fitness_subvertical_weight_training">Weight Training</string>
    <string name="fitness_subvertical_weight_training_activity">Weight training</string>
    <string name="fitness_subvertical_gymnastics">Gymnastics</string>
    <string name="fitness_subvertical_martial_arts">Martial Arts</string>
    <string name="fitness_subvertical_martial_arts_activities">Martial arts</string>
    <string name="fitness_subvertical_childrens_fitness">Children\'s Fitness</string>
    <string name="fitness_subvertical_hooping">Hooping</string>
    <string name="fitness_subvertical_core">Core</string>
    <string name="fitness_subvertical_aerobics_classes">Aerobics Classes</string>
    <string name="fitness_subvertical_high_intensity_training">High Intensity Training</string>
    <string name="fitness_subvertical_physical_therapy">Physical Therapy/Physiotherapy</string>
    <string name="fitness_subvertical_swim">Swim</string>
    <string name="fitness_subvertical_tennis">Tennis</string>
    <string name="fitness_subvertical_dance">Dance</string>

    <string name="beauty_subvertical_salon">Hair Salon</string>
    <string name="beauty_subvertical_salon_activity">Hair salon</string>
    <string name="beauty_subvertical_salon_searchable">Hair</string>
    <string name="beauty_subvertical_wax">Wax</string>
    <string name="beauty_subvertical_nails">Nails</string>
    <string name="beauty_subvertical_face">Face Treatments</string>
    <string name="beauty_subvertical_face_activity">Face treatments</string>
    <string name="beauty_subvertical_skin">Skin</string>
    <string name="beauty_subvertical_makeup_lashes_brows">Makeup/Lashes/Brows</string>
    <string name="beauty_subvertical_makeup_lashes_brows_activity">Makeup/lashes/brows</string>
    <string name="beauty_subvertical_massage">Massage</string>
    <string name="beauty_subvertical_tanning">Tanning</string>
    <string name="beauty_subvertical_tattoo_piercing">Tattoo/Piercing</string>
    <string name="beauty_subvertical_tattoo_piercing_activity">Tattoo/piercing</string>
    <string name="beauty_subvertical_barber">Barber</string>
    <string name="beauty_subvertical_braids">Braids</string>
    <string name="beauty_subvertical_textured_hair">Textured hair</string>
    <string name="beauty_subvertical_sauna">Sauna</string>
    <string name="beauty_subvertical_spa">Med Spa</string>
    <string name="beauty_subvertical_spa_activity">Med spa</string>
    <string name="beauty_subvertical_eyebrow_threading">Eyebrow Threading</string>
    <string name="beauty_subvertical_laser_hair_removal">Laser Hair Removal</string>
    <string name="beauty_subvertical_hair_removal">Hair Removal</string>
    <string name="beauty_subvertical_hair_removal_activity">Hair removal</string>
    <string name="wellness_subvertical_massage">Massage</string>
    <string name="wellness_subvertical_chiropractor">Chiropractor</string>
    <string name="wellness_subvertical_acupuncture">Acupuncture</string>
    <string name="wellness_subvertical_meditation">Meditation</string>
    <string name="wellness_subvertical_tai_chi">Tai Chi</string>
    <string name="wellness_subvertical_tai_chi_activity">Tai chi</string>
    <string name="wellness_subvertical_physical_therapy">Physical Therapy/Physiotherapy</string>
    <string name="wellness_subvertical_coaching_healing">Coaching/Healing</string>
    <string name="wellness_subvertical_coaching_healing_activity">Coaching/healing</string>
    <string name="wellness_subvertical_cryotherapy">Cryotherapy</string>
    <string name="wellness_subvertical_heated_therapy">Heated Therapy</string>
    <string name="wellness_subvertical_heated_therapy_activity">Heated therapy</string>
    <string name="wellness_subvertical_water_therapy">Water Therapy</string>
    <string name="wellness_subvertical_water_therapy_activity">Water therapy</string>
    <string name="wellness_subvertical_body_treatments">Body Treatments</string>
    <string name="wellness_subvertical_body_treatments_activity">Body treatments</string>
    <string name="subvertical_kids">Kids</string>
    <string name="wellness_subvertical_qi_gong">Qi Gong</string>
    <string name="wellness_subvertical_chakra">Chakra</string>
    <string name="wellness_subvertical_wellness">Wellness</string>
    <string name="wellness_subvertical_naturopathic_medicine">Naturopathic Medicine</string>
    <string name="wellness_subvertical_naturopathic_medicine_activity">Naturopathic medicine</string>
    <string name="wellness_subvertical_reflexology">Reflexology</string>
    <string name="wellness_subvertical_nutrition">Nutrition</string>

    <string-array name="distance_units">
        <item>Use my country\'s units</item>
        <item>Miles</item>
        <item>Kilometers</item>
    </string-array>

    <!-- Plural strings-->

    <plurals name="numberOfFilters">
        <item quantity="one">%d Filter</item>
        <item quantity="other">%d Filters</item>
    </plurals>

    <plurals name="classes_plural">
        <item quantity="one">Class</item>
        <item quantity="other">Classes</item>
    </plurals>

    <plurals name="intro_offer_plural">
        <item quantity="one">%d intro offer</item>
        <item quantity="other">%d intro offers</item>
    </plurals>
    <!-- Regular Strings -->

    <string name="day_one_letter">S</string>
    <string name="day_two_letter">M</string>
    <string name="day_three_letter">T</string>
    <string name="day_four_letter">W</string>
    <string name="day_five_letter">T</string>
    <string name="day_six_letter">F</string>
    <string name="day_seven_letter">S</string>

    <!-- A 1-character indication of an availability in the morning (before 11am) -->
    <string name="morning_short">M</string>
    <!-- A 1-character indication of an availability in the afternoon (after 11am, but before 4pm) -->
    <string name="afternoon_short">A</string>
    <!-- A 1-character indication of an availability in the evening (after 4pm) -->
    <string name="evening_short">E</string>

    <string name="credit_card_removed">Credit card has been removed</string>
    <string name="mindbody_card_removed">Mindbody Card has been removed</string>
    <string name="credit_card_not_removed">Credit card could not be removed</string>
    <string name="cards">Cards</string>
    <string name="gifts">Gifts</string>
    <string name="gift_cards">Gift Cards</string>
    <!-- Indicates the last four digits of a card -->
    <string name="card_last_four">…<xliff:g name="last_four" example="1234">%1$s</xliff:g></string>
    <string name="tab">Tab</string>
    <string name="date_picker_footer">DONE</string>
    <string name="request_appointment">Request Appointment</string>
    <string name="appointment_text">Appointment</string>
    <string name="action_settings">Settings</string>
    <string name="action_search">Search</string>
    <string name="first_name">First name</string>
    <string name="last_name">Last name</string>
    <string name="register_button">Let\'s go</string>
    <string name="action_sign_up">Book</string>
    <string name="login_action">Sign in</string>
    <string name="title_activity_favorites">Favorites</string>
    <string name="home_favorites_section_title">Book a favorite</string>
    <string name="home_lmos_section_title">Trending deals near you</string>
    <string name="business_details_description_text">Description</string>
    <!-- title for a section that describes a class a user might attend -->
    <!-- format for start time, duration, and date of a schedule item (eg, a class or appointment) -->
    <string name="schedule_item_time_duration_date_format"><xliff:g name="start time" example="9:45am">%1$s</xliff:g> (<xliff:g name="duration" example="60">%2$d</xliff:g>min) | <xliff:g name="date" example="Sunday, July 9">%3$s</xliff:g></string>
    <!-- title for a section that describes an appointment a user might attend -->
    <string name="appointment_description_title">About this Appointment</string>
    <string name="loading_text">Loading…</string>
    <string name="submitting_text">Submitting…</string>
    <!-- Content description for the button to center map on the users current location -->
    <string name="content_description_center_location_button">Go to my location</string>
    <string name="search_this_area">Search this area</string>
    <string name="logout_dialog_message">Would you like to log out of the Mindbody App?</string>
    <string name="cancel_dialog_message">Do you want to cancel this booking?</string>
    <string name="dspo_cancel_dialog_message">A pass will not be returned when canceling this class. Do you want to cancel this booking?</string>
    <string name="late_cancel_dialog_message">If you cancel now, a session will be deducted from your pass.  Do you want to cancel your booking?</string>
    <string name="booking_confirmation_add_to_calendar_text">Add to calendar</string>
    <string name="add_a_credit_card_message">Add a Card</string>
    <string name="description_header">Description</string>
    <string name="title_activity_my_schedule">My Schedule</string>
    <string name="filter_menu_item_title">Filters</string>
    <string name="calendar_menu_item_title">Calendar</string>
    <string name="nav_feedback_text">Tell us what you think</string>
    <string name="share_subject">You should try the Mindbody App</string>
    <string name="share_subject_business">Check out %1$s on Mindbody!</string>
    <string name="share_app_content">I use the Mindbody App to easily book my fitness, wellness, and beauty services. Check it out! http://mndbdy.ly</string>
    <string name="share_business_content">I just found <xliff:g name="businessname" example="Spark Yoga">%1$s</xliff:g> on Mindbody! Let\'s go check it out: http://mndbdy.ly</string>
    <string name="share_staff_content">Hey! I just found <xliff:g name="staff" example="Joe Pesci">%1$s</xliff:g> on Mindbody! See what you can book with them now! http://mndbdy.ly</string>
    <!-- Default text for when a user is sharing a booking with another person -->
    <string name="share_booking_content">I booked <xliff:g name="class" example="Cycling Class">%1$s</xliff:g> at <xliff:g name="location" example="Blockbuster">%2$s</xliff:g> using the Mindbody App. Check it out! http://mndbdy.ly</string>
    <string name="share_unbooked_content">Hey,\n\nWant to workout? Let\'s try this class I found on Mindbody at <xliff:g name="location" example="Costco">%1$s</xliff:g>! http://mndbdy.ly</string>
    <string name="share_deal">I just found this %1$s deal at %2$s using the Mindbody app. Check it out! http://mndbdy.ly</string>
    <string name="share_activity_dashboard_linktext">How many classes have you taken? Check out all your stats on the new Activity dashboard! http://mndbdy.ly</string>
    <string name="share_activity_dashboard_choose">Share your activity dashboard using…</string>
    <string name="share_activity_dashboard_subject">Check out my activity dashboard on Mindbody!</string>
    <string name="share_activity_dashboard_title">Mindbody Activity Dashboard</string>
    <string name="share_fitbit">Check out my stats from %1$s! #mindbody http://mndbdy.ly</string>
    <string name="generic_share_class_name">a class</string>
    <string name="generic_share_appointment_name">an appointment</string>
    <string name="generic_share_appointment_content">I booked an appointment using the Mindbody App. Check it out! http://mndbdy.ly</string>
    <string name="generic_share_class_content">I booked a class using the Mindbody App. Check it out! http://mndbdy.ly</string>
    <string name="share_app_title">Share Mindbody App using…</string>
    <string name="share_business_title">Share this business using…</string>
    <string name="share_staff_title">Share this instructor using…</string>
    <string name="share_class_title">Share this class using…</string>
    <string name="share_appointment_title">Share this appointment using…</string>
    <string name="share_deal_title">Share this deal using…</string>
    <string name="share_fitbit_title">Share activity using…</string>
    <string name="user_profile_image_content_description">User Profile Image</string>
    <string name="generic_username">User</string>
    <string name="passes_title">Passes</string>
    <string name="nothanksbutton">No thanks</string>
    <string name="no_results">Reset your filters or expand your search radius.</string>
    <string name="no_results_text_for_new_search_screen">Reset your filters.</string>
    <string name="no_location_available_text">Share your current location with us, or tell us where to search.</string>
    <string name="no_results_header">Hmmm, no results found.</string>
    <string name="no_location_header">Hmmm, we don’t know where you are!</string>
    <string name="try_new_search">New search</string>
    <string name="share_location">Share Location</string>
    <string name="password_change_dialog_message">Check your email for a link to reset your password.</string>
    <string name="tour_welcome_header_message">Sign in</string>
    <string name="close">Close</string>
    <string name="rating_and_reviews_header_text">Reviews</string>
    <string name="upvote_header_text">Was this review helpful?</string>
    <string name="notification_channel_review_title">@string/pref_title_ratings_reviews</string>
    <string name="notification_channel_review_description">@string/pref_summary_ratings_reviews</string>
    <string name="notification_channel_sign_in_title">@string/pref_title_signin_notifications</string>
    <string name="notification_channel_sign_in_description">@string/pref_summary_signin_notifications</string>
    <string name="notification_channel_activity_title">@string/pref_title_activity_dashboard_notification</string>
    <string name="notification_channel_activity_description">@string/pref_summary_activity_dashboard_notification</string>
    <string name="notification_channel_sync_description">Notification displayed when running background services</string>
    <string name="notification_title">How was your visit?</string>
    <string name="review_notification_subtitle">Review %1$s</string>
    <string name="review_sample_text">Share your opinion (optional)</string>
    <string name="signed_in_text">You are signed in</string>
    <string name="resend_activation_email_title">Send Confirmation Email</string>
    <string name="resend_activation_email_subtitle">A confirmation link is being sent to <xliff:g name="email" example="<EMAIL>">%1$s</xliff:g></string>
    <string name="resend_activation_email_failed">Could not resend activation email</string>
    <!-- Indicates that an action will discard any changes the user has made to their profile -->
    <string name="discard_profile_edits_confirmation">Discard</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="current_map_area_text">Current map area</string>
    <!-- Title for My Profile screen -->
    <string name="title_activity_profile">Profile</string>
    <string name="favorite_businesses_no_businesses">Look up a business and tap the star icon to add it as a favorite.</string>
    <string name="delete_review">Delete</string>
    <string name="no_reviews">No reviews yet.</string>
    <!-- a title for a section that gives a text response to a user's review -->
    <string name="business_response_title">Response from <xliff:g name="location" example="Blockbuster">%1$s</xliff:g></string>
    <string name="by_user_phrase">by %s</string>
    <string name="by_phrase">by </string>
    <string name="no_data">Whoops!\n We can\'t connect to the network.</string>
    <string name="business_optout_title">We miss them, too</string>
    <string name="business_optout_subtitle">%1$s is no longer listed on Mindbody.</string>
    <string name="business_optout_removebutton">Remove from Favorites</string>
    <string name="share_this_business">Share</string>
    <string name="see_more_button_text">see more</string>
    <string name="close_button_text">Close</string>
    <string name="remove_review_header_text">Delete review?</string>
    <string name="remove_review_message">Would you like to delete your review?</string>
    <string name="after_review_message">Thanks for your review!</string>
    <string name="change_location_hint">City, postal code, or address</string>
    <string name="select_a_location_message">Please select a location.</string>
    <string name="my_current_location">Current location</string>
    <string name="title_activity_appointment_details">Appointment Details</string>
    <string name="booked_text">Booked</string>
    <string name="cancel_appointment_dialog_title">Cancel this appointment?</string>
    <string name="late_cancel_appointment_message">Please note, you will still be charged as though you attended.</string>
    <string name="yes_late_cancel">Yes, Late Cancel</string>
    <string name="early_cancel_appointment_message">Would you like to cancel this appointment?</string>
    <string name="cancelling_appointment_text">Cancelling your appointment…</string>
    <string name="could_not_cancel_appt">Could not cancel your appointment.</string>
    <string name="book_similar_appointment_button_text">Book Again</string>
    <string name="requested">Requested</string>
    <string name="deals_near_me">Deals Near Me</string>
    <string name="banner_enrollments">Events</string>
    <string name="banner_classes">Classes</string>
    <string name="banner_appointments">Appointments</string>
    <string name="banner_appointments_abbreviated">Appts</string>
    <string name="banner_contracts">Plans &amp; more</string>
    <string name="qualified_classes_list_action_bar_title_intro_offers">Qualified Classes</string>
    <string name="qualified_events_list_action_bar_title_intro_offers">Qualified Events</string>
    <string name="qualified_classes">Qualified Classes</string>
    <string name="view_all_qualified_classes">View qualified classes</string>
    <string name="view_all_qualified_appointments">View qualified appointments</string>
    <string name="view_all_qualified_events">View qualified events</string>
    <string name="workplace_filter_text">Only show businesses that\naccept the Mindbody Card</string>
    <string name="workplace_header_text">Mindbody Card</string>
    <string name="filter_header_text">Filters</string>
    <string name="prepaid_classes_banner">Use your %1$s to book these classes</string>
    <string name="no_classes_results">We looked, but we didn\'t find any classes on this day.</string>
    <string name="class_bookable_filter_description">Allows online booking</string>
    <string name="class_bookable_filter_title">Bookable Online</string>
    <string name="class_bookable_filter_subtext" />
    <string name="qualified_class_filter_description">Accepts my passes</string>
    <string name="qualified_class_filter_title">My Passes</string>
    <string name="qualified_class_filter_subtext">Only show qualified classes</string>
    <string name="clear_filters_button">Clear Filters</string>
    <string name="user_has_no_passes">You\'re out of passes to your favorites</string>
    <string name="user_has_no_passes_subtext">Try clearing the filters, or buy a new pass</string>
    <string name="no_classes_user_has_sessions">We looked but didn\'t find any classes that accept your passes on this day.</string>

    <string name="deal_purchased_message">You found a great deal.\nEnjoy!</string>
    <string name="morning">Morning</string>
    <string name="afternoon">Afternoon</string>
    <string name="evening">Evening</string>
    <string name="rate_review_notification_action">Rate &amp; Review</string>
    <string name="sign_in_notification_action">Sign In</string>
    <string name="skip">Skip</string>
    <string name="action_create_account">Create Account</string>
    <string name="more_button_caps">MORE</string>
    <string name="less_button_caps">LESS</string>
    <string name="book_button_caps">BOOK</string>
    <string name="close_button_caps">CLOSE</string>
    <string name="free_price">FREE</string>
    <string name="drop_in">DROP-IN</string>
    <string name="action_done_caps">DONE</string>
    <string name="action_cancel_caps" tools:ignore="ButtonCase">CANCEL</string>
    <string name="submit">Submit</string>
    <string name="remove_caps">REMOVE</string>
    <string name="delete_caps">DELETE</string>
    <string name="server_error_dialog_title">Whoops</string>
    <string name="server_error_dialog_message">Something went wrong, please try again soon.</string>
    <string name="login_with_google_button_text">Continue with Google</string>
    <string name="login_with_facebook_button_text">Continue with Facebook</string>
    <string name="login_with_apple_button_text">Continue with Apple</string>
    <string name="link_fitbit_button_text">Connect Your <xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g></string>
    <string name="unlink_fitbit_button_text">Disconnect Your <xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g></string>
    <string name="link_strava_button_text">Connect Strava</string>
    <string name="unlink_strava_button_text">Disconnect Strava</string>
    <string name="fitbit_social_message_text">Connect with <xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g> to view personal activity data achieved during your workouts.</string>
    <string name="strava_social_message_text">Sync with Strava to track all your workouts and share accomplishments with friends.</string>
    <string name="liability_waiver_title">Liability Waiver</string>
    <string name="liability_release_banner">By tapping agree below, you agree to this liability waiver for %s</string>
    <string name="liability_release_confirm_button">Agree</string>
    <string name="required_fields_error">You must provide additional information in order to book a service at %s</string>
    <string name="share_header_text">Share</string>
    <string name="checkout_fail_message">Your card was not charged. Try again, or contact the business for assistance.</string>
    <string name="checkout_fail_title">Purchase Failure</string>
    <string name="confirm">Confirm</string>
    <string name="event_signup_success_message">Thanks for booking this event!</string>
    <string name="class_signup_success_message">Enjoy your class!</string>
    <string name="pas_class_booked_message">You\'re booked!</string>
    <string name="pas_prompt_message">Don\'t forget to choose a spot in class!</string>
    <string name="liability_waiver_accepted_text">Liability waiver accepted.</string>
    <string name="event_booked_header_text">You are booked for this class</string>
    <string name="user_name_booked_header_text">%s is already booked for this class</string>
    <string name="cancelled_text">Cancelled</string>
    <string name="call_to_book_text">Call to Book</string>
    <string name="action_call_to_sign_up">Call to Book</string>
    <string name="position_in_line_text">Waitlisted. You are %1$d%2$s in line.</string>
    <string name="added_to_waitlist_confirmation_header">Added to Waitlist.</string>
    <string name="activation_email_sent_message">A confirmation link is being sent to %1$s</string>
    <string name="liability_upload_failed">Liability waiver failed to upload.  Please check your internet connection and try again</string>
    <string name="removed_from_waitlist">You\'ve been removed from the waitlist</string>
    <string name="remove_from_waitlist_failed">Could not remove from waitlist</string>
    <string name="class_full_status">This class is filled. You can try calling %s to see if they have additional availability.</string>
    <string name="online_capacity_full_status">Online bookings are filled. You can try calling %s to see if they have additional availability.</string>
    <string name="order_summary_header">Order Summary</string>
    <string name="error_emails_do_not_match">Email addresses do not match</string>
    <string name="order_summary_total_label">Total</string>
    <string name="order_summary_legal_template">Your card will be charged when you tap %s.</string>
    <string name="order_summary_legal_template_ftc">By tapping \"%s\", I agree to be charged. I also agree to the <![CDATA[<a href="https://co.mindbodyonline.com/legal/terms-of-service">Consumer agreement</a>]]> and acknowledge the <![CDATA[<a href="https://co.mindbodyonline.com/legal/privacy-policy">Privacy Policy</a>]]>.</string>
    <string name="order_summary_contract_message_ftc">By tapping \"%s\", I agree to be charged today\'s total. I also agree to the <![CDATA[<a href="https://co.mindbodyonline.com/legal/terms-of-service">Consumer agreement</a>]]> and acknowledge the <![CDATA[<a href="https://co.mindbodyonline.com/legal/privacy-policy">Privacy Policy</a>]]>.</string>
    <string name="order_summary_contract_message">Your card will be charged today\'s total when you tap %s.</string>
    <string name="review_login_request">Please log in to write a review</string>
    <string name="user_unlinked_fitbit_toast">Fitbit is disconnected</string>
    <string name="user_unlinked_strava_toast">Strava is disconnected</string>
    <string name="user_unlink_fitbit_prompt_message">Would you like to disconnect your Fitbit account?</string>
    <string name="user_unlink_strava_prompt_message">Would you like to disconnect your Strava account?</string>
    <string name="user_unlink_fitbit_prompt_title">Disconnect Fitbit</string>
    <string name="user_unlink_strava_prompt_title">Disconnect Strava</string>
    <string name="user_disconnect_action">Disconnect</string>
    <string name="pref_help_center">Mindbody support</string>
    <string name="add_gift_card_button_text">Pay with Gift Card</string>
    <!-- a prepositional phrase to indicate that a class, event, or appointment is with a particular staff -->
    <string name="with">w/ %1$s</string>
    <string name="with_flex">with</string>
    <string name="with_staff_phrase">with %s</string>
    <string name="split_payment_button">Pay with Multiple</string>
    <string name="split_payment_dialog_header">Pay with Multiple Cards</string>
    <string name="gift_card">Gift Card</string>
    <string name="gift_card_lowercase">gift card</string>
    <string name="gift_card_entry_prompt_message">What is your gift card number?</string>
    <string name="invalid_amount_entered_message">Invalid amount</string>
    <string name="deal">Deal</string>
    <string name="deal_at">Deals at <xliff:g name="deals" exammple="Pilates Club">%s</xliff:g></string>
    <string name="appointment_not_booked">Your appointment was not booked. Please try again.</string>
    <string name="enter_gift_card_value_title">Enter gift card amount</string>
    <string name="add_temporary_card_failure_default">Failed to add temporary card</string>
    <string name="user_no_longer_scheduled">Sorry, you are no longer scheduled</string>
    <string name="profile_exit_dialog_message">Are you sure you want to leave without saving these changes?</string>
    <string name="booking_appointment">Booking your appointment…</string>
    <string name="card_not_accepted_message">The credit card you have entered is not accepted at this location</string>
    <!-- indicates a section where a user can select how to pay for an item (eg, a credit card) -->
    <string name="payment_section">Pay with</string>
    <string name="biography_unavailable">No biography available.</string>
    <string name="remove_payment_button">Remove Payment</string>
    <string name="removing_payment_method">Removing Payment Method…</string>
    <string name="order_total">Order Total</string>
    <string name="sign_up_buy">Book and Buy</string>
    <string name="please_add_payment_method">Add a Card</string>
    <string name="split_cc_totals_dont_match_error">The amount entered does not match the order total</string>
    <string name="split_cc_totals_less_error">The amount entered exceeds the order total</string>
    <string name="available_balance_text">Balance %1$s</string>
    <string name="select_payment_dialog_header">Select a Card</string>
    <string name="select_contract_start_date_dialog_header">Select Start Date</string>
    <string name="error_not_consumer_mode_giftcard_message">This gift card must be redeemed at the business.</string>
    <string name="gift_card_no_money_left_error_message">There is no money left on this gift card.</string>
    <string name="quickbook_add_payment_button_default_text">Please add a payment method</string>
    <string name="truncated_card_number">&#8230;%1$s</string>
    <string name="tax_label">Tax</string>
    <string name="split_label">%1$d Cards</string>
    <string name="gift_card_number_not_recognized">We don\'t recognize that gift card number.</string>
    <string name="signed_in_short">Signed In</string>
    <string name="share_button">Share</string>
    <string name="cancel_booking_button">Cancel</string>
    <string name="gift_card_balance_withcolon">Balance</string>
    <string name="location_text">at %1$s</string>
    <string name="fully_redeemed_giftcard_toast_message">Gift card is now empty</string>
    <string name="giftcard_remaining_balance_toast_message">Remaining gift card balance: %s</string>
    <string name="action_book_now">Book Now</string>
    <string name="catalog_item_picker_dialog_header_prompt">Select a Pass</string>
    <string name="business_image_contentdescription">Business Image</string>
    <string name="class_image_contentdescription">Class Image</string>
    <string name="star_image_contentdescription">Start Image</string>
    <string name="action_rebook">Book Again</string>
    <string name="classlist_load_date_message">Tap a date above to see\n classes on that day.</string>
    <string name="loading_appointment">Loading Appointment…</string>
    <string name="not_enough_balance">Insufficient Funds</string>
    <string name="not_enough_mb_card_balance_message">Your Mindbody Card may not have enough funds to cover this purchase.</string>
    <string name="insufficient_balance_select_payment_button">Use Additional Card</string>
    <string name="insufficient_balance_mbcard_continue">Use Mindbody Card</string>
    <string name="last_update_as_of_text">Last updated on %1$s at %2$s</string>
    <string name="action_view_pricing">View Pricing</string>
    <string name="action_review_liability">Review Liability Waiver</string>
    <string name="action_review_liability_short">Review Waiver</string>
    <string name="order_summary_liability_legal">Liability waiver is required for booking</string>
    <string name="default_staff_name">Staff</string>
    <!-- header text for a section that details which staff is teaching a given class/appointment -->
    <string name="instructor_header">Instructor:</string>

    <string name="view_schedule_button">View Schedule</string>
    <string name="empty_cc_payment_methods_screen_message">It looks like you haven\'t added a payment method yet.</string>
    <string name="browse_events">Browse Events</string>
    <string name="online_booking_unsupported_toast">This business has disabled their booking options in the Mindbody App</string>
    <string name="giftcard_remaining_balance">Remaining Balance: %1$s</string>
    <string name="giftcard_last_updated">Last Updated: %1$s</string>
    <string name="default_rating_caption">I really liked it</string>
    <!-- indicates a section which details the biography of the staff member -->
    <string name="staff_bio_title">About the instructor</string>
    <string name="giftcard_noimage_text">No\nImage</string>
    <string name="navigate_menu_text">Navigate</string>
    <string name="later_button_text">Later</string>
    <string name="back_button_text">Back</string>
    <string name="generic_business_name">Business</string>
    <string name="unlimited">Unlimited</string>
    <string name="sessions_remaining_out_of_total">%1$d/%2$d available</string>
    <string name="go_back_text">Go back</string>
    <string name="to_be_determined_short">TBD</string>
    <string name="classes_at">Classes at %s</string>
    <string name="loading_classes_message">Loading classes</string>
    <string name="call_studio_failed_message">Unable to call studio</string>
    <string name="confirm_appointment_title">Appointment Booking</string>
    <string name="upcoming_tab_title">Upcoming</string>
    <string name="visit_history_tab_title">Previous</string>
    <string name="business_no_longer_listed_message">This business is no longer listed.</string>

    <string name="removing_business_toast_message">Removing…</string>

    <string name="no_email_app_error">No email app found</string>
    <string name="businesses_page_title">Businesses</string>
    <string name="classes_page_title">Classes</string>
    <string name="staff_page_title">Staff</string>
    <string name="ok_button_text">OK</string>
    <string name="invalid_giftcard_amount_error">Invalid amount entered for gift card</string>
    <string name="today_text">Today</string>
    <string name="tomorrow_text">Tomorrow</string>
    <string name="submit_feedback_title">Submit feedback…</string>
    <string name="signout_button_text">Log out</string>
    <string name="invalid_liability_release_error">Invalid liability release</string>
    <string name="invalid_location_error">Invalid location</string>

    <string name="mindbody_cards">Mindbody cards</string>
    <string name="no_provinces_message">No province to select</string>
    <string name="people_found_review_helpful_message">\"%1$d out of %2$d\" people found this helpful</string>
    <string name="delete_review_title">Would you like to delete this review?</string>
    <string name="zoom_to_search_message">Zoom in to search near this location</string>
    <string name="no_results_near_message">No results within %1$s of %2$s</string>
    <string name="no_business_search_results_message">No %1$s within %2$s of %3$s</string>
    <string name="your_current_map_area">your current map area</string>
    <string name="your_current_location">your current location</string>
    <string name="miles">miles</string>
    <string name="mile">mile</string>
    <string name="kilometers">kilometers</string>
    <string name="kilometer">kilometer</string>
    <string name="search_error_message">An error occurred while searching</string>
    <string name="location_update_error_title">Location Updates</string>
    <string name="password_change_success_message">Your password has been changed successfully</string>
    <string name="businesslist_selection_banner">Select a location where you will use this %1$s.</string>
    <string name="pass">pass</string>
    <string name="subscription_level_message_connectlisting">This business has disabled their online booking options in the Mindbody App.</string>
    <string name="call_to_book_message">Please call to book</string>

    <!-- About Activity strings -->
    <string name="about_terms">Terms of use</string>
    <string name="about_privacy">Privacy policy</string>
    <!-- TODO: discuss capatalization. Should be uppercase first letter only as per InVision, but makes it inconsistent with the rest -->
    <string name="pref_remote_privacy_preferences">Data Preferences</string>
    <string name="version_title">Version</string>
    <string name="waitlisted_text">Waitlisted</string>

    <!-- Add a Credit Card Activity strings -->
    <string name="action_save">SAVE</string>
    <string name="action_apply">APPLY</string>

    <!-- Appointment Type Details Activity strings -->
    <string name="unable_to_book_copy">Please call to book an appointment</string>
    <string name="appointment_requests_accepted_message">Appointment available by request only</string>
    <string name="staff_unavailable_message">No staff available</string>

    <!-- Book Appointment Activity strings -->
    <string name="sample_appointment_name">Woman\'s Cut</string>
    <string name="loading_staff_text">Loading Staff…</string>

    <!-- title for a screen which displays the details of a particular class -->
    <string name="class_detail_title">Class Detail</string>
    <string name="action_book_class">Book</string>
    <string name="cancel">Cancel</string>
    <string name="clear_history">Clear History</string>
    <string name="action_book_call">Call Business</string>
    <string name="action_waitlist_class">Waitlist</string>
    <string name="action_signin_class">Sign In</string>
    <string name="history_no_visits_header">Looking for something to book?</string>
    <string name="history_no_visits_body">Let\'s explore your options.</string>
    <string name="upcoming_no_visits_header">A schedule screen for what\'s most important.</string>
    <string name="upcoming_no_visits_body">And what\'s more important than your wellness? Use this screen for easy access to your upcoming classes and appointments.</string>
    <string name="your_rating">Rating</string>
    <string name="your_review">Review</string>
    <!-- Indicates that the following text is the name of the room for the class, event, or w/e -->
    <string name="room_indicator">Room:</string>
    <string name="edit_button_text">edit</string>
    <string name="prompt_password">Password</string>
    <string name="create_a_password_message">Create a password</string>
    <string name="prompt_password_confirm">Confirm password</string>
    <string name="action_sign_in_short">Log In</string>
    <string name="action_forgot_password">Forgot your password?</string>
    <string name="error_invalid_email">Valid email is required</string>
    <string name="error_field_required">This field is required</string>
    <string name="error_expiration_required">Please enter expiration date</string>
    <string name="next">Next</string>
    <string name="next_up">Next Up</string>
    <string name="invalid_full_name">Please enter a full name</string>
    <string name="invalid_card_number">Not a valid credit card number</string>
    <string name="invalid_cvv">Invalid CVV (Must be 3 or 4 numbers)</string>
    <string name="city_placeholder">City</string>
    <string name="stateprovince_placeholder">State/Province</string>
    <string name="country_placeholder">Country</string>
    <string name="exp_date_placeholder">Expiration date</string>
    <string name="cvv_placeholder">CVV</string>
    <string name="zipcode_placeholder">Postal code</string>
    <string name="store_button_text">Store this card for future use</string>
    <string name="save_button_text">Save</string>
    <string name="privacy_address">https://www.mindbodyonline.com/privacy-policy</string>
    <string name="tos_address">https://www.mindbodyonline.com/consumer-agreement</string>
    <string name="flex_limit_learn_more_address">https://support.mindbodyonline.com/s/article/How-often-can-I-use-my-Mindbody-Flex-membership?language=en_US</string>
    <string name="tos_pt1">By creating your account, you agree to the Mindbody\u0020<a href="TERMS_OF_USE_LINK">Terms of Use</a> and\u0020<a href="PRIVACY_POLICY_LINK">Privacy Policy</a>.</string>

    <!-- A title for a section about terms of use and privacy policy being updated -->
    <string name="updated_tos_title">Please accept the Terms of Use</string>
    <!-- A headline describing a section about terms of service and privacy policy being updated -->
    <string name="updated_tos_header">We’ve updated our terms of use. Please review to ensure you\'re up-to-date on any changes that may be important to you.</string>
    <string name="tos_disclaimer">You can view the updated terms of use\u0020<u>here</u></string>
    <string name="privacy_policy_disclaimer">You can view the updated privacy policy\u0020<u>here</u></string>
    <!-- Details what clicking accept in the updated terms of service section means.
         It is important to have 2 pieces of text here wrapped like <u>(text)</u>, the first of which
         should be the terms of service and the latter the privacy policy. These will be the places
         the user can click to get in-depth information about each of these -->
    <string name="updated_tos_agreement_descriptor">By clicking the “I Accept” button below, or otherwise indicating assent electronically, you agree to the \u0020<u>Mindbody Consumer Agreement</u> which includes the \u0020<u>Mindbody Privacy Policy</u>.</string>
    <!-- Action text to accept the updates to the terms of service and privacy policy -->
    <string name="updated_tos_agreement_action">I accept</string>

    <string name="session_list_title">Passes</string>
    <string name="session_empty">Explore the app to find something you love.  We\'ll automatically store all of your purchased passes here for easy access.</string>
    <string name="title_activity_settings">Settings</string>
    <string name="pref_title_sync">Resend account confirmation email</string>
    <string name="more">more</string>
    <string name="more_cap">More</string>
    <string name="check_email_button_text">Check email</string>
    <string name="appointment_details_available_staff_header">Pick a Staff</string>
    <string name="book_appointment_button_text">Book an appointment</string>
    <string name="request_appointment_button_text">Request an appointment</string>
    <string name="book_text">Book</string>
    <string name="no_appointments_available">There are no appointments for this business.</string>
    <string name="no_description_available">No description available.</string>
    <string name="loading_availability">Loading availability…</string>
    <string name="load_availability">Tap to load availability.</string>
    <string name="calendar_loading_availability">Loading availability…</string>
    <string name="appointment_payment_available_session">You have a session available for use</string>
    <string name="appointment_payment_not_required">Book now &amp; pay when you arrive</string>
    <string name="appointment_payment_required">Book and Buy</string>
    <string name="no_pricing_options_text">This class cannot be booked online; please contact the business for more information.</string>
    <string name="accepted_here_title">Accepted Here</string>
    <string name="skip_button_text">Skip</string>
    <string name="check_email_button_text_fragment">Check Email</string>
    <string name="no_connection_title">No Data Connection</string>
    <string name="no_connection_message">No available data connection has been found. Would you like to go to your settings?</string>
    <string name="limited_connection_title">Limited Data Connection</string>
    <string name="limited_connection_message">Bummer! Please check your connection</string>
    <string name="goto_connection_settings_button_text">Go to Settings</string>
    <string name="dialog_session_error_title">Session Lost</string>
    <string name="dialog_session_error_message">The current user session has been lost.  This may occur if the user has logged in to another location.  Please log in again.</string>
    <string name="title_rate_dialog">Rate Mindbody</string>
    <string name="message_rate_dialog">How are you enjoying using the Mindbody App?</string>
    <string name="rate_love_it">Love It</string>
    <string name="rate_hate_it">Needs Improvement</string>
    <string name="rate_ask_later">Not Now</string>
    <string name="title_set_expiration_dialog">Select Expiration Date</string>
    <string name="search_title">Explore</string>
    <string name="search_hint">Business name or keyword</string>
    <string name="enableGpsTitle">Your location is unavailable</string>
    <string name="enableGpsMessage">To search in your area, turn on your location in device settings.</string>
    <string name="action_map_view">Map view</string>
    <string name="turn_on_location_or_tell_us_text">Turn on your location in Settings or tell us where you\'d like to search.</string>
    <string name="enter_location_text">Enter a location</string>
    <string name="connect_notification_title">Mindbody</string>
    <string name="connect_geofencing_subtitle">Sign in to %1$s at %2$s</string>
    <string name="gender_male">Male</string>
    <string name="gender_female">Female</string>
    <string name="my_profile_saving_message">Saving…</string>
    <string name="my_profile_successful_update">Your information has been saved!</string>
    <string name="my_profile_unsuccessful_update">Your information has not been saved. Open edit and try again.</string>
    <string name="profile_verify_address">Invalid address</string>
    <string name="profile_verify_city">Invalid city</string>
    <string name="profile_verify_zip">Invalid postal code</string>
    <string name="message_class_free">This class is free!</string>
    <string name="message_unpaid">Book and pay later</string>
    <string name="message_requires_payment">Book and pay now</string>
    <string name="card_expired_text">Expired</string>
    <string name="mindbody_card_add_thankyou">You have successfully added\nyour Mindbody Card</string>
    <string name="title_activity_payment_methods">Wallet</string>
    <string name="credit_card_added_message">Your card was added</string>
    <string name="pref_share_app">Share Mindbody</string>
    <string name="pref_share_app_title">Share Mindbody</string>
    <string name="pref_support_header">Help &amp; Support</string>
    <string name="pref_title_fitbit">Fitbit</string>
    <string name="pref_title_strava">Strava</string>
    <string name="pref_title_google_fit">Google Fit</string>
    <!-- A default state to indicate a fitness activity tracker (eg, fitbit or google fit) -->
    <string name="generic_fitness_activity_tracker">Tracker</string>
    <string name="pref_notifications_header">Notifications</string>
    <string name="pref_title_marketing_opt_in">Get emails with news and promos</string>
    <string name="pref_summary_marketing_opt_in">Updates on the latest events and deals</string>
    <string name="pref_feedback_title">Tell us what you think</string>
    <string name="pref_summary_ratings_reviews">Find something great? Let people know</string>
    <string name="pref_title_ratings_reviews">Reviews</string>
    <string name="pref_summary_signin_notifications">Sign in with your phone</string>
    <string name="pref_title_signin_notifications">Mobile sign-in</string>
    <string name="pref_summary_notification_phone_led">See your alerts</string>
    <string name="pref_title_notification_phone_led">Light</string>
    <string name="pref_summary_notification_vibrate">Feel your alerts</string>
    <string name="pref_title_notification_vibrate">Vibrate</string>
    <string name="pref_summary_notification_sound">Hear your alerts</string>
    <string name="pref_title_notification_sound">Sound</string>
    <string name="pref_calendar_header">Calendar</string>
    <string name="pref_summary_sync_calendar">Add your schedule to a calendar</string>
    <string name="pref_title_user_calendar">Select calendar</string>
    <string name="pref_summary_user_calendar">Choose which calendar to use</string>
    <string name="ask_sync_calendar_title">Always add to calendar?</string>
    <string name="ask_sync_calendar_message">Turn on automatic calendar syncing so that you never miss a class or appointment!</string>
    <string name="ask_sync_calendar_ok">Yes, Enable</string>
    <string name="no_thanks_cancel_action">No, Thanks</string>
    <string name="recent_searches_label">Recent</string>
    <string name="call_menu_title">Call</string>
    <string name="full_menu_title">Full</string>
    <string name="call_business">Call Business</string>
    <string name="favorite_menu_title">Favorite</string>
    <string name="location_text_placeholder">neighborhood, city, state, or ZIP</string>
    <string name="address_placeholder">Address</string>
    <!-- The separator for city and state in an address (eg, 'Los Angeles, CA') -->
    <string name="billing_address_city_state"><xliff:g name="city" example="Los Angeles">%1$s</xliff:g>, <xliff:g name="state" example="CA">%2$s</xliff:g></string>
    <!-- The separator for the city and state and the postal code (eg, 'Los Angeles, CA 90001') -->
    <string name="billing_address_postal"><xliff:g name="cityState" example="Los Angeles, CA">%1$s</xliff:g> <xliff:g name="postalCode" example="90001">%2$s</xliff:g></string>
    <string name="no_availability_message">This appointment isn\'t available on this day. Tap a different day.</string>
    <string name="upcoming_classes_title">Upcoming Classes</string>
    <string name="upcoming_events_title">Upcoming Events</string>
    <string name="no_sessions_error_help">Can\'t find one of your passes?</string>
    <string name="recipient_full_name_label">Recipient\'s name</string>
    <string name="recipient_email_label">Recipient\'s email address</string>
    <string name="email_address_hint_text">Email Address</string>
    <string name="confirm_recipients_email_address_hint_text">Confirm recipient\'s email</string>
    <string name="subject_hint_text">Subject</string>
    <string name="personal_message_hint_text">Message (optional)</string>
    <string name="class_search_text">Finding classes…</string>
    <string name="event_search_text">Finding events…</string>
    <string name="no_classes_found_text">No %1$s scheduled within the next %2$s days.</string>
    <string name="no_classes_found_at_this_time_text">No %1$s scheduled at this time within the next %2$s days.</string>
    <string name="rate_text">How was your %1$s?</string>
    <string name="rate_characters_left_format">%1$d/%2$d</string>
    <string name="visit_class_type_string">class</string>
    <string name="visit_class_type_string_capitalized">Class</string>
    <string name="visit_enrollment_type_string">event</string>
    <string name="visit_appointment_type_string">appointment</string>
    <string name="verify_account_header">Check Your Email</string>
    <string name="verify_account_content">Want to see your schedule? Tap the button below to get started.</string>
    <string name="verify_account_passes_content">Want to see your passes? Tap the button below to get started.</string>
    <string name="verify_account_resend_email_button_text">Send Confirmation Email</string>
    <string name="verify_account_support_content">Questions?</string>
    <string name="logged_out_message">Then, come here to quickly see your\nSchedule and Favorites.</string>
    <string name="logged_out_action">Tap Mindbody to sign in.</string>
    <string name="widget_no_schedule_items">Book some time for you.\nTap Mindbody to get started.</string>
    <string name="widget_no_favorites_items">Tap Mindbody to start exploring,\nthen view your Favorites here by tapping\nthe heart next to places you love.</string>
    <string name="payment_button_text">Wallet</string>
    <string name="services_pricing_title">Pricing</string>
    <string name="calendar_app_not_found_message">Could not find Calendar application</string>
    <string name="pref_title_manage_notifications">Notifications</string>
    <string name="view_classes">View Classes</string>
    <string name="view_appointments">View Appointments</string>
    <string name="view_events">View events</string>
    <string name="view_passes">View Passes</string>
    <string name="view_pricing">View Pricing</string>
    <!-- Date and time dividing text on Explore screen and date time picker-->
    <string name="date_time_range_delimiter">\u0020from\u0020</string>
    <!-- Time range divider on Explore screen and date time picker-->
    <string name="time_range_delimiter">-</string>

    <plurals name="num_appointments">
        <item quantity="one">%d appointment</item>
        <item quantity="other">%d appointments</item>
    </plurals>

    <plurals name="num_classes">
        <item quantity="one">%d class</item>
        <item quantity="other">%d classes</item>
    </plurals>

    <plurals name="distance_unit_mile_placeholder">
        <item quantity="one">%d mile</item>
        <item quantity="other">%d miles</item>
    </plurals>

    <plurals name="distance_unit_km_placeholder">
        <item quantity="one">%d km</item>
        <item quantity="other">%d kms</item>
    </plurals>

    <plurals name="numberOfPasses">
        <item quantity="one">%1$d %2$s pass</item>
        <item quantity="other">%1$d %2$s passes</item>
    </plurals>

    <plurals name="num_sessions">
        <item quantity="one">%1$d session</item>
        <item quantity="other">%1$d sessions</item>
    </plurals>

    <plurals name="num_times_visited">
        <item quantity="one">Visited %1$d time</item>
        <item quantity="other">Visited %1$d times</item>
    </plurals>

    <plurals name="num_times_visited_last_month">
        <item quantity="one">Visited %1$d time last month</item>
        <item quantity="other">Visited %1$d times last month</item>
    </plurals>

    <plurals name="spaces_available_text">
        <item quantity="one">%d spot available</item>
        <item quantity="other">%d spots available</item>
    </plurals>

    <plurals name="spaces_booked_text">
        <item quantity="one">%d spot booked</item>
        <item quantity="other">%d spots booked</item>
    </plurals>

    <plurals name="num_intro_offers">
        <item quantity="one">%d Intro Offer</item>
        <item quantity="other">%d Intro Offers</item>
    </plurals>

    <plurals name="num_spots_left">
        <item quantity="one">%d spot left</item>
        <item quantity="other">%d spots left</item>
    </plurals>

    <plurals name="you_are_signed_in_multiple">
        <item quantity="one">You are signed in for %1$s and %2$d upcoming class.</item>
        <item quantity="other">You are signed in for %1$s and %2$d upcoming classes.</item>
    </plurals>

    <string name="expiration_field">Expires: <xliff:g name="expiration_date" example="11/12/18">%1$s</xliff:g></string>
    <string name="banner_classes_service_category_only">You\'re viewing classes in this category only</string>
    <string name="alacarte_purchase_confirmation_header">Enjoy your pass!\nBook now to save your space.</string>
    <string name="contract_purchase_confirmation_header">Enjoy your purchase!</string>
    <string name="contract_purchase_confirmation_text">You can view your purchases in your profile tab.</string>
    <string name="unlimited_class_pass">Unlimited classes</string>
    <string name="unlimited_appt_pass">Unlimited appointments</string>
    <string name="use_gift_card_prompt">Use gift card?</string>
    <string name="buy">Buy</string>
    <string name="pricing_unavailable_header">Pricing is unavailable for this category</string>
    <string name="pricing_unavailable">Pricing is unavailable</string>
    <string name="pricing_unavailable_message">Check the schedule to see what\'s available.</string>
    <string name="no_classes_in_service_category_message">Please call %s to find out what\'s available.</string>
    <string name="call_business_coming_soon_message">Please call %s for more info.</string>
    <string name="call_business_to_book_and_buy">Please call %1$s to book and buy this %2$s.</string>
    <string name="pay_waitlist_message">Join waitlist and pay now</string>
    <string name="waitlist_sms_consent_message">By clicking “%1$s”, you agree to receiving SMS to the number on your profile with updates about the waitlist. &lt;a href="https://co.mindbodyonline.com/legal/sms-texting-terms">SMS Texting Terms&lt;/a> apply.</string>
    <string name="online_booking_unavailable_message">Apologies! We are having technical difficulties. You could try booking in a few minutes.</string>
    <string name="overlapping_waitlisted_message">%s doesn’t allow you to have more than one waitlisted class at a time.</string>
    <string name="waitlistable_message">Join waitlist and pay later</string>
    <string name="outside_booking_window_message">You missed the booking window for this class at \n%s.</string>
    <string name="prerequisites_not_met_message">%s has a set of pre-requisites to book this class which you have not yet met. Please try booking another class.</string>
    <string name="class_details_error_message">We\'re unable to process your request. Please update your app and try again. If issues persist, reach out to the business for assistance with booking.</string>
    <string name="booked_at_this_time_message">You are already booked at \n%s at this time.</string>
    <string name="session_already_booked_at_this_time">You already have a session booked at this time. Please choose a different time or option.</string>
    <string name="passes_available_message">You have a pass available</string>
    <string name="no_service_categories_message">This business has no available pricing</string>
    <string name="update_required_header">Update Required</string>
    <string name="update_required_message">A new version of the Mindbody App is available.  Please update in order to continue using the app.</string>
    <string name="action_update">Update</string>
    <string name="update_available_header">Update Available</string>
    <string name="update_available_message">A new version of the Mindbody App is available.  Please update now.</string>
    <string name="title_activity_routine_services">Schedule</string>
    <string name="book_waitlist_text">Waitlist and Buy</string>
    <string name="pay_booking_cancelation_subtext">A pass will not be returned when canceling this booking</string>
    <string name="fitbit_sync_failed_message">Failed to connect Fitbit account</string>
    <string name="strava_sync_failed_message">Failed to connect Strava account</string>
    <string name="calories_burned">Calories Burned</string>
    <!-- an abbreviation for calories_burned -->
    <string name="calories_burned_short">cal</string>
    <string name="active_minutes">Active Minutes</string>
    <!-- an abbreviation for active_minutes -->
    <string name="active_minutes_short">min</string>
    <string name="peak_hr_label">Peak</string>
    <string name="cardio_hr_label">Cardio</string>
    <string name="fatburn_hr_label">Fat Burn</string>
    <string name="fitbit_steps">steps</string>
    <string name="fitbit_steps_taken">Steps Taken</string>
    <string name="fitbit_context_helper_title">Fitbit is connected!</string>
    <string name="strava_context_helper_title">Strava is connected!</string>
    <string name="google_fit_context_helper_title">Google Fit is connected!</string>
    <string name="fitbit_disclaimer_badge_message">All data is collected from your <xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g></string>
    <string name="fitbit_badge_title_no_data_available"><xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g> data is unavailable</string>
    <string name="fitbit_badge_message_no_data_available"><xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g> didn\'t record any data during this class.</string>
    <string name="last_sync_message">Last updated %s</string>
    <string name="fitbit_data_empty_need_sync_title">Unable to find your <xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g> data</string>
    <string name="fitbit_data_empty_need_sync_message">Open the <xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g> app to sync your data</string>
    <string name="action_open_fitbit">Sync now</string>
    <string name="heart_rate_title">Heart Rate</string>
    <string name="heart_rate_zones_title">Heart Rate Zones (min)</string>
    <string name="fitbit_badge_title_reconnect"><xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g> is disconnected</string>
    <string name="fitbit_badge_message_reconnect">Please log in to your <xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g> account to reconnect.</string>
    <string name="action_reconnect">Log in to <xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g></string>
    <string name="fitbit_before_account_creation">The visit occurred before your <xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g> account was created.</string>
    <!-- Indicates the steps counted by the user's tracker -->
    <string name="tracker_steps_counted">Steps Taken</string>
    <!-- Indicates the calories burned estimated by the user's tracker -->
    <string name="tracker_calories_burned">Calories Burned</string>
    <!-- A short text snippet that indicates we have no data for the specified field -->
    <string name="tracker_no_data">-</string>
    <string name="unfavorite_business">Remove</string>
    <string name="goto_classes_text">Go to Classes</string>
    <string name="goto_appts_text">Go to Appointments</string>
    <string name="distance_units_pref_title">Distance</string>
    <string name="favorite_routines_title">Services</string>
    <string name="last_visited">Last visited %1s</string>
    <string name="routines_no_history">Start booking your classes and appointments, then come back here to see the services you book most often</string>
    <string name="pref_apps_devices_header">Integrations</string>
    <string name="average_bpm_short">avg BPM</string>
    <string name="max_bpm">max BPM</string>
    <string name="fitbit_badge_data_error_message"><xliff:g name="tracker name" example="Fitbit">%1$s</xliff:g> data is temporarily unavailable.  Please try again later.</string>
    <string name="visited_these_days">Visited %s</string>
    <string name="help_center_faq">FAQ</string>
    <string name="help_center_tickets">Submit a Ticket</string>
    <string name="nav_home">Home</string>
    <string name="nav_explore">Search</string>
    <string name="nav_schedule">Schedule</string>
    <string name="nav_favorites">Favorites</string>
    <string name="nav_profile">Profile</string>
    <string name="nav_deals">Deals</string>

    <!-- Edit Profile screen section header for Personal Info -->
    <string name="personal_info">Personal Information</string>
    <!-- Title for edit profile screen -->
    <string name="edit_profile">Edit Profile</string>
    <string name="gender">Gender</string>
    <string name="choose_from_existing">Choose Existing</string>
    <string name="take_photo">Take Photo</string>
    <!-- Text link to change profile photo -->
    <string name="change_profile_photo">Change Profile Photo</string>
    <string name="first_name_error">Please enter your first name</string>
    <string name="last_name_error">Please enter your last name</string>
    <string name="business_schedule_button_text"><u>View schedule</u></string>
    <string name="ellipsis">…</string>
    <string name="no_ellipsis">"   "</string>
    <string name="mindbody_card_indicator">Mindbody Card</string>
    <string name="num_ratings">%.1f</string>
    <string name="num_total_ratings">%d reviews</string>
    <!-- the number of reviews of something there are -->
    <string name="num_total_reviews_plain"><xliff:g name="count" example="128">%1$d</xliff:g> reviews</string>
    <string name="num_total_studio_reviews_plain"><xliff:g name="count" example="128">%1$d</xliff:g> studio reviews</string>
    <!-- A certain place's name and distance from the user (with units such as mi or km) -->
    <string name="location_name_and_distance"><xliff:g name="location" example="Blockbuster">%1$s</xliff:g> - <xliff:g name="distance" example="1.3mi">%2$s</xliff:g></string>
    <string name="unavailable">Unavailable</string>
    <!-- short text prompt that describes an action to reveal the hidden password letters -->
    <string name="action_show_password">Show</string>
    <!-- short text prompt that describes an action to hide the revealed password letters -->
    <string name="action_hide_password">Hide</string>

    <string name="invalid_email_message">Please enter a valid email address.</string>
    <string name="empty_password_message">Please enter a password</string>
    <string name="invalid_password_message">Password must be 8 to 30 characters and include at least one number or special character, one upper case letter, and one lower case letter.</string>
    <string name="invalid_password_message_short">Password does not meet requirements</string>
    <string name="empty_first_name_message">Please enter your first name.</string>
    <string name="empty_last_name_message">Please enter your last name.</string>
    <string name="unselected_country_message">Please select your country.</string>
    <string name="incorrect_login_message">Email or password is incorrect.</string>
    <string name="incorrect_password_message">Password is incorrect</string>
    <string name="register_step_bubble_1_text">1</string>
    <string name="register_step_bubble_2_text">2</string>
    <string name="register_step_bubble_3_text">3</string>
    <string name="login_email_check_message">What is your email?</string>
    <string name="login_input_password_message">Sign in to link your accounts or create a new one.\nNot you? Create an account below.</string>
    <string name="register_final_step_message">You\'re almost done!</string>
    <string name="or_divider_text">or</string>
    <string name="register_create_your_account_message">Create Your App Account</string>
    <string name="register_create_password_header_message">New to the app? Let\'s create your login!</string>
    <string name="create_password_message">Create a password</string>
    <string name="check_your_email_tokenized_message">To link your passes and make purchases, confirm your account via email at:\n%s</string>
    <string name="login_welcome_back_header_message">Hey, we found your Mindbody App Account!</string>

    <!-- Title for the reviews section for Business details page -->
    <string name="review_card_header">Reviews</string>
    <string name="featured_review_title">Review</string>
    <!-- Call-out title for gift cards on the business page -->
    <string name="business_details_gift_card_card_title">Treat your friends and family</string>
    <!-- Call-out button text to buy a gift card on the business page -->
    <string name="business_details_gift_card_card_button">Buy a Gift Card</string>
    <!-- Header for selecting a gift card with the associated studio name -->
    <string name="gift_card_selection_header_text">For use at %1$s</string>
    <string name="no_favorites_classes_subtext">Tap the ? on your favorite business’s info screen. Show them love, and we’ll show you all their classes here.</string>
    <string name="no_favorites_subtext">Tap the heart on any business listing and we\'ll save them here for faster access.</string>
    <string name="no_favorites_text">Where is the love?</string>
    <string name="no_favorites_classes_text">It\'s okay to play favorites</string>
    <string name="no_favorites_explore_button_text">Explore Businesses</string>
    <string name="view_all_button">View All</string>
    <string name="my_location_button">My current location</string>
    <string name="distance_category_header">Distance</string>
    <string name="sort_category_header">Sort By</string>
    <string name="sort_item_rating">Rating</string>
    <string name="sort_item_pricing">Pricing</string>
    <string name="sort_item_distance">Distance %1$s</string>
    <string name="sort_item_relevance">Relevance</string>
    <!-- Text for number of reviews for a studio -->
    <string name="business_detail_listrow_rating">%1$d reviews</string>
    <string name="business_listrow_mbcard_approved">Mindbody Card approved</string>
    <string name="questions_text">Questions?</string>
    <string name="free_class_button_action_text">Book</string>
    <string name="cards_on_file">Credit/Debit Cards</string>
    <string name="remaining">Remaining</string>
    <string name="credit_card">Credit Card</string>
    <string name="card_information">Card Information</string>
    <string name="billing_address">Billing Address</string>
    <string name="remove_this_card">Delete this card</string>
    <string name="remove_this_card_q">Delete this card?</string>
    <string name="remove_only_credit_card_message">This is your only card on file.  Do you want to delete it?</string>
    <string name="balance">Balance</string>
    <string name="card_number">Card number</string>
    <string name="billing_info">Billing Information</string>
    <string name="card_number_hint">Card number</string>
    <string name="select_billing_address">Select Billing Address</string>
    <string name="new_billing_address">Add a new billing address</string>
    <string name="select_one_billing_address">Please select one billing address.</string>
    <string name="my_fitness_header">My Activity</string>
    <string name="deals_near_me_card_title">Deals</string>
    <string name="class_time_string">%1$s &#8211; %2$s</string>
    <string name="waitlist_text">Waitlist</string>
    <string name="tap_different_day_text">Try another day.</string>
    <string name="tap_different_day_filter_text">Try another day or change your filters.</string>
    <string name="no_classes_filtered_results">None of your favorites offer classes that match your search</string>
    <string name="change_filters_button">Change Filters</string>
    <string name="deal_description_header">Details</string>
    <string name="cta_details">Details</string>
    <string name="no_catalog_item_found">Sorry, deal is unavailable.  You may have bought this deal already, or are not qualified for purchase.</string>
    <string name="call_business_message">Call the business for more details</string>
    <string name="share_text">Share</string>
    <string name="action_call_to_cancel_long">Call to Cancel</string>
    <string name="late_cancel_message">A session will be deducted from your pass</string>
    <string name="exp">Exp</string>
    <!-- Indicates how many calories were burned during a time period -->
    <string name="cal_burned_phrase">%d Cal</string>
    <string name="prereq_info_header">Prerequisite Info</string>
    <string name="any_staff">Any staff</string>
    <string name="view_all_availability">View all availability</string>
    <string name="view_availability">View Availability</string>
    <string name="request_sent_message">Request sent!</string>
    <string name="date_row_header">Date</string>
    <string name="time_row_header">Time</string>
    <string name="share">Share</string>
    <string name="add_to_calendar">Add to calendar</string>
    <string name="appointment_booked_message">You are booked for this appointment</string>
    <string name="appointment_details_date_time">%1$s &#8211; %2$s | %3$s</string>
    <!-- Action to leave a review for a class -->
    <string name="share_your_opinion">Write a Review</string>
    <!-- Action to edit a review for a class -->
    <string name="edit_your_opinion">Edit Review</string>
    <!-- Action to add a visit to the calendar -->
    <string name="fitbit_connected_message">Your Fitbit tracker\nis now connected</string>
    <string name="review_ask_each_time_header">Ask for my opinion</string>
    <string name="review_ask_each_time_body">Prompt me to rate my visit every time</string>
    <string name="view_more">VIEW MORE</string>
    <string name="delete_review_string">Delete Review</string>
    <string name="qb_tbd_time_format">TBD | %1$s</string>
    <string name="verify_account_dialog_header">Your account is unconfirmed</string>
    <string name="verify_account_dialog_message">Confirm your email address to book services in the app - haircuts, fitness classes, massages, and more.</string>
    <string name="event_tag">Event</string>
    <string name="done">Done</string>
    <string name="add_temporary_card_failure_not_supported">The card you have entered is not accepted at this location.</string>
    <string name="add_temporary_card_failure_conflict">The card you have entered has already been added.</string>
    <string name="default_calendar">Default Calendar</string>
    <string name="thanks_for_your_feedback">Thanks for your feedback</string>
    <string name="enjoy_your_visit_message">Enjoy your visit!</string>
    <!-- Gift Card style subtitle -->
    <string name="select_a_style">Select Style</string>
    <!-- Gift Card recipient title -->
    <string name="recipient_info">Recipient Info</string>
    <!-- Gift Card recipient subtitle -->
    <string name="recipient_information">Recipient Information</string>
    <!-- Gift Card message subtitle -->
    <string name="message">Message</string>
    <string name="gift_card_value">Gift card value</string>
    <!-- Gift Card delivery date subtitle -->
    <string name="delivery_date">Select Delivery Date</string>
    <!-- Item label for the delivery date for the selected Gift Card -->
    <string name="delivery_date_description">Delivery Date</string>
    <!-- Gift Card payment confirmation screen title -->
    <string name="gc_confirmation_title">Payment Confirmation</string>
    <!-- Gift Card payment confirmation screen payment success text -->
    <string name="gc_confirmation_header">Your Payment was Successfully Processed!</string>
    <!-- Gift Card payment confirmation screen receipt emailed info -->
    <string name="gc_confirmation_subheader">Check your email for confirmation</string>
    <!-- Gift Card payment confirmation screen delivery info title -->
    <string name="gc_confirmation_details">Your gift will be emailed to</string>
    <!-- Gift Card payment confirmation screen delivery date -->
    <string name="gc_confirmation_receive_date">on <xliff:g name="date" example="Monday 01/04/2019">%1$s</xliff:g></string>
    <!-- Gift Card payment confirmation screen buy another text -->
    <string name="gc_confirmation_button">Buy another Gift Card</string>
    <string name="choose_date_header">Choose date</string>
    <string name="choose_time_header">Choose time</string>
    <string name="appointment_booking_notes_and_special_instructions_hint">Notes and Special Instructions</string>
    <string name="lorem_upsum">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</string>
    <string name="filter_title_text">Accept Mindbody Card</string>
    <string name="booking_not_available_text">Booking not available online</string>
    <string name="coming_soon">Coming soon</string>
    <string name="history_no_visits_button_default">Explore</string>
    <string name="history_no_visits_body_fitbit">Book a workout, then see the results here after your visit.</string>
    <string name="history_no_visits_header_fitbit">Want to see your Fitbit activity?</string>
    <string name="mbcard_filter_on_snackbar_text">Mindbody Card filter is on</string>
    <string name="pref_title_push_notifications">Announcements</string>
    <string name="pref_summary_push_notifications">Purchase info, deals, and new features</string>
    <!-- Notification preference item title for enabling/disabling the schedule count badge -->
    <!-- Notification preference item description for enabling/disabling the schedule count badge -->
    <string name="cancel_request_button">Cancel request</string>
    <string name="early_cancel_request_message">Would you like to cancel this request?</string>
    <string name="appointment_requested_message">Request pending business approval</string>
    <string name="favorite_added">%s\nwas saved to your Favorites.</string>
    <string name="button_go_to_location">Go to %s</string>
    <string name="ual_dialog_masterlocation_text">You\'ve shown interest in \n%s\nso we\'ve saved them to your Favorites.</string>
    <string name="ual_dialog_studioid_only_text">You\'ve shown interest in \n%s\nso let\'s save them to your Favorites.</string>
    <string name="search_results_at_string">Showing results within %1$s of %2$s</string>
    <string name="event">event</string>
    <string name="class_text">class</string>
    <string name="appointment">appointment</string>
    <string name="failed_message">"Failed: "</string>
    <string name="required_fields_activity_title">Update Your Account</string>
    <string name="phone_number_legal_text">This number will be stored in My Info. Businesses you make reservations at may contact you via phone, text (SMS) and/or WhatsApp to provide updates about your reservation. <u><b><a href="SMS_TEXTING_TERMS">SMS Texting Terms</a></b></u> apply.</string>
    <string name="mobile_phone_text">Mobile phone</string>
    <string name="ten_digit_mobile_phone_text">10-digit mobile phone</string>
    <string name="required_fields_header">Where can %s reach you?</string>
    <string name="required_fields_mobile_phone_are_you_sure_message">Without your number, %s may not be able to contact you if there\'s a schedule change.  Continue anyway?</string>
    <string name="required_fields_mobile_phone_cant_skip_message">%s requires a phone number for booking.</string>
    <string name="proceed">Proceed</string>
    <string name="invalid_phone_number">Invalid phone number</string>
    <string name="invalid_phone_number_message">Please re-enter your phone number.</string>
    <string name="invalid_phone_number_message_booker">This business requires a 10-digit phone number.</string>
    <string name="okay">Okay</string>
    <string name="profile_verify_mobile_phone">Invalid phone number</string>
    <string name="continue_dialog_text">Continue</string>
    <string name="confirm_adding_not_accepted_card">This card is not accepted at %s. Are you sure you want to continue adding this card?</string>
    <string name="action_review_required_fields">Next</string>
    <string name="order_summary_required_fields_legal">Additional info is required for booking</string>
    <string name="currently_unavailable">%s are currently unavailable</string>
    <string name="learn_more_space">\nFind out more</string>
    <string name="learn_more">Learn More</string>
    <string name="create_linked_account_support">Have you used Mindbody at a business before? If so, create your Mindbody app account using the same email, otherwise you\'ll need to link your accounts later.</string>
    <string name="link_fitbit_support">The Mindbody app will show active minutes and calories burned during your workouts and it will retroactively sync with past workouts.</string>
    <string name="add_to_home_button">Make my primary favorite</string>
    <!-- FIXME: copy pending -->
    <string name="reorder_favorites">Reorder</string>
    <string name="added_to_home">%s is on your Home screen</string>
    <string name="removed_from_home">%s was removed from Home screen</string>
    <!-- Text to inform the user how to change between searching by classes and searching by businesses -->
    <string name="view_business">View Business</string>
    <string name="go_to_favorites">Go to Favorites</string>
    <string name="empty_cc_payment_methods_screen_subtext">Store debit, credit or gift cards to make checking out a breeze.\n</string>
    <string name="add_a_credit_card_disclaimer">Use stored cards to pay in the app and in person at Mindbody businesses.</string>
    <string name="mb_pay_dialog_title">No wallet, no phone,\nno problem</string>
    <string name="mb_pay_dialog_message_appt">Cards you save in the Mindbody app are now ready for you to use at the front desk.</string>
    <string name="go_to_business_button">Go to Business</string>
    <string name="facebook_share_title">The Mindbody App</string>
    <string name="facebook_share_description">Book your best day. Every day.</string>
    <string name="search_deals">Search deals</string>
    <string name="class_has_passed">This class has passed</string>
    <string name="location_denied_dialog_confirm_button">Share Location</string>
    <string name="location_denied_dialog_message">We want to show you what\'s available in your area, but we aren\'t sure where you are.  Please share your location with us.</string>
    <string name="location_denied_dialog_title">Find the best places nearby</string>
    <string name="google_fit_activity_dialog_confirm_button">Share Activity</string>
    <string name="google_fit_activity_dialog_message">We want to connect your Google Fit account to show your activity data on the dashboard!  Please share your activity data with us.</string>
    <string name="google_fit_activity_dialog_title">Track your workouts</string>
    <string name="deeplink_failure_business_message">This business is no longer listed.</string>
    <string name="deeplink_failure_class_title">This class is no longer available</string>
    <string name="deeplink_failure_class_message">Check the business schedule to see more options.</string>
    <string name="deeplink_failure_review_message">Check the business to see more options.</string>
    <string name="deeplink_failure_appt_title">This appointment is no longer available</string>
    <string name="deeplink_failure_review_title">This review is no longer available</string>
    <string name="deeplink_failure_deal_title">This deal is no longer available</string>
    <string name="deeplink_failure_deal_message">See what else the business has to offer.</string>
    <!-- indicates the ending time of a class, appointment, etc. For example, if it was held
         10:30am - 11:00, then this string would represent the latter half -->
    <string name="through_time">- <xliff:g name="time" example="10:30">%1$s</xliff:g></string>
    <!-- indicates how much an item used to be (before discounts, etc.) -->
    <string name="old_price">was <xliff:g name="price" example="$30.00">%1$s</xliff:g></string>
    <!-- Instructor cell description -->
    <string name="view_schedule_for_instructor">View Info and Schedule</string>
    <string name="about_phrase">About %s</string>
    <string name="combined_deal_share_title">%1$s at %2$s</string>
    <string name="combined_appt_share_title">%1$s at %2$s</string>
    <string name="combined_class_share_title">%1$s at %2$s</string>
    <string name="combined_staff_share_title">%1$s at %2$s</string>
    <string name="deals">Deals</string>
    <string name="pref_distance_summary">Use miles, kilometers, or your country\'s units</string>
    <string name="deal_details_banner_qualified">You\'re eligible for this deal</string>
    <string name="deal_details_banner_unqualified">You\'re not eligible for this deal</string>
    <string name="pref_notifications_section_header">Types</string>
    <string name="alerts">Alerts</string>
    <string name="action_switch">switch</string>
    <string name="connected">Connected</string>
    <string name="not_connected">Not Connected</string>
    <string name="phone_copied_to_clipboard">Phone number copied to clipboard</string>
    <string name="location_required_message">Please enable location services to search nearby, or enter a location</string>
    <string name="location_denied_enter_location">Location permissions denied, please enter a location</string>
    <string name="location_denied_enter_location_description">To search in your area, please allow location permission in device settings.</string>
    <string name="remove_from_favorites_success">Successfully removed from favorites</string>
    <string name="request_password_change_failure">Unable to reset password at this time, please try again later.</string>
    <string name="quick_info_cta_default_text">Other questions?</string>
    <string name="signed_in_text_dialog">You are signed in!</string>
    <string name="error_signing_into_class">Error signing into class</string>
    <string name="signin_dialog_message">Save time later by signing in now.</string>
    <string name="signin_query_w_classname">Would you like to sign in to %s?</string>
    <string name="send_email_header">Send email…</string>
    <string name="email_referral_subject">Request a Business</string>
    <string name="email_referral_body"><![CDATA[Hi!<br><br>I want to recommend a business for the Mindbody app.  It\'s within <b>%2$s</b> of <b>%3$s</b> and is called <b>%1$s</b>.]]></string>
    <string name="default_referral_searchterm"><![CDATA[<BUSINESS NAME>]]></string>
    <string name="default_referral_num_miles"><![CDATA[<#> miles]]></string>
    <string name="default_referral_location"><![CDATA[<LOCATION>]]></string>
    <string name="your_class">your class</string>
    <string name="call_to_cancel_dialog_message">This business does not allow online cancellations.  Would you like to call them?</string>
    <string name="call_to_cancel_dialog_call_action">Call</string>
    <string name="call_to_cancel_copy_number">Copy Number</string>
    <string name="session_empty_title">No passes yet? No problem.</string>
    <string name="business_list_activity_title">Select Location</string>
    <!-- Edit Profile screen section header for Contact Info -->
    <string name="my_info_contact_info_header">Contact Information</string>
    <string name="insert_address_here">[business name here]</string>
    <string name="password_request_already_sent_message">A password reset request has already been made, please check your email to continue.</string>
    <string name="support_email_subject">Here\'s my feedback about the Mindbody app</string>
    <string name="support_email_prompt_header">Hello!</string>
    <string name="support_email_prompt_message">We\'d love to hear from you! The Mindbody team is happy to field all of your questions and comments.</string>
    <string name="support_email_positive_button">Open Email</string>
    <string name="support_email_body"><![CDATA[<br><br><br><br><br>For the Mindbody team:<br><br>Device: %1$s, App Version: %2$s]]></string>
    <!-- Indicates that there are no last minute offers for today, but there are some for tomorrow with the current search filters -->
    <!-- The time format for a last minute offer on the home screen -->
    <string name="home_last_minute_offer_time_format"><xliff:g name="start_time" example="8:30am">%1$s</xliff:g> <xliff:g name="time_zone" example="PST">%2$s</xliff:g> <xliff:g name="start_date" example="tomorrow">%3$s</xliff:g></string>
    <string name="num_min"><xliff:g example="40" id="ordinal number">%1$d</xliff:g>min</string>
    <string name="num_min_parentheses">(<xliff:g example="40" id="ordinal number">%1$d</xliff:g>min)</string>
    <string name="str_mi_short">%s mi</string>
    <string name="str_km_short">%s km</string>
    <string name="gcm_consent_title">Get the Inside Scoop</string>
    <string name="gcm_consent_message">Enable notifications for events near you, class recommendations, and special offers on brands we love!</string>
    <string name="gcm_button_not_now">Not now</string>
    <string name="gcm_button_ok">Enable</string>
    <!-- Profile item to launch live chat -->
    <string name="action_live_chat">Contact us</string>
    <!-- Profile item to got to FAQ -->
    <string name="action_faq">FAQ</string>
    <!-- Profile item to log the user out -->
    <string name="action_logout">Log out</string>
    <!-- Profile item to email support -->
    <string name="action_contact_us">Contact us</string>
    <string name="action_try_live_chat"><u>Find help here</u></string>
    <string name="trouble_logging_in">Having trouble logging in?</string>
    <string name="intro_better_price_header">Great news!</string>
    <string name="intro_better_price_message">We found you an Intro Offer!</string>
    <string name="account_locked_out">Due to a number of failed attempts your account has been temporarily locked. Please try again shortly.</string>
    <string name="missing_passes_message">All accounts are linked based on email address and name. Resend your confirmation email here to link your accounts.</string>
    <!-- This is the "SEE ALL" button to the right of a subheader in business / class details -->
    <string name="see_all">See all</string>
    <!-- The following 3 are subheader titles for business detail cards -->
    <string name="about_this_studio_header">About this Studio</string>
    <string name="business_description_header">Description</string>
    <string name="business_links_header">Contact</string>
    <string name="intro_offer_label">Intro Offer</string>
    <string name="service_with_instructor">%1$s w/ %2$s</string>
    <string name="time_with_instructor">%1$s w/ %2$s</string>
    <string name="appointment_date_time">%1$s at %2$s - %3$s</string>
    <string name="time_with_instructor_multiline">%1$s\nw/ %2$s</string>
    <string name="intro_offers_header">Intro Offers</string>
    <string name="intro_offers_header_alt">Offers for first-time clients</string>
    <string name="intro_offers_deals_subheader">Save on something new</string>
    <string name="lmo_offers_header_alt">Weekly deals by interest</string>
    <string name="spas_carousel_heading">Spas to treat yourself</string>
    <!-- This is the default text that shows up in the search bar in the Explore tab -->
    <string name="explore_search_hint_text">Start Typing</string>
    <!-- This is the default text that shows up in the location bar in the Explore tab -->
    <string name="explore_search_location_hint_text">Current location - <xliff:g name="distance" example="5mi">%1$s</xliff:g></string>
    <!-- This is the view by header on the explore page, when you click the dropdown carat -->
    <string name="my_location_header">My Location</string>
    <!-- This is the text that shows up in the header, when the user types a keyword search-->
    <string name="filter_action_button_text">Filter</string>
    <string name="reset">Reset</string>
    <string name="reset_all">Reset all</string>
    <string name="favorites_only_switch_label">Favorite businesses</string>
    <string name="virtual_only_switch_label">Virtual classes</string>
    <string name="flex_only_switch_label">Mindbody Flex only</string>
    <string name="flex_only_new_label">NEW</string>
    <!-- action text that applies the filters the user has set to a search for classes -->
    <string name="class_search_apply_filters">Apply</string>
    <string name="fitness_subvertical_outdoor">Outdoor</string>
    <string name="fitness_filter_categories_header">Fitness Categories</string>
    <string name="beauty_filter_categories_header">Beauty Categories</string>
    <string name="wellness_filter_categories_header">Wellness Categories</string>
    <string name="all_filter_categories_header">All Categories</string>
    <string name="home_search_directive_header">Search for <b>Anything</b></string>
    <string name="explore_card_title">Discover Classes</string>
    <string name="virtual_card_title">Sweat from home!</string>
    <string name="virtual_header">Virtual Classes</string>
    <string name="intro_offer_card_title">Deals to get started</string>
    <!-- Title of card on home screen directing user to their single favorite -->
    <!-- Title of card on home screen directing user to their multiple favorites -->
    <!-- Title of card on home screen directing user to favorite some locations -->
    <string name="your_favorites_card_empty">Start finding your favorite businesses today</string>
    <!-- Call to action on home screen directing user to find locations to favorite -->
    <string name="your_favorites_card_empty_action">Explore</string>
    <!-- Title of section on home screen allowing users to view intro offers -->
    <string name="last_minute_offers_empty_title">Your workouts, your way</string>
    <!-- Subtitle of section on home screen allowing users to view intro offers -->
    <!-- Subtitle of section on home screen allowing users to view intro offers -->
    <string name="virtual_empty_subtitle">Stay in shape and support your favorite studios.</string>
    <!-- Action for section on home screen allowing user to view intro offers -->
    <string name="price_disclaimer">Prices are subject to change</string>
    <!-- This is the photo attribution text below the image in business details -->
    <string name="photo_by">Photo by <xliff:g name="contributor" example="Joe Pesci">%s</xliff:g></string>
    <string name="business_details_cancellation_policy_header">Cancellation</string>
    <!-- Copy for onboarding screens -->
    <string name="onboarding_classes_text">Classes where you want, when you want</string>
    <string name="onboarding_classes_subtext">Find the best in fitness, beauty and wellness, wherever you are</string>
    <string name="onboarding_deals_text">Access exclusive deals</string>
    <string name="onboarding_deals_subtext">Discover a new routine or book a favorite while getting exclusive deals on intro and last minute offers</string>
    <string name="onboarding_track_text">Track your progress + reach your goals </string>
    <string name="onboarding_track_subtext">Track and share your progress toward your wellness goals and get exclusive offers through the Mindbody app</string>
    <string name="onboarding_start_text">Get started</string>

    <string name="deals_empty_lmo_subtitle">Choose from a variety of fitness classes</string>

    <!-- Home screen title for the explore category buttons -->
    <!-- The following 3 are for the photo cards on the main home screen -->
    <string name="home_intro_offer_card_subtext">Ready to score a sweet deal?</string>
    <!-- This is the "VALUE: $50.00" text that goes in the giftcard quickbook header -->
    <string name="value_gc_concat">Gift Card Value: <xliff:g name="giftcardvalue" example="$50.00">%s</xliff:g></string>
    <string name="login_helper_explanation"><![CDATA[If you\'ve logged into a Mindbody App, we\'ll find your login info & sync your passes. If you only have accounts at individual studio websites, you\'ll need to create a new login for the app.]]></string>
    <!-- This is the subtext that sits on the login screen, if you have an account -->
    <string name="sign_in_subtext">Sign in to continue.</string>
    <string name="class_unavailable_error_message">This class is currently unavailable</string>
    <string name="no_businesses_message">This business is no longer listed in our app. Please contact the business for more details.</string>
    <!-- This is the message on the favorites card that shows on the Home screen -->
    <!-- Shows 99+ on the schedule badge when there is more than 9 schedule items -->
    <!-- Used in search, when user leaves it blank -->
    <string name="anything">Anything</string>
    <string name="businesses_button">Businesses</string>
    <!-- notification channel for making API calls for the widget -->
    <string name="app_sync_channel">Sync</string>
    <!-- Title for a section that allows the user to book a class, that was taken before, again -->
    <string name="book_it_again_section_title">Book it Again</string>
    <!-- This is the button on Beauty / Wellness that returns back to the tiles -->
    <string name="return_to_tiles_button">Back to Categories</string>
    <string name="strava_activity_title">Strava</string>
    <!-- Title for a screen which lists the most recent searches made by the user -->
    <string name="recent_searches_title">Recent History</string>
    <!-- Text for a message that indicates the recent search history has been cleared -->
    <!-- indicates that pressing the button will clear the history for the user's most recent searches -->
    <string name="delete_recent_searches">Clear recent history</string>
    <!-- On the home screen, this is the title of the cloudsearch area when DSPOs are not available in the area -->
    <string name="title_flex_starting_soon">Flex classes starting soon</string>
    <string name="title_flex_early_birds">Flex for early birds</string>
    <string name="title_flex_night_owls">Flex for night owls</string>
    <string name="invalid_location">Invalid Location</string>
    <string name="strava_upload_title_format">%1$s with %2$s @ %3$s</string>
    <string name="strava_upload_description">Powered by Mindbody</string>
    <!-- popup Alert Dialog - Used in search, to clear the history  -->
    <string name="clear_history_msg">This action will delete all of your recent search history. Clear your data anyway?</string>
    <!-- region My Activity Dashboard -->

    <string name="activity_dashboard_title">Activity</string>
    <string name="fitbit_activity_dashboard_title">Fitbit Activity</string>
    <string name="google_fit_activity_dashboard_title">Google Fit Activity</string>
    <!-- Indicates that the range will cover the last month of classes -->
    <string name="activity_dashboard_month_range">Month</string>
    <!-- Indicates that the range will cover the last week of classes -->
    <string name="activity_dashboard_week_range">Week</string>
    <!-- Indicates that the range will cover today's classes -->
    <string name="activity_dashboard_day_range">Today</string>
    <!-- Indicates the total number of classes represented by the data -->
    <string name="activity_dashboard_total_classes"><xliff:g name="num_classes" example="4302">%1$,d</xliff:g> Total Classes</string>
    <string name="connect_activity_tracker">Connect your health tracker to view activity data.</string>
    <string name="connect_activity_tracker_connect_now">Connect Now</string>
    <!-- Header for the categories horizontal slider in the activity dashboard -->
    <string name="completed_categories_header">Your Completed Categories</string>
    <string name="last_activity_section_title">Last Activity</string>
    <string name="bpm_time_in_zones_header">BPM\nTime in Zones (<xliff:g name="date_range" example="Jul 4-Jul 11">%1$s</xliff:g>)</string>
    <!-- Header format for week/month chart. Indicates time spent in minutes -->
    <string name="chart_header_format">%1$,dm</string>
    <!-- Label format for week/month chart. Indicates the heart rate BPM range per column -->
    <string name="chart_label_format">%1$d-%2$d</string>

    <!-- endregion -->
    <!-- Generic text that links to the user's activity page -->
    <string name="explore_classes">Explore Classes</string>
    <string name="activity_dashboard_no_fitbit_data_message">Haven\'t booked a class today? Let\'s find something that moves you!</string>
    <string name="amenities_subtitle">Amenities</string>
    <!-- Title of the charts, stands for "Beats per minute" -->
    <string name="bpm_title">BPM</string>
    <string name="num_min_short">%dm</string>
    <string name="class_activity_title">Class Activity</string>
    <string name="pref_title_activity_dashboard_notification">Activity</string>
    <string name="pref_summary_activity_dashboard_notification">Send me reminders to check my fitness tracker data</string>
    <string name="activity_notification_title">Activity Data</string>
    <string name="activity_notification_message">Check out your steps, calories, and heart rate zones from today\'s class in your activity dashboard!</string>
    <string name="quickbook_dialog_pass_name">Pass (%s)</string>
    <!-- This is used to append the time zone to the class time -->
    <string name="time_zone_append"><xliff:g name="class_time" example="6 - 6:30pm">%1$s</xliff:g> <xliff:g name="timezone" example="EST">%2$s</xliff:g></string>
    <string name="continue_as_guest_button"><u>Continue as a guest</u></string>
    <string name="login_required_title">Sign in or up to continue</string>
    <string name="login_required_message">You’ll need to sign into Mindbody or create an account to continue</string>
    <string name="login_required_confirm_button">Sign in or up</string>
    <string name="login_required_cancel_button"><u>No thanks</u></string>
    <string name="pricing_not_logged_in_banner">You\'re not logged in!  So if you have a membership or a pack of classes your pack isn\'t showing.\u0020<u>Log in</u></string>
    <string name="contracts_list_not_logged_in_banner">You\'re not logged in. In order to purchase a contract, please \u0020<u>login</u>.</string>
    <string name="schedule_item_pricing_continue_button">Log in to purchase</string>
    <string name="guest_mode_no_favorites_title">Love a business? Add it here.</string>
    <string name="guest_mode_no_favorites_subtext">Make this your one stop for all the businesses you’ll want to book again.</string>
    <string name="guest_mode_no_favorites_classes_text">Add your go-to workouts.</string>
    <string name="guest_mode_no_favorite_classes_subtext">Make a list of your favorite classes and book them with just a tap.</string>
    <string name="guest_mode_visit_history_empty_title">Check out your past accomplishments.</string>
    <string name="guest_mode_visit_history_empty_subtext">See what classes and services you have completed, leave a review, book again and more!</string>
    <string name="log_in_or_create_an_account">Log in or create an account</string>
    <string name="guest_mode_intro_offer_banner">Ready to score a sweet deal? Log in now to see if you\'re eligible for this Intro Offer.</string>
    <string name="guest_mode_class_book_button">Log in or create an account to book this class or service.</string>
    <string name="terms_of_service_cancel_text">No, cancel</string>
    <string name="access_token_action_button">Start exploring</string>
    <string name="sub_header_purchased_passes">Your Passes</string>
    <string name="sub_header_non_purchased_passes">Purchase a Pass</string>
    <string name="user_unlinked_google_fit_toast">Google Fit is disconnected</string>
    <string name="user_unlink_google_fit_prompt_message">Would you like to disconnect your Google Fit account?</string>
    <string name="user_unlink_google_fit_prompt_title">Disconnect Google Fit</string>
    <string name="google_fit_activity_title">Google Fit</string>
    <string name="google_fit_social_message_text">Sync with Google Fit to track all your workouts and share accomplishments with friends.</string>
    <string name="unlink_google_fit_button_text">Disconnect Google Fit</string>
    <string name="link_google_fit_button_text">Connect Google Fit</string>

    <!-- The title and body for a picker prompting the user to decide which fitness tracker to use data from (eg, Google Fit or Fitbit) -->
    <string name="pick_fitness_tracker_title">Your Activity Data</string>
    <string name="pick_fitness_tracker_body">How would you like your activity to come through?</string>
    <string name="no_favorite_staff_text">Show your favorite staff some love!</string>
    <string name="guest_mode_no_favorite_staff_text">Add your top teachers and trainers.</string>
    <string name="no_favorite_staff_subtext">Want easy access to your go-to staff members and their schedules? Tap the heart to instantly save them here!</string>
    <string name="guest_mode_no_favorite_staff_subtext">Tap the heart to save your go-to staff members for fast access to their schedules.</string>
    <string name="view_info_text">View Info</string>

    <!-- TODO: get finalized copy -->
    <string name="confirmation_popup_partner_offers_body">Get access to exclusive deals and more from the best brands!</string>
    <string name="credit_card_capture_content_description">Credit Card Capture</string>
    <string name="remove_fav_staff_confirmation_title">Are you sure you want to remove this staff member from your favorites?</string>
    <string name="pref_calendar_setting_header">Calendar syncing</string>
    <string name="pref_category_title_more">@string/more_cap</string>
    <string name="empty_string" />
    <string name="no_calendar_message">No calendars found.  Please set up a calendar from your device settings</string>
    <string name="calendar_sync_successfully_enabled_message">Calendar syncing has been enabled.  Choose your calendar to sync with in Settings</string>
    <string name="activity_dashboard_guest_prompt_header">View your class activity and more!</string>
    <string name="activity_dashboard_guest_prompt_subheader">Crushing it? Get a recap of all you\'ve accomplished.</string>
    <string name="activity_dashboard_guest_prompt_signup">Create an Account</string>
    <string name="activity_dashboard_guest_prompt_login">I already have an Account, log in</string>
    <string name="pass_list_row_title"><xliff:g name="sessions_remaining" example="1/4">%1$s</xliff:g> - <xliff:g name="pass_name" example="1 Week Free Trial (Special)">%2$s</xliff:g></string>
    <string name="use_pass_action">Use pass</string>
    <string name="profile_sign_up_title">View your schedule, passes, and more!</string>
    <string name="profile_sign_up_subtext">See which classes you\'ve already taken to easily find and book them again.</string>
    <string name="guest_user_name">Guest User</string>
    <string name="no_total_classes">0 Total Classes</string>
    <string name="appointment_booking_confirmation_text">Would you like to book this appointment?</string>
    <string name="livestream_info_label_link"><u>Livestream access info</u></string>
    <string name="livestream_info_label">Livestream access info</string>
    <string name="cta_join_livestream">Join Livestream</string>
    <string name="starts_in_countdown">starts %s</string>
    <string name="livestream_info_body">The link to watch your livestream video will appear %d minutes before class. Just tap join to start!</string>
    <string name="livestream_link_failure_message">Livestream is unavailable. Please refresh and try again</string>
    <string name="account_button_text">Account</string>
    <string name="manage_or_cancel_contracts">Manage or cancel contracts</string>
    <string name="current_password">Current password</string>
    <string name="new_password">New password</string>
    <string name="password_validation_char_limits">Between 8 and 30 characters</string>
    <string name="password_validation_uppercase_char">At least one upper case letter</string>
    <string name="password_validation_lowercase_char">At least one lower case letter</string>
    <string name="password_validation_number_special_char">At least one number or special character</string>
    <string name="change_password_title">Change password</string>
    <string name="account_info_header">Account Information</string>
    <string name="change_underlined"><u>Change</u></string>
    <string name="change">change</string>
    <string name="change_password_underlined"><u>Change password</u></string>
    <string name="faux_password" translatable="false">••••••••••••</string>
    <string name="new_email_hint">New Email</string>
    <string name="change_email_activity_title">Change Email</string>
    <string name="passwords_do_not_match_message_short">Passwords do not match</string>
    <string name="updating_password_loading_message">Updating password…</string>
    <string name="confirm_email_dialog_title">Confirm Email</string>
    <string name="confirm_email_dialog_text">Please confirm your new email address. Your email will not be changed until you have confirmed your new email address</string>
    <string name="updating_email_loading_message">Updating email…</string>
    <string name="unknown_error_dialog_text">Having trouble? Contact customer service</string>
    <string name="email_in_use">Email already in use</string>
    <string name="resend_confirmation_email_underlined"><u>Resend email confirmation</u></string>
    <string name="confirm_now_action">Confirm now</string>
    <string name="resend_verification_email_underlined"><u>Resend verification email</u></string>
    <string name="verification_email_sent_confirmation">Verification email sent</string>
    <string name="user_rating_ada_description">User rating</string>
    <string name="close_button_ada">Close</string>
    <string name="banner_classes_description">Classes Button</string>
    <string name="businesses_button_description">Businesses Button</string>
    <string name="book_cta_ada_description">%1s Button</string>
    <string name="stars_ada_description">%1$s out of 5 stars</string>
    <string name="filter_ada_description">Filter Button</string>
    <string name="backspace_text">Backspace</string>
    <string name="intro_offer_ada_description">%1$d Intro Offers Available</string>
    <string name="flex_label">flex</string>
    <string name="flex_price_suffix">with Flex</string>
    <string name="flex_banner_start">Learn more</string>
    <string name="flex_category_title">Explore Flex categories</string>
    <string name="non_flex_category_title">Explore categories</string>
    <string name="non_flex_category_title_v2">Inspiration for your next workout</string>
    <string name="flex_pass_name">Mindbody Flex Membership</string>
    <string-array name="fitness_category_names">
        <item>Yoga</item>
        <item>Meditation</item>
        <item>Pilates</item>
        <item>Barre</item>
        <item>Strength</item>
    </string-array>
    <string name="flex_limit_reached_title">Limit reached this month!</string>
    <string name="flex_limit_reached_message">You\'ve already been to this business 3 times this month with your Mindbody Flex Membership. Keep exploring or select a different payment method.</string>
    <string name="flex_update_payment">Update Payment</string>
    <string name="first_slash_second">%1$s/%2$s</string>
    <string name="time_zone_dislaimer_title">Class Times Updated</string>
    <string name="time_zone_dislaimer_message">We see that you\'re searching in an area that has a different time zone. To make things easier, we\'ve updated the class times to your local time.</string>
    <string name="action_manage_underlined"><u>MANAGE</u></string>
    <string name="num_classes_available">%1$d/%2$d Classes Available</string>
    <string name="renews_on">Renews: %1$s</string>
    <string name="flex_membership_recurring_charge">Recurring %1$s (+tax) to %2$s %3$s</string>
    <string name="flex_membership_expired">Payment failed, active until %1$s. Please try updating your payment method.</string>
    <string name="time_slider_string_format">%1$s %2$s</string>
    <string name="authentication_required_header">Authentication Required</string>
    <string name="sca_authentication_required_message">You must authenticate your card before we can charge it.</string>
    <string name="sca_authentication_failed">You must authenticate your card in order to complete checkout</string>
    <string name="quickbookdialogv2">QuickBookDialogV2</string>
    <string name="map_pin">Destination Point</string>
    <string name="logo_mindbody">Mindbody logo</string>
    <string name="favorite">favorite</string>
    <string name="heading">Heading</string>
    <string name="profile_photo">Profile Photo</string>
    <string name="more_options">See More Options</string>
    <string name="flex_trial_message">Access to the best live stream classes</string>
    <string name="flex_credit_support_message">For further support or to send feedback, contact</string>
    <string name="flex_credit_support_message_highlight">Flex\u00A0Support</string>
    <string name="credit_flow_feedback_label">"I would like to give feedback "</string>
    <string name="request_account_credit_label">"Request account credit  "</string>
    <string name="credit_request_reason">"Was there an issue with your Mindbody Flex class?\nPlease select all that apply:"</string>
    <string name="reason_details_hint">Please provide more details</string>
    <string name="credit_reason_audio">Poor audio quality</string>
    <string name="credit_reason_video">Poor video quality</string>
    <string name="credit_reason_no_link">No link provided</string>
    <string name="credit_reason_no_access">Couldn\'t access class</string>
    <string name="credit_reason_other">Other</string>
    <string name="flex_request_subtitle">Need help?</string>
    <string name="thank_you_header">Thank you!</string>
    <string name="we_value_your_feedback">We value your feedback.</string>
    <string name="flex_credit_apology">We apologize for any issues you may have encountered, and will follow up with the studio to address them. Your input will also be used to continue improving the Mindbody Flex experience.</string>
    <string name="back_to_profile">Back to profile</string>
    <string name="happy_to_help">We\'re happy we could help!</string>
    <string name="any_more_issues">If you have any more issues, don\'t hesitate to reach out!</string>
    <string name="flex_credit_faq_header">Troubleshoot</string>
    <string name="flex_credit_faq_subheader">"View our FAQ for help with issues you may be experiencing."</string>
    <string name="contact_text">Contact Studio</string>
    <string name="contact_text_title">Contact Studio?</string>
    <string name="flex_contact_instructions">"For immediate assistance,\nplease contact the studio."</string>
    <string name="resolution_question_label">"Did this resolve your issue?"</string>
    <string name="help_needed">Need help?</string>
    <string name="you_can">You can </string>
    <string name="expect_to_receive_credit">expect to receive your class credit within the next 24 to 48 hours. </string>
    <string name="it_will_be_reflected">It will be reflected in the </string>
    <string name="passes"> Passes </string>
    <string name="section_of_profile">section of your profile.</string>
    <string name="last_minute_offers">Last Minute Offers</string>
    <string name="credit_processing">Thank you for reporting this issue.\nOur records indicate a credit has already been refunded to your account for this class. If you need additional assistance, please reach out to our support team.</string>
    <string name="title_maintenance_mode">Heads Up!</string>
    <string name="message_maintenance_mode">The Mindbody App is experiencing a maintenance outage. Some functionality may be limited at this time. Please check back soon for updates.</string>
    <string name="call_studio">Call Studio</string>
    <string name="purchase_blocked_message">We know it\'s a hassle, but you can\'t purchase virtual classes in the app.</string>
    <string name="maintenance_mode_server_status_link"><a href="https://bookabilityStatus.mindbodyonline.com/"><u>Check bookabilityStatus of Mindbody systems</u></a></string>
    <string name="read_about_gender_options">Read about the 20+ gender options and their definitions</string>
    <string name="here">here</string>
    <string name="about_information">about how Mindbody uses your information</string>
    <string name="privacy_policy_description">Privacy Policy</string>
    <string name="nav_activity_dashboard">Activity</string>
    <string name="studio_logo">Studio Logo</string>
    <string name="content_description_virtual_class">Virtual Class</string>
    <string name="deal_ratings">(%1$s)</string>
    <string name="deal_ratings_zero_reviews">(0 reviews)</string>
    <string name="lmo_deal_category_class">%1$s \u2022 %2$s</string>
    <string name="lmo_deal_distance_location">%1$s mi \u2022 %2$s</string>
    <string name="enable_location_for_deals">Enable your location to find\ndeals near you.</string>
    <string name="deals_empty_state_alt_message">We can\’t find any deals\nfor you right now.</string>
    <string name="lmo_deals_select_diff_filter">There aren\’t any deals at this time</string>
    <string name="order_summary_subtotal_label">subtotal</string>
    <string name="order_summary_discount_label">discount</string>
    <string name="order_summery_service_fee_label">Service fee</string>
    <string name="order_summary_tax_label">sales tax</string>
    <string name="service_fee_info">service fee information</string>
    <string name="home_greeting_personal">Good %1$s, %2$s!</string>
    <string name="home_greeting_generic">Good %s!</string>
    <string name="personalized_encouragement">Let\'s do this, %s!</string>
    <string name="generic_encouragement">Let\'s do this!</string>
    <string name="no_favorites_yet">No favorites yet!</string>
    <string name="find_and_add_businesses_here">Find and add businesses here.</string>
    <string name="search_link"><u>Search</u></string>
    <string name="home_location_permission_request"><b>Turn on location permissions?</b>\nFind studios you\'ll love.</string>
    <string name="home_location_permission_cta"><u>Allow location access</u></string>
    <string name="nearby_results_for_you">Check out these nearby results:</string>
    <string name="find_similar_subtitle">Find your new favorite</string>
    <string name="class_has_started">This class has started.</string>
    <string name="cannot_book_class">Sorry, cannot book.</string>

    <!-- Family Accounts -->
    <string name="book_for">Book for</string>
    <string name="select_account">Select an account</string>
    <string name="choose_account_to_book">Choose account to book with</string>
    <string name="selected_member">Selected Member Name</string> <!-- Remove this string later-->
    <string name="view_family_accounts">View Family Accounts</string>
    <string name="manage_family_accounts_button_text">Manage Family Accounts</string>
    <string name="concatenate_string">%1s %2s</string>
    <string name="concatenate_strings_with_underscore">%1s_%2s</string>
    <string name="user_name_booked_at_same_time_header_text">%s is already booked at this time</string>
    <string name="attribution_survey_question">How did you discover this\n business?</string>
    <string name="attribution_prompt">Select one</string>
    <string name="answer_mb_attribution">I found it on the Mindbody App</string>
    <string name="answer_studio_attribution">I heard about it directly from the business</string>
    <string name="class_booked_confirmation_title">Enjoy your class!</string>
    <string name="booked_confirmation_title">You\'re booked!</string>
    <string name="class_booked_confirmation_pas_subtitle">Don\’t forget to choose a spot in class!</string>
    <string name="class_pass">ClassPass</string>
    <string name="give_class_pass">Give %s of free ClassPass</string>
    <string name="invite_deals_title">Have you seen our new Deals section?</string>
    <string name="view_deals">View deals</string>
    <string name="class_pass_gift"><![CDATA[Share your love of studio fitness with a friend. They\'ll get %s free to try top-rated studios & gyms on ClassPass.]]></string>
    <string name="learn_more_sentence_case">Learn more</string>
    <string name="copy_link_sentence_case">Copy link</string>
    <string name="copy_link_uppercase">COPY LINK</string>
    <string name="link_copied_uppercase">LINK COPIED</string>
    <string name="message_uppercase">MESSAGE</string>
    <string name="share_classpass_uppercase">SHARE CLASSPASS</string>
    <string name="share_link_uppercase">SHARE LINK</string>
    <string name="share_this_class_uppercase">SHARE THIS CLASS</string>
    <string name="add_to_calendar_uppercase">ADD TO CALENDAR</string>
    <string name="check_ada">Check</string>
    <string name="message_classpass"><![CDATA[Hey! You\'d love ClassPass — it\'s great for discovering top-rated fitness & wellness brands nearby. Here\'s a free %s trial. LMK when you sign up & we can book something together!]]></string>
    <string name="classpass_share_title"><![CDATA[Book Fitness Classes & Salons]]></string>
    <string name="explore_home_title">Discover your next activity.</string>
    <string name="explore_hint">Search for yoga, massage, HIIT, etc.</string>

    <!--  Search revamp filters  -->
    <string name="text_filter_by_category">Filter by category</string>
    <string name="text_filter_append">%s+%d</string>
    <string name="text_filter_reset">Reset</string>
    <string name="text_filter_apply">Apply</string>
    <string name="explore_home_all_activities">All activities</string>
    <string name="text_filter_by_time">Filter by time</string>
    <string name="text_activities_by_time_period">Activities by time period</string>
    <string name="text_activities_by_start_time">Activities by start time</string>
    <string name="text_activity_by_time_morning">Morning</string>
    <string name="text_activity_by_time_afternoon">Afternoon</string>
    <string name="text_activity_by_time_evening">Evening</string>
    <string name="text_activity_by_time_night">Night</string>
    <string name="filter_by_time_content_description">Activity by time</string>
    <string name="range_slider_content_description">Time range</string>
    <string name="text_selected_time_range">%1s - %2s</string>

    <string name="content_description_fitness_image">Fitness Image</string>
    <string name="content_description_beauty_image">Beauty Image</string>
    <string name="content_description_wellness_image">Wellness Image</string>
    <string name="explore_query_search_for_anything">Search for anything</string>
    <string name="search_current_location_hint">Current location</string>
    <string name="search_use_current_location_hint">Use current location</string>
    <string name="content_description_search_back">Back Image</string>
    <string name="content_description_search_icon">Search Image</string>
    <string name="text_enable_location_services">Enable location services</string>
    <string name="text_see_more">See more</string>
    <string name="text_see_less">See less</string>
    <string name="text_recent_searches">Recent searches</string>
    <string name="text_clear">Clear</string>
    <string name="text_clear_recent_history">Clear recent history</string>
    <!-- Filter By Distance BottomSheet Dialog -->
    <string name="filter_by_distance">Filter by distance</string>
    <string name="filter_option_placeholder">Option</string>
    <string name="i_agree_on_behalf_of">I agree to terms and conditions on behalf of %s.</string>
    <string name="agree">Agree</string>
    <string name="by_clicking_agree_to_all_terms">By clicking agree, you agree to all the terms and conditions</string>
    <string name="decline">Decline</string>
    <string name="home_saved_title">Saved</string>
    <string name="see_all_v2">See all</string>
    <string name="popular_searches">Popular searches</string>

    <string name="_5_stars">5 stars</string>
    <string name="_4_stars">4 stars</string>
    <string name="_3_stars">3 stars</string>
    <string name="_2_stars">2 stars</string>
    <string name="_1_stars">1 stars</string>
    <string name="total_review">%d%%</string>

    <string name="difficulty">Difficulty</string>
    <string name="ratings">Ratings</string>

    <string name="review_dialog_thank_you_description">Your answers will help others find\n great experiences!</string>
    <string name="review_dialog_delete_description">You won\'t be able to recover it.</string>
    <string name="dialog_icon_content_description">Icon</string>

    <string name="how_was_your_experience">How was your experience?</string>
    <string name="overall_rating">Overall Rating</string>
    <string name="tell_us_about_your_experience">Tell us about your experience.</string>
    <string name="your_review_must_be_at_least_x_characters">Your review must be at least %d characters.</string>
    <string name="rate_this_class">Rate this class</string>
    <string name="how_easy_or_difficult_was_this_class">How easy or difficult was this class?</string>
    <string name="finish_review">FINISH REVIEW</string>
    <string name="optional">&#160;(Optional)</string>
    <string name="read_more">read more</string>
    <string name="show_less">show less</string>
    <string name="with_label">\u0020with\u0020</string>
    <string name="not_rated">Not Rated</string>
    <string name="cancel_review_text_changes">Cancel new changes?</string>

    <string name="num_space_min_parentheses">(<xliff:g example="40" id="ordinal number">%1$d</xliff:g> min)</string>
    <string name="today">today</string>
    <string name="tomorrow">tomorrow</string>
    <string name="this_week">this week</string>
    <string name="next_week">next week</string>
    <string name="see_your_schedule">See your schedule</string>
    <string name="upcoming_visit_count">(<xliff:g example="40" id="ordinal number">%1$d</xliff:g>)</string>
    <string name="location_distance">Location distance</string>
    <string name="num_space_mi_short">%.1f mi</string>
    <string name="based_on_your_past_bookings">Based on your past bookings</string>
    <string name="combined_date_and_time">%1$s at %2$s</string>
    <string name="lmo_percent_off">%d\%% off</string>
    <string name="discovery_image">Discovery Image</string>
    <string name="deals_near_you">Deals near you</string>
    <string name="fitness_near_you">Fitness near you</string>
    <string name="fitness">Fitness</string>
    <string name="wellness_near_you">Wellness near you</string>
    <string name="wellness">Wellness</string>
    <string name="beauty_near_you">Beauty near you</string>
    <string name="beauty">Beauty</string>
    <string name="home_near_you_title">Near you</string>
    <string name="view_map_text">View Map</string>
    <string name="no_location_available">No Location Image</string>
    <string name="enable_location">Enable Location</string>
    <string name="lmo_surge_title">Almost sold out</string>
    <string name="intro_offer_label_v2">intro offer</string>
    <string name="filter_categories_title">Categories</string>
    <string name="filter_distance_title">Distance</string>
    <string name="filter_sort_by_title">Sort by</string>
    <string name="filter_category_section_title">Category</string>
    <string name="filter_time_section_title">Time</string>
    <string name="filter_drop_down_image_content_description">Drop down</string>
    <string name="distance_filter_auto_mile_title">Auto (25 miles)</string>
    <string name="distance_filter_auto_km_title">Auto (25 kms)</string>
    <string name="distance_filter_50_plus_miles_title">50+ miles</string>
    <string name="distance_filter_50_plus_kms_title">50+ kms</string>
    <string name="distance_filter_50_plus_mi_selection">50+ mi</string>
    <string name="distance_filter_50_plus_kms_selection">50+ kms</string>
    <string name="distance_mile_placeholder">%d mi</string>
    <string name="all_filter_ada_description">All Filter</string>
    <string name="back_button_ada_description">Back button</string>
    <string name="highlights_subtitle">Highlights</string>
    <string name="sort_option_recommended">Recommended for you (default)</string>
    <string name="sort_option_ratings">Ratings (highest to lowest)</string>
    <string name="sort_option_distance">Distance (nearest to farthest)</string>
    <string name="sorting_option_title">Sort by</string>
    <string name="filter_sort_place_holder">Sort: %s</string>
    <string name="report_abuse">Report</string>
    <string name="flag_as_inappropriate" tools:ignore="ExtraTranslation">Flag as inappropriate?</string>
    <string name="map_title">Map</string>
    <string name="see_all_schedule">%s schedule</string>
    <string name="description_business_image">Business Image</string>
    <string name="contracts_details_items_label">Items</string>
    <string name="contracts_details_payments_banner_label">Payment Schedule</string>
    <string name="contracts_details_start_date_value">Starts %s</string>
    <string name="contracts_details_start_date_value_ftc">Contract starts %s</string>
    <string name="contracts_details_start_date_text">Starts: %s</string>
    <string name="contracts_details_payments_label">recurring amount</string>
    <string name="contracts_details_recurring_amount_text">Recurring amount: %s</string>
    <string name="contracts_details_recurring_amount_qualifier">(not including tax)</string>
    <string name="contracts_details_due_date_label">due</string>
    <string name="contracts_details_due_date_text">Due: %s</string>
    <string name="contracts_details_duration_label">duration</string>
    <string name="contracts_discount_label">discount</string>
    <string name="contracts_details_duration_text">Duration: %s</string>
    <string name="contracts_details_renews_label">renews</string>
    <string name="contracts_details_renews_label_ftc">auto-renewal date</string>
    <string name="contracts_details_total_label">todays total</string>
    <string name="contracts_signature_banner_text">To buy, you must agree to the terms and conditions of </string>
    <string name="contracts_terms_title">terms &amp; conditions</string>
    <string name="contracts_until_pricing_options_run_out">When last class is used</string>
    <string name="contracts_of_every_month">%s of every month</string>
    <string name="contracts_every_x_months">every %s months</string>
    <string name="contracts_every_week">every week</string>
    <string name="contracts_every_x_weeks">every %s weeks</string>
    <string name="contracts_every_year">every year</string>
    <string name="contracts_every_x_years">every %s years</string>
    <string name="contracts_end_of_month">End of month</string>
    <string name="week">week</string>
    <string name="month">month</string>
    <string name="year">year</string>

    <plurals name="num_months">
        <item quantity="one"><xliff:g example="1" id="count_of_months">%d</xliff:g> month</item>
        <item quantity="other"><xliff:g example="5" id="count_of_months">%d</xliff:g> months</item>
    </plurals>
    <plurals name="num_weeks">
        <item quantity="one"><xliff:g example="1" id="count_of_weeks">%d</xliff:g> week</item>
        <item quantity="other"><xliff:g example="5" id="count_of_weeks">%d</xliff:g> weeks</item>
    </plurals>
    <plurals name="num_years">
        <item quantity="one"><xliff:g example="1" id="count_of_years">%d</xliff:g> year</item>
        <item quantity="other"><xliff:g example="5" id="count_of_years">%d</xliff:g> years</item>
    </plurals>
    <string name="contracts_renews_until_pricing_options_run_out_one_zero">every payment</string>
    <string name="contracts_renews_until_pricing_options_run_out_one_zero_ftc">Auto-renews every payment</string>
    <string name="contracts_renews_on_date">Auto-renews on %1$s</string>
    <plurals name="contracts_num_renews_when_pricing_option_runs_out">
        <item quantity="one">every <xliff:g example="1" id="count_of_payments">%1$d</xliff:g> payment</item>
        <item quantity="other">every <xliff:g example="5" id="count_of_payments">%1$d</xliff:g> payments</item>
    </plurals>
    <plurals name="contracts_num_renews_when_pricing_option_runs_out_ftc">
        <item quantity="one">Auto-renews every <xliff:g example="1" id="count_of_payments">%1$d</xliff:g> payment</item>
        <item quantity="other">Auto-renews every <xliff:g example="5" id="count_of_payments">%1$d</xliff:g> payments</item>
    </plurals>
    <plurals name="num_payments">
        <item quantity="one"> payment</item>
        <item quantity="other"><xliff:g example="5" id="count_of_payments">%d</xliff:g> payments</item>
    </plurals>
    <string name="auto_renewal_recurring">Recurring</string>
    <string name="by_clicking_next_you_agree_to_pay">Click \'Next\’, to agree to pay </string>
    <string name="less_applicable_discounts">less applicable discounts</string>
    <string name="auto_renewal_every_payment_terms_and_conditions"><xliff:g example="$1000" id="amount">%1$s</xliff:g> (plus tax) each time your pass(es) run out or expire during each renewal term. Contract auto-renews every <xliff:g example="4 payments/1 payment" id="no_payments_1">%2$s</xliff:g> until you cancel. To stop renewal charges, cancel via ‘Settings’ screen before renewal.</string>
    <string name="auto_renewal_set_date_terms_and_conditions"><xliff:g example="$1000" id="amount">%1$s</xliff:g> (plus tax) per month for each renewal term<xliff:g example="(less applicable discounts)" id="terms_discount">%2$s</xliff:g>. Contract auto-renews every <xliff:g example="4 months/month" id="no_months_1">%3$s</xliff:g> until you cancel. To stop renewal charges, cancel via ‘Settings’ screen before renewal date above.</string>
    <string name="contracts_primary_user_required">To purchase a contract, please switch your profile to the primary account</string>
    <string name="contracts_qb_details_start_label">Start</string>
    <string name="qb_order_details_total_label">today\'s total</string>

    <string name="login_or_sign_up_header">Login or sign up</string>
    <string name="login_email_hint">Enter your email address*</string>
    <string name="error_required_field">Required field</string>
    <string name="login_email_error_invalid_email">Invalid email</string>
    <string name="login_or_sign_up_with">login or sign up with</string>
    <string name="login_terms">By continuing or otherwise indicating assent electronically, you agree to the\u0020<b><a href="TERMS_OF_USE_LINK">Mindbody Consumer Agreement</a></b>\u0020which includes the\u0020<b><a href="PRIVACY_POLICY_LINK">Mindbody Privacy Policy</a></b>.</string>
    <string name="login_header">Login</string>
    <string name="login_password_hint">Password*</string>
    <string name="login_action_forgot_password">Forgot Password?</string>
    <string name="sign_up_link">Don\'t have an account?</string>
    <string name="last_name_hint">Last name*</string>
    <string name="first_name_hint">First name*</string>
    <string name="confirm_password_hint">Confirm password*</string>
    <string name="email_hint">Email*</string>
    <string name="get_email_updates_description">Get emails with news and promos and updates on the latest events and deals</string>
    <string name="create_an_account">Create an account</string>
    <string name="already_have_an_account">Already have an account?</string>
    <string name="network_error_try_again">Network error. Please try again later.</string>
    <string name="continue_as_guest_user_content_description">Continue as guest user</string>
    <string name="spinner_drop_down_content_description">Country drop down image</string>
    <string name="email_pending_verification_title">Email verification pending</string>
    <string name="email_pending_verification_description">In order to finish setting up your account, please check your email for a verification link.</string>
    <string name="resend_email_verification_description">Didn’t get an email?</string>
    <string name="resend_email">Resend</string>
    <string name="log_in">Log in</string>
    <string name="email_verification_image_content_description">Email verification image</string>
    <string name="sign_up">Sign Up</string>
    <string name="confirm_password_validate_message">Passwords do not match</string>
    <string name="incorrect_email_password_message">Incorrect email or password. Please check your details and try again.</string>
    <string name="login_failed_message">Login failed. Please try again. </string>
    <string name="account_locked_out_message">Your account is temporarily locked due to multiple invalid login attempts. Please try again after some time.</string>
    <string name="account_already_exists_message">An account with this email already exists.</string>
    <string name="email_verification_request_already_sent_message">An email verification request has already been made, please check your email to continue.</string>
    <string name="resend_verification_email_failure">Unable to resend verification email at this time, please try again later.</string>
    <string name="activity_price_from">from</string>
    <string name="see_x_more_activities_text">See %1$d more</string>
    <string name="see_less_activities_text">See less</string>
    <string name="no_classes_today_text">No classes today, try adjusting the filters.</string>
    <string name="relaxation">Relaxation</string>
    <string name="flexibility">Flexibility</string>
    <string name="cardio">Cardio</string>
    <string name="strength">Strength</string>
    <string name="outdoor">Outdoor</string>
    <string name="competition">Competition</string>
    <string name="for_you">For you</string>
    <string name="all">All</string>
    <string name="pas_pick_spot_cta"><u>Pick a spot</u></string>
    <string name="pas_confirmation_cta">Book a Spot</string>
    <string name="pas_spot_label">Spot %s</string>
    <string name="pas_edit_cta"><u>edit</u></string>
    <string name="categories_text">%1s | %2s</string>
    <string name="global_location_indicator_nearby_text">Nearby</string>
    <string name="global_location_indicator_no_location_text">No location</string>
    <string name="no_location_card_title">Where should we look?</string>
    <string name="no_location_card_body">Start finding wellness around you by enabling location while using the app</string>
    <string name="open_location_settings">Open Location Settings</string>
    <string name="deals_new_client">New Client Offer</string>
    <string name="deals_returning_client">New And Returning Clients</string>
    <string name="distance_away">%s away</string>
    <string name="pay_with_ideal">Pay with iDEAL</string>
    <string name="ideal">iDEAL</string>
    <string name="ideal_book_text">Confirm payment on the next screen</string>
    <string name="homescreen_price_drop_title">Price drop</string>
    <string name="homescreen_try_something_new_title">Try something new</string>
    <string name="show_more_styled"><u><b>Show more</b></u></string>
    <string name="search_for">Search for</string>
    <string name="spell_correct_search_text">Results for \"%1$s\". Search for \"%2$s\" instead?</string>
    <string name="suggestions">Suggestions</string>
    <string name="single_bullet">•</string>
    <string name="pay_with_bancontact">Pay with Bancontact</string>
    <string name="bancontact">Bancontact</string>
    <string name="pay_with_klarna">Pay with Klarna</string>
    <string name="klarna">Klarna</string>
    <string name="notification_permission_denied_title">Notification Permission is unavailable</string>
    <string name="pay_with_google_pay">Pay with Google Pay</string>
    <string name="google_pay">Google Pay</string>
    <string name="deal_tag_text">%1$s %2$s</string>
    <string name="get_this_deal">Get this deal</string>
    <string name="pay_with_twint">Pay with TWINT</string>
    <string name="twint">TWINT</string>
    <string name="pay_with_fpx">Pay with FPX</string>
    <string name="fpx">FPX</string>
    <string name="deal_expiry_date_formatter">Expires %s</string>
    <string name="expires_after_date">Expires after %s</string>
    <string name="pay_with_alipay">Pay with Alipay</string>
    <string name="alipay">Alipay</string>
    <string name="pay_with_PayNow">Pay with PayNow</string>
    <string name="text_paynow">PayNow</string>
    <string name="pay_with_WeChat">Pay with WeChat Pay</string>
    <string name="text_wechat">WeChat Pay</string>
    <string name="ineligible_deal_title">Sorry, you\'re not eligible for this deal</string>
    <string name="ineligible_deal_message">It seems like you are not eligible for the %s deal. Please contact the business directly for assistance.</string>
    <string name="unpaid_late_cancellation_message">We are past the cancellation window. If you decide to continue, this business may charge a cancellation fee.</string>
    <string name="validate_credit_card_failed_message">Your card is invalid! Please select another card or add a new card.</string>
    <string name="thanks_for_your_purchase">Thanks for your purchase!</string>
    <string name="gift_card_purchase_confirmation_text">Your gift card will be sent to %1$s by %2$s</string>
    <string name="buy_another_gift_card">Buy another gift card</string>
    <string name="added_to_waitlist">Added to waitlist!</string>
    <string name="pick_a_spot">Pick a Spot</string>
    <string name="apm_checkout_failed">We apologize your checkout with %1$s failed. Please try again.</string>
    <string name="apm_checkout_timed_out">Error : %1$s checkout timed out and failed. Please try again.</string>
    <string name="apm_message_for_polling">Your payment session will timeout in %1$d minutes! Please navigate back to the Mindbody app after payment authorization to complete the booking.</string>
    <string name="heads_up">Heads Up!</string>
    <string name="you_have_not_been_charged">You have not been charged. Please try again.</string>
    <string name="payment_in_progress">Payment in Progress</string>
    <string name="cancel_ongoing_payment">Are you sure you want to cancel a ongoing payment?</string>
    <string name="close_anyway">Close Anyway</string>
    <string name="stay">Stay</string>
</resources>
