<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- region Colors and Accents -->
    <!-- use non-gradient color if item is too small for gradient -->

    <!--
        Theme Color
        Primary CTA & Background
        Main "Fitness" Color -->
    <color name="primary_orange_gradient_start">@color/burnt_red</color>
    <color name="primary_orange_gradient_end">@color/orange_yellow</color>
    <color name="primary">@color/basil</color>
    <color name="primary_solid_header_color">@color/basil</color>

    <!-- Main "Wellness" Color -->
    <color name="secondary_green_gradient_start">@color/yellow_green</color>
    <color name="secondary_green_gradient_end">@color/green_blue</color>
    <color name="secondary_green">@color/green</color>

    <!-- Main "Salon" Color -->
    <color name="secondary_violet_gradient_start">@color/blue_violet</color>
    <color name="secondary_violet_gradient_end">@color/violet</color>
    <color name="secondary_violet">@color/purple</color>

    <!-- Main "Your Profile" Color -->
    <color name="secondary_pink_gradient_start">@color/purple_red</color>
    <color name="secondary_pink_gradient_end">@color/red_orange</color>
    <color name="secondary_pink">@color/pink</color>

    <!-- Assistant UI colors -->
    <color name="scrim">@color/translucent_black</color>
    <color name="list_divider_color">@color/neutral_grey_30</color>
    <color name="shadow_scrim_color">@color/neutral_light_grey_50</color>
    <color name="translucent_button_background_color">@color/white_20</color>
    <color name="nav_bar_tab_indicator_color">@color/transparent</color>

    <!-- endregion -->

    <!-- region Neutral Colors -->

    <!--
        Primary Color & Font Color
        Headlines, captions, selected states, tapped links -->
    <color name="text_header">@color/neutral_black</color>
    <!-- Font Color for body copy -->
    <color name="text_body">@color/neutral_dark_grey</color>
    <!-- Font Color for body copy on a dark background-->
    <color name="text_body_dark">@color/white</color>
    <!-- text and icons that are buttons and unselected buttons (eg, back arrows and plus icons) -->
    <color name="button">@color/dark_gray</color>
    <color name="colorControlNormal">@color/neutral_dark_grey</color>
    <color name="colorControlActivated">@color/neutral_black</color>
    <!-- general background of the app -->
    <color name="background">@color/neutral_white</color>
    <!-- borders -->
    <color name="border">@color/neutral_light_grey</color>

    <!-- endregion -->

    <!-- Hex values -->
    <color name="purple_red">#B52B72</color>
    <color name="burnt_red">#CE3D4A</color>
    <color name="red_orange">#D54542</color>
    <color name="red_orange1">#D54400</color>
    <color name="red_orange_2">#E8631E</color>
    <color name="red_orange_3">#E65926</color>
    <color name="orange_yellow">#F77D0C</color>
    <color name="yellow">#DEE200</color>
    <color name="yellow_green">#5AC344</color>
    <color name="green_blue">#208D9F</color>
    <color name="blue" tools:override="true">#3078B9</color>
    <color name="blue_violet">#7E40B2</color>
    <color name="violet">#A72E83</color>

    <color name="white_20">#33FFFFFF</color>
    <color name="white_30">#4DFFFFFF</color>
    <color name="white_50">#80FFFFFF</color>
    <color name="orange1">#D54400</color>
    <color name="dark_gray">#696C74</color>
    <color name="orange" tools:override="true">#E85815</color>
    <color name="orange_50" tools:override="true">#80E85815</color>
    <color name="green">#15AB66</color>
    <color name="purple">#7E40B2</color>
    <color name="pink">#CE3D4B</color>
    <color name="vermilion">#ED4929</color>

    <color name="neutral_black">#2D2D2D</color>
    <color name="neutral_dark_grey">#696C74</color>
    <color name="neutral_grey">#A0A6AB</color>
    <color name="neutral_grey_30">#4CA0A6AB</color>
    <color name="neutral_white">#F2F3F5</color>
    <color name="neutral_light_grey">#D2D3D3</color>
    <color name="neutral_light_grey_50">#80D3D2D3</color>
    <color name="lighter_grey">#EFF1F3</color>
    <color name="text_grey">#a0a1a3</color>
    <color name="light_gray_whitish">#d6d6d6</color>

    <color name="translucent_black">#********</color>

    <color name="explore_time_text_orange">#E85815</color>
    <color name="explore_time_text_green">#2AB75E</color>
    <color name="explore_time_text_violet">#9A3195</color>
    <color name="deal_category_filter_pill_border">#D5D9DE</color>

    <color name="lmo_pill_outline">#D5D9DE</color>
    <color name="glazed_carrot">#EA682C</color>
    <color name="pale_blue">#D5D9DE</color>
    <color name="old_silver">#7C7C7C</color>

    <color name="family_account_user_name_black">#232329</color>
    <color name="family_account_view_gray_border_color">#D3D2D3</color>
    <color name="survey_options_divider">#D5D9DE</color>

    <color name="invite_a_friend_divider">#E0E0E0</color>
    <color name="upcoming_day_color_old">#C52F11</color>
    <color name="upcoming_waitlist_bg_color_old">#FFF3CC</color>
    <color name="upcoming_waitlist_text_color_old">#E85815</color>
    <color name="upcoming_day_bg_color">#E4F3E4</color>
    <color name="upcoming_day_text_color">#218323</color>

    <color name="popular_keyword_base_light">#FFF7F4</color>
    <color name="business_highlight_grey">#696C73</color>
    <color name="business_highlight_light_orange">#33D6612E</color>
    <color name="business_highlight_light_blue">#333430BB</color>

    <!-- ****** New Colors ****** -->
    <color name="aloe">#88EBBE</color>
    <color name="charcoal">#313131</color>

    <color name="brand_text_color">@color/basil</color>
    <color name="primary_text_color">@color/light_black1</color>
    <color name="secondary_text_color">@color/neutral_dark_grey</color>
    <color name="progress_bar_color">@color/charcoal</color>
    <color name="divider_color">@color/charcoal</color>
    <color name="divider_color_light">@color/light_gray_whitish</color>

    <color name="upcoming_day_color">@color/basil</color>
    <color name="upcoming_waitlist_bg_color">@color/aloe</color>
    <color name="upcoming_waitlist_text_color">@color/light_black1</color>

    <color name="discovery_item_text_color">@color/light_black1</color>

    <color name="primary_gradient_start">@color/basil</color>
    <color name="primary_gradient_end">@color/aloe</color>
</resources>
