<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="16dp"
    android:background="@color/white"
    tools:ignore="RtlSymmetry, HardcodedText">

    <ProgressBar
        android:id="@+id/contract_details_progress"
        style="@style/MbConnectLargeLoadingBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone" />
    <!--Added this layout for FTC screenshot purpose. without this layout I will not get a
    proper scrollable content screenshot and this will not change anything on UI side -->
    <FrameLayout
        android:id="@+id/contract_details_next_container"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true">
        <TextView
            android:id="@+id/contract_details_next"
            style="@style/BlackActionButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:text="@string/next" />
    </FrameLayout>
    <androidx.core.widget.NestedScrollView
        android:id="@+id/contract_details_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/contract_details_next_container">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/scroll_view_layout"
            android:layout_width="match_parent"
            android:background="@color/white"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/contract_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:paddingTop="16dp"
                android:text="8 Classes/Month Membership"
                android:textColor="@color/contracts_black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/site_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:text="MINDBODY Health Club Demo"
                android:textColor="@color/contracts_grey"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/contract_title"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/payment_details_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/grey_rect_hairlines"
                android:paddingStart="24dp"
                android:paddingTop="24dp"
                android:paddingBottom="12dp"
                android:layout_marginTop="16dp"
                android:text="@string/contracts_details_payments_banner_label"
                android:textAllCaps="true"
                android:textColor="@color/neutral_black"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/site_name"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/contract_details_change"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foreground="?selectableItemBackgroundBorderless"
                android:paddingStart="24dp"
                android:paddingTop="24dp"
                android:paddingEnd="24dp"
                android:paddingBottom="20dp"
                android:text="@string/change"
                android:textColor="@color/brand_text_color"
                android:textSize="14sp"
                android:textStyle="bold"
                android:elevation="1dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/payment_details_label"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/contract_details_start_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:paddingTop="24dp"
                android:paddingBottom="16dp"
                android:text="Starts 12/9/22"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="@id/contract_details_change"
                app:layout_constraintEnd_toStartOf="@id/contract_details_change"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/contract_details_change"
                app:layout_constraintVertical_bias="0.0" />

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/contracts_divider"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/contract_details_change"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/contract_details_payment_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="16dp"
                android:paddingEnd="24dp"
                android:text="$99 (not including tax)"
                android:textColor="@color/business_highlight_grey"
                android:textSize="13sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/contract_details_payment_amount_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:paddingTop="16dp"
                android:paddingEnd="24dp"
                android:text="@string/contracts_details_payments_label"
                android:textAllCaps="true"
                android:textColor="@color/business_highlight_grey"
                android:textSize="11sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/contract_details_payment_amount"
                app:layout_constraintEnd_toStartOf="@id/contract_details_payment_amount"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/contract_details_payment_amount"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/contract_details_due_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="16dp"
                android:paddingEnd="24dp"
                android:text=" 9th of every month"
                android:textColor="@color/business_highlight_grey"
                android:textSize="13sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/contract_details_payment_amount"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/contract_details_due_date_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:paddingTop="16dp"
                android:paddingEnd="24dp"
                android:text="@string/contracts_details_due_date_label"
                android:textAllCaps="true"
                android:textColor="@color/business_highlight_grey"
                android:textSize="11sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/contract_details_due_date"
                app:layout_constraintEnd_toStartOf="@id/contract_details_due_date"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/contract_details_due_date"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/contract_details_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="16dp"
                android:paddingEnd="24dp"
                android:text=" 8 months"
                android:textColor="@color/business_highlight_grey"
                android:textSize="13sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/contract_details_due_date"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/contract_details_duration_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:paddingTop="16dp"
                android:paddingEnd="24dp"
                android:text="@string/contracts_details_duration_label"
                android:textAllCaps="true"
                android:textColor="@color/business_highlight_grey"
                android:textSize="11sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/contract_details_duration"
                app:layout_constraintEnd_toStartOf="@id/contract_details_duration"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/contract_details_duration"
                app:layout_constraintVertical_bias="0.0" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/group_contract_duration"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="visible"
                app:constraint_referenced_ids="contract_details_duration,contract_details_duration_label"/>

            <TextView
                android:id="@+id/contract_details_renews_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="16dp"
                android:paddingEnd="24dp"
                android:text="2/8/23"
                android:textColor="@color/business_highlight_grey"
                android:textSize="13sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/contract_details_duration"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/contract_details_renews_date_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:paddingTop="16dp"
                android:paddingEnd="24dp"
                android:text="@string/contracts_details_renews_label"
                android:textAllCaps="true"
                android:textColor="@color/business_highlight_grey"
                android:textSize="11sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/contract_details_renews_date"
                app:layout_constraintEnd_toStartOf="@id/contract_details_renews_date"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/contract_details_renews_date"
                app:layout_constraintVertical_bias="0.0" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/contracts_renews_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                app:constraint_referenced_ids="contract_details_renews_date_label,contract_details_renews_date" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/contracts_recurring_payment_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                app:constraint_referenced_ids="contract_details_payment_amount_label,contract_details_payment_amount,
                    contract_details_due_date,contract_details_due_date_label" />

            <TextView
                android:id="@+id/contract_details_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="24dp"
                android:paddingEnd="24dp"
                android:text="$99"
                android:textColor="@color/business_highlight_grey"
                android:textSize="13sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/contract_details_renews_date"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/contract_details_total_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:paddingTop="24dp"
                android:paddingEnd="24dp"
                android:text="@string/contracts_details_total_label"
                android:textAllCaps="true"
                android:textColor="@color/business_highlight_grey"
                android:textSize="13sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/contract_details_total"
                app:layout_constraintEnd_toStartOf="@id/contract_details_total"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/contract_details_total"
                app:layout_constraintVertical_bias="0.0" />
            <TextView
                android:id="@+id/description_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="24dp"
                android:background="@drawable/grey_rect_hairlines"
                android:paddingStart="24dp"
                android:paddingTop="24dp"
                android:paddingBottom="16dp"
                android:text="@string/description_header"
                android:textAllCaps="true"
                android:textColor="@color/neutral_black"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/contract_details_total"
                app:layout_constraintVertical_bias="0.0" />

            <com.mindbodyonline.android.views.SimpleExpandableTextView
                android:id="@+id/description_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:paddingTop="20dp"
                android:paddingEnd="24dp"
                android:paddingBottom="16dp"
                android:text="We have developed this auto-pay membership where you get 8 classes per month for on $12.38 each! This low rate is guaranteed to keep you driven. This descripion is really long to demonstrate the expandable nature"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/description_label"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/items_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/grey_rect_hairlines"
                android:paddingStart="24dp"
                android:paddingTop="24dp"
                android:paddingBottom="12dp"
                android:text="@string/contracts_details_items_label"
                android:textAllCaps="true"
                android:textColor="@color/neutral_black"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/description_text"
                app:layout_constraintVertical_bias="0.0" />
            <TextView
                android:id="@+id/items_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:paddingTop="24dp"
                android:paddingBottom="20dp"
                android:text="8 Classes/Month Membership"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/items_label"
                app:layout_constraintVertical_bias="0.0" />

            <View
                android:id="@+id/divider1"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="24dp"
                android:background="@color/contracts_divider"
                android:paddingStart="24dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintVertical_bias="0.0" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</RelativeLayout>
