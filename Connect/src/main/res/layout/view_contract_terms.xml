<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/toolbar_elevated"
        android:id = "@+id/toolbar"/>

    <TextView
        android:id="@+id/terms_banner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/basil"
        android:gravity="center"
        android:padding="24dp"
        android:text="@string/contracts_signature_banner_text"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"/>

    <ScrollView
        android:id="@+id/contract_terms_scroll_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@+id/terms_accept"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/terms_banner">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical">
            <TextView
                android:id="@+id/terms_text"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="top"
                android:lineSpacingMultiplier="1.5"
                android:padding="24dp"
                android:text="@string/lorem_upsum"/>
        </LinearLayout>
    </ScrollView>
    <ScrollView
        android:id="@+id/contract_terms_scroll_view_ftc"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@+id/divider_terms"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/terms_banner">

        <TextView
            android:id="@+id/terms_text_ftc"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top"
            android:lineSpacingMultiplier="1.5"
            android:padding="24dp"
            android:text="@string/lorem_upsum" />
    </ScrollView>
    <View
        android:id="@+id/divider_terms"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="24dp"
        android:background="@color/divider_color_light"
        app:layout_constraintBottom_toTopOf="@+id/agree_to_terms_and_conditions"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
    <TextView
        android:id="@+id/agree_to_terms_and_conditions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/by_clicking_agree_to_all_terms"
        android:gravity="center_horizontal"
        android:padding="12dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/terms_accept" />
    <TextView
        android:id="@+id/terms_accept"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/agree"
        app:layout_constraintBottom_toBottomOf="parent"
        style="@style/BlackActionButton"/>

    <com.mindbodyonline.views.LoadingOverlay
        android:id="@+id/terms_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
