<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="HardcodedText">

    <TextView
        android:id="@+id/contract_details_start_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:textColor="@color/business_highlight_grey"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        tools:text="2/22/23" />

    <TextView
        android:id="@+id/contract_details_start_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="@string/contracts_qb_details_start_label"
        android:textAllCaps="true"
        android:textColor="@color/business_highlight_grey"
        android:textSize="11sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/contract_details_start_date"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/contract_details_start_date"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/contract_details_payment_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:paddingStart="16dp"
        android:text="$99 (not including tax)"
        android:textColor="@color/business_highlight_grey"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/contract_details_start_label"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/contract_details_payment_amount_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:text="@string/contracts_details_payments_label"
        android:textAllCaps="true"
        android:textColor="@color/business_highlight_grey"
        android:textSize="11sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/contract_details_payment_amount"
        app:layout_constraintEnd_toStartOf="@id/contract_details_payment_amount"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/contract_details_payment_amount"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/contract_details_due_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:paddingStart="16dp"
        android:text=" 9th of every month"
        android:textColor="@color/business_highlight_grey"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/contract_details_payment_amount"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/contract_details_due_date_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:text="@string/contracts_details_due_date_label"
        android:textAllCaps="true"
        android:textColor="@color/business_highlight_grey"
        android:textSize="11sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/contract_details_due_date"
        app:layout_constraintEnd_toStartOf="@id/contract_details_due_date"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/contract_details_due_date"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/contract_details_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:paddingStart="16dp"
        android:text=" 8 months"
        android:textColor="@color/business_highlight_grey"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/contract_details_due_date"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/contract_details_duration_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:text="@string/contracts_details_duration_label"
        android:textAllCaps="true"
        android:textColor="@color/business_highlight_grey"
        android:textSize="11sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/contract_details_duration"
        app:layout_constraintEnd_toStartOf="@id/contract_details_duration"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/contract_details_duration"
        app:layout_constraintVertical_bias="0.0" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/contracts_duration_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="contract_details_duration_label,contract_details_duration" />

    <TextView
        android:id="@+id/contract_details_renews_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:paddingStart="16dp"
        android:text="2/8/23"
        android:textColor="@color/business_highlight_grey"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/contract_details_duration"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/contract_details_renews_date_ftc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:paddingStart="16dp"
        android:text="2/8/23"
        android:textColor="@color/business_highlight_grey"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/contract_details_duration"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/contract_details_renews_date_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:text="@string/contracts_details_renews_label"
        android:textAllCaps="true"
        android:textColor="@color/business_highlight_grey"
        android:textSize="11sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/contract_details_renews_date"
        app:layout_constraintEnd_toStartOf="@id/contract_details_renews_date"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/contract_details_renews_date"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/contract_details_renews_date_label_ftc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingBottom="16dp"
        android:paddingEnd="16dp"
        android:text="@string/contracts_details_renews_label_ftc"
        android:textAllCaps="true"
        android:textColor="@color/business_highlight_grey"
        android:textSize="11sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/contract_details_renews_date"
        app:layout_constraintEnd_toStartOf="@id/contract_details_renews_date"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/contract_details_renews_date"
        app:layout_constraintVertical_bias="0.0" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/contracts_renews_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="contract_details_renews_date_label,contract_details_renews_date" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/contracts_renews_group_ftc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="contract_details_renews_date_label_ftc,contract_details_renews_date_ftc" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/contracts_recurring_payment_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="contract_details_payment_amount_label,contract_details_payment_amount,
                    contract_details_due_date,contract_details_due_date_label" />
    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/list_divider_color"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintVertical_bias="1.0"/>
</androidx.constraintlayout.widget.ConstraintLayout>