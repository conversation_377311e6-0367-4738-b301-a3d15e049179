<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/quick_book_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/start_guide"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_begin="20dp" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/top_guide"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_begin="20dp" />

        <ImageView
            android:id="@+id/close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?selectableItemBackground"
            android:contentDescription="@string/close"
            android:padding="20dp"
            android:src="@drawable/ic_close_icon_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/class_row_type"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/semibold"
            android:maxLines="1"
            android:paddingBottom="4dp"
            android:textAllCaps="true"
            android:textColor="@color/text_black"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/close"
            app:layout_constraintStart_toEndOf="@id/start_guide"
            app:layout_constraintTop_toTopOf="@id/top_guide"
            tools:text="Boxing/Kickboxing"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/class_name"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:ellipsize="end"
            android:fontFamily="@font/semibold"
            android:maxLines="1"
            android:textColor="@color/text_black"
            android:textSize="16sp"
            app:layout_constraintEnd_toStartOf="@id/close"
            app:layout_constraintStart_toEndOf="@+id/start_guide"
            app:layout_constraintTop_toBottomOf="@id/class_row_type"
            tools:text="Kickboxing Workshop" />

        <TextView
            android:id="@+id/class_location"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintEnd_toStartOf="@id/close"
            app:layout_constraintStart_toEndOf="@+id/start_guide"
            app:layout_constraintTop_toBottomOf="@id/class_name"
            tools:text="The Boxing Club - 0.3mi" />

        <TextView
            android:id="@+id/alacarte_item_expiration"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/close"
            app:layout_constraintStart_toEndOf="@+id/start_guide"
            app:layout_constraintTop_toBottomOf="@id/class_location"
            tools:text="Expires Feb 12, 2022"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/class_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="16dp"
            android:ellipsize="end"
            android:fontFamily="@font/semibold"
            android:maxLines="1"
            android:paddingBottom="4dp"
            android:textColor="@color/text_black"
            app:layout_constraintEnd_toStartOf="@id/close"
            app:layout_constraintStart_toEndOf="@+id/start_guide"
            app:layout_constraintTop_toBottomOf="@+id/alacarte_item_expiration"
            app:layout_goneMarginTop="8dp"
            tools:text="Wednesday, Feb 13" />

        <TextView
            android:id="@+id/class_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:ellipsize="end"
            android:fontFamily="@font/semibold"
            android:maxLines="1"
            android:textColor="@color/text_black"
            app:layout_constraintEnd_toStartOf="@id/close"
            app:layout_constraintStart_toEndOf="@+id/start_guide"
            app:layout_constraintTop_toBottomOf="@+id/class_date"
            tools:text="9:30 - 10:30am w/ Raul" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/non_contract_header_items_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="class_time,class_date, alacarte_item_expiration,class_row_type" />

        <View
            android:id="@+id/divider_1"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginTop="12dp"
            android:background="@color/list_divider_color"
            app:layout_constraintTop_toBottomOf="@id/class_time" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/contract_details_layout"
        layout="@layout/view_contract_qb_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_book_for_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <include
            android:id="@+id/include_book_for_layout"
            layout="@layout/view_book_for_family_member_layout"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/divider_book_for"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/list_divider_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include_book_for_layout" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/quickbook_loading"
        style="@style/MbConnectLargeLoadingBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="24dp"
        tools:visibility="gone" />

    <LinearLayout
        android:id="@+id/quickbook_main_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/quickbook_payment_selector"
            android:layout_width="match_parent"
            android:layout_height="48sp"
            android:background="?selectableItemBackground"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/semibold"
                android:letterSpacing="0.1"
                android:text="@string/payment_section"
                android:textAllCaps="true"
                android:textSize="12sp" />

            <androidx.legacy.widget.Space
                android:layout_width="8dp"
                android:layout_height="0dp" />

            <TextView
                android:id="@+id/card_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/semibold"
                android:textColor="@color/text_header"
                tools:text="Visa" />

            <androidx.legacy.widget.Space
                android:layout_width="4dp"
                android:layout_height="0dp" />

            <TextView
                android:id="@+id/quickbook_payment_selector_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="*5555" />

            <androidx.legacy.widget.Space
                android:layout_width="8dp"
                android:layout_height="0dp" />

            <ImageView
                android:id="@+id/quickbook_payment_selector_image"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:src="@drawable/cc_icon" />

            <androidx.legacy.widget.Space
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_pencil_icon_grey"
                app:layout_constraintEnd_toEndOf="parent" />

        </LinearLayout>

        <View
            android:id="@+id/divider_2"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/list_divider_color" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/quickbook_popt_button"
            android:layout_width="match_parent"
            android:layout_height="48sp"
            android:background="?selectableItemBackground"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <!-- ensures that the price is displayed no matter the length of the pricing option name -->
            <TableLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:shrinkColumns="0"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/edit_pricing_option_icon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TableRow
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    tools:ignore="UselessParent">

                    <TextView
                        android:id="@+id/quickbook_popt_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:fontFamily="@font/semibold"
                        android:letterSpacing="0.1"
                        android:maxLines="1"
                        android:textAllCaps="true"
                        android:textSize="12sp"
                        tools:text="Single pricing - yes for a new client" />

                    <androidx.legacy.widget.Space android:layout_width="16dp" />

                    <TextView
                        android:id="@+id/quickbook_popt_slash_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="$30.00" />

                    <androidx.legacy.widget.Space android:layout_width="4dp" />

                    <TextView
                        android:id="@+id/quickbook_popt_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/semibold"
                        android:textColor="@color/text_header"
                        tools:text="$20.00" />

                    <androidx.legacy.widget.Space android:layout_width="8dp" />

                </TableRow>
            </TableLayout>

            <ImageView
                android:id="@+id/edit_pricing_option_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_chevron_right_grey"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <RelativeLayout
            android:id="@+id/quickbook_giftcard_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/transparent_light_gray_selector"
            android:padding="12dp"
            tools:visibility="visible">

            <TextView
                android:id="@+id/quickbook_use_gc_prompt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/use_gift_card_prompt"
                android:textColor="@color/light_black"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/quickbook_gc_balance_pretext"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/quickbook_use_gc_prompt"
                android:paddingTop="2dp"
                android:text="@string/gift_card_balance_withcolon"
                android:textColor="@color/dark_gray" />

            <TextView
                android:id="@+id/quickbook_gc_balance_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/quickbook_gc_balance_pretext"
                android:layout_toRightOf="@id/quickbook_gc_balance_pretext"
                android:paddingStart="5dp"
                android:paddingTop="5dp"
                android:textColor="@color/dark_gray"
                android:textStyle="bold"
                tools:text="$25.00" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:duplicateParentState="true"
                android:src="@drawable/checkbox_selector" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/list_divider_color" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/quickbook_order_total_details_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp">

            <TextView
                android:id="@+id/order_detail_subtotal_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:letterSpacing="0.1"
                android:paddingBottom="4dp"
                android:text="@string/order_summary_subtotal_label"
                android:textAllCaps="true"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/order_detail_subtotal_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/order_detail_subtotal_label"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/order_detail_subtotal_label"
                tools:ignore="HardcodedText"
                tools:text="$30.25" />

            <TextView
                android:id="@+id/order_detail_discount_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:letterSpacing="0.1"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:text="@string/order_summary_discount_label"
                android:textAllCaps="true"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/order_detail_subtotal_label" />

            <TextView
                android:id="@+id/order_detail_discount_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/order_detail_discount_label"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/order_detail_discount_label"
                tools:ignore="HardcodedText"
                tools:text="$0.00" />

            <!-- Discount line group used to toggle visibility -->
            <androidx.constraintlayout.widget.Group
                android:id="@+id/order_detail_discount_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                app:constraint_referenced_ids="order_detail_discount_amount,order_detail_discount_label" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/order_service_fee_row"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?selectableItemBackground"
                app:layout_constraintTop_toBottomOf="@id/order_detail_discount_label">

                <TextView
                    android:id="@+id/order_detail_fee_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0.1"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    android:text="@string/order_summery_service_fee_label"
                    android:textAllCaps="true"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/order_detail_fee_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:contentDescription="@string/service_fee_info"
                    android:src="@drawable/ic_info_icon_grey"
                    app:layout_constraintBottom_toBottomOf="@id/order_detail_fee_label"
                    app:layout_constraintStart_toEndOf="@id/order_detail_fee_label"
                    app:layout_constraintTop_toTopOf="@id/order_detail_fee_label" />

                <TextView
                    android:id="@+id/order_detail_fee_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="@id/order_detail_fee_label"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/order_detail_fee_label"
                    tools:ignore="HardcodedText"
                    tools:text="$0.25" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/order_detail_tax_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:letterSpacing="0.1"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:text="@string/order_summary_tax_label"
                android:textAllCaps="true"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/order_service_fee_row" />

            <TextView
                android:id="@+id/order_detail_tax_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="$0.00"
                app:layout_constraintBottom_toBottomOf="@id/order_detail_tax_label"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/order_detail_tax_label"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/order_detail_total_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/semibold"
                android:letterSpacing="0.1"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:text="@string/order_summary_total_label"
                android:textAllCaps="true"
                android:textColor="@color/black"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/order_detail_tax_label" />

            <TextView
                android:id="@+id/order_detail_total_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/semibold"
                android:text="$30.25"
                android:textColor="@color/black"
                app:layout_constraintBottom_toBottomOf="@id/order_detail_total_label"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/order_detail_total_label"
                tools:ignore="HardcodedText" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/quickbook_order_total_row"
            android:layout_width="match_parent"
            android:layout_height="48sp"
            android:background="?selectableItemBackground"
            android:gravity="center_vertical"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <TextView
                android:id="@+id/total_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:letterSpacing="0.1"
                android:text="@string/order_summary_total_label"
                android:textAllCaps="true"
                android:textSize="12sp" />

            <androidx.legacy.widget.Space
                android:layout_width="8dp"
                android:layout_height="0dp" />

            <TextView
                android:id="@+id/quickbook_order_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/semibold"
                android:textColor="@color/text_header"
                tools:text="$20.00" />

            <androidx.legacy.widget.Space
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/quickbook_order_summary_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_info_icon_grey" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/list_divider_color" />

        <TextView
            android:id="@+id/quickbook_tos"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:lineSpacingExtra="5dp"
            android:linksClickable="true"
            android:padding="16dp"
            android:textColorLink="@color/link_blue_color"
            tools:text="Your card will be charged when you tap Book and pay now" />

        <TextView
            android:id="@+id/quickbook_tos_sub_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/semibold"
            android:gravity="center"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:text="@string/pay_booking_cancelation_subtext"
            android:textColor="@color/text_header"
            android:textSize="12sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/text_apm_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:fontFamily="@font/semibold"
            android:visibility="gone"
            android:textSize="14sp"
            android:lineSpacingExtra="5dp"
            android:textColor="@color/red"
            android:padding="16dp"
            android:text="@string/apm_checkout_failed" />

        <TextView
            android:id="@+id/quickbook_book_button"
            style="@style/BlackActionButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:letterSpacing="0.1"
            android:text="@string/sign_up_buy"
            android:textAllCaps="true"
            tools:visibility="visible" />
    </LinearLayout>

    <!-- TODO this is very disjointed, pull components out into an <include> -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quickbook_main_nopay_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible">

        <View
            android:id="@+id/quickbook_current_pass_click_view_area"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/quickbook_current_pass_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/quickbook_current_pass_name" />

        <TextView
            android:id="@+id/quickbook_current_pass_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/semibold"
            android:paddingStart="20dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp"
            android:text="@string/payment_section"
            android:textAllCaps="true"
            android:textSize="12sp"
            app:layout_constraintBaseline_toBaselineOf="@id/quickbook_current_pass_name" />

        <TextView
            android:id="@+id/quickbook_current_pass_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:fontFamily="@font/semibold"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp"
            android:textColor="@color/text_header"
            app:drawableEndCompat="@drawable/ic_chevron_right_grey"
            app:drawableRightCompat="@drawable/ic_chevron_right_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/quickbook_current_pass_label"
            tools:ignore="RtlSymmetry"
            tools:text="Pass Name" />

        <View
            android:id="@+id/quickbook_current_pass_divider"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/list_divider_color"
            app:layout_constraintTop_toBottomOf="@id/quickbook_current_pass_name" />

        <TextView
            android:id="@+id/quickbook_nopay_bookability_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="16dp"
            android:text="@string/message_unpaid"
            app:layout_constraintTop_toBottomOf="@id/quickbook_current_pass_divider" />

        <View
            android:id="@+id/quickbook_nopay_bookability_divider"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/list_divider_color"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/quickbook_nopay_bookability_status" />

        <TextView
            android:id="@+id/quickbook_nopay_bookability_sub_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="16dp"
            android:text="@string/message_unpaid"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/quickbook_nopay_bookability_divider" />

        <TextView
            android:id="@+id/quickbook_book_nopay_button"
            style="@style/BlackActionButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:letterSpacing="0.1"
            android:text="@string/action_book_now"
            app:layout_constraintTop_toBottomOf="@id/quickbook_nopay_bookability_sub_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>