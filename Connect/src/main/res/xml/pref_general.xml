<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">
    <Preference
        android:key="@string/log_in_or_create_an_account"
        android:title="@string/log_in_or_create_an_account"
        android:layout="@layout/view_padded_orange_cta_preference"/>
    <Preference
        android:key="@string/pref_key_account"
        android:title="@string/account_button_text">
        <intent android:action="com.mindbodyonline.connect.activities.details.EditMyProfileActivity"/>
    </Preference>
    <Preference
        android:key="@string/pref_key_manage_cancel_contracts"
        android:title="@string/manage_or_cancel_contracts">
    </Preference>
    <Preference
        android:key="@string/pref_key_wallet"
        android:title="@string/payment_button_text">
        <intent android:action="com.mindbodyonline.connect.activities.workflow.PaymentMethodsActivity"/>
    </Preference>
    <Preference
        android:key="@string/pref_apps_devices_header"
        android:title="@string/pref_apps_devices_header">
        <intent android:action="pref_key_integrations"/>
    </Preference>
    <Preference
        android:key="@string/pref_calendar_setting_header"
        android:title="@string/pref_calendar_setting_header">
        <intent android:action="pref_key_calendar"/>
    </Preference>
    <Preference
        android:key="@string/pref_key_manage_family_accounts"
        android:title="@string/manage_family_accounts_button_text" />
    <Preference
        android:key="@string/pref_key_manage_notifications"
        android:title="@string/pref_title_manage_notifications">
        <intent android:action="pref_key_notifications"/>
    </Preference>
    <Preference
        android:key="@string/action_live_chat"
        android:title="@string/action_live_chat" />
    <Preference
        android:key="@string/pref_category_title_more"
        android:title="@string/pref_category_title_more">
        <intent android:action="pref_key_more"/>
    </Preference>
    <Preference
        android:key="@string/action_logout"
        android:title="@string/action_logout"
        android:layout="@layout/view_padded_preference"/>
</PreferenceScreen>
