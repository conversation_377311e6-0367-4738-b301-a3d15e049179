<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:windowSoftInputMode="stateHidden|adjustPan">
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission
        android:name="android.permission.VIBRATE"
        android:required="false" />
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" /> <!-- These are needed to allow users to upload screenshots to intercom -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" /> <!-- Google Fit physical activity (steps, calories, heart rate, etc) permission -->

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.location"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.location.gps"
        android:required="false" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <queries>
        <intent>
            <action android:name="android.intent.action.DIAL" />
        </intent>
        <intent>
            <action android:name="android.intent.action.EDIT" />
        </intent>
        <intent>
            <action android:name="android.intent.action.SEND" />
        </intent>
        <intent>
            <action android:name="android.intent.action.SENDTO" />
        </intent>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
        </intent>
    </queries>

    <application
        android:name="com.mindbodyonline.ConnectApp"
        android:allowBackup="false"
        android:fullBackupContent="@xml/backup_rules"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:logo="@drawable/mindbody_logo"
        android:resizeableActivity="false"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:screenOrientation="portrait"
        android:theme="@style/Theme.Mbconnect"
        tools:ignore="LockedOrientationActivity"
        tools:replace="android:allowBackup">
        <activity
            android:name=".stripe.StripePaymentActivity"
            android:theme="@style/TransparentActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name=".stripe.StripeWebViewActivity"
            android:theme="@style/TransparentActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
        <!--
         Part of the google play services require the legacy apache http library for API 28.
             More information:
             https://developers.google.com/maps/documentation/android-sdk/config#specify_requirement_for_apache_http_legacy_library
        -->
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" /> <!-- TODO: Remove max_aspect once target 24+ w/ resizeable activities -->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.1" /> <!-- For sharing images and screenshots -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.mindbodyonline.connect.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="com.facebook.app.FacebookContentProvider1485657611676811"
            android:exported="true" /> <!-- Our referrer receiver forwards events to other analytics tools -->
        <receiver
            android:name=".services.InstallReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".services.ConnectEventAlarmReceiver"
            android:exported="false" /> <!-- region Push notification requirements -->
        <!-- This following class is just for push analytics -->
        <receiver
            android:name=".services.PushNotificationReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.braze.push.intent.NOTIFICATION_RECEIVED" />
                <action android:name="com.braze.push.intent.NOTIFICATION_OPENED" />
                <action android:name="com.braze.push.intent.NOTIFICATION_DELETED" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.braze.push.BrazeFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service> <!-- endregion -->
        <receiver
            android:name=".widgets.v3.ConnectAppWidget"
            android:exported="true"
            android:icon="@drawable/mindbody_logo"
            android:label="@string/app_name">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.mindbodyonline.connect.widget_update" />
                <action android:name="com.mindbodyonline.connect.widget.business_clicked" />
                <action android:name="com.mindbodyonline.connect.widget.visit_clicked" />
                <action android:name="com.mindbodyonline.connect.widget.receive_visits" />
                <action android:name="com.mindbodyonline.connect.widget.receive_locations" />
                <action android:name="android.appwidget.action.APPWIDGET_ENABLED" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/my_widget_provider" />
        </receiver>

        <meta-data
            android:name="com.google.android.maps.v2.API_KEY"
            android:value="AIzaSyChbJnwUPgZ2gS2BNKP5CyQNJ_p2HwIRYw" />
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" /> <!-- User Action Links (Branch.io params) -->
        <meta-data
            android:name="io.branch.sdk.BranchKey.test"
            android:value="key_test_nnbJgIIllSSUmqNqtvm0DhbptqjupVm2" />
        <meta-data
            android:name="io.branch.sdk.BranchKey"
            android:value="key_live_afkKjTVeeJSKbzNtDBh4EjlhztivgHcU" />
        <meta-data
            android:name="com.google.android.gms.wallet.api.enabled"
            android:value="true" />

        <receiver
            android:name="com.mindbodyonline.connect.services.AlarmReceiver"
            android:exported="false" />
        <receiver
            android:name="com.mindbodyonline.connect.profile.dashboard.ActivityNotificationAlarmReceiver"
            android:exported="false" />
        <receiver
            android:name="com.mindbodyonline.connect.services.StravaSyncReceiver"
            android:exported="false" /> <!-- Google Analytics Version v4 needs this value for easy tracking -->
        <!-- <meta-data -->
        <!-- android:name="com.google.android.gms.analytics.globalConfigResource" -->
        <!-- android:resource="@xml/app_tracker" /> -->
        <activity
            android:name=".activities.TourActivity"
            android:configChanges="keyboard"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.Tour"
            android:windowSoftInputMode="stateHidden|adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <data
                    android:host="get.mndbdy.ly"
                    android:scheme="https" />
                <data
                    android:host="mindbody.app.link"
                    android:scheme="https" />
                <data
                    android:host="mindbody-alternate.app.link"
                    android:scheme="https" />
                <data
                    android:host="mindbody.test-app.link"
                    android:scheme="https" />
                <data
                    android:host="mindbody-alternate.test-app.link"
                    android:scheme="https" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <data android:scheme="mindbodyconnect" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <data
                    android:host="authcode"
                    android:scheme="x-mindbodyconnect-oauth-mindbody" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.MainActivity"
            android:label="@string/title_activity_main"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.details.BusinessDetailsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar" />
        <activity
            android:name=".activities.list.services.classes.QualifiedClassesListActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar" />
        <activity
            android:name=".activities.workflow.PaymentMethodsActivity"
            android:exported="true"
            android:label="@string/title_activity_payment_methods"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar">
            <intent-filter>
                <action android:name="com.mindbodyonline.connect.activities.workflow.PaymentMethodsActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.details.AppointmentDetailsActivity"
            android:label="@string/title_activity_appointment_details"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.details.AppointmentStaffDetailsActivity"
            android:label="Staff Details"
            android:noHistory="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.list.search.SearchDealsListActivity"
            android:label="@string/deals_near_me"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.Search"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activities.list.ReviewListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.list.services.BusinessDealsListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.details.ReviewDetailsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".appointments.AppointmentServiceListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar" />
        <activity
            android:name=".appointments.AppointmentCategoryListActivity"
            android:label="@string/banner_appointments"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar" />
        <activity
            android:name=".activities.list.services.classes.RebookClassListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar" />
        <activity
            android:name=".activities.list.services.classes.ClassListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.workflow.BookAppointmentActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.workflow.AppointmentTimeSlotActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.workflow.ConfirmAppointmentActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activities.workflow.booking.ComposableBookingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activities.workflow.booking.FullScreenConfirmationScreenActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activities.list.services.SessionListActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.details.EditMyProfileActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar">
            <intent-filter>
                <action android:name="com.mindbodyonline.connect.activities.details.EditMyProfileActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.workflow.ReviewDialogActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/MbTransparent"
            android:windowSoftInputMode="stateHidden|adjustUnspecified">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.workflow.SignInDialogActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/MbTransparent"
            android:windowSoftInputMode="stateHidden|adjustUnspecified">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.SettingsActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.Settings">
            <intent-filter>
                <action android:name="pref_key_notifications" />
                <action android:name="pref_key_integrations" />
                <action android:name="pref_key_calendar" />
                <action android:name="pref_key_more" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.workflow.SearchLocationMapActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.Search"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activities.details.AppointmentTypeDetailsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.list.search.BusinessListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar" />
        <activity
            android:name=".activities.workflow.LiabilityReleaseActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.workflow.RequiredFieldsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar"
            android:windowSoftInputMode="stateVisible" />

        <service
            android:name=".services.GeofenceReceiver"
            android:exported="false" />

        <activity
            android:name=".activities.info.MapActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.details.ClassTypeDetailsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.list.services.classes.ClassStaffDetailsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar" />
        <activity
            android:name=".activities.list.services.EventListActivity"
            android:label="Upcoming Events"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".giftcards.GiftCardPickerActivity"
            android:label="@string/gift_cards"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.workflow.AddPaymentCardActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activities.list.services.PricingListActivity"
            android:label="@string/services_pricing_title"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar" />
        <activity
            android:name=".activities.list.services.classes.ClassPricingListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar" />
        <activity
            android:name=".activities.list.services.RoutineServicesActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar" />
        <activity
            android:name=".activities.schedule.BusinessScheduleActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar"
            android:windowSoftInputMode="adjustPan"/>
        <activity
            android:name=".activities.workflow.FitbitAccessTokenActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="authcode"
                    android:scheme="x-mindbodyconnect-oauth-fitbit" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.details.ViewCreditCardDetailsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect" />
        <activity
            android:name=".activities.details.DealDetailsActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.list.SupportActivity"
            android:exported="true"
            android:label="@string/pref_support_header"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar">
            <intent-filter>
                <action android:name="com.mindbodyonline.connect.activities.list.SupportActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.info.ProxySnackbarActivity"
            android:excludeFromRecents="true"
            android:noHistory="true"
            android:theme="@style/MbTransparent" />
        <!--
<meta-data android:name="android.app.searchable"
                  android:resource="@xml/searchable" />
        -->
        <activity
            android:name=".activities.workflow.SearchQueryEntryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".explore.search.SearchQueryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.theartofdev.edmodo.cropper.CropImageActivity"
            android:theme="@style/Base.Theme.AppCompat" />
        <activity
            android:name=".activities.workflow.StravaAccessTokenActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="authcode"
                    android:scheme="x-mindbodyconnect-oauth-strava" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.workflow.GoogleFitAccessTokenActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".activities.pas.PickASpotActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
        <activity
            android:name=".settings.account.ChangePasswordActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="com.mindbodyonline.connect.settings.account.ChangePasswordActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".settings.account.ChangeEmailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="com.mindbodyonline.connect.settings.account.ChangeEmailActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".sca.SCAAuthorizationActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="payment"
                    android:scheme="x-mindbodyconnect-sca" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.flex.CreditFlowActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.WhiteBackground"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".explore.result.ExploreResultActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar.ClearStatusBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".quickbook.requiredfields.RequiredFieldsActivityV2"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Mbconnect.NoActionBar" />
    </application>

</manifest>