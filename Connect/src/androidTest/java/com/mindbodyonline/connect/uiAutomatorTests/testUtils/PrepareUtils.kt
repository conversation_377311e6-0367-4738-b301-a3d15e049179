package com.mindbodyonline.connect.uiAutomatorTests.testUtils

import androidx.test.uiautomator.UiDevice
import com.mindbodyonline.connect.uiAutomatorTests.testData.mob_test_password
import com.mindbodyonline.connect.uiAutomatorTests.testData.mob_test_username
import com.mindbodyonline.connect.uiAutomatorTests.testModels.login
import com.mindbodyonline.connect.uiAutomatorTests.testModels.permissionEnableNotifications

fun UiDevice.launchHomeScreenWithTestLogin() {
    launchApp()
    login(mob_test_username, mob_test_password)
    permissionShareLocation()
    permissionWhileUsingAppPopUp()
    permissionEnableNotifications()
    permissionPromptAllowNotifications()
}
