package com.mindbodyonline.connect.uiAutomatorTests.regressionTests.loginToAccount

import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.runner.AndroidJUnit4
import androidx.test.uiautomator.UiDevice
import com.mindbodyonline.connect.uiAutomatorTests.testData.mob_test_password
import com.mindbodyonline.connect.uiAutomatorTests.testData.mob_test_username
import com.mindbodyonline.connect.uiAutomatorTests.testModels.cancelWebviewLogin
import com.mindbodyonline.connect.uiAutomatorTests.testModels.handleWebviewLoginError
import com.mindbodyonline.connect.uiAutomatorTests.testModels.permissionEnableNotifications
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyBackInApp
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyHomeScreenIcon
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyWebviewLoginInitiated
import com.mindbodyonline.connect.uiAutomatorTests.testModels.webviewLogin
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.launchApp
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.permissionPromptAllowNotifications
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.permissionShareLocation
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.permissionWhileUsingAppPopUp
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Simple webview login tests for SHARED_LOGIN_UI_FLOW feature flag.
 * Assumes Chrome Custom Tabs with standard form fields.
 */
@RunWith(AndroidJUnit4::class)
class WebviewLoginTest {
    private var device: UiDevice =
        UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())

    @Before
    fun setup() = with(device) {
        launchApp()
    }

    /**
     * Test successful webview login - similar to existing loginWithExistingEmail_1298263
     */
    @Test
    fun webviewLoginWithExistingEmail_1298263() = with(device) {
        // App should launch Chrome Custom Tabs for webview login
        verifyWebviewLoginInitiated()

        // Login in webview
        webviewLogin(mob_test_username, mob_test_password)

        // Handle permissions like existing test
        permissionShareLocation()
        permissionWhileUsingAppPopUp()
        permissionEnableNotifications()
        permissionPromptAllowNotifications()

        // Verify home screen
        verifyHomeScreenIcon()
    }

    /**
     * Test webview login cancellation
     */
    @Test
    fun webviewLoginCancellation() = with(device) {
        // Verify webview opens
        verifyWebviewLoginInitiated()

        // Cancel by pressing back
        cancelWebviewLogin()

        // Should be back in app
        verifyBackInApp()

        // Handle guest permissions
        permissionShareLocation()
        permissionWhileUsingAppPopUp()
        permissionEnableNotifications()
        permissionPromptAllowNotifications()
    }
}
