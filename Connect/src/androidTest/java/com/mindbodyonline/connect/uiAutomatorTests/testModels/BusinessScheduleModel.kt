package com.mindbodyonline.connect.uiAutomatorTests.testModels

import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import com.mindbodyonline.connect.uiAutomatorTests.testData.businesses_tab
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.findByUiSelector

// UI Selectors for navigation
private val businessesTabSelector = UiSelector().text(businesses_tab)
private val scheduleButtonSelector = UiSelector().resourceId("com.mindbodyonline.connect:id/schedule_button")

// UI Selectors for Business Schedule screen
private val classesTabSelector = UiSelector().text("Classes")
private val appointmentsTabSelector = UiSelector().text("Appointments")
private val bookButtonSelector = UiSelector().text("BOOK")
private val quickBookDialogSelector = UiSelector().resourceId("com.mindbodyonline.connect:id/quick_book_layout")
private val classDetailsRootSelector = UiSelector().resourceId("com.mindbodyonline.connect:id/class_type_details_root")
private val classRowSelector = UiSelector().descriptionContains("ClassListRow")
private val appointmentCategoryRowSelector = UiSelector().descriptionContains("AppointmentCategoryRow")
private val appointmentServiceHeaderSelector = UiSelector().resourceId("com.mindbodyonline.connect:id/activity_service_category_list_header")

internal fun UiDevice.navigateToFavoritesBusinessTab() {
    // Navigate to Favorites tab
    clickFavoriteIcon()

    // Navigate to Businesses tab within Favorites
    val businessesTab = findByUiSelector(businessesTabSelector, ScreenLoadTimeout)
    businessesTab.click()
}

internal fun UiDevice.clickOnScheduleButton() {
    val scheduleButton = findByUiSelector(scheduleButtonSelector, ScreenLoadTimeout)
    scheduleButton.click()

    // Verify the Business Schedule screen is displayed
    verifySchedulePageHeader()
}

internal fun UiDevice.switchToAppointmentsTab() {
    val appointmentsTab = findByUiSelector(appointmentsTabSelector, ScreenLoadTimeout)
    appointmentsTab.click()
}

internal fun UiDevice.switchToClassesTab() {
    val classesTab = findByUiSelector(classesTabSelector, ScreenLoadTimeout)
    classesTab.click()
}

internal fun UiDevice.clickBookButtonAndVerifyQuickBookDialog() {
    // Find and click the Book button on a class
    val bookButton = findByUiSelector(bookButtonSelector, ScreenLoadTimeout)
    bookButton.click()

    // Verify that the QuickBook dialog is displayed
    val quickBookDialog = findByUiSelector(quickBookDialogSelector, ScreenLoadTimeout)
    assert(quickBookDialog.exists()) { "QuickBook dialog not found" }
}

internal fun UiDevice.clickClassItemAndVerifyClassDetails() {
    // Find a class row by its content description
    val classRow = findByUiSelector(classRowSelector, ScreenLoadTimeout)

    // Click on the class row
    classRow.click()

    // Verify that the Class Details screen is displayed
    val classDetailsRoot = findByUiSelector(classDetailsRootSelector, ScreenLoadTimeout)
    assert(classDetailsRoot.exists()) { "Class Details screen not found" }
}

internal fun UiDevice.clickAppointmentCategoryAndVerifyDetails() {
    // Find an appointment category row by its content description
    val appointmentRow = findByUiSelector(appointmentCategoryRowSelector, ScreenLoadTimeout)

    // Click on the appointment category row
    appointmentRow.click()

    // Verify that the Appointment Service List screen is displayed
    val appointmentServiceHeader = findByUiSelector(appointmentServiceHeaderSelector, ScreenLoadTimeout)
    assert(appointmentServiceHeader.exists()) { "Appointment Service List screen not found" }
}
