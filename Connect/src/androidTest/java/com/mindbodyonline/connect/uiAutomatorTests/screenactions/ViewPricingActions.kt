package com.mindbodyonline.connect.uiAutomatorTests.screenactions

import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiObject
import androidx.test.uiautomator.UiSelector
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.findByUiSelector
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.findItemInScrollableList

private const val ACTIVITY_SERVICES_PRICING_TAB_LAYOUT_ID =
    "com.mindbodyonline.connect:id/activity_services_pricing_tab_layout"
private const val CONTRACTS_CATEGORY_LIST = "com.mindbodyonline.connect:id/category_list"

private const val CONTRACTS_TAB_TEXT = "Plans & More"

fun UiDevice.getContractsTab(): UiObject =
    findByUiSelector(
        UiSelector().descriptionContains(CONTRACTS_TAB_TEXT),
    )

fun UiDevice.getViewPricingTabBar() =
    findByUiSelector(
        UiSelector().resourceId(ACTIVITY_SERVICES_PRICING_TAB_LAYOUT_ID),
    )

fun UiDevice.getContractFromListing(contractName: String): UiObject =
    findItemInScrollableList(
        UiSelector().resourceId(CONTRACTS_CATEGORY_LIST),
        UiSelector().textContains(contractName),
    ) ?: throw IllegalStateException("Contract not found")
