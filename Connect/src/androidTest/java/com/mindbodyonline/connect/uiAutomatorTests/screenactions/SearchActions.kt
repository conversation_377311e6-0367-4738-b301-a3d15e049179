@file:JvmName("SearchActions")

package com.mindbodyonline.connect.uiAutomatorTests.screenactions

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiObject
import androidx.test.uiautomator.UiSelector
import com.mindbodyonline.connect.R

private const val SEARCH_TERM_EDIT_TEXT_ID = "com.mindbodyonline.connect:id/search_term_text"
private const val SEARCH_LOCATION_CLEAR_TEXT_ID = "com.mindbodyonline.connect:id/ivClearSearchLocation"
private const val WAIT_FOR_EXISTS_TIMEOUT = 10000L
private val searchTextView
    get() = onView(withId(R.id.explore_home_search_text))
private val searchEditText
    get() = onView(withId(R.id.search_term_text))
private val locationEditText
    get() = onView(withId(R.id.search_location_text))

fun UiDevice.enterSearchTextWithLocationAndWaitForResult(
    searchText: String,
    locationText: String,
    studioSelectionCriteriaText: String
): UiObject {
    searchTextView.perform(click())
    // Wait for the searchEditText displayed
    val searchEditTextObject = findObject(UiSelector().resourceId(SEARCH_TERM_EDIT_TEXT_ID))
    searchEditTextObject.waitForExists(WAIT_FOR_EXISTS_TIMEOUT)
    // clear location if it exists
    val clearLocation = findObject(UiSelector().resourceId(SEARCH_LOCATION_CLEAR_TEXT_ID))
    if (clearLocation.exists()) {
        clearLocation.click()
    }
    // enter location
    locationEditText.perform(click()).perform(ViewActions.typeText(locationText))
    // wait till locations are visible
    val locationList = findObject(UiSelector().resourceId("com.mindbodyonline.connect:id/auto_suggestion_list"))
    locationList.waitForExists(WAIT_FOR_EXISTS_TIMEOUT)
    locationList.getChild(UiSelector().index(0)).click()

    // enter text
    searchEditText.perform(click()).perform(ViewActions.typeText(searchText))
    val studiosList = findObject(UiSelector().resourceId("com.mindbodyonline.connect:id/rvAutoCompleteStudio"))
    studiosList.waitForExists(WAIT_FOR_EXISTS_TIMEOUT)
    return studiosList.getChild(UiSelector().textContains(studioSelectionCriteriaText))
}
