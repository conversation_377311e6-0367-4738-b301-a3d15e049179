package com.mindbodyonline.connect.uiAutomatorTests.testUtils

import android.os.ParcelFileDescriptor
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiObject
import androidx.test.uiautomator.UiObjectNotFoundException
import androidx.test.uiautomator.UiScrollable
import androidx.test.uiautomator.UiSelector
import com.mindbodyonline.connect.uiAutomatorTests.testModels.ScreenLoadTimeout
import java.io.BufferedReader
import java.io.ByteArrayOutputStream
import java.io.FileInputStream
import java.io.InputStreamReader

/*
 * Search the current screen, scrolling if necessary, until the [UiSelector] is found
 * and returns that component, or null if no matching component is found.
 */
internal fun UiDevice.findByUiSelector(
    selector: UiSelector,
    timeoutMs: Long = ScreenLoadTimeout,
): UiObject =
    when {
        findObject(selector).waitForExists(timeoutMs) -> findObject(selector)
        else -> {
            this.dumpTheViewHierarchy()
            throw UiObjectNotFoundException("Could not find object matching selector: $selector")
        }
    }

private fun UiDevice.dumpTheViewHierarchy() {
    val output = ByteArrayOutputStream()
    dumpWindowHierarchy(output)

    val os = InstrumentationRegistry.getInstrumentation().uiAutomation
    output.toString().chunked(4000).forEach {
        print(os.executeShellCommand("echo $it"))
    }
}

private fun print(fd: ParcelFileDescriptor) {
    val stdInput = BufferedReader(InputStreamReader(FileInputStream(fd.fileDescriptor)))
    // Read the output from the command
    println("Here is the standard output of the command:\n")
    var s: String?
    while ((stdInput.readLine().also { s = it }) != null) {
        println(s)
    }
}

@Throws(UiObjectNotFoundException::class)
fun scrollWithText(searchText: String?) {
    var textScroll: UiScrollable? = null
    if (searchText != null) {
        textScroll = UiScrollable(UiSelector().scrollable(true))
        textScroll.scrollIntoView(UiSelector().text(searchText))
    }
}

@Throws(UiObjectNotFoundException::class)
fun scrollTillDescriptionText(searchText: String?) {
    var textScroll: UiScrollable? = null
    if (searchText != null) {
        textScroll = UiScrollable(UiSelector().scrollable(true))
        textScroll.scrollIntoView(UiSelector().descriptionContains(searchText))
    }
}

internal fun UiDevice.selectText(
    matchingText: String,
    descendantMatchingText: String?,
    timeoutMs: Long = ScreenLoadTimeout,
): UiObject? {
    val selector =
        UiSelector()
            .text(matchingText)
            .run {
                if (descendantMatchingText.isNullOrBlank()) {
                    this
                } else {
                    childSelector(UiSelector().text(descendantMatchingText))
                }
            }
    return findByUiSelector(selector, timeoutMs)
}

/**
 * Searches for an item in a scrollable list, scrolling until found or reaching the end
 * @param listSelector UiSelector for the scrollable container/list
 * @param itemSelector UiSelector for the item to find
 * @param maxScrolls Maximum number of scroll attempts
 * @return The found UiObject or null if not found
 */
fun UiDevice.findItemInScrollableList(
    listSelector: UiSelector,
    itemSelector: UiSelector,
): UiObject? {
    val list = UiScrollable(listSelector)

    // First check if item is already visible
    val initialItem = findObject(itemSelector)
    if (initialItem.exists()) {
        return initialItem
    }

    // If not found, scroll and search
    if (list.scrollIntoView(itemSelector)) {
        return findObject(itemSelector)
    }
    return null
}
