package com.mindbodyonline.connect.uiAutomatorTests.regressionTests.schedule

import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.runner.AndroidJUnit4
import androidx.test.uiautomator.UiDevice
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickAppointmentCategoryAndVerifyDetails
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickBookButtonAndVerifyQuickBookDialog
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickClassItemAndVerifyClassDetails
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickOnScheduleButton
import com.mindbodyonline.connect.uiAutomatorTests.testModels.navigateToFavoritesBusinessTab
import com.mindbodyonline.connect.uiAutomatorTests.testModels.switchToAppointmentsTab
import com.mindbodyonline.connect.uiAutomatorTests.testModels.switchToClassesTab
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.launchHomeScreenWithTestLogin
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class BusinessScheduleComposeTest {
    private var device: UiDevice =
        UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())

    @Before
    fun setup() = with(device) {
        // Step 1: Login and handle permissions
        launchHomeScreenWithTestLogin()
    }

    @Test
    fun givenUserNavigatedToBusinessScheduleScreenFromFavoritesTab_whenClassItemBookClicked_thenQuickBookDialogAppears() = with(device) {
        // Step 2: Navigate to Favorites Business tab
        navigateToFavoritesBusinessTab()

        // Step 3: Click on Schedule button and verify Business Schedule screen
        clickOnScheduleButton()

        // Step 4: Click on Book button and verify QuickBook dialog
        clickBookButtonAndVerifyQuickBookDialog()
    }

    @Test
    fun givenUserNavigatedToBusinessScheduleScreenFromFavoritesTab_whenClassItemClicked_thenClassDetailsAppears() = with(device) {
        // Step 2: Navigate to Favorites Business tab
        navigateToFavoritesBusinessTab()

        // Step 3: Click on Schedule button and verify Business Schedule screen
        clickOnScheduleButton()

        // Step 4: Click on a class item and verify Class Details screen
        clickClassItemAndVerifyClassDetails()
    }

    @Test
    fun givenUserNavigatedToBusinessScheduleScreenFromFavoritesTab_whenAppointmentCategoryItemClicked_thenAppointmentSubCategoryScreenAppears() = with(device) {
        // Step 2: Navigate to Favorites Business tab
        navigateToFavoritesBusinessTab()

        // Step 3: Click on Schedule button and verify Business Schedule screen
        clickOnScheduleButton()

        // Step 4: Switch to Appointments tab
        switchToAppointmentsTab()

        // Step 5: Click on an appointment category and verify Appointment Details screen
        clickAppointmentCategoryAndVerifyDetails()
    }

    @Test
    fun givenUserNavigatedToBusinessScheduleScreenFromBusinessDetails_whenClassItemBookClicked_thenQuickBookDialogAppears() = with(device) {
    }

    @Test
    fun givenUserNavigatedToBusinessScheduleScreenFromBusinessDetails_whenClassItemClicked_thenClassDetailsAppears() = with(device) {
    }

    // Regression suite test case 1384639 - should be covered as part of this (tab switching happens automatically if we click appointment categories)
    @Test
    fun givenUserNavigatedToBusinessScheduleScreenFromBusinessDetails_whenAppointmentCategoryItemClicked_thenAppointmentSubCategoryScreenAppears() = with(device) {
    }
}
