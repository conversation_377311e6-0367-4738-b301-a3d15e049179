package com.mindbodyonline.connect.uiAutomatorTests.regressionTests.loginToAccount

import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import com.mindbodyonline.connect.uiAutomatorTests.testData.find_your_new_favorite
import com.mindbodyonline.connect.uiAutomatorTests.testData.mob_test_password
import com.mindbodyonline.connect.uiAutomatorTests.testData.mob_test_username
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickLoginToPurchase
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickOnAlreadyHaveAnAccountButton
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickOnFindNewFavoriteClassTile
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickOnNextButton
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickProfileIcon
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickSessionLostPopup
import com.mindbodyonline.connect.uiAutomatorTests.testModels.closeButtonOnLoginScreen
import com.mindbodyonline.connect.uiAutomatorTests.testModels.login
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyClassDetailsPage
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyFindNewFavoriteHeader
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyLoginToPurchaseButton
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyUserName
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.acceptAllPermissions
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.launchApp
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.scrollWithText
import org.junit.Before
import org.junit.Test

class GuestUserLoginTest {
    private var device: UiDevice =
        UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())

    @Before
    fun setup() = with(device) {
        launchApp()
    }

    @Test
    fun guestUserLoginWithAlreadyHaveAnAccount_1396448() = with(device){
        closeButtonOnLoginScreen()
        acceptAllPermissions()
        clickProfileIcon()
        clickOnAlreadyHaveAnAccountButton()
        clickSessionLostPopup()
        login(mob_test_username, mob_test_password)
        clickProfileIcon()
        verifyUserName()
    }

    @Test
    fun guestUserPromptedToLogInBookingClass_1396445() : Unit = with(device){
        closeButtonOnLoginScreen()
        acceptAllPermissions()
        scrollWithText(find_your_new_favorite)
        verifyFindNewFavoriteHeader()
        clickOnFindNewFavoriteClassTile()
        clickOnNextButton()
        verifyLoginToPurchaseButton()
        clickLoginToPurchase()
        login(mob_test_username, mob_test_password)
        verifyClassDetailsPage()
    }
}
