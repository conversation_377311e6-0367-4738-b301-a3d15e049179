package com.mindbodyonline.connect.uiAutomatorTests.testUtils

import androidx.test.uiautomator.By
import androidx.test.uiautomator.BySelector
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiObject2
import androidx.test.uiautomator.Until
import junit.framework.Assert.assertTrue

/**
 * Utility functions for webview testing, specifically for Custom Tabs and webview interactions
 */

internal const val WEBVIEW_WAIT_TIMEOUT = 15000L
internal const val WEBVIEW_INTERACTION_TIMEOUT = 10000L

// Common webview and browser selectors
private val chromePackageSelector: BySelector = By.pkg("com.android.chrome")
private val browserPackageSelector: BySelector = By.pkg("com.android.browser")
private val webviewSelector: BySelector = By.clazz("android.webkit.WebView")

/**
 * Waits for any webview-based browser to appear (Chrome, default browser, or embedded webview)
 */
internal fun UiDevice.waitForAnyWebview(timeout: Long = WEBVIEW_WAIT_TIMEOUT): Boolean {
    // Try Chrome first (most common for Custom Tabs)
    if (wait(Until.findObject(chromePackageSelector), timeout / 3) != null) {
        return true
    }
    
    // Try default browser
    if (wait(Until.findObject(browserPackageSelector), timeout / 3) != null) {
        return true
    }
    
    // Try embedded webview
    if (wait(Until.findObject(webviewSelector), timeout / 3) != null) {
        return true
    }
    
    return false
}

/**
 * Finds an element in webview by multiple selector strategies
 */
internal fun UiDevice.findWebviewElement(
    vararg selectors: BySelector,
    timeout: Long = WEBVIEW_INTERACTION_TIMEOUT
): UiObject2? {
    for (selector in selectors) {
        val element = wait(Until.findObject(selector), timeout / selectors.size)
        if (element != null) {
            return element
        }
    }
    return null
}

/**
 * Safely enters text in a webview input field with multiple fallback strategies
 */
internal fun UiDevice.safeWebviewTextEntry(
    text: String,
    vararg selectors: BySelector,
    timeout: Long = WEBVIEW_INTERACTION_TIMEOUT
): Boolean {
    val element = findWebviewElement(*selectors, timeout = timeout)
    
    return if (element != null) {
        try {
            element.clear()
            element.text = text
            true
        } catch (e: Exception) {
            // Fallback: try clicking first then entering text
            try {
                element.click()
                element.text = text
                true
            } catch (e2: Exception) {
                false
            }
        }
    } else {
        false
    }
}

/**
 * Safely clicks an element in webview with retry logic
 */
internal fun UiDevice.safeWebviewClick(
    vararg selectors: BySelector,
    timeout: Long = WEBVIEW_INTERACTION_TIMEOUT
): Boolean {
    val element = findWebviewElement(*selectors, timeout = timeout)
    
    return if (element != null) {
        try {
            element.click()
            true
        } catch (e: Exception) {
            // Retry once
            try {
                Thread.sleep(1000)
                element.click()
                true
            } catch (e2: Exception) {
                false
            }
        }
    } else {
        false
    }
}

/**
 * Checks if we're currently in a webview context
 */
internal fun UiDevice.isInWebview(): Boolean {
    return hasObject(chromePackageSelector) || 
           hasObject(browserPackageSelector) || 
           hasObject(webviewSelector)
}

/**
 * Waits for webview to disappear (indicating return to app)
 */
internal fun UiDevice.waitForWebviewToClose(timeout: Long = WEBVIEW_WAIT_TIMEOUT): Boolean {
    return wait(Until.gone(chromePackageSelector), timeout) &&
           wait(Until.gone(browserPackageSelector), timeout) &&
           wait(Until.gone(webviewSelector), timeout)
}

/**
 * Forces webview to close using various methods
 */
internal fun UiDevice.forceCloseWebview(): Boolean {
    if (!isInWebview()) {
        return true
    }
    
    // Try pressing back button
    pressBack()
    if (!isInWebview()) {
        return true
    }
    
    // Try pressing back multiple times
    repeat(3) {
        pressBack()
        Thread.sleep(500)
        if (!isInWebview()) {
            return true
        }
    }
    
    // Try home button as last resort
    pressHome()
    Thread.sleep(1000)
    
    return !isInWebview()
}

/**
 * Verifies that webview login URL contains expected patterns
 */
internal fun UiDevice.verifyWebviewLoginUrl(expectedPatterns: List<String>): Boolean {
    // This is a placeholder - actual URL verification would require
    // accessing the webview's URL which might not be directly available
    // through UiAutomator. This could be enhanced with additional tooling.
    return isInWebview()
}

/**
 * Handles common webview loading states and errors
 */
internal fun UiDevice.handleWebviewLoadingStates(maxWaitTime: Long = WEBVIEW_WAIT_TIMEOUT): Boolean {
    val startTime = System.currentTimeMillis()
    
    while (System.currentTimeMillis() - startTime < maxWaitTime) {
        if (isInWebview()) {
            // Check for common loading indicators
            val loadingIndicators = listOf(
                By.text("Loading..."),
                By.text("Please wait..."),
                By.clazz("android.widget.ProgressBar")
            )
            
            var isLoading = false
            for (indicator in loadingIndicators) {
                if (hasObject(indicator)) {
                    isLoading = true
                    break
                }
            }
            
            if (!isLoading) {
                // Webview appears to be loaded
                return true
            }
        }
        
        Thread.sleep(500)
    }
    
    return false
}

/**
 * Debug function to print current webview state
 */
internal fun UiDevice.debugWebviewState(): String {
    val state = StringBuilder()
    state.append("Webview State Debug:\n")
    state.append("- Chrome present: ${hasObject(chromePackageSelector)}\n")
    state.append("- Browser present: ${hasObject(browserPackageSelector)}\n")
    state.append("- WebView present: ${hasObject(webviewSelector)}\n")
    state.append("- Current package: ${currentPackageName}\n")
    
    return state.toString()
}
