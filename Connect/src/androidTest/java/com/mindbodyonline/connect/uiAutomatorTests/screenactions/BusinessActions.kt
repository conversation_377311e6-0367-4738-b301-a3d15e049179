package com.mindbodyonline.connect.uiAutomatorTests.screenactions

import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiObject
import androidx.test.uiautomator.UiSelector
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.UI_VIEW_APPEAR_TIMEOUT_SHORT
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.findByUiSelector

private const val VIEW_PRICING_BUTTON_ID = "com.mindbodyonline.connect:id/business_details_pricing_button"

fun UiDevice.getViewPricingButton(): UiObject =
    findByUiSelector(
        UiSelector().resourceId(VIEW_PRICING_BUTTON_ID),
        UI_VIEW_APPEAR_TIMEOUT_SHORT,
    )
