package com.mindbodyonline.connect.uiAutomatorTests.regressionTests.loginToAccount

import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.runner.AndroidJUnit4
import androidx.test.uiautomator.UiDevice
import com.mindbodyonline.connect.uiAutomatorTests.testData.check_email_message
import com.mindbodyonline.connect.uiAutomatorTests.testData.mob_test_password
import com.mindbodyonline.connect.uiAutomatorTests.testData.mob_test_username
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickContinue
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickForgotPassword
import com.mindbodyonline.connect.uiAutomatorTests.testModels.enterEmail
import com.mindbodyonline.connect.uiAutomatorTests.testModels.login
import com.mindbodyonline.connect.uiAutomatorTests.testModels.permissionEnableNotifications
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyCheckEmailPopUp
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyHomeScreenIcon
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.launchApp
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.permissionPromptAllowNotifications
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.permissionShareLocation
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.permissionWhileUsingAppPopUp
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class LoginTest {
	private var device: UiDevice =
		UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())

	@Before
	fun setup() = with(device) {
		launchApp()
	}

	@Test
	fun loginWithExistingEmail_1298263() = with(device) {
		launchApp()
		login(mob_test_username, mob_test_password)
		permissionShareLocation()
		permissionWhileUsingAppPopUp()
		permissionEnableNotifications()
		permissionPromptAllowNotifications()
		verifyHomeScreenIcon()
	}

	@Test
	fun forgotPasswordLink_1298265() = with(device) {
		enterEmail(mob_test_username)
		clickContinue()
		clickForgotPassword()
		verifyCheckEmailPopUp(check_email_message)
	}
}
