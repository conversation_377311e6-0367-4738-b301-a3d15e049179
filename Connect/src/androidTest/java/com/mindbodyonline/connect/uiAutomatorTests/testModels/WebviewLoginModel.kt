package com.mindbodyonline.connect.uiAutomatorTests.testModels

import androidx.test.uiautomator.By
import androidx.test.uiautomator.BySelector
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.Until
import junit.framework.Assert.assertTrue

internal const val WebviewTimeout = 10000L

// Chrome Custom Tabs selector
private val chromeSelector: BySelector = By.pkg("com.android.chrome")

// Simple webview form selectors
private val emailFieldSelector: BySelector = By.res("email")
private val passwordFieldSelector: BySelector = By.res("password") 
private val loginButtonSelector: BySelector = By.text("Sign In")

// App return selector
private val appSelector: BySelector = By.pkg(currentPackageName)

/**
 * Simple webview login - assumes Chrome Custom Tabs with standard form fields
 */
internal fun UiDevice.webviewLogin(email: String, password: String) {
    // Wait for Chrome to open
    wait(Until.findObject(chromeSelector), WebviewTimeout)
    assertTrue("Chrome should open for webview login", hasObject(chromeSelector))
    
    // Enter email
    wait(Until.findObject(emailFieldSelector), WebviewTimeout)?.apply {
        clear()
        text = email
    }
    
    // Enter password  
    wait(Until.findObject(passwordFieldSelector), WebviewTimeout)?.apply {
        clear()
        text = password
    }
    
    // Click login
    wait(Until.findObject(loginButtonSelector), WebviewTimeout)?.click()
    
    // Wait to return to app
    wait(Until.findObject(appSelector), WebviewTimeout)
    assertTrue("Should return to app after login", hasObject(appSelector))
}

/**
 * Cancel webview login by pressing back
 */
internal fun UiDevice.cancelWebviewLogin() {
    // Wait for Chrome to open
    wait(Until.findObject(chromeSelector), WebviewTimeout)
    
    // Press back to close
    pressBack()
    
    // Wait to return to app
    wait(Until.findObject(appSelector), WebviewTimeout)
    assertTrue("Should return to app after cancel", hasObject(appSelector))
}

/**
 * Verify webview login started
 */
internal fun UiDevice.verifyWebviewLoginInitiated() {
    val chromeOpened = wait(Until.findObject(chromeSelector), WebviewTimeout) != null
    assertTrue("Chrome should open for webview login", chromeOpened)
}

/**
 * Verify back in main app
 */
internal fun UiDevice.verifyBackInApp() {
    assertTrue("Should be back in main app", hasObject(appSelector))
}

/**
 * Handle webview login error - just return to app
 */
internal fun UiDevice.handleWebviewLoginError() {
    // If still in Chrome, press back
    if (hasObject(chromeSelector)) {
        pressBack()
    }
    
    // Wait to return to app
    wait(Until.findObject(appSelector), WebviewTimeout)
}
