package com.mindbodyonline.connect.uiAutomatorTests.testModels

import androidx.test.uiautomator.By
import androidx.test.uiautomator.BySelector
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import androidx.test.uiautomator.Until
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.findByUiSelector
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.waitForAnyWebview
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.safeWebviewTextEntry
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.safeWebviewClick
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.isInWebview
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.waitForWebviewToClose
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.forceCloseWebview
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.handleWebviewLoadingStates
import junit.framework.Assert.assertTrue

internal const val WebviewLoadTimeout = 10000L
internal const val WebviewInteractionTimeout = 15000L

// Webview login selectors
private val webviewSelector: BySelector = By.clazz("android.webkit.WebView")
private val customTabsSelector: BySelector = By.pkg("com.android.chrome")
private val browserSelector: BySelector = By.pkg("com.android.browser")

// Login form selectors within webview
private val webviewEmailSelector: BySelector = By.res("email")
private val webviewPasswordSelector: BySelector = By.res("password")
private val webviewLoginButtonSelector: BySelector = By.text("Sign In")
private val webviewSignInButtonSelector: BySelector = By.text("LOG IN")

// Alternative selectors for webview elements
//private val webviewEmailInputSelector: BySelector = By.xpath("//input[@type='email']")
//private val webviewPasswordInputSelector: BySelector = By.xpath("//input[@type='password']")
//private val webviewSubmitButtonSelector: BySelector = By.xpath("//button[@type='submit']")

// Chrome Custom Tabs selectors
private val chromeUrlBarSelector: UiSelector = UiSelector().resourceId("com.android.chrome:id/url_bar")
private val chromeMenuSelector: UiSelector = UiSelector().resourceId("com.android.chrome:id/menu_button")
private val chromeCloseSelector: UiSelector = UiSelector().resourceId("com.android.chrome:id/close_button")

// App return selectors
private val appReturnSelector: BySelector = By.pkg(currentPackageName)

/**
 * Initiates webview login by waiting for the custom tabs to launch
 * and then interacting with the webview login form
 */
internal fun UiDevice.webviewLogin(
    email: String,
    password: String
) {
    // Wait for webview or custom tabs to appear
    waitForWebviewToLoad()

    // Fill in login credentials in the webview
    enterEmailInWebview(email)
    enterPasswordInWebview(password)

    // Submit the login form
    clickWebviewLoginButton()

    // Wait for redirect back to app
    waitForAppReturn()

    // Verify we're back in the app
    assertTrue("Should return to app after webview login", hasObject(appReturnSelector))
}

/**
 * Waits for the webview (Custom Tabs) to load
 */
internal fun UiDevice.waitForWebviewToLoad() {
    val webviewFound = waitForAnyWebview(WebviewLoadTimeout)
    assertTrue("Webview should load within timeout", webviewFound)

    // Wait for webview to finish loading
    handleWebviewLoadingStates(WebviewLoadTimeout)
}

/**
 * Enters email in the webview login form
 */
internal fun UiDevice.enterEmailInWebview(email: String) {
    val success = safeWebviewTextEntry(
        email,
        webviewEmailSelector,
        webviewEmailInputSelector,
        timeout = WebviewInteractionTimeout
    )

    if (!success) {
        throw AssertionError("Could not find email input field in webview")
    }
}

/**
 * Enters password in the webview login form
 */
internal fun UiDevice.enterPasswordInWebview(password: String) {
    val success = safeWebviewTextEntry(
        password,
        webviewPasswordSelector,
        webviewPasswordInputSelector,
        timeout = WebviewInteractionTimeout
    )

    if (!success) {
        throw AssertionError("Could not find password input field in webview")
    }
}

/**
 * Clicks the login button in the webview
 */
internal fun UiDevice.clickWebviewLoginButton() {
    val success = safeWebviewClick(
        webviewLoginButtonSelector,
        webviewSignInButtonSelector,
        webviewSubmitButtonSelector,
        timeout = WebviewInteractionTimeout
    )

    if (!success) {
        throw AssertionError("Could not find login button in webview")
    }
}

/**
 * Waits for the app to return after successful webview login
 */
internal fun UiDevice.waitForAppReturn() {
    val appReturned = wait(Until.findObject(appReturnSelector), WebviewInteractionTimeout) != null
    assertTrue("App should return after webview login", appReturned)
}

/**
 * Simulates cancelling the webview login by closing the custom tabs
 */
internal fun UiDevice.cancelWebviewLogin() {
    waitForWebviewToLoad()

    // Use utility function to force close webview
    val closed = forceCloseWebview()

    if (!closed) {
        throw AssertionError("Could not close webview")
    }

    // Wait for return to app
    waitForAppReturn()
}

/**
 * Verifies that webview login was initiated (custom tabs opened)
 */
internal fun UiDevice.verifyWebviewLoginInitiated() {
    val webviewOpened = waitForAnyWebview(WebviewLoadTimeout)
    assertTrue("Webview login should be initiated", webviewOpened)
}

/**
 * Verifies that we're back in the main app after webview interaction
 */
internal fun UiDevice.verifyBackInApp() {
    assertTrue("Should be back in the main app", hasObject(appReturnSelector))
}

/**
 * Handles webview login error scenarios
 */
internal fun UiDevice.handleWebviewLoginError() {
    // Wait for any error messages or dialogs in webview
    // This is a placeholder for error handling logic
    waitForAppReturn()
}
