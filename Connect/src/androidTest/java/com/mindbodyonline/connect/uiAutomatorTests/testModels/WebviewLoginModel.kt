package com.mindbodyonline.connect.uiAutomatorTests.testModels

import androidx.test.uiautomator.By
import androidx.test.uiautomator.BySelector
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import androidx.test.uiautomator.Until
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.findByUiSelector
import junit.framework.Assert.assertTrue

internal const val WebviewTimeout = 10000L

// Chrome Custom Tabs selector
private val chromeSelector: BySelector = By.pkg("com.android.chrome")

// Simple webview form selectors - use visible text/hints from screenshot
private val emailFieldSelector: BySelector = By.hint("Email")
private val continueButtonSelector: BySelector = By.text("Continue")
private val passwordFieldSelector: BySelector = By.hint("Password")
private val loginButtonSelector: BySelector = By.text("Sign In")

private val userNameInProfileScreen =
    UiSelector().resourceId("com.mindbodyonline.connect:id/user_full_name")

// App return selector
private val appSelector: BySelector = By.pkg("com.mindbodyonline.connect")

/**
 * Simple webview login - assumes Chrome Custom Tabs with standard form fields
 */
internal fun UiDevice.webviewLogin(email: String, password: String) {
    // Wait for Chrome to open
    wait(Until.findObject(chromeSelector), WebviewTimeout)
    assertTrue("Chrome should open for webview login", hasObject(chromeSelector))

    // Enter email (same pattern as password)
    wait(Until.findObject(emailFieldSelector), WebviewTimeout)?.apply {
        clear()
        text = email
    }

    // Click Continue button (from the screenshot this is the first step)
    wait(Until.findObject(continueButtonSelector), WebviewTimeout)?.click()

    // Wait for password field to appear and enter password
    wait(Until.findObject(passwordFieldSelector), WebviewTimeout)?.apply {
        clear()
        text = password
    }

    // Click login/sign in button
    wait(Until.findObject(loginButtonSelector), WebviewTimeout)?.click()

    // Wait longer for OAuth redirect back to app
    val returned = wait(Until.findObject(appSelector), WebviewTimeout * 2) != null
    if (!returned && hasObject(chromeSelector)) {
        throw AssertionError("Still in webview after login - check credentials or webview flow")
    }

    // Just verify we're back in app - actual login success will be verified by subsequent test steps
    // (like checking home screen, which only appears after successful login)
    assertTrue("Should return to app after login", hasObject(appSelector))
}

/**
 * Cancel webview login by pressing back
 */
internal fun UiDevice.cancelWebviewLogin() {
    // Wait for Chrome to open
    wait(Until.findObject(chromeSelector), WebviewTimeout)

    // Press back to close
    pressBack()

    // Wait to return to app
    wait(Until.findObject(appSelector), WebviewTimeout)
    assertTrue("Should return to app after cancel", hasObject(appSelector))
}

/**
 * Verify webview login started
 */
internal fun UiDevice.verifyWebviewLoginInitiated() {
    val chromeOpened = wait(Until.findObject(chromeSelector), WebviewTimeout) != null
    assertTrue("Chrome should open for webview login", chromeOpened)
}

/**
 * Verify back in main app
 */
internal fun UiDevice.verifyBackInApp() {
    assertTrue("Should be back in main app", hasObject(appSelector))
}

internal fun UiDevice.verifyGuestUserName() {
    val loggedInUsername = findByUiSelector(userNameInProfileScreen, ScreenLoadTimeout)?.text
    assertTrue("Expected 'Guest User' but found: $loggedInUsername",
        loggedInUsername?.contains("Guest User", ignoreCase = true) == true)
}

internal fun UiDevice.verifyMobTestUserName() {
    val loggedInUsername = findByUiSelector(userNameInProfileScreen, ScreenLoadTimeout)?.text
    assertTrue("Expected 'mob test' but found: $loggedInUsername",
        loggedInUsername?.contains("mob test", ignoreCase = true) == true)
}
