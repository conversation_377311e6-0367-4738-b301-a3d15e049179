package com.mindbodyonline.connect.uiAutomatorTests.testModels

import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import com.mindbodyonline.connect.uiAutomatorTests.testData.classDetailsHeader
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.findByUiSelector

private val nextButton =
    UiSelector().resourceId("com.mindbodyonline.connect:id/class_event_type_book_button")
private val classDetailsPageSelector = UiSelector().text(classDetailsHeader)
private val loginToPurchaseButton =
    UiSelector().resourceId("com.mindbodyonline.connect:id/next_button")

internal fun UiDevice.clickOnNextButton(){
    findByUiSelector(nextButton, ScreenLoadTimeout).click()
}

internal fun UiDevice.verifyLoginToPurchaseButton() {
    assert(findByUiSelector(loginToPurchaseButton, ScreenLoadTimeout).exists())
}

internal fun UiDevice.clickLoginToPurchase() {
    val button = findByUiSelector(loginToPurchaseButton, ScreenLoadTimeout)
    // Added a wait to ensure the "Login to Purchase" button click works reliably after the screen loads
    Thread.sleep(ScreenLoadTimeout)
    button.click()
}

internal fun UiDevice.verifyClassDetailsPage() {
    assert(findByUiSelector(classDetailsPageSelector, ScreenLoadTimeout).exists())
}
