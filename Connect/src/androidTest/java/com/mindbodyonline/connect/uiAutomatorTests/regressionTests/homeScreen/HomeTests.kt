package com.mindbodyonline.connect.uiAutomatorTests.regressionTests.homeScreen

import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import com.mindbodyonline.connect.uiAutomatorTests.testData.back_button
import com.mindbodyonline.connect.uiAutomatorTests.testData.beauty_near_you_header
import com.mindbodyonline.connect.uiAutomatorTests.testData.beauty_term
import com.mindbodyonline.connect.uiAutomatorTests.testData.book_a_favorite_header
import com.mindbodyonline.connect.uiAutomatorTests.testData.businesses_tab
import com.mindbodyonline.connect.uiAutomatorTests.testData.deals_near_you_header
import com.mindbodyonline.connect.uiAutomatorTests.testData.explore_categories
import com.mindbodyonline.connect.uiAutomatorTests.testData.favoriteBusinessStudioText
import com.mindbodyonline.connect.uiAutomatorTests.testData.fitness_near_you_header
import com.mindbodyonline.connect.uiAutomatorTests.testData.fitness_term
import com.mindbodyonline.connect.uiAutomatorTests.testData.mob_test_password
import com.mindbodyonline.connect.uiAutomatorTests.testData.mob_test_username
import com.mindbodyonline.connect.uiAutomatorTests.testData.near_you_header
import com.mindbodyonline.connect.uiAutomatorTests.testData.sort_distance
import com.mindbodyonline.connect.uiAutomatorTests.testData.view_map_header
import com.mindbodyonline.connect.uiAutomatorTests.testData.wellness_near_you_header
import com.mindbodyonline.connect.uiAutomatorTests.testData.wellness_term
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickBackButton
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickDealsIcon
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickDiscoveryArrowSelector
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickFavoriteBusinessTile
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickFavoriteIcon
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickHomeScreenIcon
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickProfileIcon
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickSearchIcon
import com.mindbodyonline.connect.uiAutomatorTests.testModels.clickSeeAll
import com.mindbodyonline.connect.uiAutomatorTests.testModels.login
import com.mindbodyonline.connect.uiAutomatorTests.testModels.permissionEnableNotifications
import com.mindbodyonline.connect.uiAutomatorTests.testModels.scrollVerticalIndicator
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyBusinessTab
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyBusinessViewHeader
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyDealsHeader
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyDealsNearYou
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyFindNewFavoriteHeader
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyHomePageCarousalHeader
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyNearYouHeader
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyProfileHeader
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifySchedulePageHeader
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifySearchHeader
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifySortDistance
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyVerticalNearYou
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyVerticalTerm
import com.mindbodyonline.connect.uiAutomatorTests.testModels.verifyViewMap
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.launchApp
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.launchHomeScreenWithTestLogin
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.permissionPromptAllowNotifications
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.permissionShareLocation
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.permissionWhileUsingAppPopUp
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.scrollWithText
import org.junit.Before
import org.junit.Test

class HomeTests {
	private var device: UiDevice =
		UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())

	@Before
	fun setup() = with(device) {
		launchHomeScreenWithTestLogin()
	}

	@Test
	fun authenticatedHomeScreen_BookAFavorite_1378295() = with(device) {
		verifyHomePageCarousalHeader(book_a_favorite_header)
		clickSeeAll()
		verifyFindNewFavoriteHeader()
		clickHomeScreenIcon()
		clickFavoriteBusinessTile(favoriteBusinessStudioText)
		verifySchedulePageHeader()
	}

	@Test
	fun authenticatedHomeScreen_nearYouMapCarousal_1378298() = with(device) {
		scrollWithText(near_you_header)
		verifyNearYouHeader(near_you_header)
		verifyViewMap(view_map_header)
		verifyBusinessViewHeader()
	}

	@Test
	fun authenticatedHomeScreen_navigationBar_1378305() = with(device) {
		clickFavoriteIcon()
		verifyFindNewFavoriteHeader()
		clickSearchIcon()
		verifySearchHeader()
		clickDealsIcon()
		verifyDealsHeader()
		clickProfileIcon()
		verifyProfileHeader()
		clickHomeScreenIcon()
	}

	@Test
	fun authenticatedHomeScreen_discoveryCarousal_1378305(): Unit = with(device) {
		scrollWithText(explore_categories)
		verifyDealsNearYou(deals_near_you_header)
		verifyBusinessTab(businesses_tab)
		verifySortDistance(sort_distance)
		pressBack()
		scrollVerticalIndicator(1, 1)
		verifyVerticalNearYou(fitness_near_you_header)
		clickDiscoveryArrowSelector()
		verifyVerticalTerm(fitness_term)
		pressBack()
		scrollVerticalIndicator(2, 1)
		verifyVerticalNearYou(wellness_near_you_header)
		clickDiscoveryArrowSelector()
		verifyVerticalTerm(wellness_term)
		clickBackButton(back_button)
		scrollVerticalIndicator(3, 1)
		verifyVerticalNearYou(beauty_near_you_header)
		clickDiscoveryArrowSelector()
		verifyVerticalTerm(beauty_term)
	}
}
