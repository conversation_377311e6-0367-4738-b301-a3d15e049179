package com.mindbodyonline.connect.uiAutomatorTests.contracts

import android.util.Log
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.By
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import androidx.test.uiautomator.Until
import com.mindbodyonline.connect.automation.main.MainPage
import com.mindbodyonline.connect.uiAutomatorTests.screenactions.enterSearchTextWithLocationAndWaitForResult
import com.mindbodyonline.connect.uiAutomatorTests.screenactions.getContractFromListing
import com.mindbodyonline.connect.uiAutomatorTests.screenactions.getContractsTab
import com.mindbodyonline.connect.uiAutomatorTests.screenactions.getViewPricingButton
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.UI_VIEW_APPEAR_TIMEOUT_LONG
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.launchHomeScreenWithTestLogin
import com.mindbodyonline.connect.utils.time.DateFormatUtils
import org.junit.Before
import org.junit.Test
import java.lang.Thread.sleep
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

class ContractDialogTest {
    // Contract names
    private companion object {
        // Contract types
        const val CONTRACT_NO_AUTOPAYS_NO_RENEWS = "0 autopays, no renews"
        const val CONTRACT_RENEWS_ON_15TH = "contract-renew-exact-date-15"
        const val CONTRACT_RENEWS_WHEN_PASSES_EXPIRE = "renew-when-passes-expire"
        const val CONTRACT_FIRST_PAYMENT_FREE = "Contract first payment free"
        const val CONTRACT_LAST_PAYMENT_FREE = "Last payment free"

        // Search parameters
        const val SEARCH_BUSINESS_NAME = "Bootcamp & Brews"
        const val SEARCH_LOCATION = "Fort Irwin"
        const val SEARCH_AREA = "Downtown"

        // UI text patterns
        const val TEXT_CONTRACT_STARTS = "Contract starts"
        const val TEXT_AUTO_RENEWS = "Auto-renews"
        const val TEXT_TERMS_PREFIX = "By clicking 'Next', you agree to pay"
    }

    private lateinit var device: UiDevice

    @Before
    fun setUp() {
        // Initialize UiDevice
        device = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
        device.launchHomeScreenWithTestLogin()

        // Navigate to search screen
        MainPage.goToExplore()
    }

    @Test
    fun testContractDialog() {
        goToContractsTab()
        delayScreen(500)
        testContractDialogFor0Autopays0Renews()
        delayScreen(500L)
        device.pressBack()

        testContractDialogWhereContractRenews15thEveryMonth()
        delayScreen(2000L)
        device.pressBack()

        testContractDialogWhereContractRenewsEvery6Payments()
        delayScreen(2000L)
        device.pressBack()
        testContractDialogWhereFirstPaymentFree()
        delayScreen(2000L)
        device.pressBack()

        testContractDialogWhereLastPaymentFree()
//        delayScreen(2000L)
    }

    private fun delayScreen(timeout: Long) {
        sleep(timeout)
    }

    /**
     * Test contract with no autopays and no renewals
     */
    private fun testContractDialogFor0Autopays0Renews() {
        openContractAndWaitForDialog(CONTRACT_NO_AUTOPAYS_NO_RENEWS)
        verifyContractStartDate()
        verifyNoContractTerms()
    }

    /**
     * Test contract that renews on the 15th of each month
     */
    private fun testContractDialogWhereContractRenews15thEveryMonth() {
        openContractAndWaitForDialog(CONTRACT_RENEWS_ON_15TH)
        val contractTerms = device.findObject(UiSelector().resourceId("com.mindbodyonline.connect:id/contract_terms_and_conditions")).text
        Log.e("Contract Terms", contractTerms)
        verifyStartDateExists()
        verifyContractTerms(
            contractTerms
//            "Click \'Next’, to agree to pay \$4 (plus tax) per month for each renewal term. Contract auto-renews every month until you cancel. To stop renewal charges, cancel via ‘Settings’ before renewal date above.",
        )
        verifyContractTerms("Click 'Next’, to agree to pay \$4 (plus tax) per month for each renewal term. Contract auto-renews every month until you cancel. To stop renewal charges, cancel via ‘Settings’ screen before renewal date above.")
        verifyAutoRenewsTextExists()
    }

    /**
     * Test contract that renews after 6 payments
     */
    private fun testContractDialogWhereContractRenewsEvery6Payments() {
        openContractAndWaitForDialog(CONTRACT_RENEWS_WHEN_PASSES_EXPIRE)

        verifyContractStartDate()
        verifyContractTerms(
            "Click 'Next’, to agree to pay $20 (plus tax) each time your pass(es) run out or expire during each renewal term. Contract auto-renews every 6 payments until you cancel. To stop renewal charges, cancel via ‘Settings’ screen before renewal.",
        )
        verifyTextExists("Auto-renews every 6 payments")
    }

    /**
     * Test contract where first payment is free
     */
    private fun testContractDialogWhereFirstPaymentFree() {
        openContractAndWaitForDialog(CONTRACT_FIRST_PAYMENT_FREE)
        val contractTerms = device.findObject(UiSelector().resourceId("com.mindbodyonline.connect:id/contract_terms_and_conditions")).text
        Log.e("Contract Terms", contractTerms)
        verifyContractStartDate()
        verifyContractTerms(
            "Click 'Next’, to agree to pay \$30 (plus tax) per month for each renewal term (less applicable discounts). Contract auto-renews every 12 months until you cancel. To stop renewal charges, cancel via ‘Settings’ screen before renewal date above.",
        )
        verifyRenewsInOneYear()
    }

    /**
     * Test contract where last payment is free
     */
    private fun testContractDialogWhereLastPaymentFree() {
        openContractAndWaitForDialog(CONTRACT_LAST_PAYMENT_FREE)

        verifyContractStartDate()
        verifyContractTerms(
            "Click 'Next’, to agree to pay \$30 (plus tax) per month for each renewal term (less applicable discounts). Contract auto-renews every 12 months until you cancel. To stop renewal charges, cancel via ‘Settings’ screen before renewal date above.",
        )
        verifyRenewsInOneYear()
    }

    /**
     * Navigate to contracts tab in pricing screen
     */
    private fun goToContractsTab() {
        device.enterSearchTextWithLocationAndWaitForResult(
            SEARCH_BUSINESS_NAME,
            SEARCH_LOCATION,
            SEARCH_AREA,
        ).click()

        // Navigate to view pricing
        device.getViewPricingButton().click()

        // Navigate to contracts tab
        device.getContractsTab().click()
    }

    // Helper methods

    private fun openContractAndWaitForDialog(contractName: String) {
        device.getContractFromListing(contractName).also {
            device.waitForIdle()
            delayScreen(500)
        }.click()
        device.waitForIdle()
        device
            .wait(
                Until.hasObject(By.textContains("Fort Irwin - Downtown")),
                UI_VIEW_APPEAR_TIMEOUT_LONG,
            ).also {
                if (!it) {
                    throw IllegalStateException("Contract dialog did not appear")
                }
            }
    }

    private fun verifyContractStartDate() {
//        val date =
//            DateFormatUtils.format(Date(), "MMM dd, yyyy", TimeZone.getTimeZone("UTC"), Locale.US)
        verifyTextExists(TEXT_CONTRACT_STARTS) // the contract date doesn't get updated in app
    }

    private fun verifyStartDateExists() {
        verifyTextExists(TEXT_CONTRACT_STARTS)
    }

    private fun verifyNoContractTerms() {
        assert(!device.hasObject(By.textContains(TEXT_TERMS_PREFIX)))
    }

    private fun verifyContractTerms(termsText: String) {
        verifyTextExists(termsText)
    }

    private fun verifyAutoRenewsTextExists() {
        verifyTextExists(TEXT_AUTO_RENEWS)
    }

    private fun verifyRenewsInOneYear() {
        val calendar = Calendar.getInstance()
        calendar.time = Date()
        calendar.add(Calendar.YEAR, 1)
        val nextYearDate = DateFormatUtils.format(
            calendar.time, "MMM dd, yyyy",
            TimeZone.getTimeZone("UTC"), Locale.US,
        )
        verifyTextExists("$TEXT_AUTO_RENEWS on")
    }

    private fun verifyTextExists(text: String) {
        assert(device.hasObject(By.textContains(text)))
    }
}
