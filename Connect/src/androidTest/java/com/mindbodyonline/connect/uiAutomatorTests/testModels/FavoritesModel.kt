package com.mindbodyonline.connect.uiAutomatorTests.testModels

import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import com.mindbodyonline.connect.uiAutomatorTests.testData.find_your_new_favorite
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.findByUiSelector

private val favoriteSelector = UiSelector().text(find_your_new_favorite)
private val favoriteIcon: UiSelector =
	UiSelector().resourceId("com.mindbodyonline.connect:id/image_view_nav_button").instance(2)

internal fun UiDevice.verifyFindNewFavoriteHeader() {
	val favoriteSelectorText = findByUiSelector(favoriteSelector, ScreenLoadTimeout)
	assert(favoriteSelectorText != null)
}

internal fun UiDevice.clickFavoriteIcon() {
	val favoriteSelectorIcon = findByUiSelector(favoriteIcon, ScreenLoadTimeout)
	favoriteSelectorIcon?.click()
}
