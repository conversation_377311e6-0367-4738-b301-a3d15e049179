package com.mindbodyonline.connect.uiAutomatorTests.testModels

import androidx.test.uiautomator.By
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import androidx.test.uiautomator.Until
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.findByUiSelector
import com.mindbodyonline.connect.uiAutomatorTests.testUtils.scrollWithText


private val homeButtonSelector: UiSelector =
	UiSelector().resourceId("com.mindbodyonline.connect:id/image_view_nav_button")
		.instance(0);
private val seeAllButtonSelector: UiSelector =
	UiSelector().resourceId("com.mindbodyonline.connect:id/see_all_button").index(1)
		.text("See all")
private val nearYouHeaderSelector: UiSelector =
	UiSelector().resourceId("com.mindbodyonline.connect:id/home_near_you_title")
private val viewMapHeaderSelector: UiSelector =
	UiSelector().resourceId("com.mindbodyonline.connect:id/view_map_btn").index(2).text("View Map")
private val discoveryArrow: UiSelector =
	UiSelector().resourceId("com.mindbodyonline.connect:id/discovery_arrow_iv")
private val verticalIndicator: UiSelector =
	UiSelector().resourceId("com.mindbodyonline.connect:id/dot")
private val verticalHeader: UiSelector =
	UiSelector().resourceId("com.mindbodyonline.connect:id/discovery_title_tv")
private val enableButton = By.text("ENABLE")
private val titleText = By.res("com.mindbodyonline.connect:id/title_bar_text")
private val findNewFavoriteClass =
	UiSelector().resourceId("com.mindbodyonline.connect:id/class_iv").instance(0)

internal fun UiDevice.permissionEnableNotifications() {
	wait(
			Until.findObject(enableButton), ScreenLoadTimeout
	)?.click()
}

internal fun UiDevice.verifyHomePageCarousalHeader(headerText: String) {
	val homeCarousalText = wait(
			Until.findObject(titleText.text(headerText)),
			ScreenLoadTimeout)
	assert(homeCarousalText!=null)
}

internal fun UiDevice.verifyHomeScreenIcon() {
	val homeButton = findByUiSelector(homeButtonSelector, ScreenLoadTimeout)
	if (homeButton != null) {
		assert(homeButton.isEnabled())
	}
}

internal fun UiDevice.clickSeeAll() {
	val seeAllButton = findByUiSelector(seeAllButtonSelector, ScreenLoadTimeout)
	seeAllButton?.click()
}

internal fun UiDevice.clickFavoriteBusinessTile(favoriteBusinessStudioPartialText: String) {
	wait(
			Until.findObject(By.text(favoriteBusinessStudioPartialText)), ScreenLoadTimeout
	).click()
}

internal fun UiDevice.clickHomeScreenIcon() {
	val homeButton = findByUiSelector(homeButtonSelector, ScreenLoadTimeout)
	homeButton?.click()
}

internal fun UiDevice.verifyNearYouHeader(headerText: String) {
	val nearYouHeader = findByUiSelector(nearYouHeaderSelector, ScreenLoadTimeout)
	assert(nearYouHeader?.text == (headerText))
}

internal fun UiDevice.verifyViewMap(headerText: String) {
	scrollWithText(headerText)
	val viewMapHeader = findByUiSelector(viewMapHeaderSelector, ScreenLoadTimeout)
	viewMapHeader?.click();
}

internal fun UiDevice.clickDiscoveryArrowSelector() {
	findByUiSelector(discoveryArrow, ScreenLoadTimeout)?.click()
}

internal fun UiDevice.verifyDealsNearYou(headerText: String) {
	scrollWithText(headerText)
	clickDiscoveryArrowSelector()
}

internal fun UiDevice.scrollVerticalIndicator(position: Int, steps: Int) {
	findByUiSelector(verticalIndicator.instance(position), ScreenLoadTimeout)?.swipeLeft(steps)
}

internal fun UiDevice.verifyVerticalNearYou(headerText: String) {
	val fitnessNearYouHeader = findByUiSelector(verticalHeader.text(headerText), ScreenLoadTimeout)
	assert(fitnessNearYouHeader != null)
}

internal fun UiDevice.clickOnFindNewFavoriteClassTile(){
	findByUiSelector(findNewFavoriteClass, ScreenLoadTimeout).click()
}
