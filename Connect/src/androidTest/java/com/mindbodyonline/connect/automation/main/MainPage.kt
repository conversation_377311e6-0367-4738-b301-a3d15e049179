package com.mindbodyonline.connect.automation.main

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.matcher.ViewMatchers.withId
import com.mindbodyonline.connect.R

object MainPage {
    private val home = onView(withId(R.id.container_nav_home))
    private val explore = onView(withId(R.id.container_nav_explore))
    private val favorites = onView(withId(R.id.container_nav_fav))
    private val myInfo = onView(withId(R.id.container_nav_profile))

    fun goToExplore() {
        explore.perform(click())
    }

    fun goToFavorites() {
        favorites.perform(click())
    }

    fun goToHome() {
        home.perform(click())
    }

    fun goToProfile() {
        myInfo.perform(click())
    }
}