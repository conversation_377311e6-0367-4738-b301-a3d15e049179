package com.mindbodyonline.connect.ftc

import com.android.volley.Response
import com.android.volley.VolleyError
import com.mindbodyonline.android.api.clients.MbClientsAPIManager
import com.mindbodyonline.android.api.clients.api.MbClientsEndPoint
import com.mindbodyonline.android.api.clients.api.MbClientsService
import com.mindbodyonline.android.api.clients.model.FTCImageUrlRequest
import com.mindbodyonline.android.api.clients.model.FTCImageUrlResponse
import com.mindbodyonline.android.api.clients.model.FTCUploadConfirmationRequest
import com.mindbodyonline.android.api.clients.model.FTCUploadConfirmationResponse
import com.mindbodyonline.android.util.api.request.MBRequest
import com.mindbodyonline.connect.ftc.api.FTCRepositoryImpl
import io.mockk.MockKAnnotations
import io.mockk.Runs
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.slot
import io.mockk.unmockkAll
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.async
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Assert.fail
import org.junit.Before
import org.junit.Test
import java.util.Collections

@ExperimentalCoroutinesApi
class FTCRepositoryImplTest {

    private lateinit var ftcRepository: FTCRepositoryImpl

    @MockK
    private lateinit var mbClientsService: MbClientsService

    private val subscriberId = "sub123"
    private val contractId = "contract456"
    private val imageRequest = FTCImageUrlRequest(
        "1234", "image/jpeg", "Mobile", "Connect-Android", Collections.emptyMap()
    )
    private val imageResponse =
        FTCImageUrlResponse("12312321312313", "https://example.com/upload", true)
    private val confirmationRequest = FTCUploadConfirmationRequest(
        mbUserId = "dummyUser123",
        recordId = "record456",
        orderId = "order789",
        uploadSuccessful = true,
        verifiedClientContract = false,
        metadata = mapOf("source" to "test", "env" to "dev")
    )
    private val confirmationResponse = FTCUploadConfirmationResponse("12312321312313", true)

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        ftcRepository = FTCRepositoryImpl()

        mockkObject(MbClientsAPIManager)
        every { MbClientsAPIManager.setMbClientsService(any()) } just Runs
        every { MbClientsAPIManager.getMbClientsEndPoint() } returns mockk<MbClientsEndPoint>()

        MbClientsAPIManager.setMbClientsService(mbClientsService)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `fetchPreSignedURL returns successful response`() = runTest(UnconfinedTestDispatcher()) {
        val successListenerSlot = slot<Response.Listener<FTCImageUrlResponse>>()
        val errorListenerSlot = slot<Response.ErrorListener>()

        every {
            MbClientsAPIManager.fetchPreSignedURL(
                subscriberId,
                contractId,
                imageRequest,
                capture(successListenerSlot),
                capture(errorListenerSlot)
            )
        } returns mockk<MBRequest<FTCImageUrlResponse>>()

        val job = async {
            ftcRepository.fetchPreSignedURL(subscriberId, contractId, imageRequest)
        }

        successListenerSlot.captured.onResponse(imageResponse)

        assertEquals(imageResponse, job.await())
        verify {
            MbClientsAPIManager.fetchPreSignedURL(
                subscriberId, contractId, imageRequest, any(), any()
            )
        }
    }

    @Test
    fun `fetchPreSignedURL throws exception on error`() = runTest(UnconfinedTestDispatcher()) {
        val successListenerSlot = slot<Response.Listener<FTCImageUrlResponse>>()
        val errorListenerSlot = slot<Response.ErrorListener>()
        val volleyError = mockk<VolleyError>(relaxed = true)

        every {
            MbClientsAPIManager.fetchPreSignedURL(
                subscriberId,
                contractId,
                imageRequest,
                capture(successListenerSlot),
                capture(errorListenerSlot)
            )
        } returns mockk<MBRequest<FTCImageUrlResponse>>()

        val job = async {
            try {
                ftcRepository.fetchPreSignedURL(subscriberId, contractId, imageRequest)
                fail("Expected an Exception to be thrown")
                false
            } catch (e: Exception) {
                true
            }
        }

        errorListenerSlot.captured.onErrorResponse(volleyError)

        job.await()

        verify {
            MbClientsAPIManager.fetchPreSignedURL(
                subscriberId, contractId, imageRequest, any(), any()
            )
        }
    }

    @Test
    fun `confirmUpload returns true on success`() = runTest(UnconfinedTestDispatcher()) {
        val successListenerSlot = slot<Response.Listener<FTCUploadConfirmationResponse>>()
        val errorListenerSlot = slot<Response.ErrorListener>()

        every {
            MbClientsAPIManager.postUploadConfirmation(
                subscriberId,
                contractId,
                confirmationRequest,
                capture(successListenerSlot),
                capture(errorListenerSlot)
            )
        } returns mockk<MBRequest<FTCUploadConfirmationResponse>>()

        val job = async {
            ftcRepository.confirmUpload(subscriberId, contractId, confirmationRequest)
        }

        successListenerSlot.captured.onResponse(confirmationResponse)

        assertTrue(job.await())
        verify {
            MbClientsAPIManager.postUploadConfirmation(
                subscriberId, contractId, confirmationRequest, any(), any()
            )
        }
    }

    @Test
    fun `confirmUpload throws exception on error`() = runTest(UnconfinedTestDispatcher()) {
        val successListenerSlot = slot<Response.Listener<FTCUploadConfirmationResponse>>()
        val errorListenerSlot = slot<Response.ErrorListener>()
        val volleyError = mockk<VolleyError>(relaxed = true)

        every {
            MbClientsAPIManager.postUploadConfirmation(
                subscriberId,
                contractId,
                confirmationRequest,
                capture(successListenerSlot),
                capture(errorListenerSlot)
            )
        } returns mockk<MBRequest<FTCUploadConfirmationResponse>>()

        val job = async {
            try {
                ftcRepository.confirmUpload(subscriberId, contractId, confirmationRequest)
                fail("Expected an Exception to be thrown")
                false
            } catch (e: Exception) {
                true
            }
        }

        errorListenerSlot.captured.onErrorResponse(volleyError)
        job.await()

        verify {
            MbClientsAPIManager.postUploadConfirmation(
                subscriberId, contractId, confirmationRequest, any(), any()
            )
        }
    }
}
