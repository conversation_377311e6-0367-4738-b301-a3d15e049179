package com.mindbodyonline.connect.ftc

import io.mockk.MockKAnnotations
import io.mockk.Runs
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import java.io.IOException

@ExperimentalCoroutinesApi
class S3ZipUploaderTest {

    @RelaxedMockK
    private lateinit var mockOkHttpClient: OkHttpClient

    @MockK
    private lateinit var mockCall: Call
    private lateinit var s3ZipUploader: S3ZipUploader
    private val testPreSignedUrl = "https://test-bucket.s3.amazonaws.com/test-file.zip"
    private val testZipBytes = "test data".toByteArray()

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        every { mockOkHttpClient.newCall(any()) } returns mockCall
        s3ZipUploader = S3ZipUploader(
            okHttpClient = mockOkHttpClient,
            config = S3ZipUploader.S3UploaderConfig(
                maxRetries = 3,
                baseRetryDelayMs = 100,
                useExponentialBackoff = true
            )
        )
    }

    @After
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `uploadImagesAsZip returns error when zip bytes are empty`() = runTest {
        val emptyZipBytes = ByteArray(0)

        val result = s3ZipUploader.uploadImagesAsZip(testPreSignedUrl, emptyZipBytes)

        assertTrue(result is S3ZipUploader.UploadResult.Error)
        assertEquals(
            S3ZipUploader.ErrorCode.EMPTY_ZIP,
            (result as S3ZipUploader.UploadResult.Error).code
        )
        assertEquals("No bytes provided for zipping", result.message)
    }

    @Test
    fun `uploadImagesAsZip returns success when upload is successful`() = runTest {
        val callbackSlot = slot<Callback>()
        every { mockCall.enqueue(capture(callbackSlot)) } answers {
            val mockResponse = mockk<Response>()
            every { mockResponse.isSuccessful } returns true
            every { mockResponse.close() } just Runs
            every { mockResponse.body } returns null
            callbackSlot.captured.onResponse(mockCall, mockResponse)
        }

        val result = s3ZipUploader.uploadImagesAsZip(testPreSignedUrl, testZipBytes)

        assertTrue(result is S3ZipUploader.UploadResult.Success)

        verify {
            mockOkHttpClient.newCall(match { request ->
                request.url.toString() == testPreSignedUrl &&
                        request.method == "PUT"
            })
        }
    }

    @Test
    fun `uploadImagesAsZip returns error when all upload attempts fail`() = runTest {
        every { mockCall.enqueue(any()) } answers {
            val callback = firstArg<Callback>()
            callback.onFailure(mockCall, IOException("Network error"))
        }

        val result = s3ZipUploader.uploadImagesAsZip(testPreSignedUrl, testZipBytes)

        assertTrue(result is S3ZipUploader.UploadResult.Error)
        assertEquals(
            S3ZipUploader.ErrorCode.MAX_RETRIES_EXCEEDED,
            (result as S3ZipUploader.UploadResult.Error).code
        )
        assertEquals("Failed to upload after 3 attempts", result.message)
        assertNotNull(result.exception)

        verify(exactly = 4) { mockCall.enqueue(any()) }
    }

    @Test
    fun `uploadImagesAsZip retries with exponential backoff`() = runTest {
        var attemptCount = 0
        every { mockCall.enqueue(any()) } answers {
            val callback = firstArg<Callback>()
            attemptCount++

            if (attemptCount == 1) {
                // First attempt fails
                callback.onFailure(mockCall, IOException("Network error"))
            } else {
                // Second attempt succeeds
                val mockResponse = mockk<Response>()
                every { mockResponse.isSuccessful } returns true
                every { mockResponse.close() } just Runs
                every { mockResponse.body } returns null
                callback.onResponse(mockCall, mockResponse)
            }
        }

        val result = s3ZipUploader.uploadImagesAsZip(testPreSignedUrl, testZipBytes)

        assertTrue(result is S3ZipUploader.UploadResult.Success)

        // Verify correct number of attempts - should be exactly 2 (one failure followed by one success)
        verify(exactly = 2) { mockCall.enqueue(any()) }
    }

    @Test
    fun `uploadImagesAsZip retries on unsuccessful HTTP response`() = runTest {
        var attemptCount = 0
        every { mockCall.enqueue(any()) } answers {
            val callback = firstArg<Callback>()
            attemptCount++

            if (attemptCount == 1) {
                // First attempt returns 500 error
                val errorResponse = mockk<Response>()
                val errorResponseBody = mockk<ResponseBody>()
                every { errorResponseBody.string() } returns "Server Error"
                every { errorResponseBody.close() } just Runs
                every { errorResponse.isSuccessful } returns false
                every { errorResponse.code } returns 500
                every { errorResponse.body } returns errorResponseBody
                every { errorResponse.close() } just Runs
                callback.onResponse(mockCall, errorResponse)
            } else {
                // Second attempt succeeds
                val successResponse = mockk<Response>()
                every { successResponse.isSuccessful } returns true
                every { successResponse.body } returns null
                every { successResponse.close() } just Runs
                callback.onResponse(mockCall, successResponse)
            }
        }

        val result = s3ZipUploader.uploadImagesAsZip(testPreSignedUrl, testZipBytes)

        assertTrue(result is S3ZipUploader.UploadResult.Success)
        verify(exactly = 2) { mockCall.enqueue(any()) }
    }

    @Test
    fun `uploadImagesAsZip creates correct request body`() = runTest {
        val requestSlot = slot<Request>()
        every { mockOkHttpClient.newCall(capture(requestSlot)) } returns mockCall

        every { mockCall.enqueue(any()) } answers {
            val callback = firstArg<Callback>()
            val successResponse = mockk<Response>()
            every { successResponse.isSuccessful } returns true
            every { successResponse.body } returns null
            every { successResponse.close() } just Runs
            callback.onResponse(mockCall, successResponse)
        }

        s3ZipUploader.uploadImagesAsZip(testPreSignedUrl, testZipBytes)

        assertTrue(requestSlot.isCaptured)
        assertEquals("PUT", requestSlot.captured.method)
        assertEquals(testPreSignedUrl, requestSlot.captured.url.toString())
        assertNotNull(requestSlot.captured.body)
    }

    @Test
    fun `companion object create method configures correctly`() {
        val customMaxRetries = 5
        val customBaseRetryDelayMs = 2000L
        val customUseExponentialBackoff = false

        val uploader = S3ZipUploader.create(
            maxRetries = customMaxRetries,
            baseRetryDelayMs = customBaseRetryDelayMs,
            useExponentialBackoff = customUseExponentialBackoff
        )

        val configField = S3ZipUploader::class.java.getDeclaredField("config")
        configField.isAccessible = true
        val config = configField.get(uploader) as S3ZipUploader.S3UploaderConfig

        assertEquals(customMaxRetries, config.maxRetries)
        assertEquals(customBaseRetryDelayMs, config.baseRetryDelayMs)
        assertEquals(customUseExponentialBackoff, config.useExponentialBackoff)
    }

    @Test
    fun `uploadImagesAsZip retries with fixed delay when exponential backoff disabled`() = runTest {
        val uploaderWithoutBackoff = S3ZipUploader(
            okHttpClient = mockOkHttpClient,
            config = S3ZipUploader.S3UploaderConfig(
                maxRetries = 3,
                baseRetryDelayMs = 100,
                useExponentialBackoff = false
            )
        )

        var attemptCount = 0
        every { mockCall.enqueue(any()) } answers {
            val callback = firstArg<Callback>()
            attemptCount++

            if (attemptCount <= 2) {
                // First two attempts fail
                callback.onFailure(mockCall, IOException("Network error"))
            } else {
                // Third attempt succeeds
                val mockResponse = mockk<Response>()
                every { mockResponse.isSuccessful } returns true
                every { mockResponse.close() } just Runs
                every { mockResponse.body } returns null
                callback.onResponse(mockCall, mockResponse)
            }
        }

        val result = uploaderWithoutBackoff.uploadImagesAsZip(testPreSignedUrl, testZipBytes)

        assertTrue(result is S3ZipUploader.UploadResult.Success)

        // Verify correct number of attempts - should be exactly 3 (two failures followed by one success)
        verify(exactly = 3) { mockCall.enqueue(any()) }
    }
}
