package com.mindbodyonline.connect.profile.schedule.cancellation

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.ViewModel
import com.mindbodyonline.connect.common.repository.MockLocationRepository
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.api.gateway.model.CancellabilityStatusCode
import com.mindbodyonline.connect.utils.gateway.MockSwamiApi
import com.mindbodyonline.connect.utils.viewmodels.MainCoroutineRule
import com.mindbodyonline.connect.utils.viewmodels.getOrAwaitValue
import com.mindbodyonline.connect.widgets.v3.refreshWidget
import com.mindbodyonline.domain.VisitCancelStatus
import com.mindbodyonline.domain.connv1.Visit
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runBlockingTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@ExperimentalCoroutinesApi
class VisitCancellationViewModelTest {

    @get:Rule
    var instantExecutorRule = InstantTaskExecutorRule()

    @get:Rule
    var mainCoroutineRule = MainCoroutineRule()

    private lateinit var visitCancellationViewModel: VisitCancellationViewModel
    private lateinit var locationRepository: MockLocationRepository
    private lateinit var mockSwamiApi: MockSwamiApi

    @Before
    fun setUp() {
        mockSwamiApi = MockSwamiApi()
        locationRepository = MockLocationRepository(MockLocationRepository.MOCK_PHONE)
        locationRepository.isReturnNull=false
        visitCancellationViewModel = VisitCancellationViewModel(mockSwamiApi,locationRepository)
    }

    @Test
    fun testQueryPhoneNumberNull(){
        mainCoroutineRule.runBlockingTest {
            visitCancellationViewModel.queryPhoneNumber()
            val phoneNumber = visitCancellationViewModel.phoneNumber.getOrAwaitValue()
            assertNull(phoneNumber)
        }
    }

    @Test
    fun testCancelVisitNull(){
        mainCoroutineRule.runBlockingTest {
            visitCancellationViewModel.queryVisitCancelStatus(getMockVisit(false))
            visitCancellationViewModel.cancelVisit()
            val isVisitCancel = visitCancellationViewModel.visitCancelResult.getOrAwaitValue()
            assertFalse(isVisitCancel)
        }
    }

    @Test
    fun testCancelVisitSuccess(){
        mainCoroutineRule.runBlockingTest {
            mockSwamiApi.responseType = MockSwamiApi.ResponseType.SUCCESS
            mockkStatic(SharedPreferencesUtils::class)
            mockkStatic(ViewModel::refreshWidget) {
                with(mockk<ViewModel>()) {
                    every { refreshWidget(any()) } just Runs
                }
            }
            every { SharedPreferencesUtils.isCalendarSyncEnabled() } returns false
            every { SharedPreferencesUtils.incrementOrDecrementTotalBookings(false) } returns 10
            visitCancellationViewModel.queryVisitCancelStatus(getMockVisit(true))
            visitCancellationViewModel.cancelVisit()
            val isVisitCancel = visitCancellationViewModel.visitCancelResult.getOrAwaitValue()
            assertTrue(isVisitCancel)
        }
    }

    @Test
    fun testCancelVisitFailure(){
        mainCoroutineRule.runBlockingTest {
            mockSwamiApi.responseType = MockSwamiApi.ResponseType.FAILURE
            visitCancellationViewModel.queryVisitCancelStatus(getMockVisit(true))
            visitCancellationViewModel.cancelVisit()
            val isVisitCancel = visitCancellationViewModel.visitCancelResult.getOrAwaitValue()
            assertFalse(isVisitCancel)
        }
    }

    @Test
    fun testQueryVisitCancelStatusNonCancellable(){
        mainCoroutineRule.runBlockingTest {
            visitCancellationViewModel.queryVisitCancelStatus(getMockVisit(false))
            val cancelStatus = visitCancellationViewModel.cancelStatus.getOrAwaitValue()
            assertEquals(cancelStatus, VisitCancelStatus.NON_CANCELLABLE)
        }
    }

    @Test
    fun testQueryVisitCancelStatusCancellable(){
        mainCoroutineRule.runBlockingTest {
            MockSwamiApi.cancellabilityStatusCode = CancellabilityStatusCode.CANCELLABLE
            visitCancellationViewModel.queryVisitCancelStatus(getMockVisit(true))
            val cancelStatus = visitCancellationViewModel.cancelStatus.getOrAwaitValue()
            assertEquals(cancelStatus, VisitCancelStatus.CANCELLABLE)
        }
    }

    @Test
    fun testQueryVisitCancelStatusLateCancellable(){
        mainCoroutineRule.runBlockingTest {
            MockSwamiApi.cancellabilityStatusCode = CancellabilityStatusCode.DP_CANCELLABLE
            visitCancellationViewModel.queryVisitCancelStatus(getMockVisit(true))
            val cancelStatus = visitCancellationViewModel.cancelStatus.getOrAwaitValue()
            assertEquals(cancelStatus, VisitCancelStatus.CANCELLABLE_LATE)
        }
    }

    @Test
    fun testQueryVisitCancelStatusNonCancellableSuccess(){
        mainCoroutineRule.runBlockingTest {
            MockSwamiApi.cancellabilityStatusCode = CancellabilityStatusCode.NOT_CANCELLABLE
            visitCancellationViewModel.queryVisitCancelStatus(getMockVisit(true))
            val cancelStatus = visitCancellationViewModel.cancelStatus.getOrAwaitValue()
            assertEquals(cancelStatus, VisitCancelStatus.NON_CANCELLABLE)
        }
    }

    @Test
    fun testQueryVisitCancelStatusLateCancellableSuccess(){
        mainCoroutineRule.runBlockingTest {
            MockSwamiApi.cancellabilityStatusCode = CancellabilityStatusCode.LATE_CANCELLABLE
            visitCancellationViewModel.queryVisitCancelStatus(getMockVisit(true))
            val cancelStatus = visitCancellationViewModel.cancelStatus.getOrAwaitValue()
            assertEquals(cancelStatus, VisitCancelStatus.CANCELLABLE_LATE)
        }
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    private fun getMockVisit(isBookingRefJson: Boolean) = Visit().apply {
        InventorySource = MockLocationRepository.MOCK_INVENTORY_SOURCE
        LocationInventoryRefJson = MockLocationRepository.MOCK_LOCATION_REFERENCE.toString()
        if (isBookingRefJson){
            BookingRefJson = "BookingRefJson"
        }
    }
}