package com.mindbodyonline.connect.activities.flex

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.ViewModel
import com.mindbodyonline.connect.R
import com.mindbodyonline.connect.common.repository.MockLocationRepository
import com.mindbodyonline.connect.tealium.TrackingHelperUtils
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.gateway.MockSwamiApi
import com.mindbodyonline.connect.utils.viewmodels.getOrAwaitValue
import com.mindbodyonline.connect.widgets.v3.refreshWidget
import com.mindbodyonline.domain.connv1.Visit
import io.mockk.*
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import kotlin.reflect.KFunction

class CreditRequestViewModelTest {

    @get:Rule
    var instantExecutorRule = InstantTaskExecutorRule()

    private lateinit var creditRequestViewModel: CreditRequestViewModel
    private lateinit var mockSwamiApi: MockSwamiApi
    private val mockDetails = "mockDetails"

    @Before
    fun setUp() {
        mockSwamiApi = MockSwamiApi()
        creditRequestViewModel = CreditRequestViewModel(mockSwamiApi)
    }

    @Test
    fun testStoreRequestReasonOtherAndSelectTrueNoDetails(){
        creditRequestViewModel.storeRequestReason(R.id.selector_reason_other,true)
        assertEquals(creditRequestViewModel.isReadyProceed.getOrAwaitValue(), ReadyToProceed.NOT_READY_DETAILS)
    }

    @Test
    fun testStoreRequestReasonOtherAndSelectTrueWithDetails(){
        creditRequestViewModel.storeReasonDetails(mockDetails)
        creditRequestViewModel.storeRequestReason(R.id.selector_reason_other,true)
        assertEquals(creditRequestViewModel.isReadyProceed.getOrAwaitValue(), ReadyToProceed.READY)
    }

    @Test
    fun testStoreRequestReasonOtherAndSelectFalseWithDetails(){
        creditRequestViewModel.storeReasonDetails(mockDetails)
        creditRequestViewModel.storeRequestReason(R.id.selector_reason_other,false)
        assertEquals(creditRequestViewModel.isReadyProceed.getOrAwaitValue(), ReadyToProceed.NOT_READY)
    }

    @Test
    fun testStoreRequestReasonNotOtherAndSelectTrueWithDetails(){
        creditRequestViewModel.storeReasonDetails(mockDetails)
        creditRequestViewModel.storeRequestReason(R.id.selector_reason_no_access,true)
        assertEquals(creditRequestViewModel.isReadyProceed.getOrAwaitValue(), ReadyToProceed.READY)
    }

    @Test
    fun testStoreReasonDetailsWithDetailsAndReasonAsOther(){
        creditRequestViewModel.storeRequestReason(R.id.selector_reason_other,true)
        creditRequestViewModel.storeReasonDetails(mockDetails)
        assertEquals(creditRequestViewModel.isReadyProceed.getOrAwaitValue(), ReadyToProceed.READY)
    }

    @Test
    fun testStoreReasonDetailsWithEmptyDetailsAndReasonAsOther(){
        creditRequestViewModel.storeRequestReason(R.id.selector_reason_other,true)
        creditRequestViewModel.storeReasonDetails("")
        assertEquals(creditRequestViewModel.isReadyProceed.getOrAwaitValue(), ReadyToProceed.NOT_READY_DETAILS)
    }

    @Test
    fun testContinueSelectedVisitNull(){
        creditRequestViewModel.continueSelected()
        val creditRequestNullRefJson = creditRequestViewModel.visitCancelResult.getOrAwaitValue()
        assertTrue(creditRequestNullRefJson is CreditRequestResult.CreditRequestNullRefJson)
    }

    @Test
    fun testContinueSelectedVisitWithBookingRefSuccess(){
        mockSwamiApi.responseType = MockSwamiApi.ResponseType.SUCCESS
        mockkStatic(SharedPreferencesUtils::class)
        mockkStatic(ViewModel::refreshWidget) {
            with(mockk<ViewModel>()) {
                every { refreshWidget(any()) } just Runs
            }
        }

        mockkObject(TrackingHelperUtils::class)
        every { SharedPreferencesUtils.isCalendarSyncEnabled() } returns false
        every { SharedPreferencesUtils.incrementOrDecrementTotalBookings(false) } returns 10
        creditRequestViewModel.visit = getMockVisit()
        creditRequestViewModel.continueSelected()
        val creditRequestNullRefJson = creditRequestViewModel.visitCancelResult.getOrAwaitValue()
        assertTrue(creditRequestNullRefJson is CreditRequestResult.CreditRequestSuccess)
    }

    @Test
    fun testContinueSelectedVisitWithBookingRefError_409(){
        MockSwamiApi.statusCode = 409
        mockSwamiApi.responseType = MockSwamiApi.ResponseType.FAILURE
        creditRequestViewModel.visit = getMockVisit()
        creditRequestViewModel.continueSelected()
        val creditRequestNullRefJson = creditRequestViewModel.visitCancelResult.getOrAwaitValue()
        assertTrue(creditRequestNullRefJson is CreditRequestResult.CreditRequestAlreadyExists)
    }

    @Test
    fun testContinueSelectedVisitWithBookingRefError_422(){
        MockSwamiApi.statusCode = 422
        mockSwamiApi.responseType = MockSwamiApi.ResponseType.FAILURE
        creditRequestViewModel.visit = getMockVisit()
        creditRequestViewModel.continueSelected()
        val creditRequestNullRefJson = creditRequestViewModel.visitCancelResult.getOrAwaitValue()
        assertTrue(creditRequestNullRefJson is CreditRequestResult.CreditRequestInvalid)
    }

    @Test
    fun testContinueSelectedVisitWithBookingRefError(){
        mockSwamiApi.responseType = MockSwamiApi.ResponseType.FAILURE
        creditRequestViewModel.visit = getMockVisit()
        creditRequestViewModel.continueSelected()
        val creditRequestNullRefJson = creditRequestViewModel.visitCancelResult.getOrAwaitValue()
        assertTrue(creditRequestNullRefJson is CreditRequestResult.CreditRequestGeneralResult)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    private fun getMockVisit() = Visit().apply {
        InventorySource = MockLocationRepository.MOCK_INVENTORY_SOURCE
        LocationInventoryRefJson = MockLocationRepository.MOCK_LOCATION_REFERENCE.toString()
        BookingRefJson = "BookingRefJson"
    }
}