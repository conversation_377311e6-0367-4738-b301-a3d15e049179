package com.mindbodyonline.connect.analytics.search

import android.content.SharedPreferences
import android.location.Location
import com.mindbodyonline.ConnectApp
import com.mindbodyonline.analytics.Event
import com.mindbodyonline.analytics.EventType
import com.mindbodyonline.android.api.sales.model.search.DistanceUnit
import com.mindbodyonline.connect.analytics.CITY
import com.mindbodyonline.connect.analytics.COUNTRY
import com.mindbodyonline.connect.analytics.INPUT
import com.mindbodyonline.connect.analytics.LATITUDE
import com.mindbodyonline.connect.analytics.LOCATION
import com.mindbodyonline.connect.analytics.LONGITUDE
import com.mindbodyonline.connect.analytics.SEARCH
import com.mindbodyonline.connect.analytics.STATE
import com.mindbodyonline.connect.analytics.SearchLocation
import com.mindbodyonline.connect.tealium.TrackingHelperUtils
import com.mindbodyonline.connect.utils.SharedPreferencesUtils
import com.mindbodyonline.connect.utils.analytics.AnalyticsLocator
import com.mindbodyonline.connect.utils.analytics.FallbackAnalyticsTrackerImpl
import com.mindbodyonline.domain.dataModels.ConnectSearchModel
import com.mindbodyonline.framework.abvariant.DevelopmentFlag
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.verify
import org.junit.Before
import org.junit.Test

class SearchEventTrackerTest {

    @Before
    fun setUp() {
        // mock context
        mockkStatic(ConnectApp::class)
        val mockAppContext = mockk<ConnectApp>()
        every { ConnectApp.getInstance() } returns mockAppContext

        // mock shared preferences (TODO create a separate MockSharedPrefUtils class)
        mockkStatic(SharedPreferencesUtils::class)
        val mockSharedPrefs = mockk<SharedPreferences>()
        every { SharedPreferencesUtils.getGlobalSharedPrefs() } returns mockSharedPrefs
        every { SharedPreferencesUtils.getLastSelectedSortingOption() } returns ""
        every { SharedPreferencesUtils.getSelectedFitnessClassTypes() } returns setOf()
        every { SharedPreferencesUtils.getDistanceUnits() } returns DistanceUnit.MILES
        every { SharedPreferencesUtils.getFavoritesOnlyFilterEnabled() } returns false
        every { SharedPreferencesUtils.getGlobalIndicatorLocation() } returns Location("")

        every { mockSharedPrefs.getString(any(), any()) } returns ""
        every { mockSharedPrefs.getFloat(any(), any()) } returns 1.0f
        every { mockSharedPrefs.getLong(any(), any()) } returns 1

        // mock analytics (TODO create a separate MockAnalyticsAPI class)
        mockkStatic(AnalyticsLocator::class)
        mockkObject(TrackingHelperUtils)
        every { AnalyticsLocator.analyticsTracker } returns FallbackAnalyticsTrackerImpl()
    }

    @Test
    fun `search home event should be called with correct event type and name`() {
        val eventSlot = slot<Event>()
        every { AnalyticsLocator.analyticsTracker.track(capture(eventSlot)) } just Runs

        SearchEventTracker.trackSearchHomePageView()

        with(eventSlot.captured) {
            assert(type == EventType.ScreenViewed)
            assert(name == SEARCH)
        }
    }

    @Test
    fun `search event should be called with correct event type`() {
        val eventSlot = slot<Event>()
        every { AnalyticsLocator.analyticsTracker.track(capture(eventSlot)) } just Runs

        SearchFlowTracker.trueSearchFlow = true
        SearchEventTracker.trackIfTrueSearch(ConnectSearchModel())

        with(eventSlot.captured) {
            assert(type == EventType.Search)
        }
    }

    @Test
    fun `tealium search event should be called on true search`() {
        SearchFlowTracker.trueSearchFlow = true
        SearchEventTracker.trackIfTrueSearch(ConnectSearchModel())

        verify { TrackingHelperUtils.trackSearchEvent(any(), any()) }
    }

    @Test
    fun `search results event should be called with correct event name and type`() {
        val eventSlot = slot<Event>()
        every { AnalyticsLocator.analyticsTracker.track(capture(eventSlot)) } just Runs

        SearchEventTracker.trackSearchResultsView(ConnectSearchModel(), 1, SearchResultsType.SEARCH_RESULTS_MAP)

        with(eventSlot.captured) {
            assert(type == EventType.ScreenViewed)
            assert(name == SearchResultsType.SEARCH_RESULTS_MAP.type)
        }
    }

    @Test
    fun `search event should be called with correct search location in metadata`() {
        val eventSlot = slot<Event>()
        every { AnalyticsLocator.analyticsTracker.track(capture(eventSlot)) } just Runs

        val searchModel = ConnectSearchModel()
        searchModel.address = "Fort Irwin, CA, USA"
        SearchFlowTracker.trueSearchFlow = true
        SearchEventTracker.trackIfTrueSearch(searchModel)

        val expectedSearchLocation = SearchLocation(
                input = searchModel.address,
                city = "Fort Irwin",
                state = "CA",
                country = "USA",
                latitude = 0.0,
                longitude = 0.0,
        )
        with(eventSlot.captured) {
            assert(data.containsKey(LOCATION))
            assert(data[LOCATION] is Map<*, *>)
            assert((data[LOCATION] as Map<*, *>)[INPUT] == expectedSearchLocation.input)
            assert((data[LOCATION] as Map<*, *>)[CITY] == expectedSearchLocation.city)
            assert((data[LOCATION] as Map<*, *>)[STATE] == expectedSearchLocation.state)
            assert((data[LOCATION] as Map<*, *>)[COUNTRY] == expectedSearchLocation.country)
            assert((data[LOCATION] as Map<*, *>)[LATITUDE] == expectedSearchLocation.latitude)
            assert((data[LOCATION] as Map<*, *>)[LONGITUDE] == expectedSearchLocation.longitude)
        }
    }
}
