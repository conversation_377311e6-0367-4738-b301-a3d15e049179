package com.mindbodyonline.connect.common.repository

import com.mindbodyonline.connect.adapters.filters.IFilter
import com.mindbodyonline.connect.common.Resource
import com.mindbodyonline.connect.utils.api.gateway.model.ClassStatus
import com.mindbodyonline.connect.utils.api.gateway.model.CourseAttributes
import com.mindbodyonline.connect.utils.api.gateway.model.Room
import com.mindbodyonline.connect.utils.api.gateway.model.refs.LocationRefJson
import com.mindbodyonline.data.dataModels.schedule.ClassAttributesDTO
import com.mindbodyonline.data.dataModels.schedule.GenderDTO
import com.mindbodyonline.data.dataModels.schedule.PricingInfoDTO
import com.mindbodyonline.data.dataModels.schedule.PricingTokenDTO
import com.mindbodyonline.data.dataModels.schedule.PurchaseOptionDTO
import com.mindbodyonline.data.dataModels.schedule.ScheduleAttributesDTO
import com.mindbodyonline.data.dataModels.schedule.StaffDTO
import com.mindbodyonline.domain.ClassTypeObject
import com.mindbodyonline.domain.Location
import com.mindbodyonline.domain.dataModels.schedule.ScheduleAttributes
import com.mindbodyonline.domain.datamapper.toDynamicPricingToken
import com.mindbodyonline.domain.datamapper.toPricingToken
import com.mindbodyonline.domain.datamapper.toScheduleAttributes
import com.mindbodyonline.domain.repository.BusinessScheduleRepository
import com.mindbodyonline.domain.usecase.FetchScheduledClassesAndEnrolments
import junit.framework.TestCase.assertEquals
import junit.framework.TestCase.assertTrue
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.TestCoroutineScope
import kotlinx.coroutines.test.runBlockingTest
import org.junit.Test
import java.math.BigDecimal
import java.util.Calendar

@ExperimentalCoroutinesApi
class ScheduleRepositoryTest {

    private val testDispatcher = TestCoroutineDispatcher()
    private val testScope = TestCoroutineScope(testDispatcher)

    private val STAFF_NAME = "John Doe"
    private val COURSE_NAME = "Yoga Basics"
    private val STATUS = ClassStatus().apply {
        id = 101
        status = "Bookable"
    }
    private val PRICING_TOKEN = PricingTokenDTO(
        token = "TOKEN_123",
        amount = 15.0,
        ttl = "3600",
        classTimeId = 123L,
        dropInPrice = 20.0,
        mindbodyId = "MB_456",
        surge = false,
        active = true
    )

    private val MOCK_SCHEDULE_RESPONSE = ScheduleAttributesDTO(
        subType = "class",
        attributes = ClassAttributesDTO(
            capacity = 20,
            startTime = "2025-03-18T10:00:00Z",
            endTime = "2025-03-18T11:00:00Z",
            duration = 60,
            staff = StaffDTO(
                avatar = "avatar_url",
                bio = "Experienced yoga instructor",
                gender = GenderDTO(
                    code = 1,
                    name = "John"
                ),
                name = STAFF_NAME,
                inventoryRefJson = "STAFF_REF_JSON"
            ),
            course = CourseAttributes().apply {
                name = COURSE_NAME
            },
            openings = 5,
            webOpenings = 3,
            dpOpenings = 2, purchaseOption = PurchaseOptionDTO(
                id = "PRICING_1",
                name = "Drop-In",
                isPackage = false,
                isDynamicallyPriced = false,
                isIntroOffer = false,
                isSingleSession = true,
                pricing = PricingInfoDTO(
                    retail = BigDecimal("20.00"),
                    online = BigDecimal("15.00")
                )
            ),
            inventoryRefJson = null,
            prerequisiteNotes = "None",
            isFreeToEnroll = false,
            cancellationPolicy = "24-hour notice required",
            refundPolicy = "No refunds",
            livestreamEnabled = true,
            contentFormats = listOf("video", "audio"),
            room = Room(
                id = 1,
                name = "Room A"
            ),
            status = STATUS,
            pricingToken = PRICING_TOKEN
        )
    )


    private fun generateTestRepository(): BusinessScheduleRepository = object : BusinessScheduleRepository {
        override suspend fun fetchBusinessScheduleData(
            locationRefJson: LocationRefJson?,
            startDate: Calendar,
            endDate: Calendar,
            filters: MutableSet<IFilter<ClassTypeObject>>?,
        ): List<ScheduleAttributes>? {
            return listOf(
                MOCK_SCHEDULE_RESPONSE.toScheduleAttributes()
            )
        }
    }

    @Test
    fun testFetchScheduledClassesAndEnrolmentsUseCase() = testScope.runBlockingTest {

        testScope.runBlockingTest {
            val mockRepo = generateTestRepository()
            val startDate = Calendar.getInstance()
            val endDate = Calendar.getInstance()

            val results = mutableListOf<Resource<List<ClassTypeObject>>>()

            FetchScheduledClassesAndEnrolments(mockRepo).invokeLegacy(Location(), startDate, endDate).toList(results)


            assertEquals(2, results.size)
            assertTrue(results[0] is Resource.Loading)
            val successResult = results[1] as Resource.Success

            assertEquals(1, successResult.data?.size)
            val classTypeObject = successResult.data?.first()

            // assert course and staff data
            assertEquals(COURSE_NAME, classTypeObject?.name)
            assertEquals(STAFF_NAME, classTypeObject?.staff?.displayName)

            // assert pricing data
            val token = PRICING_TOKEN.toPricingToken().toDynamicPricingToken()
            assertEquals(token.dspoPrice, classTypeObject?.dspoPrice)
            assertEquals(token.dropInPrice, classTypeObject?.dropInPrice)
            assertEquals(token.isSurge, classTypeObject?.isSurge)
            assertEquals(token.isActive, classTypeObject?.isActive)
            assertEquals(token.token, classTypeObject?.dynamicPricingToken)

            // assert bookability status data
            assertEquals(STATUS.id, classTypeObject?.status?.id)
            assertEquals(STATUS.status, classTypeObject?.status?.bookabilityStatus)
        }
    }
}
