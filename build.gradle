// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    // NOTE: Since the buildscript closure is the first thing that's evaluated, place all
    //  ext variables inside of the following block
    ext {
        kotlin_version = '2.1.0'
        newrelic_version = '7.6.5'
        roktsdk_version = '4.7.0'
    }

    project.ext.enableCoverage = false
    if (project.hasProperty("EnableCoverage")) {
        project.ext.enableCoverage = Boolean.parseBoolean(project.properties["EnableCoverage"])
    }

    repositories {
        google()
        mavenCentral()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.3'
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.5"
        classpath "com.newrelic.agent.android:agent-gradle-plugin:$newrelic_version"
        classpath "org.jetbrains.kotlin:kotlin-allopen:$kotlin_version"
        if (project.ext.enableCoverage) {
            classpath 'org.jacoco:org.jacoco.core:0.8.4'
        }
        classpath 'com.github.bjoernq:unmockplugin:0.7.9'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        jcenter()
        maven {
            url "https://maven.tealiumiq.com/android/releases/"
        }
        maven {
            url "https://apps.rokt.com/msdk"
        }
    }
}

apply plugin: 'org.sonarqube'

sonarqube {
    androidVariant 'fullDebug'
    properties {
        property 'sonar.language', 'java'
        property 'sonar.sources', 'src'
    }
}

project(":libraries") {
    sonarqube.skipProject = true
}

subprojects {
    sonarqube {
        properties {
            property 'sonar.sources', 'src/main'
            property 'sonar.tests', 'src/test'
        }
    }
}